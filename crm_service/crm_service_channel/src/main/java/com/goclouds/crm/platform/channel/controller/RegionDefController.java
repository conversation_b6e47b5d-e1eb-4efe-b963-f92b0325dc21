package com.goclouds.crm.platform.channel.controller;

import com.goclouds.crm.platform.channel.service.CrmAwsRegionDefService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/regionDef")
@RequiredArgsConstructor
public class RegionDefController {
    
    private final CrmAwsRegionDefService crmAwsRegionDefService;

    /**
     * 查询region信息
     * @return AjaxResult
     */
    @GetMapping("/queryRegionDefList")
    public AjaxResult queryRegionDefList() {
        try {
            return crmAwsRegionDefService.queryRegionDefList();
        } catch (Exception e){
            log.error("查询region信息失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }
}
