package com.goclouds.crm.platform.channel.domain.vo;

import com.goclouds.crm.platform.channel.domain.CrmAmazonMarketRegionDef;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description:
 * @author: sunlinan
 * @date: 2024-08-15 14:36
 **/
@Data
@Accessors(chain = true)
public class AmazonMarketRegionGroup {
    private String groupName;
    List<CrmAmazonMarketRegionDef> regionDefList;
}
