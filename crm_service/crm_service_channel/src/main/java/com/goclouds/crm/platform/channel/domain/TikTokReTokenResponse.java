package com.goclouds.crm.platform.channel.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR> pengliang.sun
 * @description :
 */
@Getter
@Setter
@ToString
public class TikTokReTokenResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    private String refresh_token;
    private String access_token;
    private String refresh_token_expire_in;
    private String access_token_expire_in;
}
