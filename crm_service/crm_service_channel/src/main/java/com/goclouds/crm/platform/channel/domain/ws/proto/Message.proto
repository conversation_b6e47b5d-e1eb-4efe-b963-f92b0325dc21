syntax = "proto3";
package com.goclouds.crm.platform.channel.domain.ws.proto;
option java_outer_classname="MessageProto";

message Model {
   string id = 1;
   string action = 2;
   string content = 3;
   string sender = 4;
   string receiver = 5;
   string extra = 6;
   string title = 7;
   string format = 8;
   int64 timestamp = 9;
   string username = 10;
   string role = 11;
   string sid = 12;
}
