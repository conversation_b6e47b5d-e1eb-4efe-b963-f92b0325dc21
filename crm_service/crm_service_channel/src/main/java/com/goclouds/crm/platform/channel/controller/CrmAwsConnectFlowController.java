package com.goclouds.crm.platform.channel.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.goclouds.crm.platform.channel.domain.CrmAwsConnectFlow;
import com.goclouds.crm.platform.channel.service.CrmAwsConnectFlowService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("flow")
public class CrmAwsConnectFlowController {
    @Autowired
    private CrmAwsConnectFlowService crmAwsConnectFlowService;

    /**
     * 获取联系流列表
     * @return
     */
    @GetMapping("flowList")
    public AjaxResult flowList(String connectId){
        LambdaQueryWrapper<CrmAwsConnectFlow> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(CrmAwsConnectFlow::getConnectId, connectId);
        List<CrmAwsConnectFlow> list = crmAwsConnectFlowService.list(lambdaQueryWrapper);
        return AjaxResult.ok(list);
    }
}
