package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @Description 案例别名
 * <AUTHOR>
 * @Date 2023/5/23 10:40
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class ConnectAliasVo {

    // 案例id
    @NotBlank
    @Length(max = 40)
    private String connectId;

    // 别名
    @NotBlank
    @Length(max = 80)
    private String connectAlias;

}
