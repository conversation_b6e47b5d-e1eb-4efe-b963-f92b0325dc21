package com.goclouds.crm.platform.channel.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * TikTok订单列表请求参数
 * 对应API方法签名参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TikTokOrderListRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 每页数量（最大值参考API文档）
     */
    private Integer pageSize;


    /**
     * 排序方向（ASC/DESC）
     */
    private String sortOrder;

    /**
     * 分页令牌（next_page_token）
     */
    private String pageToken;

    /**
     * 排序字段（如create_time）
     */
    private String sortBy;

    /**
     * 店铺加密信息（根据API要求）
     */
    private String shopCipher;

    private String sign;

    private Long timestamp;

    private String appKey;

    private String sortField;

    private String accessToken;

    private String appSecret;

    private String buyerUserId;
}