package com.goclouds.crm.platform.channel.handler.annotations;

import com.goclouds.crm.platform.channel.handler.selector.MediaSelector;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(MediaSelector.class)  
public @interface EnableMediaService {
    /** 根据这个值来指定注入不同的Service类  Woztell | WhatsApp */
    String value() default "WhatsApp";
}
