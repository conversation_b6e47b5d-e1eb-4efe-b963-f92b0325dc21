package com.goclouds.crm.platform.channel.controller;

import com.goclouds.crm.platform.annotation.InnerAuth;
import com.goclouds.crm.platform.channel.domain.vo.AwsAccountVo;
import com.goclouds.crm.platform.channel.service.CrmAwsAccountService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.domain.channel.AwsAccountVO;
import com.goclouds.crm.platform.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("awsAccount")
@RequiredArgsConstructor
public class AwsAccountController {
    
    private final CrmAwsAccountService awsAccountService;

    /**
     * 添加aws账号
     * @param awsAccountVo 添加的aws账号信息
     * @return AjaxResult
     */
    @PostMapping("addAwsAccount")
    public AjaxResult addAwsAccount(@RequestBody @Validated AwsAccountVo awsAccountVo) {
        try {
            return awsAccountService.addAwsAccount(awsAccountVo);
        } catch (Exception e){
            log.error("添加aws账号失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 查询所有的aws账号
     * @return AjaxResult
     */
    @GetMapping("queryAwsAccountList")
    public AjaxResult queryAwsAccountList() {
        try {
            return awsAccountService.queryAwsAccountList();
        } catch (Exception e){
            log.error("查询aws账号失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 编辑aws账号
     * @param awsAccountVo 修改的aws账号信息
     * @return AjaxResult
     */
    @PostMapping("updateAwsAccount")
    public AjaxResult updateAwsAccount(@RequestBody @Validated AwsAccountVo awsAccountVo) {
        try {
            return awsAccountService.updateAwsAccount(awsAccountVo);
        } catch (Exception e){
            log.error("编辑aws账号失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 根据账号id查询aws账号信息
     * @return AjaxResult
     */
    @GetMapping("queryAwsAccountInfo/{awsUserId}")
    public AjaxResult queryAwsAccountInfo(@PathVariable String awsUserId) {
        try {
            return awsAccountService.queryAwsAccountInfo(awsUserId);
        } catch (Exception e){
            log.error("根据账号id查询aws账号失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 删除aws账号
     * @return AjaxResult
     */
    @PostMapping("deleteAwsAccount/{awsUserId}")
    public AjaxResult deleteAwsAccount(@PathVariable String awsUserId) {
        try {
            return awsAccountService.deleteAwsAccount(awsUserId);
        } catch (Exception e){
            log.error("删除aws账号失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 获取aws账号的ak、sk
     * @param awsUserId awsUserId
     * @return AjaxResult
     */
    @InnerAuth
    @GetMapping("inner/queryAWSAccount")
    public AjaxResult<AwsAccountVO> queryAWSAccount(@RequestParam(value = "awsUserId", required = false) String awsUserId, @RequestParam("companyId") String companyId) {
        return awsAccountService.queryAWSAccount(awsUserId, companyId);
    }

    /**
     * 获取aws账号的ak、sk
     * @param payerAccountId awsUserId
     * @return AjaxResult
     */
    @InnerAuth
    @GetMapping("inner/queryAWSAccountByPayer")
    public AjaxResult<List<AwsAccountVO>> queryAWSAccountByPayer(@RequestParam(value = "payerAccountId", required = false) String payerAccountId) {
        return awsAccountService.queryAWSAccountByPayer(payerAccountId);
    }
}
