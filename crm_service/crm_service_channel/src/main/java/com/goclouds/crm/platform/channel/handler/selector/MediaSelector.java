package com.goclouds.crm.platform.channel.handler.selector;

import com.goclouds.crm.platform.channel.ChannelApplication;
import com.goclouds.crm.platform.channel.handler.annotations.EnableMediaService;
import com.goclouds.crm.platform.channel.service.impl.WhatsAppMessageImpl;
import com.goclouds.crm.platform.channel.service.impl.WoztellMessageImpl;
import lombok.SneakyThrows;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 根据不同的参数指定 注入不同的媒体Service类
 */
public class MediaSelector implements ImportSelector {
    
    @SneakyThrows
    @Override
    public String[] selectImports(AnnotationMetadata importingClassMetadata) {
        List<String> list = new ArrayList<>();
        Set<String> types = importingClassMetadata.getAnnotationTypes();
        EnableMediaService enableMediaService = ChannelApplication.class.getDeclaredAnnotation(EnableMediaService.class);
        String mediaType = enableMediaService.value();
        if (types.contains(EnableMediaService.class.getName())) {
            if ("Woztell".equals(mediaType)) {
                list.add(WoztellMessageImpl.class.getName());
            } else if ("WhatsApp".equals(mediaType)) {
                list.add(WhatsAppMessageImpl.class.getName());
            }
        }
        
        return list.toArray(new String[list.size()]);
    }
}
