package com.goclouds.crm.platform.channel.listener;

import com.goclouds.crm.platform.channel.config.JDAManager;
import com.goclouds.crm.platform.channel.service.CrmChannelInfoConfigInstService;
import com.goclouds.crm.platform.channel.service.impl.DiscordListener;
import com.goclouds.crm.platform.channel.service.impl.DiscordMessageImpl;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class StartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private CrmChannelInfoConfigInstService crmChannelInfoConfigInstService;

    /**
     * 在项目启动时执行一些代码任务
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("========>>>>> 项目启动后要执行这里的代码.......");

        // 一个服务获取锁执行任务即可 其他服务不需要执行
        // 把所有公司下的所有正常的discord渠道监听token
        initJDADiscordListener();
    }


    /**
     * 把所有公司下的所有正常的discord渠道监听token
     * 一个服务获取锁执行任务即可(其中一个服务抢到锁就可以执行任务，其他未抢到锁的服务不需要执行) 其他服务不需要执行
     */
    private void initJDADiscordListener() {
        RLock lock = redissonClient.getLock(Constants.DISCORD_REGISTER_LISTENER_LOCK);
        try {
            if (lock.tryLock(5, 180, TimeUnit.SECONDS)) {
                try {
                    AjaxResult<Map<String, Map<String, String>>> mapR = crmChannelInfoConfigInstService.queryChannelConfigInst(CrmChannelEnum.DISCORD.getCode());
                    if (mapR != null && mapR.getCode() == AjaxResult.SUCCESS) {
                        // <channelId, <code, name>>
                        Map<String, Map<String, String>> channelConfigMap = mapR.getData();
                        channelConfigMap.forEach((channelId, map) -> {
                            String botName = map.get("botName");
                            String botToken = map.get("botToken");
                            if (StringUtil.isNotBlank(botName) && StringUtil.isNotBlank(botToken)) {
                                DiscordListener discordListener = applicationContext.getBean(DiscordListener.class);
                                discordListener.setChannelId(channelId);
                                DiscordMessageImpl.discordListenerMap.putIfAbsent(channelId, discordListener);
                                JDAManager jdaManager = new JDAManager();
                                jdaManager.reinitializeJDA(botToken, discordListener);
                                DiscordMessageImpl.discordJDAMap.putIfAbsent(channelId, jdaManager);
                            }
                        });

                        log.info("========>>>>> 服务启动 discord channel 进行注册 discordListenerMap: {}, discordJDAMap: {}", DiscordMessageImpl.discordListenerMap, DiscordMessageImpl.discordJDAMap);
                    }
                } finally {
                    // 不要立即释放锁，让锁自动过期释放
                    // lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
