package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @Description aws账号入参
 * <AUTHOR>
 * @Date 2023/5/23 10:40
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class AwsAccountVo {

    // 账号名称
    @NotBlank
    @Length(max = 80)
    private String awsUserName;

    // 账户id
    @NotBlank
    @Length(max = 40)
    private String awsUserId;

    // ak
    @NotBlank
    @Length(max = 80)
    private String accessKey;

    // sk
    @NotBlank
    @Length(max = 80)
    private String secretAccessKey;

    /**
     * regionid,逗号分隔
     */
    @NotBlank
    @Length(max = 2000)
    private String regions;

}
