package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建通道配置，请求参数
 */
@Data
public class CreateChannelVo {

    @NotBlank
    private String name;

    /**
     * 通道类型
     */
    @NotNull
    private Integer channelType;

    /**
     * 通道子类型
     */
    @NotNull
    private Integer channelSubtype;

    /**
     * 区域
     */
    private String region;

//    需求修改，将如下参数的非空校验去掉
//    @NotBlank
    private String connectId;
    /**
     * 配置信息
     */
    @NotEmpty
    private List<ChannelConfigVo> config;
    /**
     * Connect集成配置信息
     */
    private List<ChannelConfigVo> openConnectConfig;

    /**
     * 配置信息扩展
     */
    private List<ChannelConfigVo> analysisConfig;

    private Integer enableStatus;

    /**
     * 是否开启Connect集成，1-开启；0-不开启
     */
    private Integer isOpenConnect;

}
