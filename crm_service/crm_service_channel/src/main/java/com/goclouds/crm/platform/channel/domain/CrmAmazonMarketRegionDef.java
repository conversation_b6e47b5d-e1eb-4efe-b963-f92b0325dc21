package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 站内信region表
 * @TableName crm_amazon_market_region_def
 */
@TableName(value ="crm_amazon_market_region_def")
@Data
public class CrmAmazonMarketRegionDef implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 域名
     */
    private String domain;

    /**
     * 国家icon
     */
    private String countryNameIcon;

    /**
     * 国家所属区域分组名称
     */
    private String groupName;

    /**
     * 数据状态
     */
    private Integer dataStatus;

    /**
     * 卖家平台url
     */
    private String sellerCentralUrl;
    /**
     * 商城Id
     */
    private String marketplaceId;

    /**
     * 国家/地区代码
     */
    private String countryCode;

    /**
     * 供应商中心url
     */
    private String vendorCentralUrl;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}