package com.goclouds.crm.platform.channel.domain;

import java.util.List;
import java.util.stream.Collectors;

public class TikTokOrderResponseConverter {

    public static TikTokOrderListResponse convert(TikTokOrderResponse response) {
        if (response == null) {
            return null;
        }

        TikTokOrderListResponse result = new TikTokOrderListResponse();
        result.setNextPageToken(response.getNext_page_token());
        result.setTotalCount(response.getTotal_count());
        
        if (response.getOrders() != null) {
            List<TikTokOrderListResponse.Order> orderList = response.getOrders().stream()
                .map(TikTokOrderResponseConverter::convertOrder)
                .collect(Collectors.toList());
            result.setOrderList(orderList);
        }

        return result;
    }

    private static TikTokOrderListResponse.Order convertOrder(TikTokOrderResponse.Order source) {
        if (source == null) {
            return null;
        }

        TikTokOrderListResponse.Order target = new TikTokOrderListResponse.Order();
        target.setId(source.getId());
        target.setBuyerMessage(source.getBuyer_message());
        target.setCancellationInitiator(source.getCancellation_initiator());
        target.setShippingProviderId(source.getShipping_provider_id());
        target.setCreateTime(source.getCreate_time());
        target.setShippingProvider(source.getShipping_provider());
        
        if (source.getPackages() != null) {
            List<TikTokOrderListResponse.Package> packages = source.getPackages().stream()
                .map(p -> {
                    TikTokOrderListResponse.Package pkg = new TikTokOrderListResponse.Package();
                    pkg.setId(p.getId());
                    return pkg;
                })
                .collect(Collectors.toList());
            target.setPackages(packages);
        }
        
        target.setPayment(convertPayment(source.getPayment()));
        target.setRecipientAddress(convertRecipientAddress(source.getRecipient_address()));
        target.setStatus(source.getStatus());
        target.setFulfillmentType(source.getFulfillment_type());
        target.setDeliveryType(source.getDelivery_type());
        target.setPaidTime(source.getPaid_time());
        target.setRtsSlaTime(source.getRts_sla_time());
        target.setTtsSlaTime(source.getTts_sla_time());
        target.setCancelReason(source.getCancel_reason());
        target.setUpdateTime(source.getUpdate_time());
        target.setPaymentMethodName(source.getPayment_method_name());
        target.setRtsTime(source.getRts_time());
        target.setTrackingNumber(source.getTracking_number());
        target.setSplitOrCombineTag(source.getSplit_or_combine_tag());
        target.setHasUpdatedRecipientAddress(source.getHas_updated_recipient_address());
        target.setCancelOrderSlaTime(source.getCancel_order_sla_time());
        target.setWarehouseId(source.getWarehouse_id());
        target.setRequestCancelTime(source.getRequest_cancel_time());
        target.setShippingType(source.getShipping_type());
        target.setUserId(source.getUser_id());
        target.setSellerNote(source.getSeller_note());
        target.setDeliverySlaTime(source.getDelivery_sla_time());
        target.setIsCod(source.getIs_cod());
        target.setDeliveryOptionId(source.getDelivery_option_id());
        target.setCancelTime(source.getCancel_time());
        target.setNeedUploadInvoice(source.getNeed_upload_invoice());
        target.setDeliveryOptionName(source.getDelivery_option_name());
        target.setCpf(source.getCpf());
        
        if (source.getLine_items() != null) {
            List<TikTokOrderListResponse.LineItem> lineItems = source.getLine_items().stream()
                .map(TikTokOrderResponseConverter::convertLineItem)
                .collect(Collectors.toList());
            target.setLineItems(lineItems);
        }
        
        target.setBuyerEmail(source.getBuyer_email());
        target.setDeliveryDueTime(source.getDelivery_due_time());
        target.setIsSampleOrder(source.getIs_sample_order());
        target.setShippingDueTime(source.getShipping_due_time());
        target.setCollectionDueTime(source.getCollection_due_time());
        target.setDeliveryOptionRequiredDeliveryTime(source.getDelivery_option_required_delivery_time());
        target.setIsOnHoldOrder(source.getIs_on_hold_order());
        target.setDeliveryTime(source.getDelivery_time());
        target.setIsReplacementOrder(source.getIs_replacement_order());
        target.setCollectionTime(source.getCollection_time());
        target.setReplacedOrderId(source.getReplaced_order_id());
        target.setIsBuyerRequestCancel(source.getIs_buyer_request_cancel());
        target.setPickUpCutOffTime(source.getPick_up_cut_off_time());
        target.setFastDispatchSlaTime(source.getFast_dispatch_sla_time());
        target.setCommercePlatform(source.getCommerce_platform());
        target.setOrderType(source.getOrder_type());
        target.setReleaseDate(source.getRelease_date());
        target.setHandlingDuration(convertHandlingDuration(source.getHandling_duration()));
        target.setAutoCombineGroupId(source.getAuto_combine_group_id());
        target.setCpfName(source.getCpf_name());
        target.setIsExchangeOrder(source.getIs_exchange_order());
        target.setExchangeSourceOrderId(source.getExchange_source_order_id());
        target.setConsultationId(source.getConsultation_id());
        target.setFastDeliveryProgram(source.getFast_delivery_program());
        
        return target;
    }

    private static TikTokOrderListResponse.Payment convertPayment(TikTokOrderResponse.Payment source) {
        if (source == null) {
            return null;
        }

        TikTokOrderListResponse.Payment target = new TikTokOrderListResponse.Payment();
        target.setCurrency(source.getCurrency());
        target.setSubTotal(source.getSub_total());
        target.setShippingFee(source.getShipping_fee());
        target.setSellerDiscount(source.getSeller_discount());
        target.setPlatformDiscount(source.getPlatform_discount());
        target.setTotalAmount(source.getTotal_amount());
        target.setOriginalTotalProductPrice(source.getOriginal_total_product_price());
        target.setOriginalShippingFee(source.getOriginal_shipping_fee());
        target.setShippingFeeSellerDiscount(source.getShipping_fee_seller_discount());
        target.setShippingFeePlatformDiscount(source.getShipping_fee_platform_discount());
        target.setShippingFeeCofundedDiscount(source.getShipping_fee_cofunded_discount());
        target.setTax(source.getTax());
        target.setSmallOrderFee(source.getSmall_order_fee());
        target.setShippingFeeTax(source.getShipping_fee_tax());
        target.setProductTax(source.getProduct_tax());
        target.setRetailDeliveryFee(source.getRetail_delivery_fee());
        target.setBuyerServiceFee(source.getBuyer_service_fee());
        target.setHandlingFee(source.getHandling_fee());
        target.setShippingInsuranceFee(source.getShipping_insurance_fee());
        target.setItemInsuranceFee(source.getItem_insurance_fee());
        
        return target;
    }

    private static TikTokOrderListResponse.RecipientAddress convertRecipientAddress(TikTokOrderResponse.RecipientAddress source) {
        if (source == null) {
            return null;
        }

        TikTokOrderListResponse.RecipientAddress target = new TikTokOrderListResponse.RecipientAddress();
        target.setFullAddress(source.getFull_address());
        target.setPhoneNumber(source.getPhone_number());
        target.setName(source.getName());
        target.setFirstName(source.getFirst_name());
        target.setLastName(source.getLast_name());
        target.setFirstNameLocalScript(source.getFirst_name_local_script());
        target.setLastNameLocalScript(source.getLast_name_local_script());
        target.setAddressDetail(source.getAddress_detail());
        target.setAddressLine1(source.getAddress_line1());
        target.setAddressLine2(source.getAddress_line2());
        target.setAddressLine3(source.getAddress_line3());
        target.setAddressLine4(source.getAddress_line4());
        
        if (source.getDistrict_info() != null) {
            List<TikTokOrderListResponse.DistrictInfo> districtInfo = source.getDistrict_info().stream()
                .map(d -> {
                    TikTokOrderListResponse.DistrictInfo district = new TikTokOrderListResponse.DistrictInfo();
                    district.setAddressLevelName(d.getAddress_level_name());
                    district.setAddressName(d.getAddress_name());
                    district.setAddressLevel(d.getAddress_level());
                    return district;
                })
                .collect(Collectors.toList());
            target.setDistrictInfo(districtInfo);
        }
        
        target.setDeliveryPreferences(convertDeliveryPreferences(source.getDelivery_preferences()));
        target.setPostalCode(source.getPostal_code());
        target.setRegionCode(source.getRegion_code());
        
        return target;
    }

    private static TikTokOrderListResponse.DeliveryPreferences convertDeliveryPreferences(TikTokOrderResponse.DeliveryPreferences source) {
        if (source == null) {
            return null;
        }

        TikTokOrderListResponse.DeliveryPreferences target = new TikTokOrderListResponse.DeliveryPreferences();
        target.setDropOffLocation(source.getDrop_off_location());
        
        return target;
    }

    private static TikTokOrderListResponse.LineItem convertLineItem(TikTokOrderResponse.LineItem source) {
        if (source == null) {
            return null;
        }

        TikTokOrderListResponse.LineItem target = new TikTokOrderListResponse.LineItem();
        target.setId(source.getId());
        target.setSkuId(source.getSku_id());
        
        if (source.getCombined_listing_skus() != null) {
            List<TikTokOrderListResponse.CombinedListingSku> combinedListingSkus = source.getCombined_listing_skus().stream()
                .map(c -> {
                    TikTokOrderListResponse.CombinedListingSku sku = new TikTokOrderListResponse.CombinedListingSku();
                    sku.setSkuId(c.getSku_id());
                    sku.setSkuCount(c.getSku_count());
                    sku.setProductId(c.getProduct_id());
                    sku.setSellerSku(c.getSeller_sku());
                    return sku;
                })
                .collect(Collectors.toList());
            target.setCombinedListingSkus(combinedListingSkus);
        }
        
        target.setDisplayStatus(source.getDisplay_status());
        target.setProductName(source.getProduct_name());
        target.setProductId(source.getProduct_id());
        target.setSellerSku(source.getSeller_sku());
        target.setSkuImage(source.getSku_image());
        target.setSkuName(source.getSku_name());
        target.setProductId(source.getProduct_id());
        target.setSalePrice(source.getSale_price());
        target.setPlatformDiscount(source.getPlatform_discount());
        target.setSellerDiscount(source.getSeller_discount());
        target.setSkuType(source.getSku_type());
        target.setCancelReason(source.getCancel_reason());
        target.setOriginalPrice(source.getOriginal_price());
        target.setRtsTime(source.getRts_time());
        target.setPackageStatus(source.getPackage_status());
        target.setCurrency(source.getCurrency());
        target.setShippingProviderName(source.getShipping_provider_name());
        target.setCancelUser(source.getCancel_user());
        target.setShippingProviderId(source.getShipping_provider_id());
        target.setIsGift(source.getIs_gift());
        
        if (source.getItem_tax() != null) {
            List<TikTokOrderListResponse.ItemTax> itemTaxes = source.getItem_tax().stream()
                .map(i -> {
                    TikTokOrderListResponse.ItemTax tax = new TikTokOrderListResponse.ItemTax();
                    tax.setTaxType(i.getTax_type());
                    tax.setTaxAmount(i.getTax_amount());
                    tax.setTaxRate(i.getTax_rate());
                    return tax;
                })
                .collect(Collectors.toList());
            target.setItemTax(itemTaxes);
        }
        
        target.setTrackingNumber(source.getTracking_number());
        target.setPackageId(source.getPackage_id());
        target.setRetailDeliveryFee(source.getRetail_delivery_fee());
        target.setBuyerServiceFee(source.getBuyer_service_fee());
        target.setSmallOrderFee(source.getSmall_order_fee());
        target.setHandlingDurationDays(source.getHandling_duration_days());
        target.setIsDangerousGood(source.getIs_dangerous_good());
        target.setNeedsPrescription(source.getNeeds_prescription());
        
        return target;
    }

    private static TikTokOrderListResponse.HandlingDuration convertHandlingDuration(TikTokOrderResponse.HandlingDuration source) {
        if (source == null) {
            return null;
        }

        TikTokOrderListResponse.HandlingDuration target = new TikTokOrderListResponse.HandlingDuration();
        target.setDays(source.getDays());
        target.setType(source.getType());
        
        return target;
    }
}