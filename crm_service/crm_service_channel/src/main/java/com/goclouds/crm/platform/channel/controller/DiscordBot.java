package com.goclouds.crm.platform.channel.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.entities.*;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import net.dv8tion.jda.api.interactions.components.LayoutComponent;
import net.dv8tion.jda.api.utils.FileUpload;
import net.dv8tion.jda.api.utils.messages.MessageCreateData;

import javax.security.auth.login.LoginException;
import java.net.URL;
import java.util.List;

@Slf4j
public class DiscordBot  extends ListenerAdapter {
    public static void main(String[] args) throws LoginException {
        String token = "MTM3MDMyMDMyNjgwNTY4ODQyMA.GyGm31.uZ3ilx2ZEzgnFx6fXL4_pXHq6mkpf4g-1R7FrU"; // 替换为你的Bot Token
        JDABuilder.createDefault(token)
                .addEventListeners(new DiscordBot())
                .build();
    }

//    @Override
    @SneakyThrows
    public void onMessageReceived(MessageReceivedEvent event) {
        // 忽略其他Bot的消息
        if (event.getAuthor().isBot()) return;

        Message message = event.getMessage();

//        Message msg = JSONObject.parseObject(JSONObject.toJSONString(message), Message.class);
//        List<Message.Attachment> list = msg.getAttachments();
//        log.info("========>>>>> msg: {}, list: {}", msg, list);


        String content = message.getContentRaw();
        List<Message.Attachment> attachments = message.getAttachments();
        MessageType type = message.getType();
        log.info("========>>>>> type: {}, attachments: {}", type, attachments);
        if (!attachments.isEmpty()) {
            Message.Attachment attachment = attachments.get(0);
            if (attachment != null) {
                log.info("========>>>>> url: {}, content-type: {}, file-name: {}, extend: {}, height: {}, width: {}", attachment.getUrl(), attachment.getContentType(), attachment.getFileName(), attachment.getFileExtension(), attachment.getHeight(), attachment.getWidth());
            }
        }
        String contentDisplay = message.getContentDisplay();

        List<MessageEmbed> embeds = message.getEmbeds();
        if (!embeds.isEmpty()) {
            MessageEmbed messageEmbed = embeds.get(0);
            if (null != messageEmbed) {
                log.info("=======>>>>> type: {}, url: {}, data: {}", messageEmbed.getType(), messageEmbed.getUrl(), messageEmbed.toData());
            }
        }

        List<LayoutComponent> components = message.getComponents();
        if (!components.isEmpty()) {
            LayoutComponent itemComponents = components.get(0);
            if (null != itemComponents) {
                log.info("=======>>>>> data: {}", itemComponents.toData());
            }
        }

        String name = message.getAuthor().getJDA().getSelfUser().getName();

        log.info("=======>>>>> name: {}, contentDisplay: {}, message: {}", name, contentDisplay, message);


        // 回复消息
        if (content.equalsIgnoreCase("你好")) {
//            event.getChannel().sendMessage("您好，欢迎联系联络中心！").queue();

//            MessageCreateData messageCreateData = new MessageCreateData();
            MessageCreateData messageCreateData = MessageCreateData.fromFiles(FileUpload.fromData(new URL("https://vd3.bdstatic.com/mda-memga45ki8a56m6m/cae_h264/1621683767597304977/mda-memga45ki8a56m6m.mp4").openStream(), "mda-memga45ki8a56m6m.mp4"));
            event.getChannel().sendMessage(messageCreateData).queue();

        }
    }
}
