package com.goclouds.crm.platform.channel.controller;

import com.alibaba.fastjson.JSONObject;
import com.goclouds.crm.platform.annotation.InnerAuth;
import com.goclouds.crm.platform.annotation.NoLogin;
import com.goclouds.crm.platform.channel.domain.vo.ConnectMessageVo;
import com.goclouds.crm.platform.channel.service.FacebookService;
import com.goclouds.crm.platform.channel.service.LineService;
import com.goclouds.crm.platform.channel.service.WXOfficeAService;
import com.goclouds.crm.platform.channel.service.impl.*;
import com.goclouds.crm.platform.channel.utils.aes.FeishuDecrypt;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.domain.validate.InsertValidation;
import com.goclouds.crm.platform.domain.validate.UpdateValidation;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.FacebookPublicHomepageVo;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.FacebookVo;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.InsShortTokenVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("media")
public class MediaController {

    /** 这里不能注入MediaMessageService抽象类，用到哪个实现子类就注入哪个子类 */
    private final YCloudMessageImpl yCloudMessage;
    private final WXOfficeAMessageImpl wxOfficeAMessage;
    private final WXBizMessageImpl wxBizMessage;
    private final FacebookMessageImpl facebookMessage;
    private final LineMessageImpl lineMessage;
    private final InstagramMessageImpl instagramMessage;
    private final FacebookService facebookService;
    private final LineService lineService;
    private final WXOfficeAService wxOfficeAService;
    private final AWSInternalMessageImpl awsInternalMessage;
    private final TikTokShopMessageImpl tikTokShopMessage;

    /**
     * 监听WhatsApp消息
     * WhatsApp对应的doc：
     *      woztell(第三方): <a href="https://doc.woztell.com/docs/documentations/channels/channels-webhook/#outbound-messages">...</a>
     *      whatsapp: <a href="https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/components">...</a>
     *      YCloud(第三方): <a href="https://docs.ycloud.com/reference/whatsapp-inbound-message-webhook-examples#inbound-audio-message">...</a>
     * @param jsonObject 传过来的消息
     */
    @NoLogin
    @PostMapping("webhooks")
    public AjaxResult<Object> webhooksWhatsApp(@RequestBody JSONObject jsonObject) {
        log.info("WhatsApp jsonObject: {}", jsonObject);
        yCloudMessage.resolveMessage(jsonObject, true);
        return AjaxResult.ok();
    }


    /**
     * aws站内信 处理消息
     * @param jsonObject jsonObject
     * @return AjaxResult
     */
    @InnerAuth
    @PostMapping("awsStation")
    public AjaxResult<Object> awsStationLetter(@RequestBody JSONObject jsonObject) {
        awsInternalMessage.resolveMessage(jsonObject, false);
        return AjaxResult.ok();
    }

    /**
     * 监听Facebook消息
     * 目前仅支持  text、image、audio、video、file 类型的消息
     * audio按照音频收发 仅支持mav格式
     * <a href="https://developers.facebook.com/docs/messenger-platform/instagram/features/webhook">...</a>
     * get请求用于验证回调url
     * post请求用于解密回复消息
     * @param mode mode
     * @param verifyToken verifyToken
     * @param challenge challenge
     * @param jsonObject jsonObject
     */
    @NoLogin
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.GET}, value = "facebook/webhooks")
    public void webhookFacebook(@RequestParam(value = "hub.mode", required = false) String mode,
                                @RequestParam(value = "hub.verify_token", required = false) String verifyToken,
                                @RequestParam(value = "hub.challenge", required = false) String challenge,
                                @RequestBody(required = false) JSONObject jsonObject) {

        JSONObject json = new JSONObject();
        json.put("mode", mode);
        json.put("verifyToken", verifyToken);
        json.put("challenge", challenge);
        json.put("json", jsonObject);

        log.info("facebook json: {}", json);
        facebookMessage.resolveMessage(json, true);
    }


    /**
     * 监听Instagram消息(Instagram属于meta旗下，验签方法一样)
     * 目前仅支持 text、image、video、audio 类型的消息  不支持接受和发送file类型
     * audio按照音频收发 仅支持mav格式
     * <a href="https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/webhooks">...</a>
     * @param jsonObject jsonObject
     */
    @NoLogin
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.GET}, value = "ig/webhooks")
    public void webhooksIg(@RequestParam(value = "hub.mode", required = false) String mode,
                         @RequestParam(value = "hub.verify_token", required = false) String verifyToken,
                         @RequestParam(value = "hub.challenge", required = false) String challenge,
                         @RequestBody(required = false) JSONObject jsonObject) {
        JSONObject json = new JSONObject();
        json.put("mode", mode);
        json.put("verifyToken", verifyToken);
        json.put("challenge", challenge);
        json.put("json", jsonObject);

        log.info("Instagram jsonObject: {}", json);
        instagramMessage.resolveMessage(json, true);
    }


    /**
     * 监听Line消息
     * 仅支持 text、image、video、audio类型   不支持接受和发送file类型
     * audio按照文件发送 file类型不支持，所以audio类型不支持发送
     * <a href="https://developers.line.biz/en/reference/messaging-api/#message-event">...</a>
     * @param jsonObject jsonObject
     * @return AjaxResult
     */
    @NoLogin
    @PostMapping("line/webhooks/{uk}")
    public AjaxResult<Object> webhooksLine(@PathVariable("uk") String uk, @RequestBody JSONObject jsonObject) {
        log.info("Line jsonObject: {}", jsonObject);
        lineMessage.resolveMessage(jsonObject, false);
        return AjaxResult.ok();
    }

    /**
     * 监听企业微信的消息webhooks
     * 仅支持 text、image、video、voice、file 类型
     * voice 按照文件发送
     * <a href="https://developer.work.weixin.qq.com/document/path/90375">...</a>
     * get请求用于验证回调url
     * post请求用于解密回复消息
     * @param sVerifyMsgSig sVerifyMsgSig
     * @param sVerifyTimeStamp sVerifyTimeStamp
     * @param sVerifyNonce sVerifyNonce
     * @param sVerifyEchoStr sVerifyEchoStr
     * @param sReqData sReqData
     */
    @NoLogin
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.GET}, value = "WXBiz/webhooks")
    public void webhooksWXBiz(@RequestParam(value = "msg_signature", required = false) String sVerifyMsgSig,
                                       @RequestParam(value = "timestamp", required = false) String sVerifyTimeStamp,
                                       @RequestParam(value = "nonce", required = false) String sVerifyNonce,
                                       @RequestParam(value = "echostr", required = false) String sVerifyEchoStr,
                                       @RequestBody(required = false) String sReqData) {

        JSONObject json = new JSONObject();
        json.put("signature", sVerifyMsgSig);
        json.put("timestamp", sVerifyTimeStamp);
        json.put("nonce", sVerifyNonce);
        json.put("echostr", sVerifyEchoStr);
        json.put("sReqData", sReqData);

        log.info("WXBiz json: {}", json);
        wxBizMessage.resolveMessage(json, false);
    }


    /**
     * 监听微信公众号消息webhooks
     * 仅支持 text、image、video、voice 类型   不支持接受和发送file类型
     * voice 按照文件类型发送
     * <a href="https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Receiving_standard_messages.html">...</a>
     * @param uk unique key 唯一id 用于去Redis中获取token
     * @param sVerifyMsgSig sVerifyMsgSig
     * @param sVerifyTimeStamp sVerifyTimeStamp
     * @param sVerifyNonce sVerifyNonce
     * @param sVerifyEchoStr sVerifyEchoStr
     * @param reqData reqData post请求发送的具体数据
     */
    @NoLogin
    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "WXOfficeA/webhooks/{uk}")
    public void webhooksWXOfficeA(@PathVariable("uk") String uk,
                                       @RequestParam(value = "signature", required = false) String sVerifyMsgSig,
                                       @RequestParam(value = "timestamp", required = false) String sVerifyTimeStamp,
                                       @RequestParam(value = "nonce", required = false) String sVerifyNonce,
                                       @RequestParam(value = "echostr", required = false) String sVerifyEchoStr,
                                       @RequestBody(required = false) String reqData) {

        JSONObject json = new JSONObject();
        json.put("uk", uk);
        json.put("signature", sVerifyMsgSig);
        json.put("timestamp", sVerifyTimeStamp);
        json.put("nonce", sVerifyNonce);
        json.put("echostr", sVerifyEchoStr);
        json.put("reqData", reqData);

        log.info("WXOfficeA json: {}", json);
        wxOfficeAMessage.resolveMessage(json, false);
    }

    @NoLogin
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.GET}, value = "feishu/webhooks")
    public String webhooksFeishu(@RequestBody String encryptStr) throws Exception {
        log.info("Feishu encryptStr: {}", encryptStr);
        JSONObject encryptStrObj = JSONObject.parseObject(encryptStr);
        String encrypt = encryptStrObj.getString("encrypt");
        log.info("Feishu encrypt: {}", encrypt);

        String key = "EYMlrEjxmutPAe1uVkqG8exlqgMfnKWG";
        FeishuDecrypt feishuDecrypt = new FeishuDecrypt(key);
        String decrypt = feishuDecrypt.decrypt(encrypt);
        log.info("Feishu decrypt: {}", decrypt);
        JSONObject jsonObject = JSONObject.parseObject(decrypt);
        String challenge = jsonObject.getString("challenge");

        JSONObject json = new JSONObject();
        json.put("challenge", challenge);
        log.info("Feishu json.toJSONString: {}", json.toJSONString());
        return json.toJSONString();
    }


    /**
     * tiktok webhooks
     * <a href="https://partner.tiktokshop.com/docv2/page/66143486ef8a1202dc323258#%E5%9B%9E%E5%88%B0%E9%A1%B6%E9%83%A8">...</a>
     * @param jsonObject jsonObject
     */
    @NoLogin
    @PostMapping("tiktok/webhooks")
    public void webhooksTikTokShop(@RequestBody JSONObject jsonObject) {
        log.info("=========>>>>> tiktok json: {}", jsonObject);
        tikTokShopMessage.resolveMessage(jsonObject, false);
    }

    /**
     * 监听Connect发送消息
     * <B>注意：这个方法仅仅是WhatsApp渠道在使用，其他渠道未使用</B>
     * @param connectMessageVo connectMessageVo
     */
    @PostMapping("message")
    public AjaxResult<Object> message(@RequestBody @Validated({InsertValidation.class}) ConnectMessageVo connectMessageVo) {
        return yCloudMessage.monitorMessageForConnect(connectMessageVo);
    }

    /**
     * 挂断电话后需要清空redis中的token
     * @param connectMessageVo connectMessageVo
     */
    @DeleteMapping("token")
    public AjaxResult<Object> clearToken(@RequestBody @Validated({UpdateValidation.class}) ConnectMessageVo connectMessageVo) {
        return yCloudMessage.clearToken(connectMessageVo);
    }


    /**
     * 获取Facebook公共主页信息
     * @param facebookVo 请求参数
     * @return 公共主页信息
     */
    @PostMapping("facebookPublicHomepage")
    public AjaxResult<List<FacebookPublicHomepageVo>> facebookPublicHomepage(@RequestBody FacebookVo facebookVo) {

        return facebookService.facebookPublicHomepage(facebookVo);

    }

    /**
     *  根据参数，获取ins的应用程序用户
     * @param insShortTokenVo
     * @return
     */
    @PostMapping("instagramApplicationUsers")
    public AjaxResult<List<FacebookPublicHomepageVo>> instagramApplicationUsers(@RequestBody InsShortTokenVo insShortTokenVo){
        return facebookService.instagramApplicationUsers(insShortTokenVo);
    }


    /**
     * 更新insToken信息
     */
    @PostMapping("updateInstagramToken")
    public void updateInstagramToken(){
        facebookService.updateInstagramToken();
    }

    /**
     * 生成line webhook地址
     */
    @PostMapping("lineWebhookUrl")
    public AjaxResult<Object> lineWebhookUrl(@RequestParam(value = "lineId", required = false) String lineId){
       return lineService.lineWebhookUrl(lineId);
    }



    /**
     * 生成微信公众号 webhook地址、token等信息
     */
    @PostMapping("wxOfficeAConfiguration")
    public AjaxResult<Object> wxOfficeAConfiguration(){
        return wxOfficeAService.wxOfficeAConfiguration();
    }

}
