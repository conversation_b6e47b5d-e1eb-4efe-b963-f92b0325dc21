package com.goclouds.crm.platform.channel.config;

import com.goclouds.crm.platform.common.enums.ResultCodeEnum;
import com.goclouds.crm.platform.common.exception.ServiceException;
import com.goclouds.crm.platform.utils.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.exceptions.InvalidTokenException;
import net.dv8tion.jda.api.hooks.EventListener;
import net.dv8tion.jda.api.requests.GatewayIntent;

import java.util.concurrent.TimeUnit;

@Slf4j
public class JDAManager {
    private volatile JDA jdaInstance;
    private final Object lock = new Object();

    /**
     * 安全地重新初始化JDA实例
     * @param newToken 新的Discord token
     * @param newListener 新的监听器实例
     * @throws IllegalStateException 如果旧实例关闭失败
     */
    public void reinitializeJDA(String newToken, EventListener newListener) {
        synchronized (lock) {
            // 1. 关闭并清理旧实例
            shutdownExistingJDA();

            // 2. 创建新实例
            initializeNewJDA(newToken, newListener);
        }
    }

    public void shutdownExistingJDA() {
        if (jdaInstance != null) {
            try {
                // 获取当前所有监听器并移除
                jdaInstance.getEventManager().getRegisteredListeners()
                        .forEach(listener -> jdaInstance.removeEventListener(listener));

                log.info("=======>>>>>> 删除后的listener数量: {}", jdaInstance.getEventManager().getRegisteredListeners().size());

                // 优雅关闭
                JDA oldInstance = jdaInstance;
                jdaInstance = null; // 先置空引用，防止并发访问

                oldInstance.shutdown(); // 开始关闭流程
                if (!oldInstance.awaitShutdown(10, TimeUnit.SECONDS)) {
                    oldInstance.shutdownNow(); // 强制关闭如果超时
                    throw new IllegalStateException("旧JDA实例关闭超时，已强制终止");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new IllegalStateException("关闭过程被中断", e);
            }
        }
    }


    private void initializeNewJDA(String token, EventListener listener) {
        try {
            // 创建新实例（启用必要权限）
            jdaInstance = JDABuilder.createDefault(token)
                    .enableIntents(GatewayIntent.MESSAGE_CONTENT) // 根据需要添加
                    .addEventListeners(listener)
                    .build();

            log.info("=======>>>>>> 注册的listener数量: {}", jdaInstance.getEventManager().getRegisteredListeners().size());

            // 等待连接就绪
            jdaInstance.awaitReady();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            if (jdaInstance != null) {
                jdaInstance.shutdownNow();
                jdaInstance = null;
            }
            throw new IllegalStateException("连接过程被中断", e);
        } catch (InvalidTokenException e) {
            // 如果是非法的token 向外抛业务异常
            throw new ServiceException(MessageUtils.get(ResultCodeEnum.DISCORD_CHANNEL_TOKEN_INVALID.getMessage()));
        }
    }

    /**
     * 安全关闭当前JDA实例
     */
    public void shutdown() {
        synchronized (lock) {
            shutdownExistingJDA();
        }
    }

    
    /**
     * 获取当前JDA实例
     */
    public JDA getJDA() {
        synchronized (lock) {
            return jdaInstance;
        }
    }
}