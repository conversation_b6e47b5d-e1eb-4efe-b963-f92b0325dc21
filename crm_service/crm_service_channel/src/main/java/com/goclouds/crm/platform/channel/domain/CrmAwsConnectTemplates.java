package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * connect实例关联模版
 * @TableName crm_aws_connect_templates
 */
@TableName(value ="crm_aws_connect_templates")
@Data
public class CrmAwsConnectTemplates implements Serializable {
    /**
     * id
     */
    @TableId
    private String templatesId;

    /**
     * connect实例ID
     */
    private String connectId;

    /**
     * aws用户id
     */
    private String awsUserId;

    /**
     * 模版名称
     */
    private String templatesName;

    /**
     * 描述
     */
    private String description;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 模版状态 0-禁用 1-启用
     */
    private Integer templatesStatus;

    /**
     * 数据状态
     */
    private Integer dataStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}