package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;

/**
 * 新的渠道列表查询条件
 */
@Data
public class ChannelListRequestVo {

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 语言
     */
    private String language;

    /**
     * 网址域名
     */
    private String websiteDomainName;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * Facebook账号名称
     */
    private String facebookName;

    /**
     * facebook账号Id
     */
    private String facebookId;

    /**
     * instagram账号名称
     */
    private String instagramName;

    /**
     * instagram账号Id
     */
    private String instagramId;

    /**
     * lineId
     */
    private String lineId;

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * appId
     */
    private String appId;

    /**
     * 卖家地址
     */
    private String sellerAddress;

    /**
     * 客服名称
     */
    private String customerName;

    /**
     * 机器人名称
     */
    private String botName;

    /**
     * 店铺名称
     */
    private String shopName;


}
