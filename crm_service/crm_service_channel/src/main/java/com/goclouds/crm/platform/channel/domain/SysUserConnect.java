package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 客服connect实例关系;用户角色关系表

 * @TableName sys_user_connect
 */
@TableName(value ="sys_user_connect")
@Data
@Accessors(chain = true)
public class SysUserConnect implements Serializable {
    /**
     * 用户id
     */
    private String userId;

    /**
     * awsConnectId
     */
    private String awsConnectId;

    /**
     * 数据状态 0-删除 1-正常
     */
    private Integer dataStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
