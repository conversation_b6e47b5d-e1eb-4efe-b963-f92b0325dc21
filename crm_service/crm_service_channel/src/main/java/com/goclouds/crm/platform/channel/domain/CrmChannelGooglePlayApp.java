package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * googleplay渠道下的应用
 * @TableName crm_channel_voc_googleplay_app
 */
@TableName(value ="crm_channel_voc_googleplay_app")
@Data
public class CrmChannelGooglePlayApp implements Serializable {
    /**
     * 应用id
     */
    @TableId
    private String appId;

    /**
     * googleplay渠道id
     */
    private String gpChannelId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用编号
     */
    private String appNumber;

    /**
     * 应用logo
     */
    private String appLogoUrl;

    /**
     * 应用包名
     */
    private String appPackage;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据状态
     */
    private Integer dataStatus;

    /**
     * 评论数量
     */
    @TableField(exist = false)
    private Integer commentCount;


}