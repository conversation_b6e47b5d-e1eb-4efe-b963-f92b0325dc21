package com.goclouds.crm.platform.channel.controller;

import com.goclouds.crm.platform.channel.service.WXBizService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.WeComAuthorizationCodeVo;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;

@RestController
@RequestMapping("WXBiz")
@Validated
@RequiredArgsConstructor
@Slf4j
public class WXBizController {

    private final WXBizService wxBizService;

    /**
     * 获取重定向url
     */
    @GetMapping("WXBizPreAuthorizationUrl")
    public AjaxResult<String> WXBizPreAuthorizationUrl(@RequestParam("redirectUri") String redirectUri, @RequestParam("state") String state){
        return wxBizService.WXBizPreAuthorizationUrl(redirectUri, state);
    }


    /**
     * 获取企业授权码,企业信息
     */
    @GetMapping("WXBizEnterpriseInformation")
    public AjaxResult<WeComAuthorizationCodeVo> WXBizEnterpriseInformation(@RequestParam("authCode") String authCode){
        return wxBizService.WXBizEnterpriseInformation(authCode);
    }


    /**
     * 企业微信 新增临时素材获取media_id
     */
    @PostMapping("uploadWXBizMaterial")
    public AjaxResult<Object> uploadWXBizMaterial(@NonNull @RequestParam("media") MultipartFile media, @NonNull @RequestParam("corpId")String corpId) {
        return wxBizService.uploadWXBizMaterial(media, corpId);
    }
}
