package com.goclouds.crm.platform.channel.domain.vo;

import com.goclouds.crm.platform.domain.validate.InsertValidation;
import com.goclouds.crm.platform.domain.validate.UpdateValidation;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ConnectMessageVo implements Serializable {
    /** 客户手机号 */
    @NotBlank(message = "客户手机号不能为空", groups = {InsertValidation.class, UpdateValidation.class})
    String customerPhone;
    /** 当前手机号 */
    @NotBlank(message = "客服手机号不能为空", groups = {InsertValidation.class, UpdateValidation.class})
    String phone;
    /** 消息类型 */
    @NotBlank(message = "消息类型不能为空", groups = {InsertValidation.class})
    String messageType;
    /** 消息内容 文本类型 */
    String message;
    /** 附件id （包含图片、视频、音频、文档等） */
    String fileUrl;
}
