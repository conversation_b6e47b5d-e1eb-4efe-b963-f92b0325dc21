package com.goclouds.crm.platform.channel.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * TikTok订单列表响应根对象
 * @param code 响应状态码（0表示成功）
 * @param data 业务数据载体
 * @param message 响应描述信息
 * @param requestId 请求唯一标识
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TikTokOrderListResponse implements Serializable {

    /** 分页令牌（用于获取下一页数据） */
    private String nextPageToken;
    /** 订单总数 */
    private Integer totalCount;
    /** 订单列表 */
    private List<Order> orderList;

    /**
     * 订单主对象
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Order {
        /** 订单ID */
        private String id;
        /** 买家留言 */
        private String buyerMessage;
        /** 取消发起方（SELLER/BUYER/SYSTEM） */
        private String cancellationInitiator;
        /** 物流服务商ID */
        private String shippingProviderId;
        /** 订单创建时间（时间戳） */
        private Long createTime;
        /** 物流服务商名称 */
        private String shippingProvider;
        /** 包裹信息列表 */
        private List<Package> packages;
        /** 支付信息 */
        private Payment payment;
        /** 收货地址信息 */
        private RecipientAddress recipientAddress;
        /** 订单状态 */
        private String status;
        /** 履约类型 */
        private String fulfillmentType;
        /** 配送类型 */
        private String deliveryType;
        /** 支付完成时间 */
        private Long paidTime;
        /** 准备发货SLA时间 */
        private Long rtsSlaTime;
        /** TTS SLA时间 */
        private Long ttsSlaTime;
        /** 取消原因 */
        private String cancelReason;
        /** 最后更新时间 */
        private Long updateTime;
        /** 支付方式名称 */
        private String paymentMethodName;
        /** 准备发货时间 */
        private Long rtsTime;
        /** 物流单号 */
        private String trackingNumber;
        /** 拆合单标识 */
        private String splitOrCombineTag;
        /** 收货地址是否更新 */
        private Boolean hasUpdatedRecipientAddress;
        /** 取消订单SLA时间 */
        private Long cancelOrderSlaTime;
        /** 仓库ID */
        private String warehouseId;
        /** 请求取消时间 */
        private Long requestCancelTime;
        /** 物流类型 */
        private String shippingType;
        /** 用户ID */
        private String userId;
        /** 卖家备注 */
        private String sellerNote;
        /** 配送承诺时间 */
        private Long deliverySlaTime;
        /** 是否为货到付款 */
        private Boolean isCod;
        /** 配送选项ID */
        private String deliveryOptionId;
        /** 实际取消时间 */
        private Long cancelTime;
        /** 发票上传状态 */
        private String needUploadInvoice;
        /** 配送选项名称 */
        private String deliveryOptionName;
        /** 巴西税号（CPF） */
        private String cpf;
        /** 商品行项目列表 */
        private List<LineItem> lineItems;
        /** 买家邮箱 */
        private String buyerEmail;
        /** 预计送达时间 */
        private Long deliveryDueTime;
        /** 是否为样品订单（API返回字符串值） */
        private String isSampleOrder;
        /** 物流截止时间 */
        private Long shippingDueTime;
        /** 揽件截止时间 */
        private Long collectionDueTime;
        /** 配送选项要求送达时间 */
        private Long deliveryOptionRequiredDeliveryTime;
        /** 是否为暂停订单 */
        private Boolean isOnHoldOrder;
        /** 实际送达时间 */
        private Long deliveryTime;
        /** 是否为换货订单 */
        private Boolean isReplacementOrder;
        /** 揽件时间 */
        private Long collectionTime;
        /** 被替换的源订单ID */
        private String replacedOrderId;
        /** 是否买家主动取消 */
        private Boolean isBuyerRequestCancel;
        /** 揽件截止时间 */
        private Long pickUpCutOffTime;
        /** 快速发货SLA时间 */
        private Long fastDispatchSlaTime;
        /** 电商平台类型 */
        private String commercePlatform;
        /** 订单类型 */
        private String orderType;
        /** 商品发布时间 */
        private Long releaseDate;
        /** 处理时长配置 */
        private HandlingDuration handlingDuration;
        /** 自动合并分组ID */
        private String autoCombineGroupId;
        /** CPF持有人姓名 */
        private String cpfName;
        /** 是否为换货订单 */
        private Boolean isExchangeOrder;
        /** 原始订单ID（换货场景） */
        private String exchangeSourceOrderId;
        /** 咨询ID */
        private String consultationId;
        /** 快速配送计划类型 */
        private String fastDeliveryProgram;
    }

    /**
     * 支付信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Payment {
        /** 货币类型 */
        private String currency;
        /** 商品小计 */
        private String subTotal;
        /** 运费 */
        private String shippingFee;
        /** 卖家折扣 */
        private String sellerDiscount;
        /** 平台折扣 */
        private String platformDiscount;
        /** 订单总金额 */
        private String totalAmount;
        /** 原始商品总价 */
        private String originalTotalProductPrice;
        /** 原始运费 */
        private String originalShippingFee;
        /** 运费卖家折扣 */
        private String shippingFeeSellerDiscount;
        /** 运费平台折扣 */
        private String shippingFeePlatformDiscount;
        /** 运费联合补贴折扣 */
        private String shippingFeeCofundedDiscount;
        /** 税费 */
        private String tax;
        /** 小额订单费 */
        private String smallOrderFee;
        /** 运费税费 */
        private String shippingFeeTax;
        /** 商品税费 */
        private String productTax;
        /** 零售配送费 */
        private String retailDeliveryFee;
        /** 买家服务费 */
        private String buyerServiceFee;
        /** 操作费 */
        private String handlingFee;
        /** 运费险费用 */
        private String shippingInsuranceFee;
        /** 商品险费用 */
        private String itemInsuranceFee;
    }

    /**
     * 收货地址信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecipientAddress {
        /** 完整地址 */
        private String fullAddress;
        /** 电话号码 */
        private String phoneNumber;
        /** 收件人姓名 */
        private String name;
        /** 名（拼音） */
        private String firstName;
        /** 姓（拼音） */
        private String lastName;
        /** 名（本地语言） */
        private String firstNameLocalScript;
        /** 姓（本地语言） */
        private String lastNameLocalScript;
        /** 详细地址 */
        private String addressDetail;
        /** 地址行1 */
        private String addressLine1;
        /** 地址行2 */
        private String addressLine2;
        /** 地址行3 */
        private String addressLine3;
        /** 地址行4 */
        private String addressLine4;
        /** 地区信息列表 */
        private List<DistrictInfo> districtInfo;
        /** 配送偏好 */
        private DeliveryPreferences deliveryPreferences;
        /** 邮编 */
        private String postalCode;
        /** 国家代码 */
        private String regionCode;
    }

    /**
     * 地区信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DistrictInfo {
        /** 地址层级名称 */
        private String addressLevelName;
        /** 地址名称 */
        private String addressName;
        /** 地址层级（L0-国家/L1-省份等） */
        private String addressLevel;
    }

    /**
     * 配送偏好
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DeliveryPreferences {
        /** 投递位置 */
        private String dropOffLocation;
    }

    /**
     * 包裹信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Package {
        /** 包裹ID */
        private String id;
    }

    /**
     * 订单行项目（商品明细）
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LineItem {
        /** 商品ID */
        private String id;
        /** SKU ID */
        private String skuId;
        /** 组合商品SKU列表 */
        private List<CombinedListingSku> combinedListingSkus;
        /** 显示状态 */
        private String displayStatus;
        /** 商品名称 */
        private String productName;
        /** 卖家SKU编码 */
        private String sellerSku;
        /** SKU图片 */
        private String skuImage;
        /** SKU名称 */
        private String skuName;
        /** 商品ID */
        private String productId;
        /** 销售价格 */
        private String salePrice;
        /** 平台折扣 */
        private String platformDiscount;
        /** 卖家折扣 */
        private String sellerDiscount;
        /** SKU类型 */
        private String skuType;
        /** 取消原因 */
        private String cancelReason;
        /** 商品原价 */
        private String originalPrice;
        /** 准备发货时间 */
        private Long rtsTime;
        /** 包裹状态 */
        private String packageStatus;
        /** 货币类型 */
        private String currency;
        /** 物流服务商名称 */
        private String shippingProviderName;
        /** 取消操作方 */
        private String cancelUser;
        /** 物流服务商ID */
        private String shippingProviderId;
        /** 是否为礼品 */
        private Boolean isGift;
        /** 商品税费列表 */
        private List<ItemTax> itemTax;
        /** 物流单号 */
        private String trackingNumber;
        /** 包裹ID */
        private String packageId;
        /** 零售配送费 */
        private String retailDeliveryFee;
        /** 买家服务费 */
        private String buyerServiceFee;
        /** 小额订单费 */
        private String smallOrderFee;
        /** 处理时长（天） */
        private String handlingDurationDays;
        /** 是否为危险品 */
        private Boolean isDangerousGood;
        /** 是否需要处方 */
        private Boolean needsPrescription;
    }

    /**
     * 组合商品SKU信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CombinedListingSku {
        /** SKU ID */
        private String skuId;
        /** SKU数量 */
        private Integer skuCount;
        /** 商品ID */
        private String productId;
        /** 卖家SKU编码 */
        private String sellerSku;
    }

    /**
     * 商品税费信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ItemTax {
        /** 税种类型 */
        private String taxType;
        /** 税额 */
        private String taxAmount;
        /** 税率 */
        private String taxRate;
    }

    /**
     * 处理时长配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HandlingDuration {
        /** 处理天数 */
        private String days;
        /** 时长类型（工作日/自然日） */
        private String type;
    }
}