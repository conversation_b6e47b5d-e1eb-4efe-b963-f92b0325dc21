package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * @Description aws账号入参
 * <AUTHOR>
 * @Date 2023/5/23 10:40
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class GooglePlayUserReviewsInfoVo {

    /**
     * 评价ID，一个用户一条评价，评价可更新。商户也可以更新对其的回复
     */
    private String reviewId;

    /**
     * 是否回复；默认0-未回复，1-已回复，2-回复后更新过、3-已删除
     */
    private Integer commentStatus;

    /**
     * 评论历史
     */
    private List<CommentHistoryInfoVo> commentHistory;

    /**
     * 回复历史
     */
    private List<ReplyHistoryVo> replyHistory;

    /**
     * 应用的完全限定软件包名称（如 com.google.android.apps.maps）
     */
    private String packageName;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 备注内容
     */
    private String noteContent;

    /**
     * 创建人
     */
    private Date noteTime;

    /**
     * 回复内容
     */
    private String developerReplyText;

    /**
     * 回复的用户名名称
     */
    private String authorName;

}
