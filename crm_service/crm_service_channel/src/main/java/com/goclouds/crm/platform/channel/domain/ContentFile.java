package com.goclouds.crm.platform.channel.domain;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ContentFile {
     
     private String fileId;
     /**
      * 附件名称
      */
     private String fileName;
     
     /**
      * 附件url(objectKey)
      */
     private String fileUrl;
     
     /**
      * 桶名称
      */
     private String bucketName;

     /**
      * 文件类型 1-附件(非公开桶)  2-图片(公开桶)
      */
     private Integer fileType;

     /**
      * 预签名url
      */
     private String url;
}
