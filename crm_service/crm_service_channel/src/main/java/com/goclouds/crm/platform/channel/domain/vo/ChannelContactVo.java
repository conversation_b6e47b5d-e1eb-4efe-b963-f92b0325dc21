package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 渠道联系方式
 */
@Data
public class ChannelContactVo {

    private String channelId;

    private String channelName;

    //渠道对应的联系方式，例如邮件类型就是邮箱；例如WhatsApp就是手机号，web聊天和app聊天为空
    private String contactWay;


}
