package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ChannelConfigInstVo implements Serializable {
    /** 当前公司 */ 
    private String companyId;
    /** 配置当前社媒的渠道 */
    private String channelId;
    /** 渠道类型 */
    private String channelTypeId;
    /** 当前渠道的详细配置 */
    private Map<String, String> instMap = new HashMap<>();
    /** 是否开启自研聊天窗 0-否(默认使用ccp面板) 1-是 */
    private Integer openSelfChat = 0;
}
