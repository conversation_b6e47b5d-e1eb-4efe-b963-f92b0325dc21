package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * aws账号表
 * @TableName crm_aws_account
 */
@TableName(value ="crm_aws_account")
@Data
public class CrmAwsAccount implements Serializable {
    /**
     * aws账号id
     */
    @TableId
    private String awsUserId;

    /**
     * aws账号名称
     */
    private String awsUserName;
    /**
     * 付费账号id
     */
    private String rootAccountId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * ak
     */
    private String accessKey;

    /**
     * sk
     */
    private String secretAccessKey;

    /**
     * regionid,逗号分隔
     */
    private String regions;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     *  数据状态 0-删除 1-正常
     */
    private Integer dataStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}