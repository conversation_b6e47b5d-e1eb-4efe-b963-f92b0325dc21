package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * aws region表
 * @TableName crm_aws_region_def
 */
@TableName(value ="crm_aws_region_def")
@Data
public class CrmAwsRegionDef implements Serializable {
    /**
     * region id
     */
    @TableId
    private String regionId;

    /**
     * region名称
     */
    private String regionName;
    /**
     * region英文名称
     */
    private String regionNameEn;

    /**
     * region编码
     */
    private String regionCode;

    /**
     * 排序
     */
    private Integer orders;

    /**
     * 启用状态 1-启用 2-停用
     */
    private Integer status;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 数据状态
     */
    private Integer dataStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}