package com.goclouds.crm.platform.channel.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> pengliang.sun
 * @description :
 */
@Data
public class QueryTikTokOrderListResponse implements Serializable {

    /** 分页令牌（用于获取下一页数据） */
    private String nextPageToken;
    /** 订单总数 */
    private Integer totalCount;
    /** 订单列表 */
    private List<Order> orderList;

    /**
     * 订单主对象
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Order {
        /** 订单ID */
        private String id;

        /** 订单创建时间 */
        private LocalDateTime createTime;

        /** 订单总金额 */
        private String totalAmount;

        /** 货币类型 */
        private String currency;

        /** 订单状态 */
        private String status;

        /** 物流类型 */
        private String shippingType;

        /** 准备发货时间 */
        private LocalDateTime rtsTime;

        /** 最晚发货时间 */
        private LocalDateTime lastRtsTime;

        /** 买家邮箱 */
        private String buyerEmail;

        /** 完整地址 */
        private String fullAddress;

        /** 支付方式名称 */
        private String paymentMethodName;

        /** 收件人姓名 */
        private String name;

        private List<OrderItem> orderItemList;

        /** 运费 */
        private String shippingFee;
    }

    /**
     * 订单组合品
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderItem {

        // todo 订购数量 ASIN 只有亚马逊平台有,返回商品唯一ID
        /** 商品唯一ID */
        private String productId;

        /** 商品名称 */
        private String productName;

        /** SKU */
        private String sellerSku;

        /** 订单金额 */
        private BigDecimal price;

    }

}
