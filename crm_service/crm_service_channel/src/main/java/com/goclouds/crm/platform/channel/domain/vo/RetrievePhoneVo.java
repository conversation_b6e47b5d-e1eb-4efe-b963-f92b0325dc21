package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;

@Data
public class RetrievePhoneVo {
    private String id;

    private String phoneNumber;

    private String displayPhoneNumber;

    private String wabaId;

    private String qualityRating;

    private String messagingLimit;

    private String verifiedName;

    private String codeVerificationStatus;

    private String isOfficialBusinessAccount;

    private String status;

    private String nameStatus;

    private String newNameStatus;

    private String decision;

    private String requestedVerifiedName;

    private String rejectionReason;

    private String qualityUpdateEvent;
}
