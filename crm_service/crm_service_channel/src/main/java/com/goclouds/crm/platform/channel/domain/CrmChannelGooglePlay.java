package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * googleplay渠道
 * @TableName crm_channel_voc_googleplay
 */
@TableName(value ="crm_channel_voc_googleplay")
@Data
public class CrmChannelGooglePlay implements Serializable {

    /**
     * 应用id
     */
    @TableId
    private String gpChannelId;

    /**
     * googleplay渠道名称
     */
    private String gpChannelName;

    /**
     * 开发者id
     */
    private String gpDeveloper;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * googleplay密码
     */
    private String gpSecret;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据状态
     */
    private Integer dataStatus;

}