package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客户资料来源渠道(CrmCustomerSourceChanel)表实体类
 *
 * <AUTHOR>
 * @since 2023-06-09 14:04:09
 */
@TableName(value ="crm_customer_source_chanel")
@Data
@Accessors(chain = true)
public class CrmCustomerSourceChanel extends Model<CrmCustomerSourceChanel> {
    //id
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    //客户资料id
    private String customerId;
    //来源渠道id
    private String channelId;
    //来源渠道name
    private String channelName;

}
