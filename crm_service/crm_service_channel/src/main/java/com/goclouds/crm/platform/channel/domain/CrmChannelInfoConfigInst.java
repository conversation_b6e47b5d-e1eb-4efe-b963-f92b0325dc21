package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道详细信息配置
 * @TableName crm_channel_info_config_inst
 */
@TableName(value ="crm_channel_info_config_inst")
@Data
public class CrmChannelInfoConfigInst implements Serializable {
    /**
     * 渠道信息id
     */
    @TableId
    private String id;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 渠道子类型
     */
    private Integer channelSubtype;

    /**
     * 编码
     */
    private String code;

    /**
     * 值
     */
    private String name;

    /**
     * 字段类型;字段类型 1：渠道需要填的字段 2：扩展字段
     */
    private Integer fieldType;

    /**
     * 正则匹配规则
     */
    private String mappingRule;

    /**
     * 数据状态
     */
    private Integer dataStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}