package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道类型参数定义表
 * @TableName crm_channel_param_def
 */
@TableName(value ="crm_channel_param_def")
@Data
public class CrmChannelParamDef implements Serializable {
    /**
     * 编码
     */
    @TableId
    private String code;

    /**
     * 值
     */
    private String name;

    /**
     * 值
     */
    private String usName;

    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 渠道子类型
     */
    private Integer channelSubtype;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 排序
     */
    private Integer seq;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}