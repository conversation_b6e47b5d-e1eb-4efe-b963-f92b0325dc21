package com.goclouds.crm.platform.channel.config;

import com.goclouds.crm.platform.channel.properties.ThreadPoolProperties;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 **/
@Configuration
@RequiredArgsConstructor
public class ThreadPoolConfig {
    
    private final ThreadPoolProperties threadPoolProperties;

    @Bean(name = "threadPoolTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(threadPoolProperties.getPool().getMaxPoolSize());
        executor.setCorePoolSize(threadPoolProperties.getPool().getCorePoolSize());
        executor.setQueueCapacity(threadPoolProperties.getPool().getQueueCapacity());
        executor.setKeepAliveSeconds(threadPoolProperties.getPool().getKeepAliveSeconds());
        executor.setThreadFactory(new BasicThreadFactory.Builder().namingPattern("channel-pool-%d").build());
        // 线程池对拒绝任务(无线程可用)的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
