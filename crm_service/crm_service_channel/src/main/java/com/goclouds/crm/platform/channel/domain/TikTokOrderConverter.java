package com.goclouds.crm.platform.channel.domain;

import com.goclouds.crm.platform.common.utils.StringUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

public class TikTokOrderConverter {

    public static QueryTikTokOrderListResponse mapToQueryResponse(TikTokOrderListResponse source) {
        if (source == null) {
            return null;
        }

        QueryTikTokOrderListResponse target = new QueryTikTokOrderListResponse();
        target.setNextPageToken(source.getNextPageToken());
        target.setTotalCount(source.getTotalCount());
        
        if (source.getOrderList() != null) {
            List<QueryTikTokOrderListResponse.Order> mappedOrders = source.getOrderList().stream()
                .map(TikTokOrderConverter::mapOrder)
                .collect(Collectors.toList());
            target.setOrderList(mappedOrders);
        }

        return target;
    }

    private static QueryTikTokOrderListResponse.Order mapOrder(TikTokOrderListResponse.Order source) {
        if (source == null) {
            return null;
        }

        QueryTikTokOrderListResponse.Order target = new QueryTikTokOrderListResponse.Order();
        target.setId(source.getId());
        target.setCreateTime(convertTimestampToLocalDateTime(source.getCreateTime()));
        target.setTotalAmount(source.getPayment() != null ? source.getPayment().getTotalAmount() : null);
        target.setShippingFee(source.getPayment() != null ? source.getPayment().getShippingFee() : null);
        target.setCurrency(source.getPayment() != null ? source.getPayment().getCurrency() : null);
        target.setStatus(source.getStatus());
        target.setShippingType(source.getShippingType());
        target.setRtsTime(convertTimestampToLocalDateTime(source.getRtsTime()));
        target.setBuyerEmail(source.getBuyerEmail());
        target.setFullAddress(source.getRecipientAddress() != null ? source.getRecipientAddress().getFullAddress() : null);
        target.setPaymentMethodName(source.getPaymentMethodName());
        target.setName(source.getRecipientAddress() != null ? source.getRecipientAddress().getName() : null);
        
        if (source.getLineItems() != null) {
            List<QueryTikTokOrderListResponse.OrderItem> mappedItems = source.getLineItems().stream()
                .map(TikTokOrderConverter::mapOrderItem)
                .collect(Collectors.toList());
            target.setOrderItemList(mappedItems);
        }

        return target;
    }

    private static QueryTikTokOrderListResponse.OrderItem mapOrderItem(TikTokOrderListResponse.LineItem source) {
        if (source == null) {
            return null;
        }

        QueryTikTokOrderListResponse.OrderItem target = new QueryTikTokOrderListResponse.OrderItem();
        target.setProductName(source.getProductName());
        if (StringUtil.isBlank(source.getSellerSku())){
            target.setSellerSku(null);
        }else {
            target.setSellerSku(source.getSellerSku());
        }
        target.setProductId(source.getProductId());

        // Convert price from String to BigDecimal
        if (source.getSalePrice() != null) {
            try {
                target.setPrice(new BigDecimal(source.getSalePrice()));
            } catch (NumberFormatException e) {
                target.setPrice(BigDecimal.ZERO);
            }
        }

        return target;
    }

    private static LocalDateTime convertTimestampToLocalDateTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp * 1000L), 
            ZoneId.systemDefault()
        );
    }
}