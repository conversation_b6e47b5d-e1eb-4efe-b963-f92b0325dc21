package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 评论历史
 */
@Data
@Accessors(chain = true)
public  class CommentHistoryInfoVo {

    /**
     * 用户名称
     */
    private String authorName;

    /**
     * 评价内容
     */
    private String userCommentText;

    /**
     * 评分1-5
     */
    private Float starRating;

    /**
     * 评价时间
     */
    private Date commentTime;

    /**
     * 评论者语言
     */
    private String reviewerLanguage;

    /**
     * 设备
     */
    private String device;

    /**
     * 安卓操作系统版本
     */
    private Integer androidOsVersion;

    /**
     * 应用程序版本代码
     */
    private Integer appVersionCode;

    /**
     * 应用程序版本名称
     */
    private String appVersionName;

    /**
     * 点赞数
     */
    private Integer thumbsUpCount;

    /**
     * 点踩数
     */
    private Integer thumbsDownCount;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 设备类别
     */
    private String deviceClass;

    /**
     * 屏幕宽度
     */
    private Integer screenWidthPx;

    /**
     * 屏幕高度
     */
    private Integer screenHeightPx;

    /**
     * 原生平台
     */
    private String nativePlatform;

    /**
     * 屏幕密度
     */
    private Integer screenDensityDpi;

    /**
     * glEs版本
     */
    private Integer glEsVersion;

    /**
     * cpu型号
     */
    private String cpuModel;

    /**
     * cpu制造商
     */
    private String cpuMake;

    /**
     * 内存
     */
    private Integer ramMb;
}