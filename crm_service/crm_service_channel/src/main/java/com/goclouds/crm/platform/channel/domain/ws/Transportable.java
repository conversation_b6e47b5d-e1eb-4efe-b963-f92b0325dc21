/*
 * Copyright 2013-2022 Xia Jun(<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************************
 *                                                                                     *
 *                        Website : http://www.farsunset.com                           *
 *                                                                                     *
 ***************************************************************************************
 */
package com.goclouds.crm.platform.channel.domain.ws;

/**
 * 需要向另一端发送的结构体
 */
public interface Transportable {
	/**
	 * 消息体字节数组
	 * @return
	 */
	byte[] getBody();

	/**
	 * 消息类型
	 * @return
	 */
	DataType getType();
}
