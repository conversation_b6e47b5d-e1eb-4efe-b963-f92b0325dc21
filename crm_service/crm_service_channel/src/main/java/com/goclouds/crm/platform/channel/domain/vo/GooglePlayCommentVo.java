package com.goclouds.crm.platform.channel.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class GooglePlayCommentVo {


    /**
     * 应用包名
     */
    private String packageName;

    /**
     * 评价内容
     * 用户评价/商户回复
     */
    private String userCommentText;

    /**
     * 用户评分1-5
     */
    private String starRating;

    /**
     * 评论开始时间
     * yy-MM-dd hh:mm:ss
     */
    private String commentStartTime;

    /**
     * 评论结束时间
     * yy-MM-dd hh:mm:ss
     */
    private String commentEndTime;


    /**
     * 评论者语言
     */
    private String reviewerLanguage;

    /**
     * 设备
     */
    private String device;

    /**
     * 安卓操作系统版本
     */
    private Integer androidOsVersion;

    /**
     * 应用程序版本代码
     */
    private Integer appVersionCode;

    /**
     * 评论状态
     */
    private String commentStatus;

    /**
     * 时间查询类型
     */
    private Integer getDateType;
}
