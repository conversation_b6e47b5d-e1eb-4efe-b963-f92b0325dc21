package com.goclouds.crm.platform.channel.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.annotation.InnerAuth;
import com.goclouds.crm.platform.annotation.NoLogin;
import com.goclouds.crm.platform.annotation.PreAuthorize;
import com.goclouds.crm.platform.channel.domain.*;
import com.goclouds.crm.platform.channel.domain.CrmChannelConfig;
import com.goclouds.crm.platform.channel.domain.vo.ChannelDefVo;
import com.goclouds.crm.platform.channel.domain.vo.WhatsAppChannelDetailVo;
import com.goclouds.crm.platform.channel.domain.vo.*;
import com.goclouds.crm.platform.channel.service.*;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.controller.BaseController;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.channel.*;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("channelConfig")
@Validated
@Slf4j
public class CrmChannelController extends BaseController {

    @Autowired
    private CrmChannelDefService crmChannelDefService;

    @Autowired
    private CrmChannelConfigService crmChannelConfigService;

    @Autowired
    private CrmChannelParamDefService crmChannelParamDefService;

    @Resource
    private CrmCustomerSourceChanelService crmCustomerSourceChanelService;

    @Autowired
    private CrmChannelInfoConfigInstService crmChannelInfoConfigInstService;

    @Autowired
    private CrmChannelTiktokShopRegionDefService tiktokShopRegionDefService;

    /**
     * 通过通道类型动态查找，配置参数
     * @return
     */
    @GetMapping("type")
    public AjaxResult typeCode(@NotNull Integer channelType,@NotNull Integer subType){

        return AjaxResult.ok(crmChannelParamDefService.typeCode(channelType, subType)).setMsg(MessageUtils.get("operate.success"));
    }

    /**
     * 查询通道类型
     * @return
     */
    @GetMapping("channelType")
    public AjaxResult channelType(@RequestParam(name = "needShowChannelType", required = false) String needShowChannelType){
        QueryWrapper<CrmChannelDef> queryWrapper = new QueryWrapper<>();
        if (StringUtil.isNotEmpty(needShowChannelType)) {
            List<String> list = Arrays.asList(needShowChannelType.split(","));
            queryWrapper.in("code", list );
        }
        queryWrapper.eq("data_status",1)
        .orderByAsc("channel_order");
        List<CrmChannelDef> list = crmChannelDefService.list(queryWrapper);
        for (CrmChannelDef crmChannel:list) {
            crmChannel.setName(TransUtil.trans(crmChannel.getName()));
        }
        return AjaxResult.ok(list).setMsg(MessageUtils.get("operate.success"));
    }

    /**
     * 内部接口查询通道类型
     * @return
     */
    @GetMapping("innerChannelType")
    @InnerAuth(isUser = true)
    public AjaxResult<List<ChannelDefVo>> innerChannelType(){
        QueryWrapper<CrmChannelDef> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_status",1)
                .orderByAsc("channel_order");
        List<CrmChannelDef> list = crmChannelDefService.list(queryWrapper);
        List<ChannelDefVo> defVoArrayList = new ArrayList<>();
        for (CrmChannelDef crmChannel:list) {
            crmChannel.setName(TransUtil.trans(crmChannel.getName()));
            ChannelDefVo channelDefVo = new ChannelDefVo();
            BeanUtils.copyProperties(crmChannel, channelDefVo);
            defVoArrayList.add(channelDefVo);
        }
        return AjaxResult.ok(defVoArrayList).setMsg(MessageUtils.get("operate.success"));
    }

    /**
     * 查询通道列表
     * @return
     */
    @GetMapping("channelList")
    @PreAuthorize("/channelConfig/channelList")
    public AjaxResult channelList(String connectId,String channelTypeId){
        try {
            IPage<CrmChannelConfig> pageParam = getPageParam();
            IPage<ChannelListVo> page = crmChannelConfigService.channelPage(pageParam,connectId,channelTypeId);
            return AjaxResult.ok(getDataTable(page)).setMsg(MessageUtils.get("operate.success"));
        }catch (Exception e){
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 查询通道类型列表
     * @return 通道类型列表
     */
    @GetMapping("channelTypeList")
    @PreAuthorize("/channelConfig/channelList")
    public AjaxResult<List<ChannelTypeListVo>> channelTypeList(){
        try {
            List<ChannelTypeListVo> list = crmChannelDefService.channelTypeList();
            return AjaxResult.ok(list);
        }catch (Exception e){
            log.error("查询通道类型列表报错：{0}",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 查询渠道列表
     * @return
     */
    @PostMapping("newChannelList")
    @PreAuthorize("/channelConfig/channelList")
    public AjaxResult<Object> newChannelList(@RequestBody ChannelListRequestVo channelList){
        try {
            IPage<ChannelListResponseVo> page = crmChannelConfigService.newChannelList(getPageParam(), channelList);
            return AjaxResult.ok(page);
        }catch (Exception e){
            log.error("查询渠道列表数据报错：{0}",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 创建通道配置
     * @return
     */
    @PostMapping("createChannel")
    @PreAuthorize("/channelConfig/createChannel")
    public AjaxResult createChannel(@RequestBody @Validated CreateChannelVo createChannelVo){
//        try {
            return crmChannelConfigService.createChannel(createChannelVo, getUsername());
//        } catch (Exception e) {
//            log.error("创建渠道异常",e);
//        }
//        return AjaxResult.failure(MessageUtils.get("operate.failure"));
    }

    /**
     * 更新通道配置
     * @return
     */
    @PostMapping("updateChannel")
    @PreAuthorize("/channelConfig/updateChannel")
    public AjaxResult updateChannel(@RequestBody @Validated CreateChannelVo createChannelVo,@NotBlank String id){
//        try {
            return crmChannelConfigService.updateChannel(createChannelVo,id, getUsername());
//        } catch (Exception e) {
//            log.error("更新渠道异常",e);
//        }
//        return AjaxResult.failure(MessageUtils.get("operate.failure"));
    }


    /**
     * 删除通道配置
     * @param id
     * @return
     */
    @PostMapping("deleteChannel")
    @PreAuthorize("/channelConfig/deleteChannel")
    public AjaxResult deleteChannel(@NotBlank String id){
        crmChannelConfigService.deleteChannel(id, getUsername());
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    /**
     * 查询渠道详情
     * @param id
     * @return
     */
    @GetMapping("channelDetails")
    public AjaxResult channelDetails(@NotBlank String id) {
        return crmChannelConfigService.channelDetails(id);
    }

    /**
     * 启用、禁用
     * @param id channelId
     * @param status status  0-禁用 1-启用
     * @return AjaxResult
     */
    @PostMapping("updateStatus")
    @PreAuthorize("/channelConfig/updateStatus")
    public AjaxResult updateStatus(@NotBlank String id, Integer status){
        CrmChannelConfig crmChannelConfig = new CrmChannelConfig();
        crmChannelConfig.setChannelId(id);
        crmChannelConfig.setEnableStatus(status);
        crmChannelConfig.setModifyTime(new Date());
        crmChannelConfig.setModifier(getUsername());
//        crmChannelConfigService.updateById(crmChannelConfig);
        return crmChannelConfigService.updateStatus(crmChannelConfig);
//        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    @GetMapping("getChannelByEmailTo")
    public AjaxResult getChannelByEmailTo(@NotBlank String emailTo){
        return crmChannelConfigService.getChannelByEmailTo(emailTo);
    }




    /**
     * 查询渠道名称
     * @return AjaxResult
     */
    @GetMapping("queryChannelNameList")
    public AjaxResult<List<ChannelNameVO>> queryChannelNameList() {
        try {
            List<ChannelNameVO> channelConfigs = crmChannelConfigService.queryChannelNameList(SecurityUtil.getLoginUser().getCompanyId());
            System.out.println("进入查询渠道");
            return AjaxResult.ok(channelConfigs).setMsg(MessageUtils.get("operate.success"));
        } catch (Exception e){
            log.error("查询渠道名称出现错误", e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }


    /**
     * 根据connectId查询当前登录人的渠道list
     * @param connectId
     * @return
     */
    @GetMapping("connect/channels")
    @InnerAuth(isUser = true)
    public AjaxResult<List<ChannelVo>> channelListByConnectId(@RequestParam(name = "connectId", required = false) String connectId) {
        return crmChannelConfigService.channelList(connectId);
    }

    /**
     * 根据connectId查询当前登录人的渠道list copy
     * @param connectId
     * @return
     */
    @GetMapping("connect/channelsCopy")
    @InnerAuth(isUser = true)
    public AjaxResult<List<ChannelVo>> channelListByConnectIdCopy(@RequestParam(name = "connectId", required = false) String connectId) {
        return crmChannelConfigService.channelListCopy(connectId);
    }

    /**
     * 查询当前登录人所在公司的所有渠道名称
     * @return AjaxResult
     */
    @GetMapping("channelNameList")
    @InnerAuth(isUser = true)
    public AjaxResult<List<ChannelNameVO>> channelNameList() {
        return crmChannelConfigService.channelNameList();
    }

    /**
     * 查询渠道名称  废弃
     * @return AjaxResult
     */
    @GetMapping("queryChannelDefNameList")
    public AjaxResult<List<ChannelNameVO>> queryChannelDefNameList() {
        try {
            List<ChannelNameVO> channelConfigs = crmChannelDefService.queryChannelDefNameList();
            System.out.println("进入查询渠道");
            return AjaxResult.ok(channelConfigs).setMsg(MessageUtils.get("operate.success"));
        } catch (Exception e){
            log.error("查询渠道名称出现错误", e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 根据customerIds查询channelName
     * @param customerIdList
     * @return
     */
    @PostMapping("customers")
    @InnerAuth
    public AjaxResult<Map<String, List<CrmCustomerSourceChanelVo>>> queryChannelNames(@RequestBody @NotEmpty List<@Valid @NotBlank String> customerIdList) {
        return crmCustomerSourceChanelService.queryChannelNames(customerIdList);
    }

    /**
     * 新增 customer-channel 关系表数据
     * @param list
     * @return
     */
    @PostMapping("customer/channels")
    @InnerAuth
    public AjaxResult createCustomerChannels(@RequestBody @NotEmpty List<@Valid CrmCustomerSourceChanelVo> list) {
        return crmCustomerSourceChanelService.createCustomerChannels(list);
    }

    /**
     * 单条新增 customer-channel 关系表数据,如果有则不存储,没有才会进行存储
     * @param chanelVo
     * @return
     */
    @PostMapping("customer/addChannel")
    @InnerAuth
    public AjaxResult  insertCustomerChannels(@RequestBody @Valid CrmCustomerSourceChanelVo chanelVo){
        return crmCustomerSourceChanelService.insertCustomerChannels(chanelVo);
    };

    /**
     * 根据customerId删除其关系表中对应的channels
     * @param customerId
     * @return
     */
    @DeleteMapping("channels/{customerId}")
    @InnerAuth
    public AjaxResult removeCustomerChannels(@PathVariable("customerId") String customerId) {
        return crmCustomerSourceChanelService.removeCustomerChannels(customerId);
    }

    /**
     * 根据customerId查询其关系表中对应的channels
     * @param customerId
     * @return
     */
    @GetMapping("channels/{customerId}")
    @InnerAuth
    public AjaxResult<List<CrmCustomerSourceChanelVo>> queryCustomerChannels(@PathVariable("customerId") String customerId) {
        return crmCustomerSourceChanelService.queryCustomerChannels(customerId);
    }

    /**
     * 根据channelId list查询 channel list
     * @param channelIdList
     * @return
     */
    @PostMapping("ids")
    @InnerAuth
    public AjaxResult<List<CrmCustomerSourceChanelVo>> channelListByIdList(@RequestBody @NotEmpty List<@Valid @NotBlank String> channelIdList) {
        return crmCustomerSourceChanelService.channelListByIdList(channelIdList);
    }

    /**
     * 根据渠道名称（该登录人所在公司下）查询该渠道的详细信息
     * @param channelName
     * @return
     */
    @GetMapping("channelName")
    @InnerAuth(isUser = true)
    public AjaxResult<CrmChannelConfigVO> selectChannelConfigByName(@RequestParam("channelName") String channelName) {
        return crmChannelConfigService.selectChannelConfigByName(channelName);
    }

    /**
     * 根据渠道ID查询渠道信息
     * @param channelId 渠道ID
     * @return 渠道信息
     */
    @GetMapping("queryChannelConfig")
    @InnerAuth
    public AjaxResult<CrmChannelConfigVO> queryChannelConfig(@RequestParam("channelId") String channelId){
        CrmChannelConfig channelConfig = crmChannelConfigService.getById(channelId);
        CrmChannelConfigVO configVO = new CrmChannelConfigVO();
        BeanUtils.copyProperties(channelConfig, configVO);
        return AjaxResult.ok(configVO);
    }

    /**
     * 根据渠道ID查询渠道信息
     * @param channelId 渠道ID
     * @return 渠道信息
     */
    @GetMapping("queryChannelById")
    public AjaxResult<CrmChannelConfigVO> queryChannelById(@RequestParam("channelId") String channelId){
        CrmChannelConfig channelConfig = crmChannelConfigService.getById(channelId);
        CrmChannelConfigVO configVO = new CrmChannelConfigVO();
        BeanUtils.copyProperties(channelConfig, configVO);
        return AjaxResult.ok(configVO);
    }

    /**
     * 根据渠道code查询渠道定义信息
     * @param channelCode 渠道code
     * @return 渠道定义信息
     */
    @GetMapping("/queryChannelDef")
    @InnerAuth
    public AjaxResult<CrmChannelDefVO> queryChannelDef(@RequestParam("channelCode") String channelCode){
        CrmChannelDef channelDef = crmChannelDefService.getById(channelCode);
        CrmChannelDefVO crmChannelDefVO = new CrmChannelDefVO();
        BeanUtils.copyProperties(channelDef, crmChannelDefVO);
        return  AjaxResult.ok(crmChannelDefVO);
    }



    /**
     * 根据渠道code集合查询渠道定义信息
     * @param channelCodes 渠道code集合
     * @return 渠道定义信息
     */
    @GetMapping("/queryChannelDefList")
    @InnerAuth
    public AjaxResult<List<CrmChannelDefForSlaVO>> queryChannelDefList(@RequestParam("channelCodes") List<String> channelCodes) {
        List<CrmChannelDef> channelNotCanCreateName = crmChannelDefService.list(
                new LambdaQueryWrapper<CrmChannelDef>()
                        .in(CrmChannelDef::getCode, channelCodes)
                        .eq(CrmChannelDef::getDataStatus, 1)
        );
        return AjaxResult.ok(BeanUtil.copyToList(channelNotCanCreateName,CrmChannelDefForSlaVO.class));
    }


    /**
     *  @author: sunlinan
     *  @Date: 2024/1/10 11:37
     *  @Description: 查询智能机器人所需的配置信息
     */
    @GetMapping("queryRobotChannelConfig")
    @InnerAuth
    public AjaxResult<Map<String,String>> queryRobotChannelConfig(@RequestParam("channelId") String channelId){
        return crmChannelInfoConfigInstService.queryRobotChannelConfig(channelId);
    }

    @GetMapping("queryTelePhone")
    @InnerAuth
    public AjaxResult<Map<String,String>> queryTelePhone(@RequestParam("channelId") String channelId){
        return crmChannelInfoConfigInstService.queryTelePhone(channelId);
    }

    /**
     * 查询whatsApp 详细信息
     * @return whatsApp 详细信息
     */
    @InnerAuth
    @GetMapping("queryWhatsAppChannel")
    public AjaxResult<List<WhatsAppChannelDetailVo>> queryWhatsAppChannel(){
        List<WhatsAppChannelDetailVo> list = crmChannelConfigService.queryWhatsAppDetail(CrmChannelEnum.WHATSAPP.getCode());
        return AjaxResult.ok(list);
    }

    /**
     * 获取html script脚本 connect内容
     *      instanceId connectNow实例Id
     *      isOpenAiAgent：是否开启AI Agent  0-关 1-开
     **/
    @GetMapping("getScript")
    public AjaxResult<Map<String, String>> getScript(String instanceId,Integer channelType,@RequestParam(required = false, defaultValue = "0") Integer isOpenAiAgent){
        return crmChannelConfigService.getScript(instanceId,channelType,isOpenAiAgent);
    }

    /**
     * 部署按钮
     * 对应作用为，获取当前web渠道的script脚本内容
     *      channelId   渠道ID
     */
    @GetMapping("deploy")
    @PreAuthorize("/channelConfig/deploy")
    public AjaxResult<Map<String, String>> deploy(String channelId){
       return crmChannelConfigService.deploy(channelId);
    }


    /**
     * 根据wabaId查询手机号集合
     * @param wabaId whatsApp wabaId
     * @return 手机号集合
     */
    @GetMapping("queryPhoneList")
    public AjaxResult<List<String>> queryPhoneList(@RequestParam("wabaId") String wabaId){
        List<String> list = crmChannelInfoConfigInstService.queryPhoneList(wabaId);
        return AjaxResult.ok(list);
    }

    /**
     *  @author: sunlinan
     *  @Date: 2024/2/26 17:23
     *  @Description: 根据渠道类型查询各个渠道配置及其联系方式
     */
    @GetMapping("queryChannelTypeContact")
    public AjaxResult<List<ChannelContactVo>> queryChannelTypeContact(@RequestParam("channelType") Integer channelType){
        return crmChannelConfigService.queryChannelTypeContact(channelType);
    }


    /**
     * 根据wabaId查询手机号集合
     * @param wabaId whatsApp wabaId
     * @return 手机号集合
     */
    @GetMapping("retrievePhone")
    public AjaxResult<RetrievePhoneVo> retrievePhone(@RequestParam("wabaId") String wabaId, @RequestParam("phoneNumber") String phoneNumber){
        RetrievePhoneVo list = crmChannelInfoConfigInstService.retrievePhone(wabaId, phoneNumber);
        return AjaxResult.ok(list);
    }


    /**
     * 查询SES渠道类型
     * @return AjaxResult
     */
    @GetMapping("querySESChannel")
    public AjaxResult<List<ChannelNameVO>> querySESChannel() {
        return crmChannelConfigService.queryChannelAndProperties(null, 1, 1, true);
    }

    /**
     * 查询SES渠道类型
     * @return AjaxResult
     */
    @InnerAuth
    @GetMapping("querySESChannelInner")
    public AjaxResult<List<ChannelNameVO>> querySESChannelInner(@RequestParam("channelId") String channelId) {
        return crmChannelConfigService.queryChannelAndProperties(channelId, 1, 1, false);
    }

    //查询国家list
    @GetMapping("queryAmazonMarketRegion")
    public AjaxResult queryAmazonMarketRegion() {
        return crmChannelConfigService.queryAmazonMarketRegion();
    }

    //给前端返回重定向地址
    @GetMapping("queryRedirectUrL")
    public AjaxResult<String> queryRedirectUrL() {
        return crmChannelConfigService.queryRedirectUrL();
    }

    //给前端返回后端生成的邮箱地址
    @GetMapping("generateAmazonEmailAddress")
    public AjaxResult<String> generateAmazonEmailAddress() {
        return crmChannelConfigService.generateAmazonEmailAddress();
    }

    /**
     * @author: sunlinan
     * @Date: 2024/9/10 17:09
     * @Description: 根据channelId查询部分渠道的指定配置
     */
    @GetMapping("queryPartConfig")
    public AjaxResult<List<ChannelConfigVo>> queryPartConfig(@RequestParam("channelId") String channelId,
                                                           @RequestParam("channelTypeCode") Integer channelTypeCode
    ) {
        List<ChannelConfigVo> result = crmChannelInfoConfigInstService.queryPartConfig(channelId, channelTypeCode);
        return AjaxResult.ok(result);
    }

    /**
     * 查询渠道的详细配置信息
     * @param channelType 渠道类型
     * @return Map<String, Map<String, String>>  <channelId, <code, name>>
     */
    @InnerAuth
    @GetMapping("inst/{channelType}")
    public AjaxResult<Map<String, Map<String, String>>> queryChannelConfigInst(@PathVariable("channelType") Integer channelType) {
        return crmChannelInfoConfigInstService.queryChannelConfigInst(channelType);
    }

    /**
     * 查询渠道的详细配置信息
     * @param channelType 渠道类型
     * @return AjaxResult
     */
    @InnerAuth
    @GetMapping("inner/inst/{channelType}")
    public AjaxResult<List<ChannelConfigDTO>> queryChannelConfigInstByChannelType(@PathVariable("channelType") Integer channelType) {
        return crmChannelConfigService.queryChannelConfigInstByChannelType(channelType);
    }


    /**
     *  根据渠道id查询出渠道的详细配置信息
     * @param channelId 渠道id
     * @return 渠道的详细配置信息
     */
    @GetMapping("queryChannelIdConfigInst")
    @InnerAuth
    public AjaxResult<Map<String, String>> queryChannelIdConfigInst(@RequestParam("channelId") String channelId){
        return crmChannelInfoConfigInstService.queryChannelIdConfigInst(channelId);
    }

    /**
     * 根据chanenIds查询channelName
     * @param channelIdList
     * @return
     */
    @PostMapping("findChannelByChannelIds")
    @InnerAuth
    public AjaxResult<Map<String, String>> findChannelByChannelIds(@RequestBody List<String> channelIdList) {
        return crmChannelConfigService.findChannelByChannelIds(channelIdList);
    }



    @PostMapping("/queryChannelConfiglDefList")
    public AjaxResult<List<CrmChannelConfigVO>> queryChannelConfiglDefList(@RequestBody List<String> collect){
        List<CrmChannelConfig> list = crmChannelConfigService.list(new LambdaQueryWrapper<CrmChannelConfig>()
                .in(CrmChannelConfig::getChannelId, collect)
                .eq(CrmChannelConfig::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                .eq(CrmChannelConfig::getDataStatus, 1)
        );
        return AjaxResult.ok(
                BeanUtil.copyToList(
                        list,CrmChannelConfigVO.class
                )
        );
    }

    /**
     * 查询指定的渠道类型下拉列表
     * @param channelCodes 渠道code集合
     * @return 渠道定义信息
     */
    @GetMapping("/querySpecialChannelDefList")
    public AjaxResult<List<ChannelTypeListVo>> querySpecialChannelDefList(@RequestParam("channelCodes") List<String> channelCodes) {
        try {
            List<ChannelTypeListVo> list = crmChannelDefService.querySpecialChannelDefList(channelCodes);
            return AjaxResult.ok(list);
        }catch (Exception e){
            log.error("查询指定的通道类型列表报错：{0}",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }


    /**
     * 亚马逊根据地区id查询配置地区渠道url
     *
     * @param marketplaceIds
     * @return
     */
    @GetMapping("/queryAmazonMarketRegionUrl")
    @NoLogin
    public AjaxResult<String> queryAmazonMarketRegion(@RequestParam("marketplaceIds") String marketplaceIds) {
        try {
           String url  = crmChannelDefService.queryAmazonMarketRegion(marketplaceIds);
            return AjaxResult.ok(url);
        }catch (Exception e){
            log.error("查询指定的订单url报错：[{}]",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }


    @GetMapping("/queryChannelTree")
    public AjaxResult<List<ChannelTreeVO>> queryChannelTree(){
        List<ChannelTreeVO> result = new ArrayList<>();
        List<ChannelTypeListVo> channelTypeListVos = crmChannelDefService.channelTypeList();
        if(ObjectUtil.isNotNull(channelTypeListVos) && ObjectUtil.isNotEmpty(channelTypeListVos) && channelTypeListVos.size()!=0){
            List<String> collect = channelTypeListVos.stream().map(ChannelTypeListVo -> ChannelTypeListVo.getCode()).collect(Collectors.toList());
            List<CrmChannelConfig> list = crmChannelConfigService.list(new LambdaQueryWrapper<CrmChannelConfig>()
                    .in(CrmChannelConfig::getChannelType, collect)
                    .eq(CrmChannelConfig::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                    .eq(CrmChannelConfig::getDataStatus, 1)
            );
            Map<Integer, List<CrmChannelConfig>> channelConfig = list.stream().collect(Collectors.groupingBy(CrmChannelConfig::getChannelType));
            channelTypeListVos.forEach(
                    item -> {
                        ChannelTreeVO channelTreeVO = new ChannelTreeVO();
                        channelTreeVO.setCode(item.getCode());
                        channelTreeVO.setName(item.getName());
                        //进行渠道配置获取
                        List<CrmChannelConfig> crmChannelConfigs = channelConfig.get(Convert.toInt(item.getCode()));
                        List<WorkManageChannelVO> voList = new ArrayList<>();
                        if (ObjectUtil.isNotNull(crmChannelConfigs) && ObjectUtil.isNotEmpty(crmChannelConfigs) && crmChannelConfigs.size() != 0) {
                            crmChannelConfigs.stream().distinct().collect(Collectors.toList()).forEach(config ->{
                                WorkManageChannelVO vo = new WorkManageChannelVO();
                                vo.setChannelId(config.getChannelId());
                                vo.setChannelType(config.getChannelType());
                                vo.setName(config.getName());
                                voList.add(vo);
                            });
                            channelTreeVO.setChannelVOList(voList);
                            result.add(channelTreeVO);
                        }else {
                            channelTreeVO.setChannelVOList(voList);
                            result.add(channelTreeVO);
                        }
                    }
            );
        }else {
            return AjaxResult.ok(result);
        }
        return AjaxResult.ok(result);
    }


    /**
     * 提供给头像个性化配置的筛选接口
     * @return
     */
    @GetMapping("/queryChannelTreeForPicture")
    public AjaxResult<List<ChannelTreeVO>> queryChannelTreeForPicture(){
        List<ChannelTreeVO> result = new ArrayList<>();
        List<ChannelTypeListVo> channelTypeListVos = crmChannelDefService.channelTypeList();
        List<ChannelTypeListVo> channelTypeListResult = new ArrayList<>();
        //进行数据校验
        Map<String, List<ChannelTypeListVo>> channelCodeMap = channelTypeListVos.stream().collect(Collectors.groupingBy(ChannelTypeListVo::getCode));
        for (Map.Entry<String, List<ChannelTypeListVo>> stringListEntry : channelCodeMap.entrySet()) {
            if(
                    CrmChannelEnum.WEB_CHAT.getCode().toString().equals(stringListEntry.getKey()) ||
                            CrmChannelEnum.APP_CHAT.getCode().toString().equals(stringListEntry.getKey()) ||
                            CrmChannelEnum.WEB_VIDEO.getCode().toString().equals(stringListEntry.getKey()) ||
                            CrmChannelEnum.APP_VIDEO.getCode().toString().equals(stringListEntry.getKey()) ||
                            CrmChannelEnum.WECHAT_MINI_PROGRAM.getCode().toString().equals(stringListEntry.getKey()) ||
                            CrmChannelEnum.SHOPIFY.getCode().toString().equals(stringListEntry.getKey()) ||
                            CrmChannelEnum.WEB_ONLINE_VOICE.getCode().toString().equals(stringListEntry.getKey()) ||
                            CrmChannelEnum.APP_ONLINE_VOICE.getCode().toString().equals(stringListEntry.getKey())
            ){
                channelTypeListResult.addAll(stringListEntry.getValue());
            }
        }
        if(ObjectUtil.isNotNull(channelTypeListResult) && ObjectUtil.isNotEmpty(channelTypeListResult) && channelTypeListResult.size()!=0){
            List<String> collect = channelTypeListResult.stream().map(ChannelTypeListVo -> ChannelTypeListVo.getCode()).collect(Collectors.toList());
            List<CrmChannelConfig> list = crmChannelConfigService.list(new LambdaQueryWrapper<CrmChannelConfig>()
                    .in(CrmChannelConfig::getChannelType, collect)
                    .eq(CrmChannelConfig::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                    .eq(CrmChannelConfig::getDataStatus, 1)
            );
            Map<Integer, List<CrmChannelConfig>> channelConfig = list.stream().collect(Collectors.groupingBy(CrmChannelConfig::getChannelType));
            channelTypeListResult.forEach(
                    item -> {
                        ChannelTreeVO channelTreeVO = new ChannelTreeVO();
                        channelTreeVO.setCode(item.getCode());
                        channelTreeVO.setName(item.getName());
                        //进行渠道配置获取
                        List<CrmChannelConfig> crmChannelConfigs = channelConfig.get(Convert.toInt(item.getCode()));
                        List<WorkManageChannelVO> voList = new ArrayList<>();
                        if (ObjectUtil.isNotNull(crmChannelConfigs) && ObjectUtil.isNotEmpty(crmChannelConfigs) && crmChannelConfigs.size() != 0) {
                            crmChannelConfigs.stream().distinct().collect(Collectors.toList()).forEach(config ->{
                                WorkManageChannelVO vo = new WorkManageChannelVO();
                                vo.setChannelId(config.getChannelId());
                                vo.setChannelType(config.getChannelType());
                                vo.setName(config.getName());
                                voList.add(vo);
                            });
                            channelTreeVO.setChannelVOList(voList);
                            result.add(channelTreeVO);
                        }else {
                            channelTreeVO.setChannelVOList(voList);
                            result.add(channelTreeVO);
                        }
                    }
            );
        }else {
            return AjaxResult.ok(result);
        }
        return AjaxResult.ok(result);
    }

    /**
     * TikTok 返回给前端region信息
     * @param
     * @return
     */
    @GetMapping("queryTiktokShopRegionDefList")
    public AjaxResult<List<ChannelTiktokShopRegionDefVo>> queryTiktokShopRegionDefList() {
        try {
            return tiktokShopRegionDefService.queryTiktokShopRegionDefList();
        } catch (Exception e) {
            log.error("查询Tiktok Shop region下拉列表 失败", e);
            return AjaxResult.failure();
        }
    }

    /**
     * TikTok 获取shop信息
     * @param
     * @return
     */
    @PostMapping("queryTiktokShopInfo")
    public AjaxResult<List<TiktokShopInfo>> queryTiktokShopInfo(@RequestBody TiktokShopInfoParams tiktokShopInfoParams) {
        try {
            return tiktokShopRegionDefService.queryTiktokShopInfo(tiktokShopInfoParams);
        } catch (Exception e) {
            log.error("查询Tiktok Shop 信息失败", e);
            return AjaxResult.failure();
        }
    }


    /**
     * TikTok 获取订单
     * @param
     * @return
     */
    @PostMapping("tikTokOrderList")
    public AjaxResult<QueryTikTokOrderListResponse> tikTokOrderList(@RequestBody TikTokOrderParamRequest request) {
        try {
            QueryTikTokOrderListResponse tikTokOrderListResponse = tiktokShopRegionDefService.tikTokOrderList(request);
            return AjaxResult.ok(tikTokOrderListResponse);
        } catch (Exception e) {
            log.error("查询Tiktok 获取订单 信息失败", e);
            return AjaxResult.failure();
        }
    }


    /**
     * TikTok 添加渠道之前判断所选的店铺是否被占用
     *
     * @return
     * @paramR
     */
    @GetMapping("checkShopInfoExist")
    public AjaxResult checkShopInfoExist(@RequestParam(name = "shopId") String shopId) {
        try {
            return crmChannelConfigService.checkShopInfoExist(shopId);
        } catch (Exception e) {
            log.error("创建Tiktok渠道之前，判断所选店铺是否已被占用失败", e);
            return AjaxResult.failure();
        }
    }
}
