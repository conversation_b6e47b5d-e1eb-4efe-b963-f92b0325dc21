package com.goclouds.crm.platform.channel.domain.enums;

import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 该枚举类应对一个抽象类的不同实现子类全部注入IoC中，根据不同的code调用不同的实现类
 * 如果后续还有不同的社媒实现类，应该在该枚举类中继续添加
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MediaMessageTypeEnum implements Serializable {
    WhatsAppImpl(CrmChannelEnum.WHATSAPP.getCode(), "YCloudMessageImpl"),
    AWSInternalMessageImpl(CrmChannelEnum.AMAZON.getCode(), "AWSInternalMessageImpl"),
    FacebookMessageImpl(CrmChannelEnum.FACEBOOK.getCode(), "FacebookMessageImpl"),
    InstagramMessageImpl(CrmChannelEnum.INSTAGRAM.getCode(), "InstagramMessageImpl"),
    LineMessageImpl(CrmChannelEnum.LINE.getCode(), "LineMessageImpl"),
    WXOfficeAMessageImpl(CrmChannelEnum.WECHAT_OFFICIAL_ACCOUNT.getCode(), "WXOfficeAMessageImpl"),
    WXBizMessageImpl(CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getCode(), "WXBizMessageImpl"),
    TikTokShopMessageImpl(CrmChannelEnum.TIKTOK_SHOP.getCode(), "TikTokShopMessageImpl"),
    DiscordMessageImpl(CrmChannelEnum.DISCORD.getCode(), "DiscordMessageImpl"),

    ;
    /** channelTypeCode 渠道类型code */
    private int code;
    /**
     * 实现了MediaMessageService抽象类的子类类名
     * SelfWebSocketHandler类中的mediaMessageServiceMap使用@Resource注入的，如果是用@Autowired注入，需要看看是否还要修改这个值
     */
    private String implement;

    /**
     * 根据code获取实现类名
     * @param code code
     * @return String
     */
    public static String getImplByCode(int code) {
        for (MediaMessageTypeEnum value : MediaMessageTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.implement;
            }
        }

        return null;
    }

}
