package com.goclouds.crm.platform.channel.controller;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.channel.domain.CrmChannelGooglePlayApp;
import com.goclouds.crm.platform.channel.domain.vo.ChannelGoogPlayInfoVo;
import com.goclouds.crm.platform.channel.domain.vo.GooglePlayAllAppsVO;
import com.goclouds.crm.platform.channel.domain.vo.GooglePlayCommentVo;
import com.goclouds.crm.platform.channel.domain.vo.GooglePlayUserReviewsInfoVo;
import com.goclouds.crm.platform.channel.service.GooglePlayService;
import com.goclouds.crm.platform.common.constant.RabbitMqConstants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.controller.BaseController;
import com.goclouds.crm.platform.utils.MessageUtils;import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.List;

/**
 * @Description: google play评论/回复相关
 * @Author: zhumengyang
 * @Date: 2024/09/05/17:08
 */
@Slf4j
@RestController
@RequestMapping("googlePlay")
@RequiredArgsConstructor
public class GooglePlayController extends BaseController {

    private final GooglePlayService googlePlayService;

    /**
     * 获取该公司下的所有应用
     *
     * @return
     */
    @GetMapping("getAllApps")
    public AjaxResult<List<GooglePlayAllAppsVO>> getAllApps(){

        try {
            return googlePlayService.getAllApps();
        } catch (Exception e) {
            log.error("查询该公司下所有app报错",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    //定时任务：评论转存es
    /**
     * 定时获取用户评价，
     * 每2h保存/更新到ES中
     */
    @RabbitListener(queues = {RabbitMqConstants.SYNC_USER_COMMENT_PER_TWO_HOURS_QUEUE}, ackMode = "MANUAL")
    private void userCommentsSaveEsPerTwoHours(Message message, Channel channel){

        try{
            log.info("接受到MQ，开始转存评论到es（每两小时）");
            googlePlayService.saveCommentAndReplyPerTwoHours();
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }catch (Exception e){
            log.error("寻找漏存评论到es失败，报错为:",e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioException) {
                log.error("每两小时转存gp评论报错{}",ioException.getMessage());
            }
        }
    }

    /**
     * 每周检查有没有漏存：评论/回复
     */
    @RabbitListener(queues = {RabbitMqConstants.SYNC_USER_COMMENT_PER_WEEK_QUEUE}, ackMode = "MANUAL")
    private void userCommentsSaveEsPerWeek(Message message, Channel channel){

        try{
            log.info("接受到MQ，开始寻找漏存评论到es（每周）");
            googlePlayService.saveCommentAndReplyPerWeek();
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }catch (Exception e){
            log.error("寻找漏存评论到es失败，报错为:",e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioException) {
                log.error("每周转存gp评论报错{}",ioException.getMessage());
            }
        }
    }

    /**
     * 获取该公司下的所有应用
     *
     * @return
     */
    @GetMapping("queryAllComment")
    public AjaxResult<List<GooglePlayAllAppsVO>> queryAllComment(){

        try {
            return googlePlayService.getAllApps();
        } catch (Exception e) {
            log.error("查询该公司下所有app报错",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }


    /**
     * 保存GoogPlay配置信息
     * @param googPlayInfoVo
     * @return
     */
    @PostMapping("saveGoogPlayInfo")
    public AjaxResult saveGoogPlayInfo(@RequestBody ChannelGoogPlayInfoVo googPlayInfoVo){
        try {
            return googlePlayService.saveGoogPlayInfo(googPlayInfoVo);
        } catch (Exception e) {
            log.error("保存GoogPlay配置信息",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 查询所有应用信息
     */
    @GetMapping("getAllAppInfo")
    public AjaxResult<IPage<ChannelGoogPlayInfoVo>> getAllAppInfo(@RequestParam(value = "name",required = false) String name){
        try {
            return googlePlayService.getAllAppInfo(getPageParam(),name);
        } catch (Exception e) {
            log.error("查询所有应用信息错误",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 删除googlePlay下应用信息
     */
    @PostMapping("saveGoogPlayAppInfo")
    public AjaxResult saveGoogPlayAppInfo(@RequestBody CrmChannelGooglePlayApp googlePlayApp){
        try {
            return googlePlayService.saveGoogPlayAppInfo(googlePlayApp);
        } catch (Exception e) {
            log.error("appId删除应用",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 根据gpChannelId删除配置信息
     */
    @PostMapping("delGoogPlay/{gpChannelId}")
    public AjaxResult delGoogPlayInfo(@PathVariable String gpChannelId){
        try {
            return googlePlayService.delGoogPlayInfo(gpChannelId);
        } catch (Exception e) {
            log.error("appId删除应用",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 删除googlePlay下应用信息
     */
    @PostMapping("delGoogPlayAppInfo/{appId}")
    public AjaxResult delGoogPlayAppInfo(@PathVariable String appId){
        try {
            return googlePlayService.delGoogPlayAppInfo(appId);
        } catch (Exception e) {
            log.error("appId删除应用",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * GoogPlay配置信息上传Account文件解析
     */
    @PostMapping("getAccount")
    public AjaxResult getAccount(@RequestParam("file")MultipartFile file){
        try {
            return googlePlayService.getAccount(file);
        } catch (Exception e) {
            log.error("上传失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 获取评论数
     * @param comment
     * @return
     */
    @GetMapping("getCommentTags")
    public AjaxResult getCommentTags(GooglePlayCommentVo comment){
        try {
            return googlePlayService.getCommentTags(comment);
        } catch (Exception e) {
            log.error("查询失败!",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 获取所有评论
     * @param comment
     * @return
     */
    @GetMapping("getAllComment")
    public AjaxResult getAllComment(GooglePlayCommentVo comment){
        try {
            return AjaxResult.ok(googlePlayService.getAllComment(getPageParam(),comment));
        } catch (Exception e) {
            log.error("查询失败!",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 批量回复
     * @param reviewList
     * @return
     */
    @PostMapping("batchReply")
    public AjaxResult batchReply(@RequestBody List<GooglePlayUserReviewsInfoVo> reviewList){
        try {
            return googlePlayService.batchReply(reviewList);
        } catch (Exception e) {
            log.error("回复失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 获取搜索条件查询框
     * @param packageName
     * @return
     */
    @GetMapping("searchCriteria")
    public AjaxResult searchCriteria(@RequestParam("packageName") String packageName){
        try {
            return googlePlayService.searchCriteria(packageName);
        } catch (Exception e) {
            log.error("查询失败!",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 查询公司下所有应用信息
     * @param
     * @return
     */
    @GetMapping("getAllApp")
    public AjaxResult<List<CrmChannelGooglePlayApp>> getAllApp(String gpChannelId){
        try {
            return googlePlayService.getAllApp(gpChannelId);
        } catch (Exception e) {
            log.error("查询失败!",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 个人备注
     * @param remarks
     * @return
     */
    @PostMapping("addRemarks")
    public AjaxResult addRemarks(@RequestBody GooglePlayUserReviewsInfoVo remarks){
        try {
            return googlePlayService.addRemarks(remarks);
        } catch (Exception e) {
            log.error("备注失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }
}
