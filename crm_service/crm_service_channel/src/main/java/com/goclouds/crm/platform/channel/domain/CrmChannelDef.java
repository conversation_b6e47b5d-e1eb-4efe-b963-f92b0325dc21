package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道类型
 * @TableName crm_channel_def
 */
@TableName(value ="crm_channel_def")
@Data
public class CrmChannelDef implements Serializable {
    /**
     * 编码
     */
    @TableId
    private String code;

    /**
     * 值
     */
    private String name;

    /**
     * 渠道分组
     */
    private Integer channelGroup;

    /**
     * 渠道描述
     */
    private String channelDescribe;

    /**
     * 列表图标
     */
    private String listIcon;

    /**
     * 值
     */
    private String nameEn;

    /**
     * 数据状态
     */
    private Integer dataStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    private String icon;
    /**
     * 排序
     */
    private String channelOrder;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}