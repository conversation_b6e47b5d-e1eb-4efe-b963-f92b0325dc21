package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 平台渠道配置
 * @TableName crm_channel_config
 */
@TableName(value ="crm_channel_config")
@Data
public class CrmChannelConfig implements Serializable {
    /**
     * 渠道id
     */
    @TableId
    private String channelId;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 启用状态
     */
    private Integer enableStatus;

    /**
     * Connect实例id
     */
    private String connectInstanceId;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据状态
     */
    private Integer dataStatus;
    /**
     *  是否开启Connect集成，1-开启；0-不开启
     */
    private Integer isOpenConnect;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}