package com.goclouds.crm.platform.channel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.goclouds.crm.platform.annotation.NoLogin;
import com.goclouds.crm.platform.channel.domain.CrmAwsConnectFlow;
import com.goclouds.crm.platform.channel.domain.vo.CrmAwsConnectFlowDemoVo;
import com.goclouds.crm.platform.channel.domain.vo.DemoTimeVO;
import com.goclouds.crm.platform.channel.service.CrmAwsConnectFlowService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @createTime 2023年09月13日 17:21
 */
@RestController
@RequestMapping("demo")
public class DemoController extends BaseController {

    @Autowired
    private CrmAwsConnectFlowService crmAwsConnectFlowService;

    @GetMapping("hello")
    @NoLogin
    public AjaxResult hello() {
        IPage<CrmAwsConnectFlow> page = new Page<>(1,10);
        page = crmAwsConnectFlowService.page(page);
        return AjaxResult.ok(getDataTable(page, CrmAwsConnectFlowDemoVo.class));
//        return AjaxResult.ok(getDataTable(page));
       // return AjaxResult.ok(new PagingDozer<>(page, CrmAwsConnectFlowDemoVo.class));
    }

    @PostMapping("hello2")
    public AjaxResult hello2(Date time,LocalDateTime time2, @RequestBody DemoTimeVO crmAwsConnectFlow) {
        System.out.println(time);
        System.out.println(time2);
        System.out.println(crmAwsConnectFlow);
        DemoTimeVO demoTimeVO = new DemoTimeVO();
        demoTimeVO.setDate(new Date());
        demoTimeVO.setLocalDateTime(LocalDateTime.now());
        System.out.println(demoTimeVO);
        return AjaxResult.ok(demoTimeVO);
    }

    @GetMapping("hello3")
    @NoLogin
    public AjaxResult hello3(LocalDateTime time) {
        return AjaxResult.ok(time);
    }

}
