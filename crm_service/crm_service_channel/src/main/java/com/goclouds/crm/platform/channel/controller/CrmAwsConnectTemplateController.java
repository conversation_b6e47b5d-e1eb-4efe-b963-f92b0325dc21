package com.goclouds.crm.platform.channel.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.goclouds.crm.platform.channel.domain.CrmAwsConnectTemplates;
import com.goclouds.crm.platform.channel.service.CrmAwsConnectTemplatesService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("template")
public class CrmAwsConnectTemplateController {
    @Autowired
    private CrmAwsConnectTemplatesService crmAwsConnectTemplatesService;

    /**
     * 获取联系流列表
     * @return
     */
    @GetMapping("templateList")
    public AjaxResult templateList(String connectId){
        LambdaQueryWrapper<CrmAwsConnectTemplates> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(CrmAwsConnectTemplates::getConnectId, connectId);
        List<CrmAwsConnectTemplates> list = crmAwsConnectTemplatesService.list(lambdaQueryWrapper);
        return AjaxResult.ok(list);
    }

    public String template(String connectId, String templateName){
        LambdaQueryWrapper<CrmAwsConnectTemplates> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(CrmAwsConnectTemplates::getConnectId, connectId);
        lambdaQueryWrapper.eq(CrmAwsConnectTemplates::getTemplatesName, templateName);
        CrmAwsConnectTemplates entity = crmAwsConnectTemplatesService.getOne(lambdaQueryWrapper);
        return entity.getTemplatesId();
    }
}
