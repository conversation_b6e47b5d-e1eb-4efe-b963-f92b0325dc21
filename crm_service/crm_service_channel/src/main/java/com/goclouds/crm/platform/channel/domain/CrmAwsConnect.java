package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * aws账号关联connect实例
 * @TableName crm_aws_connect
 */
@TableName(value ="crm_aws_connect")
@Data
public class CrmAwsConnect implements Serializable {
    /**
     * id
     */
    @TableId
    private String connectId;

    /**
     * aws用户id
     */
    private String awsUserId;

    /**
     * connect实例所在的region
     */
    private String regionId;

    /**
     * 别名
     */
    private String connectAlias;

    /**
     * 访问地址
     */
    private String connectUrl;

    /**
     * 中继地址
     */
    private String relayUrl;

    /**
     * 公司id
     */
    private String CompanyId;

    /**
     * 通道，出入站
     */
    private Integer bound;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     *  connect实例状态 0-禁用 1-启用
     */
    private Integer connectStatus;

    private String connectArn;

    /**
     * 数据状态 0-删除 1-正常
     */
    @TableLogic(value = "1", delval = "0")
    private Integer dataStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @TableField(exist = false)
    private String userId;

    /**
     * region
     */
    @TableField(exist = false)
    private String regionCode;

    private String recordS3Bucket;
    private String recordS3BucketRegion;

    private String identityManagementType;

    /**
     * 对应的网关地址-web聊天使用
     */
    private String chatApiGateway;

    /**
     * 对应的网关地址-web视频使用
     */
    private String videoApiGateway;
}
