package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 工单扩展属性配置
 * @TableName crm_agent_work_record_ext
 */

@TableName(value ="crm_agent_work_record_ext_def")
@Data
@Accessors(chain = true)
public class CrmAgentWorkRecordExtDef {
    @TableId
    private String id;

    private String companyId;

    private String code;

    private String name;

    private Integer dataStatus;

    private Date createTime;

    private String creator;

    private String modifier;

    private Date modifyTime;
}