/*
 * Copyright 2013-2022 Xia Jun(<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************************
 *                                                                                     *
 *                        Website : http://www.farsunset.com                           *
 *                                                                                     *
 ***************************************************************************************
 */
package com.goclouds.crm.platform.channel.domain.ws;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.goclouds.crm.platform.channel.domain.ws.proto.MessageProto;

import java.io.Serializable;

/**
 * 消息对象
 */
public class Message implements Serializable, Transportable,Cloneable {

	private static final long serialVersionUID = 1L;

	/**
	 * 消息id
	 */
	private String id;

	/**
	 * 消息类型，用户自定义消息类别
	 */
	private String action;
	/**
	 * 消息标题
	 */
	private String title;
	/**
	 * 消息类容，于action 组合为任何类型消息，content 根据 format 可表示为 text,json ,xml数据格式
	 */
	private String content;

	/**
	 * 消息发送者账号
	 */
	private String sender;
	/**
	 * 消息发送者接收者
	 */
	private String receiver;

	/**
	 * content 内容格式
	 */
	private String format;

	/**
	 * 附加内容 内容
	 */
	private String extra;

	private long timestamp;


	private String username;
	private String role;
	private String sid;

	public Message() {
		timestamp = System.currentTimeMillis();
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

	public String getFormat() {
		return format;
	}

	public void setFormat(String format) {
		this.format = format;
	}

	public String getExtra() {
		return extra;
	}

	public void setExtra(String extra) {
		this.extra = extra;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public String getSid() {
		return sid;
	}

	public void setSid(String sid) {
		this.sid = sid;
	}

	@Override
	public String toString() {
		StringBuffer buffer = new StringBuffer();
		buffer.append("#Message#").append("\n");
		buffer.append("id       :").append(id).append("\n");
		buffer.append("sender   :").append(sender).append("\n");
		buffer.append("receiver :").append(receiver).append("\n");
		buffer.append("action   :").append(action).append("\n");
		buffer.append("content  :").append(content).append("\n");
		buffer.append("format   :").append(format).append("\n");
		buffer.append("extra    :").append(extra).append("\n");
		buffer.append("title    :").append(title).append("\n");
		buffer.append("timestamp:").append(timestamp).append("\n");
		buffer.append("role:").append(role).append("\n");
		buffer.append("username:").append(username).append("\n");
		buffer.append("sid:").append(sid);
		return buffer.toString();
	}

	@Override
	public Message clone(){
		Message message = new Message();
		message.id = id;
		message.action = action;
		message.title = title;
		message.content = content;
		message.sender = sender;
		message.receiver = receiver;
		message.extra = extra;
		message.format = format;
		message.timestamp = timestamp;
		message.role = role;
		message.username = username;
		message.sid = sid;
		return message;
	}

	@JsonIgnore
	@Override
	public byte[] getBody() {
		MessageProto.Model.Builder builder = MessageProto.Model.newBuilder();
		builder.setId(id);
		builder.setAction(action);
		builder.setSender(sender);
		builder.setTimestamp(timestamp);

		/*
		 * 下面字段可能为空
		 */

		if (receiver != null){
			builder.setReceiver(receiver);
		}

		if (content != null) {
			builder.setContent(content);
		}

		if (title != null) {
			builder.setTitle(title);
		}

		if (extra != null) {
			builder.setExtra(extra);
		}

		if (format != null) {
			builder.setFormat(format);
		}

		if (username != null) {
			builder.setUsername(username);
		}

		if (role != null) {
			builder.setRole(role);
		}

		if (sid != null) {
			builder.setSid(sid);
		}

		return builder.build().toByteArray();
	}

	@JsonIgnore
	@Override
	public DataType getType() {
		return DataType.MESSAGE;
	}
}
