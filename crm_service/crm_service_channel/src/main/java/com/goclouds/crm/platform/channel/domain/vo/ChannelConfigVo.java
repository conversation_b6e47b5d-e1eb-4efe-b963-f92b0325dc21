package com.goclouds.crm.platform.channel.domain.vo;

import com.goclouds.crm.platform.channel.domain.CrmChannelInfoConfigInst;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

@Data
@NoArgsConstructor
public class ChannelConfigVo {

    private String id;
    /**
     * 编码
     */
    private String code;

    /**
     * 值
     */
    private String name;
    /**
     * 正则匹配规则
     */
    private String mappingRule;

    public ChannelConfigVo(CrmChannelInfoConfigInst s) {
        BeanUtils.copyProperties(s,this);
    }
}
