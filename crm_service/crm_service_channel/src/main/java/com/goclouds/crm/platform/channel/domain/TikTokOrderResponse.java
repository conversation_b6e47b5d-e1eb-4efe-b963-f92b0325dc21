package com.goclouds.crm.platform.channel.domain;

import lombok.Data;
import java.util.List;

@Data
public class TikTokOrderResponse {
        private String next_page_token;
        private Integer total_count;
        private List<Order> orders;

    @Data
    public static class Order {
        private String id;
        private String buyer_message;
        private String cancellation_initiator;
        private String shipping_provider_id;
        private Long create_time;
        private String shipping_provider;
        private List<Package> packages;
        private Payment payment;
        private RecipientAddress recipient_address;
        private String status;
        private String fulfillment_type;
        private String delivery_type;
        private Long paid_time;
        private Long rts_sla_time;
        private Long tts_sla_time;
        private String cancel_reason;
        private Long update_time;
        private String payment_method_name;
        private Long rts_time;
        private String tracking_number;
        private String split_or_combine_tag;
        private Boolean has_updated_recipient_address;
        private Long cancel_order_sla_time;
        private String warehouse_id;
        private Long request_cancel_time;
        private String shipping_type;
        private String user_id;
        private String seller_note;
        private Long delivery_sla_time;
        private Boolean is_cod;
        private String delivery_option_id;
        private Long cancel_time;
        private String need_upload_invoice;
        private String delivery_option_name;
        private String cpf;
        private List<LineItem> line_items;
        private String buyer_email;
        private Long delivery_due_time;
        private String is_sample_order;
        private Long shipping_due_time;
        private Long collection_due_time;
        private Long delivery_option_required_delivery_time;
        private Boolean is_on_hold_order;
        private Long delivery_time;
        private Boolean is_replacement_order;
        private Long collection_time;
        private String replaced_order_id;
        private Boolean is_buyer_request_cancel;
        private Long pick_up_cut_off_time;
        private Long fast_dispatch_sla_time;
        private String commerce_platform;
        private String order_type;
        private Long release_date;
        private HandlingDuration handling_duration;
        private String auto_combine_group_id;
        private String cpf_name;
        private Boolean is_exchange_order;
        private String exchange_source_order_id;
        private String consultation_id;
        private String fast_delivery_program;
    }

    @Data
    public static class Package {
        private String id;
    }

    @Data
    public static class Payment {
        private String currency;
        private String sub_total;
        private String shipping_fee;
        private String seller_discount;
        private String platform_discount;
        private String total_amount;
        private String original_total_product_price;
        private String original_shipping_fee;
        private String shipping_fee_seller_discount;
        private String shipping_fee_platform_discount;
        private String shipping_fee_cofunded_discount;
        private String tax;
        private String small_order_fee;
        private String shipping_fee_tax;
        private String product_tax;
        private String retail_delivery_fee;
        private String buyer_service_fee;
        private String handling_fee;
        private String shipping_insurance_fee;
        private String item_insurance_fee;
    }

    @Data
    public static class RecipientAddress {
        private String full_address;
        private String phone_number;
        private String name;
        private String first_name;
        private String last_name;
        private String first_name_local_script;
        private String last_name_local_script;
        private String address_detail;
        private String address_line1;
        private String address_line2;
        private String address_line3;
        private String address_line4;
        private List<DistrictInfo> district_info;
        private DeliveryPreferences delivery_preferences;
        private String postal_code;
        private String region_code;
    }

    @Data
    public static class DistrictInfo {
        private String address_level_name;
        private String address_name;
        private String address_level;
    }

    @Data
    public static class DeliveryPreferences {
        private String drop_off_location;
    }

    @Data
    public static class LineItem {
        private String id;
        private String sku_id;
        private List<CombinedListingSku> combined_listing_skus;
        private String display_status;
        private String product_name;
        private String seller_sku;
        private String sku_image;
        private String sku_name;
        private String product_id;
        private String sale_price;
        private String platform_discount;
        private String seller_discount;
        private String sku_type;
        private String cancel_reason;
        private String original_price;
        private Long rts_time;
        private String package_status;
        private String currency;
        private String shipping_provider_name;
        private String cancel_user;
        private String shipping_provider_id;
        private Boolean is_gift;
        private List<ItemTax> item_tax;
        private String tracking_number;
        private String package_id;
        private String retail_delivery_fee;
        private String buyer_service_fee;
        private String small_order_fee;
        private String handling_duration_days;
        private Boolean is_dangerous_good;
        private Boolean needs_prescription;
    }

    @Data
    public static class CombinedListingSku {
        private String sku_id;
        private Integer sku_count;
        private String product_id;
        private String seller_sku;
    }

    @Data
    public static class ItemTax {
        private String tax_type;
        private String tax_amount;
        private String tax_rate;
    }

    @Data
    public static class HandlingDuration {
        private String days;
        private String type;
    }
}