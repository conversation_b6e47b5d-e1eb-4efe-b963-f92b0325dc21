package com.goclouds.crm.platform.channel.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ToMeidaMessageTypeEnum implements Serializable {
    NotSupport(1, "notSupport"),
    RagButtonMessage(2, "ragButtonMessage"),
    RagButtonMessageNonKnow201(3, "ragButtonMessageNonKnow201"),
    ConnectMessage(4, "connectMessage"),
    WsConnectNotice(5, "wsConnectNotice"),
    WsNoticeCustomerJoinSession(6, "wsNoticeCustomerJoinSession"),
    WsNoticeCustomerBeforeJoin(7, "wsNoticeCustomerBeforeJoin"),
    WsNoticeAgentJoinSession(8, "wsNoticeAgentJoinSession"),
    WsNoticeAgentLeaveSession(9, "wsNoticeAgentLeaveSession"),
    WsSendMessage(10, "wsSendMessage"),
    WsNoticeAgentJoinSessionForTransfer(11, "wsNoticeAgentJoinSessionForTransfer"),
    WsNoticeAgentLeaveSessionForTransfer(12, "wsNoticeAgentLeaveSessionForTransfer"),
    WsNoticeCustomerNoAvailableAgentJoin(13, "wsNoticeCustomerNoAvailableAgentJoin"),
    WsNoticeTransferAgent(14, "wsNoticeTransferAgent"),
    WsNoticeBeInputting(15, "wsNoticeBeInputting"),
    ;

    private int code;
    private String type;
}


