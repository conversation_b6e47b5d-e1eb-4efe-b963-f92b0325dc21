package com.goclouds.crm.platform.channel.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goclouds.crm.platform.controller.BaseController;
import com.goclouds.crm.platform.openfeignClient.domain.channel.GooglePlayUserReviewsIndexData;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:转存漏存测试
 * @Author: zhumengyang
 * @Date: 2024/09/24/10:05
 */
//@Slf4j
//@RestController
//@RequestMapping("googlePlayTest")
//@RequiredArgsConstructor
//public class GooglePlayTestController extends BaseController {
//
//
//    //ES
//    private final RestHighLevelClient restHighLevelClient;
//
//    @Value("${es.user-reviews}")
//    private String userReviewsIndex;
//
//    @GetMapping("saveEs")
//    public void getAllApps(){
//        //评论/回复数据
//        List<GooglePlayUserReviewsIndexData> commentReplyData = Arrays.asList(new GooglePlayUserReviewsIndexData()
//                .setReview_id("2410081714")
//                .setCreator("test")
//                .setCreate_time(new Date())
//                .setModifier("App Developer")
//                .setModify_time(new Date())
//                .setNote_content("这是一个好用户，可以联系改一下评分哦")
//                .setNote_time(new Date())
//                .setComment_status(1)
//                .setPackage_name("android.app")
//                .setComment_history(Arrays.asList(
//                        new GooglePlayUserReviewsIndexData.CommentHistory()
//                                .setAuthor_name("=======TEST_name======")
//                                .setUser_comment_text("=======TEST_评论02======")
//                                .setStar_rating(5f)
//                                .setReviewer_language("en")
//                                .setDevice("Pixel 7")
//                                .setAndroid_os_version(13)
//                                .setApp_version_code(46)
//                                .setApp_version_name("2.4.6")
//                                .setThumbs_up_count(10)
//                                .setThumbs_down_count(0)
//                                .setProduct_name("MyApp")
//                                .setManufacturer("Google")
//                                .setDevice_class("smartphone")
//                                .setScreen_width_px(1080)
//                                .setScreen_height_px(2400)
//                                .setNative_platform("Android")
//                                .setScreen_density_dpi(400)
//                                .setGlEs_version("3.2")
//                                .setCpu_model("Snapdragon 8 Gen 1")
//                                .setCpu_make("Qualcomm")
//                                .setRam_mb(8192)
//                                .setComment_time(new Date(1728379020 * 1000))
//                ))
//                .setReply_history(Arrays.asList(
//                        new GooglePlayUserReviewsIndexData.ReplyHistory()
//                                .setReply_time(new Date(1728379010 * 1000))
//                                .setAuthor_name("=======TEST_name======")
//                                .setDeveloper_reply_text("=======TEST_回复01======")
//                )));
//        //添加测试数据
//
//
//        //id-数据 map
//        Map<String,GooglePlayUserReviewsIndexData> idAndDataMap = commentReplyData.stream()
//                .collect(Collectors.toMap(commentReply->commentReply.getReview_id(),
//                        commentReply->commentReply));
//        try {
//            //保存到es里
//            //索引
//            String index = userReviewsIndex + "_" + SecurityUtil.getLoginUser().getCompanyId();
//            // 查询索引是否存在
//            boolean indexExists = false;
//            indexExists = headIndexExists(index);
//            // 索引不存在,创建索引
//            if (!indexExists) {
//                log.info("索引不存在：{}",index);
//            } else { //索引存在要先检查es,存在过的id需要更新
//                //看哪些评论在es里,先获取id
//                List<String> reviewIdList = new ArrayList<>(idAndDataMap.keySet());
//                //创建查询
//                SearchRequest request = new SearchRequest(index);
//                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
//                        .query(QueryBuilders.boolQuery()
//                                //查询条件匹配多个id，es存在的id则是要更新的
//                                .must(QueryBuilders.termsQuery("review_id", reviewIdList)));
//                //获取要更新的这些数据
//                request.source(searchSourceBuilder);
//                SearchResponse searchResponse = restHighLevelClient.search(request, RequestOptions.DEFAULT);
//                SearchHit[] hits = searchResponse.getHits().getHits();
//                //遍历要更新的这些数据
//                for (SearchHit hit : hits) {
//                    // 使用 Jackson 将 JSON 映射到 data 对象
//                    ObjectMapper objectMapper = new ObjectMapper();
//                    // es中的文档映射到data对象，然后把要更新的评论/回复加进去，然后再批量存
//                    GooglePlayUserReviewsIndexData userReview = objectMapper.readValue(hit.getSourceAsString(), GooglePlayUserReviewsIndexData.class);
//                    String id = userReview.getReview_id();
//                    //修改者/时间
//                    userReview.setModify_time(new Date());
//                    //如果有评论，评论时间
//                    Date commentTime = null;
//                    //加入评论数据
//                    if (idAndDataMap.get(id).getComment_history() != null && !idAndDataMap.get(id).getComment_history().isEmpty()) {
//                        //有评论id一定有评论
//                        //获取评论里最近的时间
//                        Optional<GooglePlayUserReviewsIndexData.CommentHistory> max = userReview.getComment_history().stream()
//                                .max(Comparator.comparing(GooglePlayUserReviewsIndexData.CommentHistory::getComment_time));
//                        Date maxCommentTime = max.get().getComment_time();
//                        commentTime = idAndDataMap.get(id).getComment_history().get(0).getComment_time();
//                        //超过最新日期，则加入历史评论
//                        if (commentTime.getTime()/1000 > maxCommentTime.getTime()/1000) {
//                            //查看是否为，回复后更新的评论，置2：回复后更新
//                            if (userReview.getComment_status() == 1) {
//                                userReview.setComment_status(2);
//                            }
//                            //加入评论数据
//                            userReview.getComment_history().addAll(idAndDataMap.get(id).getComment_history());
//                        }
//                    }
//                    //加入回复数据
//                    if (idAndDataMap.get(id).getReply_history() != null && !idAndDataMap.get(id).getReply_history().isEmpty()) {
//                        //回复时间
//                        Date replyTime = idAndDataMap.get(id).getReply_history().get(0).getReply_time();
//                        //有评论id，不一定有回复
//                        //获取回复里最近的时间
//                        if (userReview.getReply_history() != null && !userReview.getReply_history().isEmpty()) {
//                            Optional<GooglePlayUserReviewsIndexData.ReplyHistory> max = userReview.getReply_history().stream()
//                                    .max(Comparator.comparing(GooglePlayUserReviewsIndexData.ReplyHistory::getReply_time));
//                            Date maxReplyTime = max.get().getReply_time();
//                            //超过最新日期，则加入历史回复
//                            if (replyTime.getTime()/1000 > maxReplyTime.getTime()/1000) {
//                                // 评论未回复=>已回复
//                                if(userReview.getComment_status() == 0){
//                                    userReview.setComment_status(1);
//                                    //检查是否为回复后更新的评论
//                                    if(commentTime != null && commentTime.getTime()/1000 > replyTime.getTime()/1000){
//                                        userReview.setComment_status(2);
//                                    }
//                                }
//                                //已回复=>回复后更新
//                                //更新了评论，并且评论时间晚于回复，则是回复后更新的状态
//                                if(userReview.getComment_status() == 1) {
//                                    if (commentTime != null && commentTime.getTime()/1000 > replyTime.getTime()/1000) {
//                                        userReview.setComment_status(2);
//                                    }
//                                }
//                                //加入回复数据
//                                userReview.getReply_history().addAll(idAndDataMap.get(id).getReply_history());
//                            }
//                            //没超过最新日期
//                            if(userReview.getComment_status() == 1) {
//                                if (commentTime != null && commentTime.getTime()/1000 > maxReplyTime.getTime()/1000) {
//                                    userReview.setComment_status(2);
//                                }
//                            }
//                        } else {
//                            //原本没有回复，则加入，且置1：已回复；且加入数据
//                            userReview.setComment_status(1);
//                            //检查评论回复时间先后
//                            if (commentTime != null && commentTime.getTime()/1000 > replyTime.getTime()/1000) {
//                                userReview.setComment_status(2);
//                            }
//                            //原本为空/null，直接set而不是add，避免空指针异常
//                            userReview.setReply_history(idAndDataMap.get(id).getReply_history());
//                        }
//                    }
//                    idAndDataMap.put(id, userReview);
//                }
//                //将map中数据放回list
//                commentReplyData = new ArrayList<>(idAndDataMap.values());
//            }
//            //批量存入评论
//            saveUserData(index, commentReplyData);
//            log.info("google play用户评论数据转存成功");
//        } catch (Exception e) {
//            log.error("google play用户评论数据报错:{0}", e);
//        }
//    }
//
//    /**
//     * 查询索引是否存在
//     * @param index 索引名称
//     * @return
//     * @throws IOException
//     */
//    private boolean headIndexExists(String index) throws IOException {
//        GetIndexRequest req = new GetIndexRequest(index);
//        boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
//        log.info("当前索引{}, 是否存在: {}", index, exists);
//        return exists;
//    }
//
//    /**
//     * 存评论/回复数据到es索引
//     * @param index
//     * @param commentReplyData
//     */
////    private void saveUserData(String index, List<GooglePlayUserReviewsIndexData> commentReplyData) throws Exception {
////        //创建者修改者
////        String creMod =  SecurityUtil.getUsername();
////        // 定义批量请求
////        BulkRequest bulkRequest = new BulkRequest(index);
////        for (GooglePlayUserReviewsIndexData  userReview : commentReplyData){
////            // 将单个对象转为json
////            String value = new ObjectMapper().writeValueAsString(userReview);
////            // 定义单条索引请求
////            IndexRequest singleIndexRequest = new IndexRequest(index);
////            singleIndexRequest.source(value, XContentType.JSON);
////            // 添加单条索引请求到批量请求
////            bulkRequest.add(singleIndexRequest);
////        }
////        if (bulkRequest.numberOfActions() != 0) {
////            // 进行批量保存ES
////            batchSaveEs(bulkRequest);
////        }
////    }
//    private void saveUserData(String index, List<GooglePlayUserReviewsIndexData> commentReplyData) throws Exception {
//        // 创建者和修改者
//        String creMod = SecurityUtil.getUsername();
//        // 定义批量请求
//        BulkRequest bulkRequest = new BulkRequest(index);
//
//        for (GooglePlayUserReviewsIndexData userReview : commentReplyData) {
//            // 将单个对象转为 JSON
//            String value = new ObjectMapper().writeValueAsString(userReview);
//            // 定义更新请求
//            UpdateRequest updateRequest = new UpdateRequest(index, userReview.getReview_id())
//                    .doc(value, XContentType.JSON)
//                    .upsert(value, XContentType.JSON); // 如果不存在，则插入
//
//            // 添加更新请求到批量请求
//            bulkRequest.add(updateRequest);
//        }
//        if (bulkRequest.numberOfActions() != 0) {
//            // 进行批量保存到 ES
//            batchSaveEs(bulkRequest);
//        }
//    }
//
//    /**
//     *  进行批量保存ES数据
//     * @param bulkRequest 保存数据及索引
//     */
//    private void batchSaveEs(BulkRequest bulkRequest){
//        try {
//            // 执行批量索引操作 es版本和spring boot版本有冲突问题，添加成功，返回200，
//            BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
//        } catch (IOException e) {
//            if (!e.getMessage().contains("200 OK")&&!e.getMessage().contains("201")) {
//                log.error("es新增文档失败，异常信息：", e);
//                throw new RuntimeException(e);
//            }
//        }
//    }
//
//}
