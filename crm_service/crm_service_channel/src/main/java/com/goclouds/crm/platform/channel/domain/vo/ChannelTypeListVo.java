package com.goclouds.crm.platform.channel.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 渠道类型列表
 */
@Data
public class ChannelTypeListVo {
    /**
     * 编码
     */
    private String code;

    /**
     * 值
     */
    private String name;

    /**
     * 渠道分组
     */
    private Integer channelGroup;

    /**
     * 渠道描述
     */
    private String channelDescribe;

    /**
     * 列表图标
     */
    private String listIcon;

    /**
     * 排序
     */
    private String channelOrder;

    /**
     * 数量
     */
    private Integer channelCount;

    /**
     * 是否展示状态 0不展示， 1展示
     */
    private Integer displayStatus;
}
