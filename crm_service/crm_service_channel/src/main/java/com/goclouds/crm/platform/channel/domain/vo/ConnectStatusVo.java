package com.goclouds.crm.platform.channel.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description 案例状态
 * <AUTHOR>
 * @Date 2023/5/23 10:40
 * @Version 1.0
 **/
@Data
@Accessors(chain = true)
public class ConnectStatusVo {

    // 案例id
    @NotBlank
    @Length(max = 40)
    private String connectId;

    // 案例状态
    @NotNull
    private Integer connectStatus;

}
