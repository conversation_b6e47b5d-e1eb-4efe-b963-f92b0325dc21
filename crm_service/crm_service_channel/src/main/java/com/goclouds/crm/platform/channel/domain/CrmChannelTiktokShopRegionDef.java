package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * Tiktok Shop region表
 * @TableName crm_channel_tiktok_shop_region_def
 */
@TableName(value ="crm_channel_tiktok_shop_region_def")
@Data
public class CrmChannelTiktokShopRegionDef implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 国家/地区代码
     */
    private String countryCode;

    /**
     * 默认区域设置代码
     */
    private String defaultLocaleCode;

    /**
     * 国家icon
     */
    private String countryNameIcon;

    /**
     * 授权url
     */
    private String authUrl;

    /**
     * 应用密钥
     */
    private String appKey;

    /**
     * 应用密码
     */
    private String appSecret;

    /**
     * 数据状态
     */
    private Integer dataStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}