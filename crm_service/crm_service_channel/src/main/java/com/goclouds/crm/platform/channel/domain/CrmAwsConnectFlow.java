package com.goclouds.crm.platform.channel.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * aws账号关联connect实例
 * @TableName crm_aws_connect_flow
 */
@TableName(value ="crm_aws_connect_flow")
@Data
public class CrmAwsConnectFlow implements Serializable {
    /**
     * id
     */
    @TableId
    private String flowId;

    /**
     * connect实例ID
     */
    private String connectId;

    /**
     * aws用户id
     */
    private String awsUserId;

    /**
     * 流名称
     */
    private String flowName;

    /**
     * 流类型
     */
    private String flowType;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 流状态 0-禁用 1-启用
     */
    private Integer flowStatus;

    /**
     * 数据状态
     */
    private Integer dataStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}