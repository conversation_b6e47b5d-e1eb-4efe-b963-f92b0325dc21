package com.goclouds.crm.platform.channel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.annotation.InnerAuth;
import com.goclouds.crm.platform.channel.domain.CrmAwsConnect;
import com.goclouds.crm.platform.channel.domain.CrmAwsRegionDef;
import com.goclouds.crm.platform.channel.domain.vo.*;
import com.goclouds.crm.platform.channel.service.CrmAwsRegionDefService;
import com.goclouds.crm.platform.openfeignClient.domain.channel.AwsConnectVo;
import com.goclouds.crm.platform.channel.service.CrmAwsConnectService;
import com.goclouds.crm.platform.channel.service.SysUserConnectService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.controller.BaseController;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmAwsConnectVo;
import com.goclouds.crm.platform.openfeignClient.domain.channel.SysUserConnectVo;
import com.goclouds.crm.platform.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("awsConnect")
@RequiredArgsConstructor
public class AwsConnectController extends BaseController {

    private final CrmAwsConnectService crmAwsConnectService;
    private final SysUserConnectService sysUserConnectService;
    private final CrmAwsRegionDefService crmAwsRegionDefService;

    /**
     * 分页查询 账号id对应的案例信息
     *
     * @param queryConnectVo 入参
     * @return AjaxResult
     */
    @PostMapping("queryAwsConnectList")
    public AjaxResult queryAwsConnectList(@RequestBody @Validated QueryConnectVo queryConnectVo) {
        try {
            IPage<CrmAwsConnect> crmAwsConnectIPage = crmAwsConnectService.queryAwsConnectList(getPageParam(), queryConnectVo.getAwsUserId());
            return AjaxResult.ok(getDataTable(crmAwsConnectIPage)).setMsg(MessageUtils.get("operate.success"));
        } catch (Exception e){
            log.error("查询案例信息失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 修改案例状态
     * @param connectStatusVo 修改的状态
     * @return AjaxResult
     */
    @PostMapping("updateConnectStatus")
    public AjaxResult updateConnectStatus(@RequestBody @Validated ConnectStatusVo connectStatusVo) {
        try {
            return crmAwsConnectService.updateConnectStatus(connectStatusVo);
        } catch (Exception e){
            log.error("修改案例状态信息失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 修改别名
     * @param connectAliasVo 修改的别名
     * @return AjaxResult
     */
    @PostMapping("updateConnectAlias")
    public AjaxResult updateConnectAlias(@RequestBody @Validated ConnectAliasVo connectAliasVo) {
        try {
            return crmAwsConnectService.updateConnectAlias(connectAliasVo);
        } catch (Exception e){
            log.error("修改案例别名信息失败",e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    /**
     * 同步connect案例
     * @param
     * @return AjaxResult
     */
    @GetMapping("synConnect/{awsUserId}")
    public AjaxResult synConnect(@PathVariable String awsUserId) {
        // 发mq
        return crmAwsConnectService.synConnect(awsUserId);
    }

    /**
     * 查询当前登录人所在公司的所有Connect list(包含当前所有人的userId)
     * 为了 在平台条用户管理list中 座席角色 展示connect 信息
     * @return
     */
    @InnerAuth(isUser = true)
    @GetMapping("all")
    public AjaxResult<List<CrmAwsConnectVo>> queryAllConnectList() {
        return crmAwsConnectService.queryAllConnectList();
    }

    /**
     * 查询当前登录人绑定的connect 
     * 管理员展示所有  座席展示绑定的
     * @return
     */
    @GetMapping("web/all")
    public AjaxResult<List<CrmAwsConnectVo>> queryWebAllConnectList() {
        return crmAwsConnectService.queryConnectList();
    }

    /**
     * 查询指定userId（座席角色）的connect list
     * @return
     */
    @InnerAuth(isUser = true)
    @GetMapping("user")
    public AjaxResult<List<CrmAwsConnectVo>> queryCurrentUserConnectList(@RequestParam("userId") String userId) {
        return crmAwsConnectService.queryCurrentUserConnectList(userId);
    }

    /**
     * 保存connect 到redis中
     * @param awsConnectVo
     * @return
     */
    @PostMapping("redis/saveConnect")
    public AjaxResult<Object> saveSelectedConnect(@RequestBody(required = false) AwsConnectVo awsConnectVo) {
        return crmAwsConnectService.saveSelectedConnect(awsConnectVo);
    }


    /**
     * 从redis中获取选中的connect
     * @return
     */
    @GetMapping("redis/connect")
    public AjaxResult<AwsConnectVo> getSelectedConnect() {
        return crmAwsConnectService.getSelectedConnect();
    }

    /**
     * 从redis中获取选中的connect
     * @return
     */
    @GetMapping("redis/selectedConnect")
    @InnerAuth(isUser = true)
    public AjaxResult<AwsConnectVo> selectedConnect() {
        return crmAwsConnectService.getSelectedConnect();
    }

    /**
     * 删除用户对应的connect
     * @return
     */
    @InnerAuth
    @DeleteMapping("{userId}")
    public AjaxResult deleteUserConnect(@PathVariable("userId") String userId) {
        return sysUserConnectService.deleteUserConnect(userId);
    }


    /**
     * 添加用户和connect的对应关系
     * @param sysUserConnectVoList
     * @return
     */
    @InnerAuth
    @PostMapping("user")
    public AjaxResult addUserConnect(@RequestBody @NotEmpty(message = "{user.connect.id.not.null}") List<SysUserConnectVo> sysUserConnectVoList) {
        return sysUserConnectService.addUserConnect(sysUserConnectVoList);
    }

    @InnerAuth
    @GetMapping("getConnectInfo")
    public AjaxResult getConnectInfo(String connectId) {
        CrmAwsConnect byId = crmAwsConnectService.getById(connectId);
        CrmAwsConnectInfoVo vo = new CrmAwsConnectInfoVo();
        BeanUtils.copyProperties(byId, vo);
        CrmAwsRegionDef regionDef = crmAwsRegionDefService.getById(byId.getRegionId());
        vo.setRegion(regionDef.getRegionCode());
        return AjaxResult.ok(vo);
    }

    /**
     *  查询connect信息
     * @return
     */
    @InnerAuth
    @GetMapping("queryConnectInstances")
    public AjaxResult<List<ConnectInfoVo>> queryConnectInstances() {
        return crmAwsConnectService.queryConnectInstances();
    }

    /**
     * 根据公司ID 查询connect实例ID
     * @param companyId 公司ID
     * @return connect实例ID
     */
    @InnerAuth
    @GetMapping("queryCompanyConnect")
    public AjaxResult<List<String>> queryCompanyConnect(@RequestParam("companyId") String companyId) {
        return crmAwsConnectService.queryCompanyConnect(companyId);
    }

    /**
     * 查询awsConnect
     * @param connectId connectId
     * @return AjaxResult
     */
    @InnerAuth
    @GetMapping("inner/queryAwsConnect")
    public AjaxResult<CrmAwsConnectVo> queryAwsConnectVo(@RequestParam("connectId") String connectId) {
        return crmAwsConnectService.queryAwsConnectVo(connectId);
    }


    /**
     *  查询connect信息
     * @return
     */
    @InnerAuth
    @PostMapping("queryConnectIdInstances")
    public AjaxResult<ConnectInfoVo> queryConnectIdInstances(@RequestParam("connectId") String connectId) {
        return crmAwsConnectService.queryConnectIdInstances(connectId);
    }

    /**
     * 查询Connect instance实例
     * 该公司必须开启了机器人电话 才能查询该公司下的instance实例
     * @return AjaxResult
     */
    @InnerAuth
    @GetMapping("inner/queryConnectInstances")
    public AjaxResult<List<ConnectInfoVo>> queryAllConnectInstances() {
        return crmAwsConnectService.queryAllConnectInstances();
    }
}
