package com.goclouds.crm.platform.channel.domain.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ReceiveMessage implements Serializable {
    /** 客户联系方式 手机号、邮箱等 */
    private String customerContactInfo;
    /** 客户昵称 */
    private String customerProfile;
    /** 座席联系方式 手机号、邮箱等 */
    private String agentContactInfo;
    /** 消息类型 */
    private String messageType;
    /** 消息内容 */
    private String message;
    /** 图片、视频、音频、附件 后缀类型 为connect操作方便 */
    private String fileSuffix;
    /** 附件 */
    private String fileName;

    /** 用于添加其他额外的信息 */
    private JSONObject extraInfo;
}
