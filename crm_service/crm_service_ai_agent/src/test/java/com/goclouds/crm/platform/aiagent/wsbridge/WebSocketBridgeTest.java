package com.goclouds.crm.platform.aiagent.wsbridge;

import com.goclouds.crm.platform.aiagent.wsbridge.utils.AudioUtils;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * WebSocket桥接功能测试类
 * 
 * <AUTHOR> Agent
 */
public class WebSocketBridgeTest {

    @Test
    public void testAudioUtilsEncodeAndDecode() {
        // 测试音频数据编码和解码
        byte[] originalAudio = new byte[]{1, 2, 3, 4, 5, 6, 7, 8};
        
        // 编码
        String base64Audio = AudioUtils.encodeAudioToBase64(originalAudio);
        assertNotNull("Base64编码不应为null", base64Audio);
        assertFalse("Base64编码不应为空", base64Audio.isEmpty());
        
        // 解码
        byte[] decodedAudio = AudioUtils.decodeBase64ToAudio(base64Audio);
        assertNotNull("解码结果不应为null", decodedAudio);
        assertArrayEquals("解码后的数据应与原始数据相同", originalAudio, decodedAudio);
    }

    @Test
    public void testAudioUtilsValidation() {
        // 测试音频格式验证
        byte[] validAudio = new byte[64]; // 64字节的音频数据
        assertTrue("有效音频数据应通过验证", AudioUtils.validateAudioFormat(validAudio));
        
        byte[] invalidAudio = new byte[16]; // 16字节的音频数据（太短）
        assertFalse("无效音频数据应不通过验证", AudioUtils.validateAudioFormat(invalidAudio));
        
        assertFalse("null音频数据应不通过验证", AudioUtils.validateAudioFormat(null));
        assertFalse("空音频数据应不通过验证", AudioUtils.validateAudioFormat(new byte[0]));
    }

    @Test
    public void testAudioUtilsCalculations() {
        // 测试音频计算功能
        byte[] audioData = new byte[32000]; // 32KB音频数据
        
        // 测试大小计算
        double sizeMB = AudioUtils.calculateAudioSizeMB(audioData);
        assertTrue("音频大小应大于0", sizeMB > 0);
        assertEquals("音频大小计算", 32000.0 / (1024.0 * 1024.0), sizeMB, 0.001);
        
        // 测试时长估算（PCM 16kHz 16bit 单声道）
        double duration = AudioUtils.estimateAudioDuration(audioData);
        assertTrue("音频时长应大于0", duration > 0);
        assertEquals("音频时长估算", 32000.0 / (16000 * 2), duration, 0.001);
    }

    @Test
    public void testAudioUtilsSplitChunks() {
        // 测试音频分块功能
        byte[] audioData = new byte[100];
        for (int i = 0; i < audioData.length; i++) {
            audioData[i] = (byte) i;
        }
        
        byte[][] chunks = AudioUtils.splitAudioIntoChunks(audioData, 30);
        assertNotNull("分块结果不应为null", chunks);
        assertEquals("应该分成4块", 4, chunks.length);
        
        // 验证前三块的大小
        for (int i = 0; i < 3; i++) {
            assertEquals("前三块应该是30字节", 30, chunks[i].length);
        }
        
        // 验证最后一块的大小
        assertEquals("最后一块应该是10字节", 10, chunks[3].length);
        
        // 验证数据完整性
        int index = 0;
        for (byte[] chunk : chunks) {
            for (byte b : chunk) {
                assertEquals("数据应保持完整", (byte) index, b);
                index++;
            }
        }
    }

    @Test
    public void testAudioUtilsEdgeCases() {
        // 测试边界情况
        assertNull("null数据编码应返回null", AudioUtils.encodeAudioToBase64(null));
        assertNull("空数据编码应返回null", AudioUtils.encodeAudioToBase64(new byte[0]));
        
        assertNull("null字符串解码应返回null", AudioUtils.decodeBase64ToAudio(null));
        assertNull("空字符串解码应返回null", AudioUtils.decodeBase64ToAudio(""));
        assertNull("空白字符串解码应返回null", AudioUtils.decodeBase64ToAudio("   "));
        
        assertEquals("null数据大小应为0", 0.0, AudioUtils.calculateAudioSizeMB(null), 0.001);
        assertEquals("null数据时长应为0", 0.0, AudioUtils.estimateAudioDuration(null), 0.001);
        
        byte[][] emptyChunks = AudioUtils.splitAudioIntoChunks(null, 10);
        assertNotNull("分块结果不应为null", emptyChunks);
        assertEquals("空数据分块应返回空数组", 0, emptyChunks.length);
    }
}
