# WebSocket Bridge 配置
wsbridge:
  # WebSocket服务器地址
  server-url: ws://192.168.110.21:8765
  
  # 心跳间隔（毫秒）
  heartbeat-interval: 10000
  
  # 连接超时时间（毫秒）
  connection-timeout: 30000
  
  # 重连最大次数
  max-reconnect-attempts: 3
  
  # 重连间隔（毫秒）
  reconnect-interval: 5000
  
  # 是否启用自动重连
  auto-reconnect: true
  
  # 消息发送超时时间（毫秒）
  send-timeout: 5000
  
  # 音频缓存最大大小（MB）
  max-audio-cache-size: 100
  
  # 音频缓存过期时间（分钟）
  audio-cache-expire-minutes: 30
  
  # 是否启用消息日志
  enable-message-log: false
  
  # TTS配置
  tts:
    # 默认TTS语言
    language: zh-CN
    # 默认TTS声音
    voice-name: zh-CN-XiaoxiaoNeural
  
  # 静默检测配置
  silence:
    # 是否启用静默检测
    enabled: true
    # 静默阈值（秒）
    threshold: 10
    # 静默结果类型
    result-type: 1
    # 连续静默次数阈值
    continuous-count: 3
    # 静默超时动作
    timeout-action: silence

---
# 开发环境配置
spring:
  profiles: dev
  
wsbridge:
  server-url: ws://localhost:8765
  enable-message-log: true
  auto-reconnect: true
  max-reconnect-attempts: 5

---
# 测试环境配置
spring:
  profiles: test
  
wsbridge:
  server-url: ws://test-server:8765
  enable-message-log: true
  auto-reconnect: false
  connection-timeout: 10000

---
# 生产环境配置
spring:
  profiles: prod
  
wsbridge:
  server-url: ws://prod-server:8765
  enable-message-log: false
  auto-reconnect: true
  max-reconnect-attempts: 3
  heartbeat-interval: 30000
