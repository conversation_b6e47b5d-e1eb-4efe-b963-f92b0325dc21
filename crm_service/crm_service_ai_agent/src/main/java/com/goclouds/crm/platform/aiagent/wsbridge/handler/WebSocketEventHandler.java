package com.goclouds.crm.platform.aiagent.wsbridge.handler;

import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;

/**
 * WebSocket事件处理器接口
 * 用于处理各种WebSocket事件回调
 * 
 * <AUTHOR> Agent
 */
public interface WebSocketEventHandler {

    /**
     * 连接建立事件
     *
     * @param clientId 客户端ID
     */
    void onConnected(String clientId);

    /**
     * 收到原始消息事件
     *
     * @param message 原始消息内容
     */
    default void onMessage(String message) {
        // 默认实现为空，子类可以选择性重写
    }

    /**
     * 连接关闭事件
     * 
     * @param clientId 客户端ID
     * @param code 关闭代码
     * @param reason 关闭原因
     */
    void onDisconnected(String clientId, int code, String reason);

    /**
     * 连接错误事件
     * 
     * @param clientId 客户端ID
     * @param exception 异常信息
     */
    void onError(String clientId, Exception exception);

    /**
     * 初始化响应事件
     * 
     * @param message 初始化消息
     */
    void onInitResponse(InitMessage message);

    /**
     * 心跳响应事件
     * 
     * @param message 心跳消息
     */
    void onHeartbeatResponse(HeartbeatMessage message);

    /**
     * 语音识别结果事件
     * 
     * @param message STT结果消息
     */
    void onSTTResult(STTResultMessage message);

    /**
     * AI回复事件
     * 
     * @param message API响应消息
     */
    void onApiResponse(ApiResponseMessage message);

    /**
     * TTS音频事件
     * 
     * @param message TTS音频消息
     */
    void onTTSAudio(TTSMessage message);

    /**
     * TTS打断事件
     * 
     * @param message TTS打断消息
     */
    void onTTSInterrupt(TTSInterruptMessage message);

    /**
     * 静默超时事件
     * 
     * @param message 静默超时消息
     */
    void onSilenceTimeout(SilenceTimeoutMessage message);

    /**
     * 错误消息事件
     * 
     * @param message 错误消息
     */
    void onErrorMessage(ErrorMessage message);

    /**
     * 关闭消息事件
     * 
     * @param message 关闭消息
     */
    void onCloseMessage(CloseMessage message);

    /**
     * 未知消息类型事件
     * 
     * @param clientId 客户端ID
     * @param messageType 消息类型
     * @param rawMessage 原始消息内容
     */
    void onUnknownMessage(String clientId, String messageType, String rawMessage);
}
