package com.goclouds.crm.platform.aiagent.wsbridge.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Base64;

/**
 * 音频处理工具类
 * 用于处理Base64音频数据的编码解码
 * 
 * <AUTHOR> Agent
 */
@Slf4j
public class AudioUtils {

    /**
     * 将音频字节数组编码为Base64字符串
     * 
     * @param audioBytes 音频字节数组
     * @return Base64编码的音频字符串
     */
    public static String encodeAudioToBase64(byte[] audioBytes) {
        if (audioBytes == null || audioBytes.length == 0) {
            log.warn("音频数据为空，无法编码");
            return null;
        }
        
        try {
            return Base64.getEncoder().encodeToString(audioBytes);
        } catch (Exception e) {
            log.error("音频数据Base64编码失败", e);
            return null;
        }
    }

    /**
     * 将Base64字符串解码为音频字节数组
     * 
     * @param base64Audio Base64编码的音频字符串
     * @return 音频字节数组
     */
    public static byte[] decodeBase64ToAudio(String base64Audio) {
        if (base64Audio == null || base64Audio.trim().isEmpty()) {
            log.warn("Base64音频数据为空，无法解码");
            return null;
        }
        
        try {
            return Base64.getDecoder().decode(base64Audio);
        } catch (Exception e) {
            log.error("Base64音频数据解码失败", e);
            return null;
        }
    }

    /**
     * 验证音频数据格式是否符合要求
     * 根据文档要求：PCM 16kHz 16bit 单声道
     * 
     * @param audioBytes 音频字节数组
     * @return 是否符合格式要求
     */
    public static boolean validateAudioFormat(byte[] audioBytes) {
        if (audioBytes == null || audioBytes.length == 0) {
            log.warn("音频数据为空");
            return false;
        }
        
        // 简单的长度检查，实际项目中可能需要更详细的格式验证
        if (audioBytes.length < 32) {
            log.warn("音频数据长度过短，可能不是有效的PCM数据");
            return false;
        }
        
        return true;
    }

    /**
     * 计算音频数据的大小（MB）
     * 
     * @param audioBytes 音频字节数组
     * @return 音频数据大小（MB）
     */
    public static double calculateAudioSizeMB(byte[] audioBytes) {
        if (audioBytes == null) {
            return 0.0;
        }
        return audioBytes.length / (1024.0 * 1024.0);
    }

    /**
     * 估算音频时长（秒）
     * 基于PCM 16kHz 16bit 单声道格式
     * 
     * @param audioBytes 音频字节数组
     * @return 估算的音频时长（秒）
     */
    public static double estimateAudioDuration(byte[] audioBytes) {
        if (audioBytes == null || audioBytes.length == 0) {
            return 0.0;
        }
        
        // PCM 16kHz 16bit 单声道：每秒 16000 * 2 = 32000 字节
        double bytesPerSecond = 16000 * 2;
        return audioBytes.length / bytesPerSecond;
    }

    /**
     * 分割音频数据为指定大小的块
     * 
     * @param audioBytes 音频字节数组
     * @param chunkSize 每块的大小（字节）
     * @return 分割后的音频块数组
     */
    public static byte[][] splitAudioIntoChunks(byte[] audioBytes, int chunkSize) {
        if (audioBytes == null || audioBytes.length == 0 || chunkSize <= 0) {
            return new byte[0][];
        }
        
        int totalChunks = (int) Math.ceil((double) audioBytes.length / chunkSize);
        byte[][] chunks = new byte[totalChunks][];
        
        for (int i = 0; i < totalChunks; i++) {
            int start = i * chunkSize;
            int end = Math.min(start + chunkSize, audioBytes.length);
            int currentChunkSize = end - start;
            
            chunks[i] = new byte[currentChunkSize];
            System.arraycopy(audioBytes, start, chunks[i], 0, currentChunkSize);
        }
        
        return chunks;
    }
}
