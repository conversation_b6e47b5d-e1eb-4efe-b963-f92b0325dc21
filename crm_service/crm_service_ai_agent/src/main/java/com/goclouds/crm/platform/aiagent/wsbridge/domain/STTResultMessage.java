package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 语音识别结果消息
 * 
 * <AUTHOR> Agent
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class STTResultMessage extends BaseMessage {

    /**
     * 是否为最终识别结果
     */
    @JsonProperty("is_final")
    private Boolean isFinal;

    /**
     * 识别出的文本内容
     */
    @JsonProperty("text")
    private String text;

    /**
     * 时间戳
     */
    @JsonProperty("timestamp")
    private Double timestamp;

    /**
     * 新消息ID（仅在发生打断时包含）
     */
    @JsonProperty("new_msg_id")
    private String newMsgId;

    public STTResultMessage() {
        super("stt_result", null);
    }
}
