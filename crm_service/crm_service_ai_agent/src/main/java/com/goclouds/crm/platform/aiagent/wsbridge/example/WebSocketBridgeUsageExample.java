package com.goclouds.crm.platform.aiagent.wsbridge.example;

import com.goclouds.crm.platform.aiagent.domain.AiAgentInitRequest;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.DefaultWebSocketEventHandler;
import com.goclouds.crm.platform.aiagent.wsbridge.manager.WebSocketBridgeManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * WebSocket桥接功能使用示例
 * 展示如何在其他服务中调用WebSocket桥接功能
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@Component
public class WebSocketBridgeUsageExample {

    @Autowired
    private WebSocketBridgeManager webSocketBridgeManager;

    /**
     * 示例：创建WebSocket连接并处理语音识别
     */
    public void exampleCreateConnectionAndHandleSTT() {
        // 1. 构建初始化请求
        AiAgentInitRequest request = new AiAgentInitRequest();
        request.setCompanyId("company_001");
        request.setChannelId("channel_001");
        request.setSourceChannelId("source_001");

        // 2. 创建自定义事件处理器
        CustomEventHandler eventHandler = new CustomEventHandler();

        // 3. 创建并初始化连接
        webSocketBridgeManager.createAndInitializeConnection(request, eventHandler)
                .thenAccept(success -> {
                    if (success) {
                        log.info("WebSocket连接创建成功，可以开始发送音频数据");
                        
                        // 4. 模拟发送音频数据
                        simulateSendingAudioData(request);
                    } else {
                        log.error("WebSocket连接创建失败");
                    }
                })
                .exceptionally(throwable -> {
                    log.error("创建WebSocket连接时发生异常", throwable);
                    return null;
                });
    }

    /**
     * 示例：模拟发送音频数据
     */
    private void simulateSendingAudioData(AiAgentInitRequest request) {
        // 模拟音频数据（实际使用时应该是真实的音频字节数组）
        byte[] mockAudioData = generateMockAudioData();
        
        boolean sendSuccess = webSocketBridgeManager.sendAudioData(request, mockAudioData);
        if (sendSuccess) {
            log.info("音频数据发送成功");
        } else {
            log.error("音频数据发送失败");
        }
    }

    /**
     * 生成模拟音频数据
     */
    private byte[] generateMockAudioData() {
        // 生成模拟的PCM音频数据（16kHz, 16bit, 单声道）
        // 实际使用时应该是真实的音频数据
        byte[] mockData = new byte[1024]; // 1KB的模拟数据
        for (int i = 0; i < mockData.length; i++) {
            mockData[i] = (byte) (Math.sin(i * 0.1) * 127);
        }
        return mockData;
    }

    /**
     * 自定义事件处理器
     */
    private static class CustomEventHandler extends DefaultWebSocketEventHandler {

        @Override
        public void onConnected(String clientId) {
            super.onConnected(clientId);
            log.info("自定义处理：WebSocket连接已建立，客户端ID: {}", clientId);
        }

        @Override
        public void onInitResponse(InitMessage message) {
            super.onInitResponse(message);
            if ("success".equals(message.getStatus())) {
                log.info("自定义处理：WebSocket初始化成功，可以开始发送音频数据");
            } else {
                log.error("自定义处理：WebSocket初始化失败: {}", message.getMessage());
            }
        }

        @Override
        public void onSTTResult(STTResultMessage message) {
            super.onSTTResult(message);
            log.info("自定义处理：收到语音识别结果 - 文本: {}, 是否最终: {}", 
                    message.getText(), message.getIsFinal());
            
            // 在这里可以处理语音识别结果，比如：
            // 1. 保存识别结果到数据库
            // 2. 触发后续的AI处理流程
            // 3. 更新用户界面显示
            if (Boolean.TRUE.equals(message.getIsFinal())) {
                handleFinalSTTResult(message.getText());
            }
        }

        @Override
        public void onApiResponse(ApiResponseMessage message) {
            super.onApiResponse(message);
            log.info("自定义处理：收到AI回复 - 文本: {}, 是否最终: {}", 
                    message.getText(), message.getIsFinal());
            
            // 在这里可以处理AI回复，比如：
            // 1. 显示AI回复给用户
            // 2. 保存对话记录
            // 3. 触发TTS播放
            if (Boolean.TRUE.equals(message.getIsFinal())) {
                handleFinalApiResponse(message.getText());
            }
        }

        @Override
        public void onTTSAudio(TTSMessage message) {
            super.onTTSAudio(message);
            log.info("自定义处理：收到TTS音频数据 - 序号: {}, 是否最终: {}", 
                    message.getSequence(), message.getIsFinal());
            
            // 在这里可以处理TTS音频数据，比如：
            // 1. 播放音频
            // 2. 保存音频文件
            // 3. 发送给客户端播放
            handleTTSAudio(message);
        }

        @Override
        public void onTTSInterrupt(TTSInterruptMessage message) {
            super.onTTSInterrupt(message);
            log.info("自定义处理：TTS被打断 - 原因: {}", message.getReason());
            
            // 在这里可以处理TTS打断事件，比如：
            // 1. 停止当前音频播放
            // 2. 清理音频缓存
            // 3. 准备处理新的语音输入
            handleTTSInterrupt(message);
        }

        @Override
        public void onSilenceTimeout(SilenceTimeoutMessage message) {
            super.onSilenceTimeout(message);
            log.info("自定义处理：检测到静默超时 - 时长: {}秒", message.getTimeoutDuration());
            
            // 在这里可以处理静默超时事件，比如：
            // 1. 播放提示音
            // 2. 发送提醒消息
            // 3. 执行预设的静默处理逻辑
            handleSilenceTimeout(message);
        }

        @Override
        public void onErrorMessage(ErrorMessage message) {
            super.onErrorMessage(message);
            log.error("自定义处理：收到错误消息 - 类型: {}, 内容: {}", 
                    message.getErrorType(), message.getMessage());
            
            // 在这里可以处理错误消息，比如：
            // 1. 记录错误日志
            // 2. 通知用户
            // 3. 执行错误恢复逻辑
            handleError(message);
        }

        /**
         * 处理最终的语音识别结果
         */
        private void handleFinalSTTResult(String text) {
            log.info("处理最终语音识别结果: {}", text);
            // 实现具体的业务逻辑
        }

        /**
         * 处理最终的AI回复
         */
        private void handleFinalApiResponse(String text) {
            log.info("处理最终AI回复: {}", text);
            // 实现具体的业务逻辑
        }

        /**
         * 处理TTS音频数据
         */
        private void handleTTSAudio(TTSMessage message) {
            log.info("处理TTS音频数据，序号: {}", message.getSequence());
            // 实现音频播放逻辑
        }

        /**
         * 处理TTS打断事件
         */
        private void handleTTSInterrupt(TTSInterruptMessage message) {
            log.info("处理TTS打断事件，原因: {}", message.getReason());
            // 实现打断处理逻辑
        }

        /**
         * 处理静默超时事件
         */
        private void handleSilenceTimeout(SilenceTimeoutMessage message) {
            log.info("处理静默超时事件，时长: {}秒", message.getTimeoutDuration());
            // 实现静默处理逻辑
        }

        /**
         * 处理错误事件
         */
        private void handleError(ErrorMessage message) {
            log.error("处理错误事件，类型: {}, 消息: {}", message.getErrorType(), message.getMessage());
            // 实现错误处理逻辑
        }
    }
}
