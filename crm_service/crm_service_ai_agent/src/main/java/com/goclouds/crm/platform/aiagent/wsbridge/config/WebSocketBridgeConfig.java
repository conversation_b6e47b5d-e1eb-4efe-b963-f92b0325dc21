package com.goclouds.crm.platform.aiagent.wsbridge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * WebSocket桥接配置类
 * 用于读取application-wsbridge.yml中的配置参数
 * 
 * <AUTHOR> Agent
 */
@Data
@Component
@ConfigurationProperties(prefix = "wsbridge")
public class WebSocketBridgeConfig {

    /**
     * WebSocket服务器地址
     */
    private String serverUrl = "ws://192.168.110.21:8765";

    /**
     * 心跳间隔（毫秒）
     */
    private long heartbeatInterval = 10000;

    /**
     * 连接超时时间（毫秒）
     */
    private long connectionTimeout = 30000;

    /**
     * 重连最大次数
     */
    private int maxReconnectAttempts = 3;

    /**
     * 重连间隔（毫秒）
     */
    private long reconnectInterval = 5000;

    /**
     * 是否启用自动重连
     */
    private boolean autoReconnect = true;

    /**
     * 消息发送超时时间（毫秒）
     */
    private long sendTimeout = 5000;

    /**
     * 音频缓存最大大小（MB）
     */
    private int maxAudioCacheSize = 100;

    /**
     * 音频缓存过期时间（分钟）
     */
    private int audioCacheExpireMinutes = 30;

    /**
     * 是否启用消息日志
     */
    private boolean enableMessageLog = false;

    /**
     * TTS配置
     */
    private TtsConfig tts = new TtsConfig();

    /**
     * 静默检测配置
     */
    private SilenceConfig silence = new SilenceConfig();

    @Data
    public static class TtsConfig {
        /**
         * 默认TTS语言
         */
        private String language = "zh-CN";

        /**
         * 默认TTS声音
         */
        private String voiceName = "zh-CN-XiaoxiaoNeural";
    }

    @Data
    public static class SilenceConfig {
        /**
         * 是否启用静默检测
         */
        private boolean enabled = true;

        /**
         * 静默阈值（秒）
         */
        private int threshold = 10;

        /**
         * 静默结果类型
         */
        private int resultType = 1;

        /**
         * 连续静默次数阈值
         */
        private int continuousCount = 3;

        /**
         * 静默超时动作
         */
        private String timeoutAction = "silence";
    }
}
