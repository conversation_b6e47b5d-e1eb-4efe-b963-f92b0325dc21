# WebSocket桥接功能使用说明

## 概述

WebSocket桥接功能是一个中间层服务，用于连接外部WebSocket服务器，处理语音识别(STT)、文本转语音(TTS)、AI回复等各种事件消息。本功能为其他模块提供了简单易用的API接口。

## 功能特性

- ✅ WebSocket连接管理（连接、断开、重连）
- ✅ 心跳检测机制
- ✅ 自动重连机制
- ✅ 消息类型处理（init、STT、TTS、API响应等）
- ✅ 音频数据处理（Base64编码/解码）
- ✅ 事件回调处理
- ✅ 连接状态监控
- ✅ 多客户端连接管理

## 项目结构

```
wsbridge/
├── config/                 # 配置类
│   └── WebSocketBridgeConfig.java
├── domain/                 # 消息实体类
│   ├── BaseMessage.java
│   ├── InitMessage.java
│   ├── HeartbeatMessage.java
│   ├── STTMessage.java
│   ├── TTSMessage.java
│   └── ...
├── client/                 # WebSocket客户端
│   └── WebSocketBridgeClient.java
├── handler/                # 事件处理器
│   ├── WebSocketEventHandler.java
│   └── DefaultWebSocketEventHandler.java
├── service/                # 服务层
│   ├── WebSocketBridgeService.java
│   └── impl/
│       └── WebSocketBridgeServiceImpl.java
├── manager/                # 管理器
│   └── WebSocketBridgeManager.java
├── controller/             # 控制器
│   └── WebSocketBridgeController.java
├── utils/                  # 工具类
│   └── AudioUtils.java
└── example/                # 使用示例
    └── WebSocketBridgeUsageExample.java
```

## 配置说明

在 `application-wsbridge.yml` 中配置WebSocket服务器信息：

```yaml
wsbridge:
  server-url: ws://192.168.110.21:8765
  heartbeat-interval: 10000
  connection-timeout: 30000
  max-reconnect-attempts: 3
  reconnect-interval: 5000
  auto-reconnect: true
  send-timeout: 5000
  enable-message-log: false
  
  tts:
    language: zh-CN
    voice-name: zh-CN-XiaoxiaoNeural
    
  silence:
    enabled: true
    threshold: 10
    result-type: 1
    continuous-count: 3
    timeout-action: silence
```

## 使用方式

### 1. 基本使用（通过Manager）

```java
@Autowired
private WebSocketBridgeManager webSocketBridgeManager;

// 创建并初始化连接
AiAgentInitRequest request = new AiAgentInitRequest();
request.setCompanyId("company_001");
request.setChannelId("channel_001");
request.setSourceChannelId("source_001");

webSocketBridgeManager.createAndInitializeConnection(request)
    .thenAccept(success -> {
        if (success) {
            // 连接成功，可以发送音频数据
            byte[] audioData = getAudioData();
            webSocketBridgeManager.sendAudioData(request, audioData);
        }
    });
```

### 2. 自定义事件处理

```java
// 创建自定义事件处理器
WebSocketEventHandler customHandler = new DefaultWebSocketEventHandler() {
    @Override
    public void onSTTResult(STTResultMessage message) {
        // 处理语音识别结果
        System.out.println("识别结果: " + message.getText());
    }
    
    @Override
    public void onApiResponse(ApiResponseMessage message) {
        // 处理AI回复
        System.out.println("AI回复: " + message.getText());
    }
    
    @Override
    public void onTTSAudio(TTSMessage message) {
        // 处理TTS音频数据
        byte[] audioBytes = AudioUtils.decodeBase64ToAudio(message.getAudioData());
        // 播放音频...
    }
};

// 使用自定义处理器创建连接
webSocketBridgeManager.createAndInitializeConnection(request, customHandler);
```

### 3. 直接使用Service

```java
@Autowired
private WebSocketBridgeService webSocketBridgeService;

// 创建连接
String clientId = "client_123";
webSocketBridgeService.createConnection(clientId)
    .thenAccept(success -> {
        if (success) {
            // 初始化连接
            Map<String, String> settingMap = new HashMap<>();
            settingMap.put("silence_detection_enabled", "1");
            
            List<InitMessage.PhraseInfo> phraseList = new ArrayList<>();
            // 添加话术...
            
            webSocketBridgeService.initializeConnection(
                clientId, "company_001", "agent_001",
                settingMap, phraseList,
                "zh-CN", "zh-CN-XiaoxiaoNeural", "zh-CN",
                "s3://bucket/transcript.json", "s3://bucket/audio.wav"
            );
        }
    });
```

### 4. REST API使用

```bash
# 创建连接
curl -X POST http://localhost:8080/api/websocket-bridge/connect \
  -H "Content-Type: application/json" \
  -d '{
    "companyId": "company_001",
    "channelId": "channel_001",
    "sourceChannelId": "source_001"
  }'

# 发送音频文件
curl -X POST http://localhost:8080/api/websocket-bridge/send-audio \
  -F "companyId=company_001" \
  -F "channelId=channel_001" \
  -F "sourceChannelId=source_001" \
  -F "file=@audio.wav"

# 检查连接状态
curl -X POST http://localhost:8080/api/websocket-bridge/status \
  -H "Content-Type: application/json" \
  -d '{
    "companyId": "company_001",
    "channelId": "channel_001",
    "sourceChannelId": "source_001"
  }'

# 关闭连接
curl -X POST http://localhost:8080/api/websocket-bridge/disconnect \
  -H "Content-Type: application/json" \
  -d '{
    "companyId": "company_001",
    "channelId": "channel_001",
    "sourceChannelId": "source_001"
  }'
```

## 消息类型说明

### 发送消息类型
- `init`: 初始化连接
- `heartbeat`: 心跳检测
- `stt_audio`: 发送音频数据进行语音识别
- `close`: 关闭连接

### 接收消息类型
- `init`: 初始化响应
- `heartbeat`: 心跳响应
- `stt_result`: 语音识别结果
- `api_response`: AI回复
- `tts_audio`: TTS音频数据
- `tts_interrupt`: TTS打断信号
- `silence_timeout`: 静默超时通知
- `error`: 错误消息
- `close`: 关闭响应

## 音频格式要求

- **编码**: PCM
- **采样率**: 16kHz
- **位深**: 16bit
- **声道**: 单声道 (Mono)
- **传输格式**: Base64编码

## 注意事项

1. **连接管理**: 每个客户端ID对应一个WebSocket连接，重复创建会覆盖旧连接
2. **音频数据**: 发送的音频数据必须符合格式要求，否则可能识别失败
3. **事件处理**: 建议实现自定义事件处理器来处理业务逻辑
4. **资源释放**: 应用关闭时会自动释放所有连接，也可手动关闭
5. **错误处理**: 连接断开时会自动尝试重连（如果启用）
6. **日志记录**: 可通过配置启用详细的消息日志记录

## 依赖说明

项目使用了以下主要依赖：
- `org.java-websocket:Java-WebSocket:1.5.3` - WebSocket客户端
- `com.alibaba:fastjson` - JSON处理
- `org.springframework.boot` - Spring Boot框架
- `lombok` - 简化代码

## 故障排除

1. **连接失败**: 检查WebSocket服务器地址和端口是否正确
2. **初始化失败**: 检查必需参数是否完整
3. **音频发送失败**: 检查音频格式和连接状态
4. **重连失败**: 检查网络连接和重连配置
5. **消息处理异常**: 检查事件处理器实现和日志输出
