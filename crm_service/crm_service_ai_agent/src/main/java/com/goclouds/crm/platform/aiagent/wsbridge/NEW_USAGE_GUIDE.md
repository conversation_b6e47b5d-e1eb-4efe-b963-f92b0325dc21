# WebSocket桥接服务使用指南

## 📋 概述

根据您的需求重新设计的WebSocket桥接服务，完全符合您描述的逻辑流程：

1. **动态WebSocket地址** - 支持传入不同的WebSocket连接地址
2. **灵活业务参数** - 支持传入任意业务逻辑参数
3. **中间层处理** - 收到init事件后自动获取配置并响应
4. **事件回调机制** - 所有WebSocket事件都通过回调返回给业务层

## 🏗️ 核心设计理念

### 三层架构
```
业务层 (Your Code) 
    ↓ 调用方法 + 传入参数和回调
中间层 (WebSocketBridgeService)
    ↓ 处理WebSocket事件和消息
WebSocket客户端层 (底层实现)
```

### 关键流程
1. **业务层调用** → 传入WebSocket地址、业务参数、事件回调
2. **中间层建立连接** → 创建WebSocket连接
3. **服务器发送init** → 外部WebSocket服务器主动发送init事件
4. **中间层响应init** → 获取配置信息，组装数据，发送init响应
5. **回调业务层** → 连接完全建立后回调`onConnected`
6. **事件处理** → 所有后续事件都经过中间层处理后回调业务层

## 🚀 使用方法

### 1. 基本使用

```java
@Autowired
private WebSocketBridgeService webSocketBridgeService;

public void yourBusinessMethod() {
    // 1. 准备WebSocket连接地址（动态传入）
    String webSocketUrl = "ws://192.168.110.21:8765";
    
    // 2. 准备业务逻辑参数值
    Map<String, Object> businessParams = new HashMap<>();
    businessParams.put("companyId", "company_001");
    businessParams.put("channelId", "channel_001");
    businessParams.put("agentId", "agent_001");
    businessParams.put("language", "zh-CN");
    businessParams.put("voiceName", "zh-CN-XiaoxiaoNeural");
    
    // 3. 定义事件回调
    WebSocketBridgeService.WebSocketEventCallbacks callbacks = createCallbacks();
    
    // 4. 创建连接请求
    WebSocketBridgeService.WebSocketConnectionRequest request = 
        new WebSocketBridgeService.WebSocketConnectionRequest();
    request.setWebSocketUrl(webSocketUrl);
    request.setBusinessParams(businessParams);
    request.setCallbacks(callbacks);
    
    // 5. 调用创建WebSocket连接
    webSocketBridgeService.createWebSocketConnection(request)
        .thenAccept(clientId -> {
            // 连接创建成功
            log.info("连接成功，客户端ID: {}", clientId);
            startYourBusinessLogic(clientId);
        })
        .exceptionally(throwable -> {
            // 连接创建失败
            log.error("连接失败", throwable);
            return null;
        });
}
```

### 2. 事件回调定义

```java
private WebSocketBridgeService.WebSocketEventCallbacks createCallbacks() {
    WebSocketBridgeService.WebSocketEventCallbacks callbacks = 
        new WebSocketBridgeService.WebSocketEventCallbacks();
    
    // 连接完全建立回调（重要：在init流程完成后才触发）
    callbacks.setOnConnected(clientId -> {
        log.info("✅ WebSocket连接完全建立，可以开始发送音频");
        // 在这里开始您的业务逻辑
        onConnectionReady(clientId);
    });
    
    // 语音识别结果回调
    callbacks.setOnSTTResult(message -> {
        log.info("🎤 语音识别: {}", message.getText());
        if (Boolean.TRUE.equals(message.getIsFinal())) {
            // 最终识别结果
            handleUserSpeech(message.getText());
        }
    });
    
    // AI回复回调
    callbacks.setOnApiResponse(message -> {
        log.info("🤖 AI回复: {}", message.getText());
        if (Boolean.TRUE.equals(message.getIsFinal())) {
            // 最终AI回复
            handleAIResponse(message.getText());
        }
    });
    
    // TTS音频回调
    callbacks.setOnTTSAudio(message -> {
        log.info("🔊 TTS音频，序号: {}", message.getSequence());
        // 播放TTS音频
        playTTSAudio(message);
    });
    
    // TTS打断回调
    callbacks.setOnTTSInterrupt(message -> {
        log.info("⏹️ TTS被打断: {}", message.getReason());
        // 停止当前音频播放
        stopCurrentAudio();
    });
    
    // 其他事件回调...
    
    return callbacks;
}
```

### 3. 发送音频数据

```java
// 在连接建立后发送音频数据
private void sendAudioToWebSocket(String clientId, byte[] audioData) {
    boolean success = webSocketBridgeService.sendAudioData(clientId, audioData);
    if (success) {
        log.info("✅ 音频发送成功");
    } else {
        log.error("❌ 音频发送失败");
    }
}
```

### 4. 关闭连接

```java
// 关闭WebSocket连接
private void closeWebSocketConnection(String clientId) {
    webSocketBridgeService.closeConnection(clientId);
    log.info("🛑 WebSocket连接已关闭");
}
```

## 🔄 完整的流程说明

### 阶段1：连接建立
1. **业务层调用** - 您在某个功能方法中调用`createWebSocketConnection()`
2. **传入参数** - WebSocket地址 + 业务参数 + 回调函数
3. **建立连接** - 中间层创建WebSocket连接到外部服务器

### 阶段2：初始化流程（关键）
4. **服务器发送init** - 外部WebSocket服务器主动发送init事件
5. **中间层收到init** - 中间层收到init事件
6. **获取配置信息** - 中间层根据业务参数获取配置信息
7. **组装响应数据** - 中间层组装init响应消息
8. **发送init响应** - 中间层发送init响应给WebSocket服务器
9. **回调业务层** - init流程完成后，回调业务层的`onConnected`

### 阶段3：正常通话流程
10. **发送音频** - 业务层发送音频数据
11. **接收事件** - 中间层接收各种WebSocket事件
12. **中间层处理** - 中间层对事件进行处理（过滤、格式化等）
13. **回调业务层** - 处理后的事件回调给业务层

## 🎯 关键特性

### 1. 动态WebSocket地址
- 支持每次调用时传入不同的WebSocket服务器地址
- 不再依赖配置文件中的固定地址

### 2. 灵活的业务参数
- 支持传入任意的业务逻辑参数
- 中间层会根据这些参数获取相应的配置信息

### 3. 智能的init处理
- 自动处理服务器发送的init事件
- 自动获取配置信息并发送响应
- 只有在init流程完成后才回调`onConnected`

### 4. 中间层事件处理
- 所有WebSocket事件都经过中间层处理
- 支持在中间层进行数据过滤、格式化、验证等
- 处理后的事件才回调给业务层

### 5. 完整的事件回调
- 支持所有类型的WebSocket事件回调
- 区分中间结果和最终结果
- 提供详细的错误处理回调

## 🔧 中间层处理能力

中间层可以对收到的消息进行以下处理：

1. **语音识别结果** - 文本过滤、格式化、敏感词检测
2. **AI回复** - 内容审核、格式化、长度限制
3. **TTS音频** - 音频格式转换、音量调节、质量优化
4. **错误处理** - 错误分类、自动重试、降级处理

## 📝 最佳实践

1. **参数设计** - 将所有必要的业务参数放入`businessParams`中
2. **回调处理** - 在回调中实现具体的业务逻辑
3. **错误处理** - 充分利用错误回调进行异常处理
4. **资源管理** - 及时关闭不需要的连接
5. **日志记录** - 记录关键事件便于调试

## 🚨 注意事项

1. **onConnected时机** - 只有在init流程完全完成后才会触发
2. **音频格式** - 确保音频数据符合要求（PCM 16kHz 16bit 单声道）
3. **线程安全** - 回调方法可能在不同线程中执行
4. **异常处理** - 充分处理各种异常情况
5. **连接管理** - 避免创建过多的并发连接

这个新设计完全符合您描述的逻辑流程，提供了更灵活、更强大的WebSocket桥接功能！
