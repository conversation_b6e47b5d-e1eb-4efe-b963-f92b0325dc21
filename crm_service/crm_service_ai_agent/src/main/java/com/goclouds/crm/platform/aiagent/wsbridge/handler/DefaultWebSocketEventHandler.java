package com.goclouds.crm.platform.aiagent.wsbridge.handler;

import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 默认WebSocket事件处理器实现
 * 提供基础的日志记录功能，可以被继承以实现具体的业务逻辑
 * 
 * <AUTHOR> Agent
 */
@Slf4j
public class DefaultWebSocketEventHandler implements WebSocketEventHandler {

    @Override
    public void onConnected(String clientId) {
        log.info("WebSocket连接已建立，客户端ID: {}", clientId);
    }

    @Override
    public void onDisconnected(String clientId, int code, String reason) {
        log.info("WebSocket连接已关闭，客户端ID: {}, 代码: {}, 原因: {}", clientId, code, reason);
    }

    @Override
    public void onError(String clientId, Exception exception) {
        log.error("WebSocket连接发生错误，客户端ID: {}", clientId, exception);
    }

    @Override
    public void onInitResponse(InitMessage message) {
        log.info("收到初始化响应，客户端ID: {}, 状态: {}, 消息: {}", 
                message.getClientId(), message.getStatus(), message.getMessage());
    }

    @Override
    public void onHeartbeatResponse(HeartbeatMessage message) {
        log.debug("收到心跳响应，客户端ID: {}, 状态: {}", 
                message.getClientId(), message.getStatus());
    }

    @Override
    public void onSTTResult(STTResultMessage message) {
        log.info("收到语音识别结果，客户端ID: {}, 是否最终结果: {}, 文本: {}", 
                message.getClientId(), message.getIsFinal(), message.getText());
    }

    @Override
    public void onApiResponse(ApiResponseMessage message) {
        log.info("收到AI回复，客户端ID: {}, 消息ID: {}, 是否最终: {}, 文本: {}", 
                message.getClientId(), message.getMsgId(), message.getIsFinal(), message.getText());
    }

    @Override
    public void onTTSAudio(TTSMessage message) {
        log.info("收到TTS音频，客户端ID: {}, 消息ID: {}, 序号: {}, 是否最终: {}", 
                message.getClientId(), message.getMsgId(), message.getSequence(), message.getIsFinal());
    }

    @Override
    public void onTTSInterrupt(TTSInterruptMessage message) {
        log.info("收到TTS打断信号，客户端ID: {}, 被打断消息ID: {}, 新消息ID: {}, 原因: {}", 
                message.getClientId(), message.getInterruptedMsgId(), message.getNewMsgId(), message.getReason());
    }

    @Override
    public void onSilenceTimeout(SilenceTimeoutMessage message) {
        log.info("收到静默超时通知，客户端ID: {}, 超时时长: {}秒, 动作: {}", 
                message.getClientId(), message.getTimeoutDuration(), message.getAction());
    }

    @Override
    public void onErrorMessage(ErrorMessage message) {
        log.error("收到错误消息，客户端ID: {}, 错误类型: {}, 消息: {}", 
                message.getClientId(), message.getErrorType(), message.getMessage());
    }

    @Override
    public void onCloseMessage(CloseMessage message) {
        log.info("收到关闭消息，客户端ID: {}, 原因: {}", 
                message.getClientId(), message.getReason());
    }

    @Override
    public void onUnknownMessage(String clientId, String messageType, String rawMessage) {
        log.warn("收到未知消息类型，客户端ID: {}, 消息类型: {}, 原始消息: {}", 
                clientId, messageType, rawMessage);
    }
}
