package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TTS打断消息
 * 
 * <AUTHOR> Agent
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TTSInterruptMessage extends BaseMessage {

    /**
     * 被打断的消息ID
     */
    @JsonProperty("interrupted_msg_id")
    private String interruptedMsgId;

    /**
     * 新的消息ID
     */
    @JsonProperty("new_msg_id")
    private String newMsgId;

    /**
     * 打断原因
     */
    @JsonProperty("reason")
    private String reason;

    public TTSInterruptMessage() {
        super("tts_interrupt", null);
    }
}
