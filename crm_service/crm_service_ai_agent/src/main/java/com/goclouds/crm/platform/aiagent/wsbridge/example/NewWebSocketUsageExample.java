package com.goclouds.crm.platform.aiagent.wsbridge.example;

import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import com.goclouds.crm.platform.aiagent.wsbridge.facade.WebSocketBridgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 新版WebSocket桥接功能使用示例
 * 按照用户描述的逻辑流程设计
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@Component
public class NewWebSocketUsageExample {

    @Autowired
    private WebSocketBridgeService webSocketBridgeService;

    /**
     * 示例：在某个功能方法中调用创建WebSocket连接
     * 这就是您描述的使用场景
     */
    public void someBusinessFunction() {
        log.info("🚀 开始执行某个业务功能，需要创建WebSocket连接");
        
        // 1. 准备WebSocket连接地址（动态传入）
        String webSocketUrl = "ws://192.168.110.21:8765";
        
        // 2. 准备业务逻辑参数值
        Map<String, Object> businessParams = new HashMap<>();
        businessParams.put("companyId", "company_001");
        businessParams.put("channelId", "channel_001");
        businessParams.put("agentId", "agent_001");
        businessParams.put("language", "zh-CN");
        businessParams.put("voiceName", "zh-CN-XiaoxiaoNeural");
        businessParams.put("silenceDetectionEnabled", true);
        businessParams.put("silenceThreshold", 10);
        
        // 3. 定义事件回调
        WebSocketBridgeService.WebSocketEventCallbacks callbacks = createEventCallbacks();
        
        // 4. 创建连接请求
        WebSocketBridgeService.WebSocketConnectionRequest request = new WebSocketBridgeService.WebSocketConnectionRequest();
        request.setWebSocketUrl(webSocketUrl);
        request.setBusinessParams(businessParams);
        request.setCallbacks(callbacks);
        
        // 5. 调用创建WebSocket连接的方法
        webSocketBridgeService.createWebSocketConnection(request)
            .thenAccept(clientId -> {
                log.info("🎉 WebSocket连接创建成功，客户端ID: {}", clientId);
                
                // 连接创建成功后，可以开始业务流程
                startBusinessProcess(clientId);
                
            })
            .exceptionally(throwable -> {
                log.error("💥 WebSocket连接创建失败", throwable);
                handleConnectionFailure(throwable);
                return null;
            });
    }

    /**
     * 创建事件回调
     * 这里定义了收到各种WebSocket事件时的处理逻辑
     */
    private WebSocketBridgeService.WebSocketEventCallbacks createEventCallbacks() {
        WebSocketBridgeService.WebSocketEventCallbacks callbacks = new WebSocketBridgeService.WebSocketEventCallbacks();
        
        // 连接建立并初始化完成回调
        // 注意：这个回调在中间层收到服务器init事件并成功响应后才会触发
        callbacks.setOnConnected(clientId -> {
            log.info("✅ [业务层] WebSocket连接已完全建立并初始化完成，客户端ID: {}", clientId);
            
            // 在这里可以做连接建立后的业务处理
            onWebSocketFullyConnected(clientId);
        });
        
        // 语音识别结果回调
        callbacks.setOnSTTResult(message -> {
            log.info("🎤 [业务层] 收到语音识别结果: {}, 最终: {}", message.getText(), message.getIsFinal());
            
            if (Boolean.TRUE.equals(message.getIsFinal())) {
                // 处理最终识别结果
                handleFinalSTTResult(message.getText());
            } else {
                // 处理中间识别结果
                handleIntermediateSTTResult(message.getText());
            }
        });
        
        // AI回复回调
        callbacks.setOnApiResponse(message -> {
            log.info("🤖 [业务层] 收到AI回复: {}, 最终: {}", message.getText(), message.getIsFinal());
            
            if (Boolean.TRUE.equals(message.getIsFinal())) {
                // 处理最终AI回复
                handleFinalAIResponse(message.getText());
            } else {
                // 处理中间AI回复
                handleIntermediateAIResponse(message.getText());
            }
        });
        
        // TTS音频回调
        callbacks.setOnTTSAudio(message -> {
            log.info("🔊 [业务层] 收到TTS音频，序号: {}, 最终: {}", message.getSequence(), message.getIsFinal());
            
            // 处理TTS音频数据
            handleTTSAudio(message);
        });
        
        // TTS打断回调
        callbacks.setOnTTSInterrupt(message -> {
            log.info("⏹️ [业务层] TTS被打断，原因: {}", message.getReason());
            
            // 处理TTS打断事件
            handleTTSInterrupt(message);
        });
        
        // 静默超时回调
        callbacks.setOnSilenceTimeout(message -> {
            log.info("🔇 [业务层] 检测到静默超时，时长: {}秒", message.getTimeoutDuration());
            
            // 处理静默超时事件
            handleSilenceTimeout(message);
        });
        
        // 错误消息回调
        callbacks.setOnErrorMessage(message -> {
            log.error("❌ [业务层] 收到错误消息，类型: {}, 内容: {}", message.getErrorType(), message.getMessage());
            
            // 处理错误消息
            handleErrorMessage(message);
        });
        
        // 连接断开回调
        callbacks.setOnDisconnected(clientId -> {
            log.warn("❌ [业务层] WebSocket连接断开，客户端ID: {}", clientId);
            
            // 处理连接断开事件
            handleConnectionDisconnected(clientId);
        });
        
        // 错误回调
        callbacks.setOnError(error -> {
            log.error("💥 [业务层] WebSocket发生错误", error);
            
            // 处理错误
            handleWebSocketError(error);
        });
        
        return callbacks;
    }

    /**
     * 开始业务流程
     */
    private void startBusinessProcess(String clientId) {
        log.info("🎯 开始业务流程，客户端ID: {}", clientId);
        
        // 这里可以开始您的具体业务逻辑
        // 比如开始录音、准备接收用户语音等
    }

    /**
     * WebSocket完全连接后的处理
     */
    private void onWebSocketFullyConnected(String clientId) {
        log.info("🔗 WebSocket完全连接，可以开始发送音频数据");
        
        // 可以在这里启动音频采集、开始录音等
        startAudioCapture(clientId);
    }

    /**
     * 启动音频采集
     */
    private void startAudioCapture(String clientId) {
        log.info("🎤 启动音频采集，客户端ID: {}", clientId);
        
        // 模拟发送音频数据
        new Thread(() -> {
            try {
                Thread.sleep(2000); // 模拟延迟
                
                // 模拟音频数据
                byte[] mockAudioData = generateMockAudioData();
                
                // 发送音频数据
                boolean success = webSocketBridgeService.sendAudioData(clientId, mockAudioData);
                if (success) {
                    log.info("✅ 音频数据发送成功");
                } else {
                    log.error("❌ 音频数据发送失败");
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    // ========== 事件处理方法 ==========

    private void handleFinalSTTResult(String text) {
        log.info("📝 处理最终语音识别结果: {}", text);
        // 实现您的业务逻辑，比如保存到数据库、触发下一步处理等
    }

    private void handleIntermediateSTTResult(String text) {
        log.info("📝 处理中间语音识别结果: {}", text);
        // 实现您的业务逻辑，比如实时显示给用户等
    }

    private void handleFinalAIResponse(String text) {
        log.info("🤖 处理最终AI回复: {}", text);
        // 实现您的业务逻辑，比如显示给用户、保存对话记录等
    }

    private void handleIntermediateAIResponse(String text) {
        log.info("🤖 处理中间AI回复: {}", text);
        // 实现您的业务逻辑，比如实时显示AI思考过程等
    }

    private void handleTTSAudio(TTSMessage message) {
        log.info("🔊 处理TTS音频，序号: {}", message.getSequence());
        // 实现您的业务逻辑，比如播放音频等
    }

    private void handleTTSInterrupt(TTSInterruptMessage message) {
        log.info("⏹️ 处理TTS打断，原因: {}", message.getReason());
        // 实现您的业务逻辑，比如停止音频播放等
    }

    private void handleSilenceTimeout(SilenceTimeoutMessage message) {
        log.info("🔇 处理静默超时，时长: {}秒", message.getTimeoutDuration());
        // 实现您的业务逻辑，比如播放提示音等
    }

    private void handleErrorMessage(ErrorMessage message) {
        log.error("❌ 处理错误消息，类型: {}, 内容: {}", message.getErrorType(), message.getMessage());
        // 实现您的业务逻辑，比如错误恢复、用户提示等
    }

    private void handleConnectionDisconnected(String clientId) {
        log.warn("❌ 处理连接断开，客户端ID: {}", clientId);
        // 实现您的业务逻辑，比如重连、清理资源等
    }

    private void handleWebSocketError(Exception error) {
        log.error("💥 处理WebSocket错误", error);
        // 实现您的业务逻辑，比如错误恢复、用户提示等
    }

    private void handleConnectionFailure(Throwable throwable) {
        log.error("💥 处理连接失败", throwable);
        // 实现您的业务逻辑，比如重试、用户提示等
    }

    // ========== 辅助方法 ==========

    private byte[] generateMockAudioData() {
        // 生成模拟音频数据
        return new byte[1024]; // 模拟1KB的音频数据
    }
}
