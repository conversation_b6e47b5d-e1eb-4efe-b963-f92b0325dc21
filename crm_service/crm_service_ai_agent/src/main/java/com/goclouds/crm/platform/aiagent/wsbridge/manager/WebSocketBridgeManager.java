package com.goclouds.crm.platform.aiagent.wsbridge.manager;

import com.goclouds.crm.platform.aiagent.domain.AiAgentInitRequest;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.InitMessage;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.WebSocketEventHandler;
import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * WebSocket桥接管理器
 * 用于管理多个WebSocket连接实例，提供高级的业务逻辑封装
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@Component
public class WebSocketBridgeManager {

    @Autowired
    private WebSocketBridgeService webSocketBridgeService;

    /**
     * 根据AiAgentInitRequest创建并初始化WebSocket连接
     * 
     * @param request AI Agent初始化请求
     * @param eventHandler 事件处理器（可选）
     * @return 连接和初始化是否成功
     */
    public CompletableFuture<Boolean> createAndInitializeConnection(AiAgentInitRequest request, 
                                                                   WebSocketEventHandler eventHandler) {
        String clientId = generateClientId(request);
        
        return webSocketBridgeService.createConnection(clientId, eventHandler)
                .thenCompose(connected -> {
                    if (!connected) {
                        log.error("WebSocket连接失败，无法初始化，客户端ID: {}", clientId);
                        return CompletableFuture.completedFuture(false);
                    }

                    // 构建初始化参数
                    Map<String, String> settingMap = buildDefaultSettingMap();
                    List<InitMessage.PhraseInfo> phraseList = buildDefaultPhraseList();
                    
                    boolean initSuccess = webSocketBridgeService.initializeConnection(
                            clientId,
                            request.getCompanyId(),
                            generateAiAgentId(request),
                            settingMap,
                            phraseList,
                            "zh-CN", // 默认语言
                            "zh-CN-XiaoxiaoNeural", // 默认声音
                            "zh-CN",
                            generateS3JsonPath(request),
                            generateS3WavPath(request)
                    );

                    if (initSuccess) {
                        log.info("WebSocket连接初始化成功，客户端ID: {}", clientId);
                    } else {
                        log.error("WebSocket连接初始化失败，客户端ID: {}", clientId);
                        webSocketBridgeService.closeConnection(clientId);
                    }

                    return CompletableFuture.completedFuture(initSuccess);
                });
    }

    /**
     * 根据AiAgentInitRequest创建并初始化WebSocket连接（使用默认事件处理器）
     * 
     * @param request AI Agent初始化请求
     * @return 连接和初始化是否成功
     */
    public CompletableFuture<Boolean> createAndInitializeConnection(AiAgentInitRequest request) {
        return createAndInitializeConnection(request, null);
    }

    /**
     * 发送音频数据到WebSocket
     * 
     * @param request AI Agent初始化请求（用于生成客户端ID）
     * @param audioBytes 音频字节数组
     * @return 发送是否成功
     */
    public boolean sendAudioData(AiAgentInitRequest request, byte[] audioBytes) {
        String clientId = generateClientId(request);
        return webSocketBridgeService.sendSTTAudio(clientId, audioBytes);
    }

    /**
     * 关闭WebSocket连接
     * 
     * @param request AI Agent初始化请求（用于生成客户端ID）
     */
    public void closeConnection(AiAgentInitRequest request) {
        String clientId = generateClientId(request);
        webSocketBridgeService.closeConnection(clientId);
    }

    /**
     * 检查连接状态
     * 
     * @param request AI Agent初始化请求（用于生成客户端ID）
     * @return 是否已连接
     */
    public boolean isConnected(AiAgentInitRequest request) {
        String clientId = generateClientId(request);
        return webSocketBridgeService.isConnected(clientId);
    }

    /**
     * 生成客户端ID
     * 
     * @param request AI Agent初始化请求
     * @return 客户端ID
     */
    private String generateClientId(AiAgentInitRequest request) {
        return String.format("client_%s_%s_%s_%d", 
                request.getCompanyId(),
                request.getChannelId(),
                request.getSourceChannelId(),
                System.currentTimeMillis());
    }

    /**
     * 生成AI Agent ID
     * 
     * @param request AI Agent初始化请求
     * @return AI Agent ID
     */
    private String generateAiAgentId(AiAgentInitRequest request) {
        return String.format("agent_%s_%s", 
                request.getCompanyId(),
                request.getChannelId());
    }

    /**
     * 构建默认设置映射
     * 
     * @return 设置映射
     */
    private Map<String, String> buildDefaultSettingMap() {
        Map<String, String> settingMap = new HashMap<>();
        settingMap.put("silence_detection_enabled", "1");
        settingMap.put("silence_threshold", "10");
        settingMap.put("silence_result_type", "1");
        settingMap.put("continuous_silence_count", "3");
        settingMap.put("silence_timeout_action", "silence");
        return settingMap;
    }

    /**
     * 构建默认话术列表
     * 
     * @return 话术列表
     */
    private List<InitMessage.PhraseInfo> buildDefaultPhraseList() {
        List<InitMessage.PhraseInfo> phraseList = new ArrayList<>();
        
        InitMessage.PhraseInfo silencePhrase = new InitMessage.PhraseInfo();
        silencePhrase.setPhraseId("phrase_silence_001");
        silencePhrase.setPhraseType(1); // 沉默话术
        silencePhrase.setContent("您好，请问有什么可以帮助您的吗？");
        silencePhrase.setLanguage("zh-CN");
        phraseList.add(silencePhrase);
        
        InitMessage.PhraseInfo endPhrase = new InitMessage.PhraseInfo();
        endPhrase.setPhraseId("phrase_end_001");
        endPhrase.setPhraseType(2); // 结束话术
        endPhrase.setContent("感谢您的咨询，祝您生活愉快！");
        endPhrase.setLanguage("zh-CN");
        phraseList.add(endPhrase);
        
        return phraseList;
    }

    /**
     * 生成S3 JSON路径
     * 
     * @param request AI Agent初始化请求
     * @return S3 JSON路径
     */
    private String generateS3JsonPath(AiAgentInitRequest request) {
        return String.format("s3://bucket/transcripts/%s_%s_%s_%d_transcript.json",
                request.getCompanyId(),
                request.getChannelId(),
                request.getSourceChannelId(),
                System.currentTimeMillis());
    }

    /**
     * 生成S3 WAV路径
     * 
     * @param request AI Agent初始化请求
     * @return S3 WAV路径
     */
    private String generateS3WavPath(AiAgentInitRequest request) {
        return String.format("s3://bucket/audio/%s_%s_%s_%d.wav",
                request.getCompanyId(),
                request.getChannelId(),
                request.getSourceChannelId(),
                System.currentTimeMillis());
    }

    /**
     * 获取连接统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getConnectionStats() {
        return webSocketBridgeService.getConnectionStats();
    }

    /**
     * 关闭所有连接
     */
    public void closeAllConnections() {
        webSocketBridgeService.closeAllConnections();
    }
}
