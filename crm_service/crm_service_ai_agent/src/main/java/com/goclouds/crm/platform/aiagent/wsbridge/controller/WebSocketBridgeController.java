package com.goclouds.crm.platform.aiagent.wsbridge.controller;

import com.goclouds.crm.platform.aiagent.domain.AiAgentInitRequest;
import com.goclouds.crm.platform.aiagent.wsbridge.manager.WebSocketBridgeManager;
import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.Set;

/**
 * WebSocket桥接控制器
 * 提供WebSocket桥接功能的REST API接口
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@RestController
@RequestMapping("/api/websocket-bridge")
public class WebSocketBridgeController {

    @Autowired
    private WebSocketBridgeManager webSocketBridgeManager;

    @Autowired
    private WebSocketBridgeService webSocketBridgeService;

    /**
     * 创建并初始化WebSocket连接
     * 
     * @param request AI Agent初始化请求
     * @return 操作结果
     */
    @PostMapping("/connect")
    public AjaxResult<Boolean> createConnection(@RequestBody AiAgentInitRequest request) {
        try {
            log.info("创建WebSocket连接请求，公司ID: {}, 渠道ID: {}", 
                    request.getCompanyId(), request.getChannelId());

            Boolean result = webSocketBridgeManager.createAndInitializeConnection(request).get();
            
            if (result) {
                return AjaxResult.success(result, "WebSocket连接创建成功");
            } else {
                return AjaxResult.error("WebSocket连接创建失败");
            }
        } catch (Exception e) {
            log.error("创建WebSocket连接时发生错误", e);
            return AjaxResult.error("创建WebSocket连接时发生错误: " + e.getMessage());
        }
    }

    /**
     * 发送音频数据
     * 
     * @param companyId 公司ID
     * @param channelId 渠道ID
     * @param sourceChannelId 来源渠道ID
     * @param audioFile 音频文件
     * @return 操作结果
     */
    @PostMapping("/send-audio")
    public AjaxResult<Boolean> sendAudio(@RequestParam String companyId,
                                       @RequestParam String channelId,
                                       @RequestParam String sourceChannelId,
                                       @RequestParam("file") MultipartFile audioFile) {
        try {
            if (audioFile.isEmpty()) {
                return AjaxResult.error("音频文件不能为空");
            }

            // 构建请求对象
            AiAgentInitRequest request = new AiAgentInitRequest();
            request.setCompanyId(companyId);
            request.setChannelId(channelId);
            request.setSourceChannelId(sourceChannelId);

            // 检查连接状态
            if (!webSocketBridgeManager.isConnected(request)) {
                return AjaxResult.error("WebSocket连接不存在或已断开，请先创建连接");
            }

            // 发送音频数据
            byte[] audioBytes = audioFile.getBytes();
            boolean result = webSocketBridgeManager.sendAudioData(request, audioBytes);

            if (result) {
                return AjaxResult.success(result, "音频数据发送成功");
            } else {
                return AjaxResult.error("音频数据发送失败");
            }
        } catch (Exception e) {
            log.error("发送音频数据时发生错误", e);
            return AjaxResult.error("发送音频数据时发生错误: " + e.getMessage());
        }
    }

    /**
     * 关闭WebSocket连接
     * 
     * @param request AI Agent初始化请求
     * @return 操作结果
     */
    @PostMapping("/disconnect")
    public AjaxResult<String> closeConnection(@RequestBody AiAgentInitRequest request) {
        try {
            log.info("关闭WebSocket连接请求，公司ID: {}, 渠道ID: {}", 
                    request.getCompanyId(), request.getChannelId());

            webSocketBridgeManager.closeConnection(request);
            return AjaxResult.success("WebSocket连接已关闭");
        } catch (Exception e) {
            log.error("关闭WebSocket连接时发生错误", e);
            return AjaxResult.error("关闭WebSocket连接时发生错误: " + e.getMessage());
        }
    }

    /**
     * 检查连接状态
     * 
     * @param request AI Agent初始化请求
     * @return 连接状态
     */
    @PostMapping("/status")
    public AjaxResult<Boolean> checkConnectionStatus(@RequestBody AiAgentInitRequest request) {
        try {
            boolean connected = webSocketBridgeManager.isConnected(request);
            return AjaxResult.success(connected, connected ? "连接正常" : "连接已断开");
        } catch (Exception e) {
            log.error("检查连接状态时发生错误", e);
            return AjaxResult.error("检查连接状态时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取所有活跃连接
     * 
     * @return 活跃连接列表
     */
    @GetMapping("/active-connections")
    public AjaxResult<Set<String>> getActiveConnections() {
        try {
            Set<String> activeClients = webSocketBridgeService.getActiveClientIds();
            return AjaxResult.success(activeClients, "获取活跃连接成功");
        } catch (Exception e) {
            log.error("获取活跃连接时发生错误", e);
            return AjaxResult.error("获取活跃连接时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取连接统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/stats")
    public AjaxResult<Map<String, Object>> getConnectionStats() {
        try {
            Map<String, Object> stats = webSocketBridgeManager.getConnectionStats();
            return AjaxResult.success(stats, "获取统计信息成功");
        } catch (Exception e) {
            log.error("获取统计信息时发生错误", e);
            return AjaxResult.error("获取统计信息时发生错误: " + e.getMessage());
        }
    }

    /**
     * 关闭所有连接
     * 
     * @return 操作结果
     */
    @PostMapping("/disconnect-all")
    public AjaxResult<String> closeAllConnections() {
        try {
            log.info("关闭所有WebSocket连接请求");
            webSocketBridgeManager.closeAllConnections();
            return AjaxResult.success("所有WebSocket连接已关闭");
        } catch (Exception e) {
            log.error("关闭所有WebSocket连接时发生错误", e);
            return AjaxResult.error("关闭所有WebSocket连接时发生错误: " + e.getMessage());
        }
    }
}
