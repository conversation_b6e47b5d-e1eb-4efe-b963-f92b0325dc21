package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI回复消息
 * 
 * <AUTHOR> Agent
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiResponseMessage extends BaseMessage {

    /**
     * AI回复的文本内容
     */
    @JsonProperty("text")
    private String text;

    /**
     * 是否为最终回复
     */
    @JsonProperty("is_final")
    private Boolean isFinal;

    /**
     * 消息唯一标识符
     */
    @JsonProperty("msg_id")
    private String msgId;

    public ApiResponseMessage() {
        super("api_response", null);
    }
}
