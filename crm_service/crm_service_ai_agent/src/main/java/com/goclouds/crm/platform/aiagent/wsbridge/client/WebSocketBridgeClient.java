package com.goclouds.crm.platform.aiagent.wsbridge.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.goclouds.crm.platform.aiagent.wsbridge.config.WebSocketBridgeConfig;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.DefaultWebSocketEventHandler;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.WebSocketEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket桥接客户端核心类
 * 实现WebSocket连接、消息发送接收、心跳检测、重连机制等核心功能
 * 
 * <AUTHOR> Agent
 */
@Slf4j
public class WebSocketBridgeClient {

    private final WebSocketBridgeConfig config;
    private final WebSocketEventHandler eventHandler;
    private final String clientId;
    
    private WebSocketClient webSocketClient;
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final AtomicBoolean connecting = new AtomicBoolean(false);
    private final AtomicInteger reconnectAttempts = new AtomicInteger(0);
    
    private ScheduledExecutorService heartbeatExecutor;
    private ScheduledExecutorService reconnectExecutor;
    private final Object connectionLock = new Object();

    public WebSocketBridgeClient(String clientId, WebSocketBridgeConfig config) {
        this(clientId, config, new DefaultWebSocketEventHandler());
    }

    public WebSocketBridgeClient(String clientId, WebSocketBridgeConfig config, WebSocketEventHandler eventHandler) {
        this.clientId = clientId;
        this.config = config;
        this.eventHandler = eventHandler;

        log.info("WebSocket客户端已创建，客户端ID: {}", clientId);
    }

    /**
     * 连接WebSocket服务器
     */
    public CompletableFuture<Boolean> connect() {
        return CompletableFuture.supplyAsync(() -> {
            synchronized (connectionLock) {
                if (connected.get() || connecting.get()) {
                    log.warn("WebSocket已连接或正在连接中，客户端ID: {}", clientId);
                    return connected.get();
                }

                connecting.set(true);

                // 按需创建线程池
                if (heartbeatExecutor == null) {
                    heartbeatExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                        Thread t = new Thread(r, "WebSocket-Heartbeat-" + clientId);
                        t.setDaemon(true);
                        return t;
                    });
                }

                if (reconnectExecutor == null) {
                    reconnectExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                        Thread t = new Thread(r, "WebSocket-Reconnect-" + clientId);
                        t.setDaemon(true);
                        return t;
                    });
                }

                try {
                    URI serverUri = URI.create(config.getServerUrl());
                    webSocketClient = new WebSocketClient(serverUri) {
                        @Override
                        public void onOpen(ServerHandshake handshake) {
                            handleOnOpen(handshake);
                        }

                        @Override
                        public void onMessage(String message) {
                            handleOnMessage(message);
                        }

                        @Override
                        public void onClose(int code, String reason, boolean remote) {
                            handleOnClose(code, reason, remote);
                        }

                        @Override
                        public void onError(Exception ex) {
                            handleOnError(ex);
                        }
                    };

                    boolean connectResult = webSocketClient.connectBlocking(config.getConnectionTimeout(), TimeUnit.MILLISECONDS);
                    if (connectResult) {
                        connected.set(true);
                        reconnectAttempts.set(0);
                        startHeartbeat();
                        log.info("WebSocket连接成功，客户端ID: {}", clientId);
                    } else {
                        log.error("WebSocket连接超时，客户端ID: {}", clientId);
                    }
                    return connectResult;
                } catch (Exception e) {
                    log.error("WebSocket连接失败，客户端ID: {}", clientId, e);
                    return false;
                } finally {
                    connecting.set(false);
                }
            }
        });
    }

    /**
     * 断开WebSocket连接
     */
    public void disconnect() {
        synchronized (connectionLock) {
            connected.set(false);
            stopHeartbeat();
            stopReconnect();
            
            if (webSocketClient != null && !webSocketClient.isClosed()) {
                try {
                    // 发送关闭消息
                    CloseMessage closeMessage = new CloseMessage(clientId, "user_disconnect");
                    sendMessage(closeMessage);
                    
                    webSocketClient.closeBlocking();
                } catch (Exception e) {
                    log.error("关闭WebSocket连接时发生错误，客户端ID: {}", clientId, e);
                }
            }
            log.info("WebSocket连接已断开，客户端ID: {}", clientId);
        }
    }

    /**
     * 发送消息
     */
    public boolean sendMessage(BaseMessage message) {
        if (!connected.get() || webSocketClient == null || webSocketClient.isClosed()) {
            log.warn("WebSocket未连接，无法发送消息，客户端ID: {}", clientId);
            return false;
        }

        try {
            message.setClientId(clientId);
            String jsonMessage = JSON.toJSONString(message);
            
            if (config.isEnableMessageLog()) {
                log.debug("发送WebSocket消息，客户端ID: {}, 消息: {}", clientId, jsonMessage);
            }
            
            webSocketClient.send(jsonMessage);
            return true;
        } catch (Exception e) {
            log.error("发送WebSocket消息失败，客户端ID: {}", clientId, e);
            return false;
        }
    }

    /**
     * 发送初始化消息
     */
    public boolean sendInitMessage(InitMessage initMessage) {
        initMessage.setType("init");
        return sendMessage(initMessage);
    }

    /**
     * 发送STT音频数据
     */
    public boolean sendSTTAudio(String audioData) {
        STTMessage sttMessage = new STTMessage(clientId, audioData);
        return sendMessage(sttMessage);
    }

    /**
     * 发送心跳消息
     */
    public boolean sendHeartbeat() {
        HeartbeatMessage heartbeatMessage = new HeartbeatMessage(clientId);
        return sendMessage(heartbeatMessage);
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return connected.get() && webSocketClient != null && webSocketClient.isOpen();
    }

    /**
     * 获取客户端ID
     */
    public String getClientId() {
        return clientId;
    }

    /**
     * 处理连接打开事件
     */
    private void handleOnOpen(ServerHandshake handshake) {
        log.info("WebSocket连接已打开，客户端ID: {}", clientId);
        eventHandler.onConnected(clientId);
    }

    /**
     * 处理消息接收事件
     */
    private void handleOnMessage(String message) {
        if (config.isEnableMessageLog()) {
            log.debug("收到WebSocket消息，客户端ID: {}, 消息: {}", clientId, message);
        }

        try {
            JSONObject jsonObject = JSON.parseObject(message);
            String type = jsonObject.getString("type");
            
            switch (type) {
                case "init":
                    InitMessage initMessage = JSON.parseObject(message, InitMessage.class);
                    eventHandler.onInitResponse(initMessage);
                    break;
                case "heartbeat":
                    HeartbeatMessage heartbeatMessage = JSON.parseObject(message, HeartbeatMessage.class);
                    eventHandler.onHeartbeatResponse(heartbeatMessage);
                    break;
                case "stt_result":
                    STTResultMessage sttResultMessage = JSON.parseObject(message, STTResultMessage.class);
                    eventHandler.onSTTResult(sttResultMessage);
                    break;
                case "api_response":
                    ApiResponseMessage apiResponseMessage = JSON.parseObject(message, ApiResponseMessage.class);
                    eventHandler.onApiResponse(apiResponseMessage);
                    break;
                case "tts_audio":
                    TTSMessage ttsMessage = JSON.parseObject(message, TTSMessage.class);
                    eventHandler.onTTSAudio(ttsMessage);
                    break;
                case "tts_interrupt":
                    TTSInterruptMessage ttsInterruptMessage = JSON.parseObject(message, TTSInterruptMessage.class);
                    eventHandler.onTTSInterrupt(ttsInterruptMessage);
                    break;
                case "silence_timeout":
                    SilenceTimeoutMessage silenceTimeoutMessage = JSON.parseObject(message, SilenceTimeoutMessage.class);
                    eventHandler.onSilenceTimeout(silenceTimeoutMessage);
                    break;
                case "error":
                    ErrorMessage errorMessage = JSON.parseObject(message, ErrorMessage.class);
                    eventHandler.onErrorMessage(errorMessage);
                    break;
                case "close":
                    CloseMessage closeMessage = JSON.parseObject(message, CloseMessage.class);
                    eventHandler.onCloseMessage(closeMessage);
                    break;
                default:
                    eventHandler.onUnknownMessage(clientId, type, message);
                    break;
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败，客户端ID: {}, 消息: {}", clientId, message, e);
        }
    }

    /**
     * 处理连接关闭事件
     */
    private void handleOnClose(int code, String reason, boolean remote) {
        connected.set(false);
        log.info("WebSocket连接已关闭，客户端ID: {}, 代码: {}, 原因: {}, 远程关闭: {}",
                clientId, code, reason, remote);

        stopHeartbeat();
        eventHandler.onDisconnected(clientId, code, reason);

        // 如果启用自动重连且不是主动关闭
        if (config.isAutoReconnect() && remote) {
            scheduleReconnect();
        }
    }

    /**
     * 处理连接错误事件
     */
    private void handleOnError(Exception ex) {
        log.error("WebSocket连接发生错误，客户端ID: {}", clientId, ex);
        eventHandler.onError(clientId, ex);
    }

    /**
     * 启动心跳检测
     */
    private void startHeartbeat() {
        if (heartbeatExecutor != null && !heartbeatExecutor.isShutdown()) {
            heartbeatExecutor.scheduleWithFixedDelay(
                    this::sendHeartbeat,
                    config.getHeartbeatInterval(),
                    config.getHeartbeatInterval(),
                    TimeUnit.MILLISECONDS
            );
            log.debug("心跳检测已启动，客户端ID: {}, 间隔: {}ms", clientId, config.getHeartbeatInterval());
        }
    }

    /**
     * 停止心跳检测
     */
    private void stopHeartbeat() {
        if (heartbeatExecutor != null && !heartbeatExecutor.isShutdown()) {
            heartbeatExecutor.shutdownNow();
            heartbeatExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "WebSocket-Heartbeat-" + clientId);
                t.setDaemon(true);
                return t;
            });
            log.debug("心跳检测已停止，客户端ID: {}", clientId);
        }
    }

    /**
     * 安排重连
     */
    private void scheduleReconnect() {
        if (!config.isAutoReconnect()) {
            return;
        }

        int currentAttempts = reconnectAttempts.get();
        if (currentAttempts >= config.getMaxReconnectAttempts()) {
            log.error("重连次数已达上限，停止重连，客户端ID: {}, 尝试次数: {}",
                    clientId, currentAttempts);
            return;
        }

        reconnectAttempts.incrementAndGet();
        long delay = config.getReconnectInterval() * (currentAttempts + 1); // 指数退避

        log.info("安排WebSocket重连，客户端ID: {}, 第{}次尝试，延迟: {}ms",
                clientId, currentAttempts + 1, delay);

        reconnectExecutor.schedule(() -> {
            if (!connected.get()) {
                log.info("开始WebSocket重连，客户端ID: {}", clientId);
                connect().thenAccept(success -> {
                    if (!success) {
                        scheduleReconnect();
                    }
                });
            }
        }, delay, TimeUnit.MILLISECONDS);
    }

    /**
     * 停止重连
     */
    private void stopReconnect() {
        if (reconnectExecutor != null && !reconnectExecutor.isShutdown()) {
            reconnectExecutor.shutdownNow();
            reconnectExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "WebSocket-Reconnect-" + clientId);
                t.setDaemon(true);
                return t;
            });
            log.debug("重连调度已停止，客户端ID: {}", clientId);
        }
    }

    /**
     * 销毁客户端，释放资源
     */
    public void destroy() {
        disconnect();

        if (heartbeatExecutor != null) {
            heartbeatExecutor.shutdown();
        }

        if (reconnectExecutor != null) {
            reconnectExecutor.shutdown();
        }

        log.info("WebSocket客户端已销毁，客户端ID: {}", clientId);
    }
}
