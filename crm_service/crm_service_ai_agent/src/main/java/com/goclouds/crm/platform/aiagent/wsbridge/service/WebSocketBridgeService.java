package com.goclouds.crm.platform.aiagent.wsbridge.service;

import com.goclouds.crm.platform.aiagent.wsbridge.domain.InitMessage;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.WebSocketEventHandler;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * WebSocket桥接服务接口
 * 作为对外提供的API接口，供其他模块调用
 * 
 * <AUTHOR> Agent
 */
public interface WebSocketBridgeService {

    /**
     * 创建WebSocket连接
     * 
     * @param clientId 客户端ID
     * @param eventHandler 事件处理器（可选）
     * @return 连接是否成功
     */
    CompletableFuture<Boolean> createConnection(String clientId, WebSocketEventHandler eventHandler);

    /**
     * 创建WebSocket连接（使用默认事件处理器）
     * 
     * @param clientId 客户端ID
     * @return 连接是否成功
     */
    CompletableFuture<Boolean> createConnection(String clientId);

    /**
     * 初始化WebSocket连接
     * 
     * @param clientId 客户端ID
     * @param companyId 公司ID
     * @param aiAgentId AI代理ID
     * @param settingMap 设置映射
     * @param phraseList 话术列表
     * @param ttsLanguage TTS语言
     * @param ttsVoiceName TTS声音名称
     * @param language 语言
     * @param s3JsonPath S3 JSON路径
     * @param s3WavPath S3 WAV路径
     * @return 初始化是否成功
     */
    boolean initializeConnection(String clientId, String companyId, String aiAgentId,
                               Map<String, String> settingMap, 
                               java.util.List<InitMessage.PhraseInfo> phraseList,
                               String ttsLanguage, String ttsVoiceName, String language,
                               String s3JsonPath, String s3WavPath);

    /**
     * 发送STT音频数据
     * 
     * @param clientId 客户端ID
     * @param audioData Base64编码的音频数据
     * @return 发送是否成功
     */
    boolean sendSTTAudio(String clientId, String audioData);

    /**
     * 发送STT音频数据（字节数组）
     * 
     * @param clientId 客户端ID
     * @param audioBytes 音频字节数组
     * @return 发送是否成功
     */
    boolean sendSTTAudio(String clientId, byte[] audioBytes);

    /**
     * 关闭WebSocket连接
     * 
     * @param clientId 客户端ID
     */
    void closeConnection(String clientId);

    /**
     * 检查连接状态
     * 
     * @param clientId 客户端ID
     * @return 是否已连接
     */
    boolean isConnected(String clientId);

    /**
     * 获取所有活跃连接的客户端ID
     * 
     * @return 客户端ID列表
     */
    java.util.Set<String> getActiveClientIds();

    /**
     * 关闭所有连接
     */
    void closeAllConnections();

    /**
     * 获取连接统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getConnectionStats();
}
