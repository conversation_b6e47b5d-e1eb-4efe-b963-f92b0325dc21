package com.goclouds.crm.platform.aiagent.wsbridge.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.goclouds.crm.platform.aiagent.wsbridge.config.WebSocketBridgeConfig;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.WebSocketEventHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * WebSocket桥接服务 - 新版本
 * 根据用户需求重新设计的WebSocket桥接服务
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@Service
public class WebSocketBridgeService {

    @Autowired
    private WebSocketBridgeConfig config;

    // 连接管理
    private final Map<String, WebSocketClient> clients = new ConcurrentHashMap<>();
    private final Map<String, WebSocketConnectionRequest> requests = new ConcurrentHashMap<>();

    /**
     * 创建WebSocket连接
     * 
     * @param connectionRequest 连接请求参数
     * @return 连接操作的Future
     */
    public CompletableFuture<String> createWebSocketConnection(WebSocketConnectionRequest connectionRequest) {
        log.info("🚀 开始创建WebSocket连接，地址: {}", connectionRequest.getWebSocketUrl());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 创建WebSocket客户端
                String clientId = generateClientId(connectionRequest);
                
                // 2. 创建事件处理器（中间层处理）
                WebSocketEventHandler eventHandler = createMiddlewareEventHandler(connectionRequest, clientId);
                
                // 3. 建立WebSocket连接
                boolean connected = connectToWebSocket(connectionRequest.getWebSocketUrl(), clientId, eventHandler);
                
                if (connected) {
                    log.info("✅ WebSocket连接建立成功，客户端ID: {}", clientId);
                    return clientId;
                } else {
                    log.error("❌ WebSocket连接建立失败");
                    throw new RuntimeException("WebSocket连接失败");
                }
                
            } catch (Exception e) {
                log.error("💥 创建WebSocket连接时发生异常", e);
                throw new RuntimeException("创建WebSocket连接异常", e);
            }
        });
    }

    /**
     * 发送音频数据
     * 
     * @param clientId 客户端ID
     * @param audioData 音频数据
     * @return 发送是否成功
     */
    public boolean sendAudioData(String clientId, byte[] audioData) {
        log.debug("🎤 发送音频数据，客户端ID: {}, 数据大小: {} bytes", clientId, audioData.length);
        
        try {
            // 编码音频数据为Base64
            String base64Audio = encodeAudioToBase64(audioData);
            
            // 创建STT消息
            STTMessage sttMessage = new STTMessage();
            sttMessage.setClientId(clientId);
            sttMessage.setAudioData(base64Audio);
            
            // 发送消息
            return sendMessage(clientId, sttMessage);
            
        } catch (Exception e) {
            log.error("💥 发送音频数据时发生异常，客户端ID: {}", clientId, e);
            return false;
        }
    }

    /**
     * 关闭WebSocket连接
     * 
     * @param clientId 客户端ID
     */
    public void closeConnection(String clientId) {
        log.info("🛑 关闭WebSocket连接，客户端ID: {}", clientId);
        
        try {
            // 发送关闭消息
            CloseMessage closeMessage = new CloseMessage();
            closeMessage.setClientId(clientId);
            closeMessage.setReason("用户主动关闭");
            
            sendMessage(clientId, closeMessage);
            
            // 关闭底层连接
            closeWebSocketConnection(clientId);
            
        } catch (Exception e) {
            log.error("💥 关闭WebSocket连接时发生异常，客户端ID: {}", clientId, e);
        }
    }

    /**
     * 创建中间层事件处理器
     * 这里是关键：收到WebSocket事件后，先在中间层处理，然后回调给业务层
     */
    private WebSocketEventHandler createMiddlewareEventHandler(WebSocketConnectionRequest request, String clientId) {
        return new WebSocketEventHandler() {
            
            @Override
            public void onConnected(String clientId) {
                log.info("🔗 [中间层] WebSocket连接已建立，等待服务器发送init事件");
                // 连接建立后，等待服务器发送init事件，不立即回调业务层
            }

            @Override
            public void onMessage(String message) {
                log.debug("📨 [中间层] 收到原始消息: {}", message);
                
                try {
                    // 解析消息类型
                    BaseMessage baseMessage = parseMessage(message);
                    
                    if (baseMessage != null) {
                        handleParsedMessage(baseMessage, request, clientId);
                    }
                    
                } catch (Exception e) {
                    log.error("💥 [中间层] 解析消息时发生异常", e);
                    if (request.getCallbacks().getOnError() != null) {
                        request.getCallbacks().getOnError().accept(e);
                    }
                }
            }

            @Override
            public void onDisconnected(String clientId, int code, String reason) {
                log.warn("❌ [中间层] WebSocket连接断开，客户端ID: {}, 代码: {}, 原因: {}", clientId, code, reason);
                
                // 回调业务层
                if (request.getCallbacks().getOnDisconnected() != null) {
                    request.getCallbacks().getOnDisconnected().accept(clientId);
                }
            }

            @Override
            public void onError(String clientId, Exception ex) {
                log.error("💥 [中间层] WebSocket发生错误，客户端ID: {}", clientId, ex);
                
                // 回调业务层
                if (request.getCallbacks().getOnError() != null) {
                    request.getCallbacks().getOnError().accept(ex);
                }
            }

            @Override
            public void onInitResponse(InitMessage message) {

            }

            @Override
            public void onHeartbeatResponse(HeartbeatMessage message) {

            }

            @Override
            public void onSTTResult(STTResultMessage message) {

            }

            @Override
            public void onApiResponse(ApiResponseMessage message) {

            }

            @Override
            public void onTTSAudio(TTSMessage message) {

            }

            @Override
            public void onTTSInterrupt(TTSInterruptMessage message) {

            }

            @Override
            public void onSilenceTimeout(SilenceTimeoutMessage message) {

            }

            @Override
            public void onErrorMessage(ErrorMessage message) {

            }

            @Override
            public void onCloseMessage(CloseMessage message) {

            }

            @Override
            public void onUnknownMessage(String clientId, String messageType, String rawMessage) {

            }
        };
    }

    /**
     * 处理解析后的消息
     * 这里是中间层的核心处理逻辑
     */
    private void handleParsedMessage(BaseMessage message, WebSocketConnectionRequest request, String clientId) {
        String messageType = message.getType();
        log.info("📋 [中间层] 处理消息类型: {}", messageType);

        switch (messageType) {
            case "init":
                handleInitMessage((InitMessage) message, request, clientId);
                break;
                
            case "stt_result":
                handleSTTResultMessage((STTResultMessage) message, request);
                break;
                
            case "api_response":
                handleApiResponseMessage((ApiResponseMessage) message, request);
                break;
                
            case "tts_audio":
                handleTTSAudioMessage((TTSMessage) message, request);
                break;
                
            case "tts_interrupt":
                handleTTSInterruptMessage((TTSInterruptMessage) message, request);
                break;
                
            case "silence_timeout":
                handleSilenceTimeoutMessage((SilenceTimeoutMessage) message, request);
                break;
                
            case "error":
                handleErrorMessage((ErrorMessage) message, request);
                break;
                
            default:
                log.warn("⚠️ [中间层] 未知消息类型: {}", messageType);
        }
    }

    /**
     * 处理服务器发送的init事件
     * 这是您描述的关键流程：收到init事件后，获取配置信息并发送响应
     */
    private void handleInitMessage(InitMessage initMessage, WebSocketConnectionRequest request, String clientId) {
        log.info("🎯 [中间层] 收到服务器init事件，开始获取配置信息并发送响应");
        
        try {
            // 1. 获取配置信息（从业务参数中获取或从配置服务获取）
            Map<String, Object> configData = getConfigurationData(request);
            
            // 2. 组装init响应消息
            InitMessage responseMessage = buildInitResponseMessage(clientId, request.getBusinessParams(), configData);
            
            // 3. 发送init响应给WebSocket服务器
            boolean sent = sendMessage(clientId, responseMessage);
            
            if (sent) {
                log.info("✅ [中间层] init响应发送成功，WebSocket初始化完成");
                
                // 4. 回调业务层，告知连接已完全建立并初始化完成
                if (request.getCallbacks().getOnConnected() != null) {
                    request.getCallbacks().getOnConnected().accept(clientId);
                }
            } else {
                log.error("❌ [中间层] init响应发送失败");
                throw new RuntimeException("init响应发送失败");
            }
            
        } catch (Exception e) {
            log.error("💥 [中间层] 处理init事件时发生异常", e);
            if (request.getCallbacks().getOnError() != null) {
                request.getCallbacks().getOnError().accept(e);
            }
        }
    }

    /**
     * 处理语音识别结果
     */
    private void handleSTTResultMessage(STTResultMessage message, WebSocketConnectionRequest request) {
        log.info("🎤 [中间层] 处理语音识别结果: {}, 最终: {}", message.getText(), message.getIsFinal());
        
        // 中间层可以做一些处理，比如过滤、格式化等
        String processedText = processSTTResult(message.getText());
        message.setText(processedText);
        
        // 回调业务层
        if (request.getCallbacks().getOnSTTResult() != null) {
            request.getCallbacks().getOnSTTResult().accept(message);
        }
    }

    /**
     * 处理AI回复
     */
    private void handleApiResponseMessage(ApiResponseMessage message, WebSocketConnectionRequest request) {
        log.info("🤖 [中间层] 处理AI回复: {}, 最终: {}", message.getText(), message.getIsFinal());
        
        // 中间层可以做一些处理，比如敏感词过滤、格式化等
        String processedText = processAIResponse(message.getText());
        message.setText(processedText);
        
        // 回调业务层
        if (request.getCallbacks().getOnApiResponse() != null) {
            request.getCallbacks().getOnApiResponse().accept(message);
        }
    }

    /**
     * 处理TTS音频
     */
    private void handleTTSAudioMessage(TTSMessage message, WebSocketConnectionRequest request) {
        log.info("🔊 [中间层] 处理TTS音频，序号: {}, 最终: {}", message.getSequence(), message.getIsFinal());
        
        // 中间层可以做一些处理，比如音频格式转换、音量调节等
        String processedAudio = processTTSAudio(message.getAudioData());
        message.setAudioData(processedAudio);
        
        // 回调业务层
        if (request.getCallbacks().getOnTTSAudio() != null) {
            request.getCallbacks().getOnTTSAudio().accept(message);
        }
    }

    /**
     * 处理TTS打断
     */
    private void handleTTSInterruptMessage(TTSInterruptMessage message, WebSocketConnectionRequest request) {
        log.info("⏹️ [中间层] 处理TTS打断，原因: {}", message.getReason());
        
        // 回调业务层
        if (request.getCallbacks().getOnTTSInterrupt() != null) {
            request.getCallbacks().getOnTTSInterrupt().accept(message);
        }
    }

    /**
     * 处理静默超时
     */
    private void handleSilenceTimeoutMessage(SilenceTimeoutMessage message, WebSocketConnectionRequest request) {
        log.info("🔇 [中间层] 处理静默超时，时长: {}秒", message.getTimeoutDuration());
        
        // 回调业务层
        if (request.getCallbacks().getOnSilenceTimeout() != null) {
            request.getCallbacks().getOnSilenceTimeout().accept(message);
        }
    }

    /**
     * 处理错误消息
     */
    private void handleErrorMessage(ErrorMessage message, WebSocketConnectionRequest request) {
        log.error("❌ [中间层] 处理错误消息，类型: {}, 内容: {}", message.getErrorType(), message.getMessage());
        
        // 回调业务层
        if (request.getCallbacks().getOnErrorMessage() != null) {
            request.getCallbacks().getOnErrorMessage().accept(message);
        }
    }

    // ========== 辅助方法 ==========
    
    private String generateClientId(WebSocketConnectionRequest request) {
        return "client_" + System.currentTimeMillis() + "_" + Math.abs(request.getBusinessParams().hashCode());
    }

    private Map<String, Object> getConfigurationData(WebSocketConnectionRequest request) {
        // 从业务参数或配置服务获取配置信息
        // 这里可以根据业务参数动态获取不同的配置
        Map<String, Object> configData = new ConcurrentHashMap<>(request.getBusinessParams());

        // 添加一些默认配置
        configData.putIfAbsent("heartbeatInterval", config.getHeartbeatInterval());
        configData.putIfAbsent("connectionTimeout", config.getConnectionTimeout());
        configData.putIfAbsent("audioFormat", "pcm_16khz_16bit_mono");

        return configData;
    }

    private InitMessage buildInitResponseMessage(String clientId, Map<String, Object> businessParams, Map<String, Object> configData) {
        // 根据业务参数和配置数据构建init响应消息
        InitMessage message = new InitMessage();
        message.setType("init");
        message.setClientId(clientId);
        message.setStatus("ready");

        // 设置业务参数
        if (businessParams.containsKey("companyId")) {
            message.setCompanyId((String) businessParams.get("companyId"));
        }
        if (businessParams.containsKey("channelId")) {
            message.setChannelId((String) businessParams.get("channelId"));
        }
        if (businessParams.containsKey("agentId")) {
            message.setAgentId((String) businessParams.get("agentId"));
        }
        if (businessParams.containsKey("language")) {
            message.setLanguage((String) businessParams.get("language"));
        }
        if (businessParams.containsKey("voiceName")) {
            message.setVoiceName((String) businessParams.get("voiceName"));
        }

        return message;
    }

    private String processSTTResult(String text) {
        // 中间层处理语音识别结果
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        // 可以在这里添加文本处理逻辑，比如：
        // 1. 去除多余空格
        // 2. 敏感词过滤
        // 3. 格式化处理
        return text.trim();
    }

    private String processAIResponse(String text) {
        // 中间层处理AI回复
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        // 可以在这里添加AI回复处理逻辑，比如：
        // 1. 内容审核
        // 2. 格式化
        // 3. 长度限制
        return text.trim();
    }

    private String processTTSAudio(String audioData) {
        // 中间层处理TTS音频
        if (audioData == null || audioData.trim().isEmpty()) {
            return audioData;
        }

        // 可以在这里添加音频处理逻辑，比如：
        // 1. 音频格式转换
        // 2. 音量调节
        // 3. 质量优化
        return audioData;
    }

    private String encodeAudioToBase64(byte[] audioData) {
        // 编码音频数据为Base64
        if (audioData == null || audioData.length == 0) {
            return "";
        }
        return java.util.Base64.getEncoder().encodeToString(audioData);
    }
    
    // ========== WebSocket底层操作实现 ==========

    /**
     * 建立WebSocket连接
     */
    private boolean connectToWebSocket(String url, String clientId, WebSocketEventHandler handler) {
        try {
            log.info("🔗 建立WebSocket连接，地址: {}, 客户端ID: {}", url, clientId);

            WebSocketClient client = new WebSocketClient(URI.create(url)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("✅ WebSocket连接已建立，客户端ID: {}", clientId);
                    handler.onConnected(clientId);
                }

                @Override
                public void onMessage(String message) {
                    log.debug("📨 收到WebSocket消息，客户端ID: {}, 消息: {}", clientId, message);
                    handler.onMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.warn("❌ WebSocket连接关闭，客户端ID: {}, 代码: {}, 原因: {}", clientId, code, reason);
                    handler.onDisconnected(clientId, code, reason);
                    clients.remove(clientId);
                    requests.remove(clientId);
                }

                @Override
                public void onError(Exception ex) {
                    log.error("💥 WebSocket连接错误，客户端ID: {}", clientId, ex);
                    handler.onError(clientId, ex);
                }
            };

            // 设置连接超时
            client.setConnectionLostTimeout(30);

            // 建立连接
            boolean connected = client.connectBlocking();
            if (connected) {
                clients.put(clientId, client);
                log.info("✅ WebSocket连接建立成功，客户端ID: {}", clientId);
            }

            return connected;

        } catch (Exception e) {
            log.error("💥 建立WebSocket连接时发生异常，客户端ID: {}", clientId, e);
            return false;
        }
    }

    /**
     * 发送消息
     */
    private boolean sendMessage(String clientId, BaseMessage message) {
        try {
            WebSocketClient client = clients.get(clientId);
            if (client == null || !client.isOpen()) {
                log.error("❌ WebSocket客户端不存在或未连接，客户端ID: {}", clientId);
                return false;
            }

            String jsonMessage = JSON.toJSONString(message);
            client.send(jsonMessage);
            log.debug("📤 发送WebSocket消息成功，客户端ID: {}, 消息: {}", clientId, jsonMessage);
            return true;

        } catch (Exception e) {
            log.error("💥 发送WebSocket消息时发生异常，客户端ID: {}", clientId, e);
            return false;
        }
    }

    /**
     * 关闭WebSocket连接
     */
    private void closeWebSocketConnection(String clientId) {
        try {
            WebSocketClient client = clients.get(clientId);
            if (client != null) {
                client.close();
                clients.remove(clientId);
                requests.remove(clientId);
                log.info("🛑 WebSocket连接已关闭，客户端ID: {}", clientId);
            }
        } catch (Exception e) {
            log.error("💥 关闭WebSocket连接时发生异常，客户端ID: {}", clientId, e);
        }
    }

    /**
     * 解析消息
     */
    private BaseMessage parseMessage(String message) {
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            String type = jsonObject.getString("type");

            if (type == null) {
                log.warn("⚠️ 消息类型为空，忽略消息: {}", message);
                return null;
            }

            switch (type) {
                case "init":
                    return JSON.parseObject(message, InitMessage.class);
                case "stt_result":
                    return JSON.parseObject(message, STTResultMessage.class);
                case "api_response":
                    return JSON.parseObject(message, ApiResponseMessage.class);
                case "tts_audio":
                    return JSON.parseObject(message, TTSMessage.class);
                case "tts_interrupt":
                    return JSON.parseObject(message, TTSInterruptMessage.class);
                case "silence_timeout":
                    return JSON.parseObject(message, SilenceTimeoutMessage.class);
                case "error":
                    return JSON.parseObject(message, ErrorMessage.class);
                default:
                    log.warn("⚠️ 未知消息类型: {}, 消息: {}", type, message);
                    return null;
            }

        } catch (Exception e) {
            log.error("💥 解析消息时发生异常，消息: {}", message, e);
            return null;
        }
    }

    /**
     * 清理资源
     */
    @PreDestroy
    public void cleanup() {
        log.info("🧹 清理WebSocket连接资源");
        clients.values().forEach(client -> {
            try {
                if (client.isOpen()) {
                    client.close();
                }
            } catch (Exception e) {
                log.error("💥 清理WebSocket连接时发生异常", e);
            }
        });
        clients.clear();
        requests.clear();
    }

    // ========== 数据结构定义 ==========

    /**
     * WebSocket连接请求参数
     */
    @Data
    public static class WebSocketConnectionRequest {
        /**
         * WebSocket连接地址
         */
        private String webSocketUrl;

        /**
         * 业务逻辑参数值
         */
        private Map<String, Object> businessParams;

        /**
         * 事件回调
         */
        private WebSocketEventCallbacks callbacks;
    }

    /**
     * WebSocket事件回调集合
     */
    @Data
    public static class WebSocketEventCallbacks {
        /**
         * 连接建立并初始化完成回调
         * 注意：这个回调在收到服务器init事件并成功响应后才会触发
         */
        private Consumer<String> onConnected;

        /**
         * 语音识别结果回调
         */
        private Consumer<STTResultMessage> onSTTResult;

        /**
         * AI回复回调
         */
        private Consumer<ApiResponseMessage> onApiResponse;

        /**
         * TTS音频回调
         */
        private Consumer<TTSMessage> onTTSAudio;

        /**
         * TTS打断回调
         */
        private Consumer<TTSInterruptMessage> onTTSInterrupt;

        /**
         * 静默超时回调
         */
        private Consumer<SilenceTimeoutMessage> onSilenceTimeout;

        /**
         * 错误消息回调
         */
        private Consumer<ErrorMessage> onErrorMessage;

        /**
         * 连接断开回调
         */
        private Consumer<String> onDisconnected;

        /**
         * 错误回调
         */
        private Consumer<Exception> onError;
    }
}
