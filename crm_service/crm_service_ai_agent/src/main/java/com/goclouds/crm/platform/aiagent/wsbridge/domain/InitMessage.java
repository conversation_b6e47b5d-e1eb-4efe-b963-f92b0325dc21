package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 初始化消息
 * 
 * <AUTHOR> Agent
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InitMessage extends BaseMessage {

    /**
     * 公司ID
     */
    @JsonProperty("company_id")
    private String companyId;

    /**
     * AI代理ID
     */
    @JsonProperty("aiAgentId")
    private String aiAgentId;

    /**
     * 设置映射
     */
    @JsonProperty("settingMap")
    private Map<String, String> settingMap;

    /**
     * 话术列表
     */
    @JsonProperty("phraseList")
    private List<PhraseInfo> phraseList;

    /**
     * TTS语言
     */
    @JsonProperty("tts_language")
    private String ttsLanguage;

    /**
     * TTS声音名称
     */
    @JsonProperty("tts_voice_name")
    private String ttsVoiceName;

    /**
     * 语言
     */
    @JsonProperty("language")
    private String language;

    /**
     * S3 JSON路径
     */
    @JsonProperty("s3_json_path")
    private String s3JsonPath;

    /**
     * S3 WAV路径
     */
    @JsonProperty("s3_wav_path")
    private String s3WavPath;

    public InitMessage() {
        super("init", null);
    }

    public InitMessage(String clientId) {
        super("init", clientId);
    }

    /**
     * 话术信息
     */
    @Data
    public static class PhraseInfo {
        /**
         * 话术ID
         */
        @JsonProperty("phraseId")
        private String phraseId;

        /**
         * 话术类型 (1=沉默话术, 2=结束话术)
         */
        @JsonProperty("phraseType")
        private Integer phraseType;

        /**
         * 话术内容
         */
        @JsonProperty("content")
        private String content;

        /**
         * 语言
         */
        @JsonProperty("language")
        private String language;
    }
}
