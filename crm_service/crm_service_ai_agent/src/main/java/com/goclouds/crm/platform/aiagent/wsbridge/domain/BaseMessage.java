package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * WebSocket消息基类
 * 
 * <AUTHOR> Agent
 */
@Data
public abstract class BaseMessage {

    /**
     * 消息类型
     */
    @JsonProperty("type")
    private String type;

    /**
     * 客户端ID
     */
    @JsonProperty("client_id")
    private String clientId;

    /**
     * 状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 消息内容
     */
    @JsonProperty("message")
    private String message;

    public BaseMessage() {}

    public BaseMessage(String type, String clientId) {
        this.type = type;
        this.clientId = clientId;
    }
}
