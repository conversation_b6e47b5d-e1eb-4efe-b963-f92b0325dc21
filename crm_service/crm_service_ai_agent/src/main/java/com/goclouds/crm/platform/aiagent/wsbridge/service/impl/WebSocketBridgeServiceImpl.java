package com.goclouds.crm.platform.aiagent.wsbridge.service.impl;

import com.goclouds.crm.platform.aiagent.wsbridge.client.WebSocketBridgeClient;
import com.goclouds.crm.platform.aiagent.wsbridge.config.WebSocketBridgeConfig;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.InitMessage;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.WebSocketEventHandler;
import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService;
import com.goclouds.crm.platform.aiagent.wsbridge.utils.AudioUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket桥接服务实现类
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@Service
public class WebSocketBridgeServiceImpl implements WebSocketBridgeService {

    @Autowired
    private WebSocketBridgeConfig config;

    private final Map<String, WebSocketBridgeClient> clients = new ConcurrentHashMap<>();

    @Override
    public CompletableFuture<Boolean> createConnection(String clientId, WebSocketEventHandler eventHandler) {
        if (clients.containsKey(clientId)) {
            WebSocketBridgeClient existingClient = clients.get(clientId);
            if (existingClient.isConnected()) {
                log.warn("客户端已存在且已连接，客户端ID: {}", clientId);
                return CompletableFuture.completedFuture(true);
            } else {
                // 清理旧的客户端
                existingClient.destroy();
                clients.remove(clientId);
            }
        }

        WebSocketBridgeClient client = new WebSocketBridgeClient(clientId, config, eventHandler);
        clients.put(clientId, client);

        return client.connect().thenApply(success -> {
            if (!success) {
                clients.remove(clientId);
                client.destroy();
            }
            return success;
        });
    }

    @Override
    public CompletableFuture<Boolean> createConnection(String clientId) {
        return createConnection(clientId, null);
    }

    @Override
    public boolean initializeConnection(String clientId, String companyId, String aiAgentId,
                                      Map<String, String> settingMap,
                                      List<InitMessage.PhraseInfo> phraseList,
                                      String ttsLanguage, String ttsVoiceName, String language,
                                      String s3JsonPath, String s3WavPath) {
        WebSocketBridgeClient client = clients.get(clientId);
        if (client == null || !client.isConnected()) {
            log.error("客户端不存在或未连接，无法初始化，客户端ID: {}", clientId);
            return false;
        }

        InitMessage initMessage = new InitMessage(clientId);
        initMessage.setCompanyId(companyId);
        initMessage.setAiAgentId(aiAgentId);
        initMessage.setSettingMap(settingMap);
        initMessage.setPhraseList(phraseList);
        initMessage.setTtsLanguage(ttsLanguage != null ? ttsLanguage : config.getTts().getLanguage());
        initMessage.setTtsVoiceName(ttsVoiceName != null ? ttsVoiceName : config.getTts().getVoiceName());
        initMessage.setLanguage(language != null ? language : config.getTts().getLanguage());
        initMessage.setS3JsonPath(s3JsonPath);
        initMessage.setS3WavPath(s3WavPath);

        return client.sendInitMessage(initMessage);
    }

    @Override
    public boolean sendSTTAudio(String clientId, String audioData) {
        WebSocketBridgeClient client = clients.get(clientId);
        if (client == null || !client.isConnected()) {
            log.error("客户端不存在或未连接，无法发送音频，客户端ID: {}", clientId);
            return false;
        }

        return client.sendSTTAudio(audioData);
    }

    @Override
    public boolean sendSTTAudio(String clientId, byte[] audioBytes) {
        if (audioBytes == null || audioBytes.length == 0) {
            log.warn("音频数据为空，客户端ID: {}", clientId);
            return false;
        }

        // 验证音频格式
        if (!AudioUtils.validateAudioFormat(audioBytes)) {
            log.warn("音频格式不符合要求，客户端ID: {}", clientId);
            return false;
        }

        String audioData = AudioUtils.encodeAudioToBase64(audioBytes);
        if (audioData == null) {
            log.error("音频数据编码失败，客户端ID: {}", clientId);
            return false;
        }

        return sendSTTAudio(clientId, audioData);
    }

    @Override
    public void closeConnection(String clientId) {
        WebSocketBridgeClient client = clients.remove(clientId);
        if (client != null) {
            client.disconnect();
            client.destroy();
            log.info("WebSocket连接已关闭，客户端ID: {}", clientId);
        } else {
            log.warn("客户端不存在，无法关闭连接，客户端ID: {}", clientId);
        }
    }

    @Override
    public boolean isConnected(String clientId) {
        WebSocketBridgeClient client = clients.get(clientId);
        return client != null && client.isConnected();
    }

    @Override
    public Set<String> getActiveClientIds() {
        Set<String> activeClients = new HashSet<>();
        clients.forEach((clientId, client) -> {
            if (client.isConnected()) {
                activeClients.add(clientId);
            }
        });
        return activeClients;
    }

    @Override
    public void closeAllConnections() {
        log.info("开始关闭所有WebSocket连接，总数: {}", clients.size());
        clients.forEach((clientId, client) -> {
            try {
                client.disconnect();
                client.destroy();
            } catch (Exception e) {
                log.error("关闭WebSocket连接时发生错误，客户端ID: {}", clientId, e);
            }
        });
        clients.clear();
        log.info("所有WebSocket连接已关闭");
    }

    @Override
    public Map<String, Object> getConnectionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalClients", clients.size());
        stats.put("activeClients", getActiveClientIds().size());
        stats.put("serverUrl", config.getServerUrl());
        stats.put("autoReconnect", config.isAutoReconnect());
        stats.put("heartbeatInterval", config.getHeartbeatInterval());
        stats.put("maxReconnectAttempts", config.getMaxReconnectAttempts());
        return stats;
    }

    @PreDestroy
    public void destroy() {
        log.info("WebSocket桥接服务正在销毁...");
        closeAllConnections();
    }
}
