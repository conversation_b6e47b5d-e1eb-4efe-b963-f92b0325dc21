package com.goclouds.crm.platform.aiagent.wsbridge.facade;

import com.goclouds.crm.platform.aiagent.domain.AiAgentInitRequest;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.*;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.DefaultWebSocketEventHandler;
import com.goclouds.crm.platform.aiagent.wsbridge.handler.WebSocketEventHandler;
import com.goclouds.crm.platform.aiagent.wsbridge.manager.WebSocketBridgeManager;
import com.goclouds.crm.platform.aiagent.wsbridge.service.WebSocketBridgeService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * WebSocket桥接门面类
 * 提供内部调用的方法，包含各种事件方法和回调处理
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@Component
public class WebSocketBridgeFacade {

    @Autowired
    private WebSocketBridgeManager webSocketBridgeManager;

    @Autowired
    private WebSocketBridgeService webSocketBridgeService;

    /**
     * 创建WebSocket连接
     * 
     * @param request AI Agent初始化请求
     * @param onConnected 连接成功回调
     * @param onDisconnected 连接断开回调
     * @param onError 错误回调
     * @return 连接操作的Future
     */
    public CompletableFuture<Boolean> createConnection(AiAgentInitRequest request,
                                                     Consumer<String> onConnected,
                                                     Consumer<String> onDisconnected,
                                                     Consumer<Exception> onError) {
        
        // 创建自定义事件处理器
        WebSocketEventHandler eventHandler = new DefaultWebSocketEventHandler() {
            @Override
            public void onConnected(String clientId) {
                super.onConnected(clientId);
                if (onConnected != null) {
                    onConnected.accept(clientId);
                }
            }

            @Override
            public void onDisconnected(String clientId, int code, String reason) {
                super.onDisconnected(clientId, code, reason);
                if (onDisconnected != null) {
                    onDisconnected.accept(clientId);
                }
            }

            @Override
            public void onError(String clientId, Exception ex) {
                super.onError(clientId, ex);
                if (onError != null) {
                    onError.accept(ex);
                }
            }
        };

        return webSocketBridgeManager.createAndInitializeConnection(request, eventHandler);
    }

    /**
     * 创建WebSocket连接（带完整事件回调）
     * 
     * @param request AI Agent初始化请求
     * @param callbacks 事件回调集合
     * @return 连接操作的Future
     */
    public CompletableFuture<Boolean> createConnectionWithCallbacks(AiAgentInitRequest request,
                                                                   WebSocketEventCallbacks callbacks) {
        
        WebSocketEventHandler eventHandler = new DefaultWebSocketEventHandler() {
            @Override
            public void onConnected(String clientId) {
                super.onConnected(clientId);
                if (callbacks.getOnConnected() != null) {
                    callbacks.getOnConnected().accept(clientId);
                }
            }

            @Override
            public void onInitResponse(InitMessage message) {
                super.onInitResponse(message);
                if (callbacks.getOnInitResponse() != null) {
                    callbacks.getOnInitResponse().accept(message);
                }
            }

            @Override
            public void onSTTResult(STTResultMessage message) {
                super.onSTTResult(message);
                if (callbacks.getOnSTTResult() != null) {
                    callbacks.getOnSTTResult().accept(message);
                }
            }

            @Override
            public void onApiResponse(ApiResponseMessage message) {
                super.onApiResponse(message);
                if (callbacks.getOnApiResponse() != null) {
                    callbacks.getOnApiResponse().accept(message);
                }
            }

            @Override
            public void onTTSAudio(TTSMessage message) {
                super.onTTSAudio(message);
                if (callbacks.getOnTTSAudio() != null) {
                    callbacks.getOnTTSAudio().accept(message);
                }
            }

            @Override
            public void onTTSInterrupt(TTSInterruptMessage message) {
                super.onTTSInterrupt(message);
                if (callbacks.getOnTTSInterrupt() != null) {
                    callbacks.getOnTTSInterrupt().accept(message);
                }
            }

            @Override
            public void onSilenceTimeout(SilenceTimeoutMessage message) {
                super.onSilenceTimeout(message);
                if (callbacks.getOnSilenceTimeout() != null) {
                    callbacks.getOnSilenceTimeout().accept(message);
                }
            }

            @Override
            public void onErrorMessage(ErrorMessage message) {
                super.onErrorMessage(message);
                if (callbacks.getOnErrorMessage() != null) {
                    callbacks.getOnErrorMessage().accept(message);
                }
            }

            @Override
            public void onDisconnected(String clientId, int code, String reason) {
                super.onDisconnected(clientId, code, reason);
                if (callbacks.getOnDisconnected() != null) {
                    callbacks.getOnDisconnected().accept(clientId);
                }
            }

            @Override
            public void onError(String clientId, Exception ex) {
                super.onError(clientId, ex);
                if (callbacks.getOnError() != null) {
                    callbacks.getOnError().accept(ex);
                }
            }
        };

        return webSocketBridgeManager.createAndInitializeConnection(request, eventHandler);
    }

    /**
     * 发送音频数据
     * 
     * @param request AI Agent初始化请求
     * @param audioData 音频字节数组
     * @return 发送是否成功
     */
    public boolean sendAudioData(AiAgentInitRequest request, byte[] audioData) {
        return webSocketBridgeManager.sendAudioData(request, audioData);
    }

    /**
     * 发送STT音频消息
     * 
     * @param clientId 客户端ID
     * @param audioData Base64编码的音频数据
     * @return 发送是否成功
     */
    public boolean sendSTTMessage(String clientId, String audioData) {
        return webSocketBridgeService.sendSTTAudio(clientId, audioData);
    }

    /**
     * 检查连接状态
     * 
     * @param request AI Agent初始化请求
     * @return 是否已连接
     */
    public boolean isConnected(AiAgentInitRequest request) {
        return webSocketBridgeManager.isConnected(request);
    }

    /**
     * 关闭连接
     * 
     * @param request AI Agent初始化请求
     */
    public void closeConnection(AiAgentInitRequest request) {
        webSocketBridgeManager.closeConnection(request);
    }

    /**
     * 获取所有活跃连接
     * 
     * @return 活跃连接的客户端ID集合
     */
    public Set<String> getActiveConnections() {
        return webSocketBridgeService.getActiveClientIds();
    }

    /**
     * 获取连接统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getConnectionStats() {
        return webSocketBridgeManager.getConnectionStats();
    }

    /**
     * 关闭所有连接
     */
    public void closeAllConnections() {
        webSocketBridgeManager.closeAllConnections();
    }

    /**
     * WebSocket事件回调集合
     */
    public static class WebSocketEventCallbacks {
        private Consumer<String> onConnected;
        private Consumer<InitMessage> onInitResponse;
        private Consumer<STTResultMessage> onSTTResult;
        private Consumer<ApiResponseMessage> onApiResponse;
        private Consumer<TTSMessage> onTTSAudio;
        private Consumer<TTSInterruptMessage> onTTSInterrupt;
        private Consumer<SilenceTimeoutMessage> onSilenceTimeout;
        private Consumer<ErrorMessage> onErrorMessage;
        private Consumer<String> onDisconnected;
        private Consumer<Exception> onError;

        // Getters and Setters
        public Consumer<String> getOnConnected() { return onConnected; }
        public void setOnConnected(Consumer<String> onConnected) { this.onConnected = onConnected; }

        public Consumer<InitMessage> getOnInitResponse() { return onInitResponse; }
        public void setOnInitResponse(Consumer<InitMessage> onInitResponse) { this.onInitResponse = onInitResponse; }

        public Consumer<STTResultMessage> getOnSTTResult() { return onSTTResult; }
        public void setOnSTTResult(Consumer<STTResultMessage> onSTTResult) { this.onSTTResult = onSTTResult; }

        public Consumer<ApiResponseMessage> getOnApiResponse() { return onApiResponse; }
        public void setOnApiResponse(Consumer<ApiResponseMessage> onApiResponse) { this.onApiResponse = onApiResponse; }

        public Consumer<TTSMessage> getOnTTSAudio() { return onTTSAudio; }
        public void setOnTTSAudio(Consumer<TTSMessage> onTTSAudio) { this.onTTSAudio = onTTSAudio; }

        public Consumer<TTSInterruptMessage> getOnTTSInterrupt() { return onTTSInterrupt; }
        public void setOnTTSInterrupt(Consumer<TTSInterruptMessage> onTTSInterrupt) { this.onTTSInterrupt = onTTSInterrupt; }

        public Consumer<SilenceTimeoutMessage> getOnSilenceTimeout() { return onSilenceTimeout; }
        public void setOnSilenceTimeout(Consumer<SilenceTimeoutMessage> onSilenceTimeout) { this.onSilenceTimeout = onSilenceTimeout; }

        public Consumer<ErrorMessage> getOnErrorMessage() { return onErrorMessage; }
        public void setOnErrorMessage(Consumer<ErrorMessage> onErrorMessage) { this.onErrorMessage = onErrorMessage; }

        public Consumer<String> getOnDisconnected() { return onDisconnected; }
        public void setOnDisconnected(Consumer<String> onDisconnected) { this.onDisconnected = onDisconnected; }

        public Consumer<Exception> getOnError() { return onError; }
        public void setOnError(Consumer<Exception> onError) { this.onError = onError; }
    }
}
