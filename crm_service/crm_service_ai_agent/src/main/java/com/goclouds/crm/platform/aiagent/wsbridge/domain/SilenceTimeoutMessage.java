package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 静默超时消息
 * 
 * <AUTHOR> Agent
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SilenceTimeoutMessage extends BaseMessage {

    /**
     * 静默持续时间（秒）
     */
    @JsonProperty("timeout_duration")
    private Double timeoutDuration;

    /**
     * 执行的动作类型
     */
    @JsonProperty("action")
    private String action;

    public SilenceTimeoutMessage() {
        super("silence_timeout", null);
    }
}
