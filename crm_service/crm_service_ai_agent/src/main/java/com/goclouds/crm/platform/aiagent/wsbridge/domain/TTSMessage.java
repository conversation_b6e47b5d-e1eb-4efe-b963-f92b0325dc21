package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TTS音频消息
 * 
 * <AUTHOR> Agent
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TTSMessage extends BaseMessage {

    /**
     * Base64编码的音频数据
     */
    @JsonProperty("audio_data")
    private String audioData;

    /**
     * 消息唯一标识符
     */
    @JsonProperty("msg_id")
    private String msgId;

    /**
     * 音频片段序号
     */
    @JsonProperty("sequence")
    private Integer sequence;

    /**
     * 是否为最后一个音频片段
     */
    @JsonProperty("is_final")
    private Boolean isFinal;

    public TTSMessage() {
        super("tts_audio", null);
    }
}
