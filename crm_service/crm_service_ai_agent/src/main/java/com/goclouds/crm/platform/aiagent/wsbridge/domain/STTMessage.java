package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 语音识别消息
 * 
 * <AUTHOR> Agent
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class STTMessage extends BaseMessage {

    /**
     * Base64编码的音频数据
     */
    @JsonProperty("audio_data")
    private String audioData;

    public STTMessage() {
        super("stt_audio", null);
    }

    public STTMessage(String clientId, String audioData) {
        super("stt_audio", clientId);
        this.audioData = audioData;
    }
}
