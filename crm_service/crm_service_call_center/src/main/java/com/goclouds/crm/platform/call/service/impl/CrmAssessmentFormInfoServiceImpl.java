package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentFormInfoMapper;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentFormVersionMapper;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentRuleMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordTypeDefService;
import com.goclouds.crm.platform.call.service.CrmAssessmentFormInfoService;
import com.goclouds.crm.platform.call.service.CrmTicketAssessmentRecordService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.client.system.CompanyClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelConfigVO;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelDefForSlaVO;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysCompanyVo;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import com.openhtmltopdf.extend.FSSupplier;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.thymeleaf.TemplateEngine;
import org.springframework.core.io.ClassPathResource;
import java.io.*;

import org.thymeleaf.context.Context;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;
import org.json.JSONArray;
import org.json.JSONObject;
import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.goclouds.crm.platform.common.utils.ConvertUtil.toDouble;

/**
 * 评估表基本信息Service实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CrmAssessmentFormInfoServiceImpl extends ServiceImpl<CrmAssessmentFormInfoMapper, CrmAssessmentFormInfo>
        implements CrmAssessmentFormInfoService {

    private final CrmAssessmentFormVersionMapper versionMapper;
    private final ChannelClient channelClient;
    private final CrmAgentWorkRecordTypeDefService recordTypeDefService;
    private final CrmAssessmentRuleMapper ruleMapper;
    private final CrmTicketAssessmentRecordService crmTicketAssessmentRecordService;
    private final CompanyClient companyClient;
    private TemplateEngine templateEngine;

    @PostConstruct
    public void initTemplateEngine() {
        ClassLoaderTemplateResolver templateResolver = new ClassLoaderTemplateResolver();
        templateResolver.setPrefix("pdf/template/"); // 移除 classpath: 前缀
        templateResolver.setSuffix(".html");
        templateResolver.setCharacterEncoding("UTF-8");
        templateResolver.setTemplateMode(TemplateMode.HTML);
        templateResolver.setCacheable(false);

        this.templateEngine = new TemplateEngine();
        this.templateEngine.setTemplateResolver(templateResolver);
    }

    @Override
    public IPage<CrmAssessmentFormInfoVo> queryFormInfoPages(IPage<Object> pageParam, CrmAssessmentFormInfo formInfo) {
        // 构建分页条件
        Page<CrmAssessmentFormInfo> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());

        // 构建查询条件
        LambdaQueryWrapper<CrmAssessmentFormInfo> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询未删除的数据
        queryWrapper.eq(CrmAssessmentFormInfo::getDataStatus, 1);

        queryWrapper.eq(CrmAssessmentFormInfo::getCompanyId, SecurityUtil.getLoginUser().getCompanyId());
        queryWrapper.orderByDesc(CrmAssessmentFormInfo::getCreateTime);
        // 执行查询
        IPage<CrmAssessmentFormInfo> resultPage = this.baseMapper.selectPage(page, queryWrapper);

        // 创建返回的VO分页对象
        Page<CrmAssessmentFormInfoVo> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        List<CrmAssessmentFormInfoVo> voList = new ArrayList<>();

        // 查询完成后并转换为VO
        if (resultPage.getRecords() != null && !resultPage.getRecords().isEmpty()) {
            for (CrmAssessmentFormInfo info : resultPage.getRecords()) {
                CrmAssessmentFormInfoVo vo = new CrmAssessmentFormInfoVo();
                // 复制基本属性
                BeanUtils.copyProperties(info, vo);

                // 查询最新版本
                LambdaQueryWrapper<CrmAssessmentFormVersion> versionWrapper = new LambdaQueryWrapper<>();
                versionWrapper.eq(CrmAssessmentFormVersion::getAssessmentId, info.getAssessmentId());
                versionWrapper.orderByDesc(CrmAssessmentFormVersion::getCreateTime);
                versionWrapper.last("LIMIT 1");

                CrmAssessmentFormVersion latestVersion = versionMapper.selectOne(versionWrapper);
                if (latestVersion != null) {
                    // 设置最新版本号
                    vo.setLatestVersionNo(latestVersion.getVersionNo());
                    // 设置部署状态
                    vo.setDeployStatus(latestVersion.getDeployStatus());
                    vo.setVersionId(latestVersion.getVersionId());
                    LambdaQueryWrapper<CrmAssessmentRule> ruleWrapper = new LambdaQueryWrapper<>();
                    ruleWrapper.eq(CrmAssessmentRule::getVersionId, latestVersion.getVersionId());
                    ruleWrapper.eq(CrmAssessmentRule::getDataStatus,1);
                    List<CrmAssessmentRule> ruleList = ruleMapper.selectList(ruleWrapper);
                    vo.setRuleCount(ruleList.size());
                }
                if (vo.getChannelConfigId() != null) {
                    List<String> channelTypeIdList = Arrays.asList(vo.getChannelConfigId().split(","));
                    Map<Integer, AssessmentChannelVo> groupedMap = new LinkedHashMap<>();
                    Set<Integer> codeSet = new HashSet<>();

                    // 1. 遍历所有 channelTypeId，先收集所有 CrmChannelConfigVO 数据
                    for (String channelTypeId : channelTypeIdList) {
                        R<CrmChannelConfigVO> result = channelClient.queryChannelConfig(channelTypeId);
                        if (result.getCode() == AjaxResult.SUCCESS && result.getData() != null) {
                            CrmChannelConfigVO data = result.getData();
                            Integer code = data.getChannelType();
                            codeSet.add(code);

                            AssessmentChanneInfolVo infoVo = new AssessmentChanneInfolVo();
                            infoVo.setChannelId(data.getChannelId());
                            infoVo.setName(data.getName());
                            infoVo.setChannelType(code);

                            if (!groupedMap.containsKey(code)) {
                                AssessmentChannelVo channelVo = new AssessmentChannelVo();
                                channelVo.setCode(code);
                                channelVo.setChannelVOList(new ArrayList<>());
                                groupedMap.put(code, channelVo);
                            }
                            groupedMap.get(code).getChannelVOList().add(infoVo);
                        }
                    }

                    // 2. 调用 queryChannelDefList 接口，获取渠道类型名称
                    List<String> codeList = codeSet.stream().map(String::valueOf).collect(Collectors.toList());
                    if(!codeList.isEmpty()) {
                        R<List<CrmChannelDefForSlaVO>> defResult = channelClient.queryChannelDefList(codeList);
                        if (defResult.getCode() == AjaxResult.SUCCESS && defResult.getData() != null) {
                            Map<Integer, String> codeNameMap = defResult.getData().stream()
                                    .collect(Collectors.toMap(
                                            item -> Integer.valueOf(item.getCode()),
                                            CrmChannelDefForSlaVO::getName,
                                            (v1, v2) -> v1
                                    ));

                            for (Map.Entry<Integer, AssessmentChannelVo> entry : groupedMap.entrySet()) {
                                Integer code = entry.getKey();
                                String name = codeNameMap.get(code);
                                if (name != null) {
                                    entry.getValue().setName(name);
                                }
                            }
                        }
                    }
                    vo.setChannelInfoList(new ArrayList<>(groupedMap.values()));
                    vo.setChannelList(channelTypeIdList);
                }



                if (info.getTicketType() != null && !info.getTicketType().isEmpty()) {
                    List<String> ticketTypeList = Arrays.asList(info.getTicketType().split(","));
                    List<String> ticketTypeNameList = new ArrayList<>();

                    for (String ticketType : ticketTypeList) {
                        CrmAgentWorkRecordTypeDef typeDef = recordTypeDefService.gatWorkRecordTypeInfo(ticketType);
                        if (typeDef != null && typeDef.getWorkRecordTypeName() != null) {
                            ticketTypeNameList.add(typeDef.getWorkRecordTypeName());
                        }
                    }

                    vo.setTicketType(ticketTypeList);
                    vo.setTicketTypeName(ticketTypeNameList);
                }

                if (info.getRaters() != null && !info.getRaters().isEmpty()) {
                    vo.setRaters(Arrays.asList(info.getRaters().split(",")));
                }
                voList.add(vo);
            }
        }

        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public List<CrmAssessmentFormInfo> listEnabledFormInfos() {
        // 查询所有启用的评估表
        LambdaQueryWrapper<CrmAssessmentFormInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentFormInfo::getStatus, 1);
        queryWrapper.eq(CrmAssessmentFormInfo::getDataStatus, 0);
        queryWrapper.orderByDesc(CrmAssessmentFormInfo::getCreateTime);

        return this.list(queryWrapper);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveOrUpdateFormInfo(CrmAssessmentFormInfoVo formInfoVo) {
        CrmAssessmentFormInfo formInfo = new CrmAssessmentFormInfo();
        BeanUtils.copyProperties(formInfoVo, formInfo);

        String companyId = SecurityUtil.getLoginUser().getCompanyId();

        List<String> channelList = formInfoVo.getChannelList();
        if (CollectionUtils.isNotEmpty(channelList)) {
            String channelTypeIdStr = channelList.stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(","));
            formInfo.setChannelConfigId(channelTypeIdStr);
        }

        if (CollectionUtils.isNotEmpty(formInfoVo.getTicketType())) {
            formInfo.setTicketType(String.join(",", formInfoVo.getTicketType()));
        }
        if (CollectionUtils.isNotEmpty(formInfoVo.getRaters())) {
            formInfo.setRaters(String.join(",", formInfoVo.getRaters()));
        }

        boolean isUpdate = StringUtils.isNotBlank(formInfo.getAssessmentId());

        //检查重复名称（排除当前 ID）
        LambdaQueryWrapper<CrmAssessmentFormInfo> query = new LambdaQueryWrapper<>();
        query.eq(CrmAssessmentFormInfo::getCompanyId, companyId)
                .eq(CrmAssessmentFormInfo::getAssessmentName, formInfo.getAssessmentName());
        if (isUpdate) {
            query.ne(CrmAssessmentFormInfo::getAssessmentId, formInfo.getAssessmentId());
        }
        long count = this.count(query);
        if (count > 0) {
            return AjaxResult.failure("当前公司下已存在相同名称的评估表，请修改名称！");
        }

        if (isUpdate) {
            formInfo.setModifyTime(LocalDateTime.now());
            this.updateById(formInfo);
            return AjaxResult.ok(formInfo);
        } else {
            formInfo.setCompanyId(companyId);
            formInfo.setAssessmentId(UUID.randomUUID().toString().replace("-", ""));
            formInfo.setCreateTime(LocalDateTime.now());
            if (formInfo.getStatus() == null) {
                formInfo.setStatus(1);
            }
            this.save(formInfo);
            return AjaxResult.ok(formInfo);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<String> deleteFormInfo(String assessmentId) {
        // 逻辑删除，将数据状态更新为已删除
        CrmAssessmentFormInfo formInfo = new CrmAssessmentFormInfo();
        formInfo.setAssessmentId(assessmentId);
        formInfo.setDataStatus(0);
        formInfo.setModifyTime(LocalDateTime.now());
        this.updateById(formInfo);
        return  AjaxResult.ok("删除成功");
    }

    @Override
    public CrmAssessmentFormInfoVo getFormInfoDetail(String assessmentId) {
        CrmAssessmentFormInfoVo formInfoVo = new CrmAssessmentFormInfoVo();
        // 查询单个评估表详情
         this.getById(assessmentId);
         BeanUtils.copyProperties(formInfoVo, formInfoVo);
         return formInfoVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateFormStatus(String assessmentId) {

        // 查询评估表
        CrmAssessmentFormInfo formInfo = this.getById(assessmentId);
        if (formInfo == null) {
            return "评估表信息不存在";
        }

        // 切换状态（1=启用,0=停用）
        Integer currentStatus = formInfo.getStatus();
        Integer newStatus = (currentStatus != null && currentStatus == 1) ? 0 : 1;

        // 更新状态
        CrmAssessmentFormInfo updateInfo = new CrmAssessmentFormInfo();
        updateInfo.setAssessmentId(assessmentId);
        updateInfo.setStatus(newStatus);
        updateInfo.setModifyTime(LocalDateTime.now());

        boolean result = this.updateById(updateInfo);

        if (result) {
            return newStatus == 1 ? "评估表已启用" : "评估表已停用";
        } else {
            return "操作失败";
        }

    }
    @Override
    public byte[] generateAndSavePdf(String recordId) throws Exception {
        log.info("开始生成PDF，记录ID: {}", recordId);

        // 获取评估详情数据
        AssessmentDetailVO record = crmTicketAssessmentRecordService.assessmentDetailRecord(recordId);

        // 处理评分颜色
        String scoreColor = getScoreColor(record.getTotalScore().doubleValue(), record.getScoreColor());

        // 格式化日期时间
        formatDateTime(record);

        // 处理null值和分数格式
        processNullValues(record);

        // 准备模板数据
        Map<String, Object> viewData = new HashMap<>();
        viewData.put("record", record);

        // 处理分类数据，包括添加编号
        List<Map<String, Object>> processedCategories = processCategoriesForHtml(record.getCategories(), "");
        viewData.put("categories", processedCategories);
        viewData.put("scoreColor", scoreColor);

        // 图片资源转Base64
        String homeBgPath = "pdf/img/home.jpg";
        String endPngPath = "pdf/img/end.jpg";
        String logoPath = "pdf/img/logo.png";
        String aiIconPath = "pdf/img/ai-icon.png";
        String manualIconPath = "pdf/img/manual-icon.png";

        // 获取公司logo
        R<SysCompanyVo> sysCompanyVoR = companyClient.queryCompanyById(SecurityUtil.getLoginUser().getCompanyId());
        if (AjaxResult.SUCCESS == sysCompanyVoR.getCode()) {
            SysCompanyVo sysCompanyVo = sysCompanyVoR.getData();
            if(!StringUtils.isEmpty(sysCompanyVo.getCompanyLogo())) { // 修正了逻辑判断
                logoPath = sysCompanyVo.getCompanyLogo();
            }
        }

        // 转换图片为Base64
        String homeBgBase64 = imageToBase64(homeBgPath);
        String endPngBase64 = imageToBase64(endPngPath);
        String logoPngBase64 = imageToBase64(logoPath);
        String aiIconBase64 = imageToBase64(aiIconPath);
        String manualIconBase64 = imageToBase64(manualIconPath);

        // 添加图片资源到模板数据
        viewData.put("homeBgBase64", homeBgBase64);
        viewData.put("endPngBase64", endPngBase64);
        viewData.put("logoPngBase64", logoPngBase64);
        viewData.put("aiIconBase64", aiIconBase64);
        viewData.put("manualIconBase64", manualIconBase64);

        // 确定使用哪种语言的模板
        String assessmentHtml = "assessment-report-en";
        Map<String, String> languageTemplateMap = new HashMap<>();
        languageTemplateMap.put("en-US", "assessment-report-en");
        languageTemplateMap.put("de-DE", "assessment-report-de");
        languageTemplateMap.put("ja", "assessment-report-ja");
        languageTemplateMap.put("zh-CN", "assessment-report-zh");
        assessmentHtml = languageTemplateMap.getOrDefault(ServletUtils.getHeaderLanguage(), assessmentHtml);

        // ==================== 第一阶段：生成临时PDF用于计算页数 ====================

        log.debug("第一阶段：生成临时PDF用于计算页数");

        // 在第一阶段，不需要传递totalPages变量，因为我们只需要计算页数
        // 使用Thymeleaf渲染临时HTML
        Context tempContext = new Context(Locale.getDefault(), viewData);
        String tempHtml = templateEngine.process(assessmentHtml, tempContext);

        // 替换模板中的变量
        String defaultBase64Image = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
        tempHtml = tempHtml.replace("${homeBgBase64}", homeBgBase64 != null ? homeBgBase64 : defaultBase64Image);
        tempHtml = tempHtml.replace("${endPngBase64}", endPngBase64 != null ? endPngBase64 : defaultBase64Image);
        tempHtml = tempHtml.replace("${logoPngBase64}", logoPngBase64 != null ? logoPngBase64 : defaultBase64Image);
        tempHtml = tempHtml.replace("${aiIconBase64}", aiIconBase64 != null ? aiIconBase64 : defaultBase64Image);
        tempHtml = tempHtml.replace("${manualIconBase64}", manualIconBase64 != null ? manualIconBase64 : defaultBase64Image);
        tempHtml = tempHtml.replace("${scoreColor}", scoreColor != null ? scoreColor : "#333333");

        // 生成临时PDF
        byte[] tempPdfBytes = generatePdf(tempHtml);

        // 计算实际总页数
        int actualTotalPages = countPages(tempPdfBytes);
        log.info("计算出实际总页数: {} 页", actualTotalPages);

        // ==================== 第二阶段：使用实际页数重新渲染最终PDF ====================

        log.debug("第二阶段：使用实际页数重新渲染最终PDF");

        // 添加实际总页数到模板数据
        actualTotalPages=actualTotalPages-2;
        viewData.put("totalPages", actualTotalPages);

        // 重新渲染HTML
        Context finalContext = new Context(Locale.getDefault(), viewData);
        String finalHtml = templateEngine.process(assessmentHtml, finalContext);

        // 替换模板中的变量
        finalHtml = finalHtml.replace("${homeBgBase64}", homeBgBase64 != null ? homeBgBase64 : defaultBase64Image);
        finalHtml = finalHtml.replace("${endPngBase64}", endPngBase64 != null ? endPngBase64 : defaultBase64Image);
        finalHtml = finalHtml.replace("${logoPngBase64}", logoPngBase64 != null ? logoPngBase64 : defaultBase64Image);
        finalHtml = finalHtml.replace("${aiIconBase64}", aiIconBase64 != null ? aiIconBase64 : defaultBase64Image);
        finalHtml = finalHtml.replace("${manualIconBase64}", manualIconBase64 != null ? manualIconBase64 : defaultBase64Image);
        finalHtml = finalHtml.replace("${scoreColor}", scoreColor != null ? scoreColor : "#333333");

        // 生成最终PDF
        byte[] finalPdfBytes = generatePdf(finalHtml);

        // 添加页码
        finalPdfBytes = addPageNumbers(finalPdfBytes);

        log.info("PDF生成完成，大小: {} bytes", finalPdfBytes != null ? finalPdfBytes.length : 0);
        return finalPdfBytes;
    }

    /**
     * 将图片转换为Base64编码
     */
    private String imageToBase64(String imagePath) {
        try {
            Resource resource = new ClassPathResource(imagePath);
            if (!resource.exists()) {
                log.warn("找不到图片资源: {}", imagePath);
                // 返回一个1x1像素的透明PNG
                return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
            }
            byte[] imageBytes = IOUtils.toByteArray(resource.getInputStream());
            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            log.error("转换图片到Base64失败: " + imagePath, e);
            // 返回一个1x1像素的透明PNG
            return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
        }
    }

    /**
     * 生成PDF文件
     */
    private byte[] generatePdf(String html) throws Exception {
        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.useFastMode();
            builder.withHtmlContent(html, "classpath:/");

            // 加载字体
            try {
                Resource fontResource = new ClassPathResource("pdf/font/msyh.ttf");
                builder.useFont(() -> {
                    try {
                        return fontResource.getInputStream();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }, "Microsoft YaHei");
                log.debug("成功加载微软雅黑字体");
            } catch (Exception e) {
                log.warn("无法加载字体文件，将使用默认字体: {}", e.getMessage());
                // 出错时继续，使用默认字体
            }

            builder.toStream(os);
            builder.run();
            log.debug("PDF基础渲染完成，大小: {} bytes", os.size());

            return os.toByteArray();
        }
    }

    /**
     * 向PDF添加页码
     */
    private byte[] addPageNumbers(byte[] pdfBytes) throws IOException {
        if (pdfBytes == null || pdfBytes.length == 0) {
            log.error("无法添加页码：PDF数据为空");
            return pdfBytes;
        }

        try (PDDocument document = PDDocument.load(pdfBytes);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            int pageCount = document.getNumberOfPages();
            log.debug("开始添加页码，共 {} 页", pageCount);

            // 加载字体
            PDType0Font font;
            try {
                Resource fontResource = new ClassPathResource("pdf/font/msyh.ttf");
                font = PDType0Font.load(document, fontResource.getInputStream());
            } catch (Exception e) {
                log.warn("加载字体失败，使用默认字体: {}", e.getMessage());
                // 使用内置字体
                font = PDType0Font.load(document, PDDocument.class.getResourceAsStream(
                        "/org/apache/pdfbox/resources/ttf/LiberationSans-Regular.ttf"));
            }

            // 从第二页开始添加页码（第一页是封面），但不包括最后一页
            for (int i = 1; i < pageCount - 1; i++) {
                PDPage page = document.getPage(i);
                try (PDPageContentStream contentStream = new PDPageContentStream(
                        document, page, PDPageContentStream.AppendMode.APPEND, true, true)) {

                    // 使用"第X页"格式
                    String text;
                    if(ServletUtils.getHeaderLanguage().equalsIgnoreCase("zh-CN")) {
                        text = "第" + i + "页";
                    }else {
                        text = String.valueOf(i);
                    }
                    // 计算文本宽度，使其居中
                    float textWidth = font.getStringWidth(text) / 1000 * 10;
                    float pageWidth = page.getMediaBox().getWidth();
                    float xPosition = (pageWidth - textWidth) / 2;

                    contentStream.beginText();
                    contentStream.setFont(font, 10);
                    contentStream.setNonStrokingColor(0, 0, 0); // 黑色文本
                    contentStream.newLineAtOffset(xPosition, 30); // 页脚位置
                    contentStream.showText(text);
                    contentStream.endText();
                }
            }

            document.save(baos);
            log.debug("页码添加完成，最终大小: {} bytes", baos.size());
            return baos.toByteArray();
        }
    }

    /**
     * 计算PDF页数
     */
    private int countPages(byte[] pdfBytes) throws IOException {
        if (pdfBytes == null || pdfBytes.length == 0) {
            log.error("无法计算页数：PDF数据为空");
            return 0;
        }

        try (PDDocument document = PDDocument.load(pdfBytes)) {
            int pageCount = document.getNumberOfPages();
            log.debug("PDF共有 {} 页", pageCount);
            return pageCount;
        } catch (Exception e) {
            log.error("计算PDF页数时发生异常", e);
            return 0;
        }
    }
    /**
     * 根据评分获取颜色
     */
    private String getScoreColor(double score, String scoreColorJson) {
        final String DEFAULT_COLOR = "#13C825"; // 默认绿色

        if (StringUtils.isEmpty(scoreColorJson)) {
            return DEFAULT_COLOR;
        }

        try {
            JSONArray jsonArray = new JSONArray(scoreColorJson);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject colorRange = jsonArray.getJSONObject(i);
                double startNum = colorRange.getDouble("startNum");
                double endNum = colorRange.getDouble("endNum");
                if (score >= startNum && score <= endNum) {
                    return colorRange.getString("colorCode");
                }
            }
        } catch (Exception e) {
            log.error("解析评分颜色失败: {}", e.getMessage());
        }

        return DEFAULT_COLOR;
    }

    /**
     * 格式化评估记录中的日期时间并设置格式化后的字符串字段
     */
    private void formatDateTime(AssessmentDetailVO record) {
        if (record == null) {
            return;
        }

        // 添加字符串格式的日期时间
        if (record.getCreateTime() != null) {
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = outputFormat.format(record.getCreateTime());
            record.setCreateTimeStr(formattedDate);
        } else {
            record.setCreateTimeStr(""); // 设置为空字符串而非null
        }
    }

    /**
     * 处理评估记录中的null值并设置整数分数
     */
    private void processNullValues(AssessmentDetailVO record) {
        if (record == null) {
            return;
        }

        // 处理总分并设置整数值
        if (record.getTotalScore() == null) {
            record.setTotalScore(BigDecimal.ZERO);
            record.setTotalScoreInt(0);
        } else {
            record.setTotalScoreInt(record.getTotalScore().setScale(0, RoundingMode.HALF_UP).intValue());
        }

        // 处理分类和规则
        if (record.getCategories() != null) {
            for (AssessmentCategoryVO category : record.getCategories()) {
                processCategoryNullValues(category);
            }
        }
    }

    /**
     * 处理分类中的null值并设置整数分数
     */
    private void processCategoryNullValues(AssessmentCategoryVO category) {
        if (category == null) {
            return;
        }

        // 处理规则
        if (category.getRules() != null) {
            for (AssessmentRuleVO rule : category.getRules()) {
                // 处理AI分数
                if (rule.getAiScore() == null) {
                    rule.setAiScoreInt(0);
                } else {
                    rule.setAiScoreInt(rule.getAiScore().setScale(0, RoundingMode.HALF_UP).intValue());
                }

                // 处理人工分数
                if (rule.getManualScore() == null) {
                    rule.setManualScoreInt(0);
                } else {
                    rule.setManualScoreInt(rule.getManualScore().setScale(0, RoundingMode.HALF_UP).intValue());
                }

                // 处理其他字段...

                // 处理质检点
                if (rule.getQualityCheckpoints() != null) {
                    for (AssessmentRuleQualityCheckpointVO point : rule.getQualityCheckpoints()) {
                        // 处理质检点分数
                        if (point.getPointScore() == null) {
                            point.setPointScoreInt(0);
                        } else {
                            point.setPointScoreInt(point.getPointScore().setScale(0, RoundingMode.HALF_UP).intValue());
                        }
                    }
                }
            }
        }

        // 递归处理子分类
        if (category.getChildren() != null) {
            for (AssessmentCategoryVO child : category.getChildren()) {
                processCategoryNullValues(child);
            }
        }
    }/**
     * 处理分类数据，添加编号
     */
    private List<Map<String, Object>> processCategoriesForHtml(List<AssessmentCategoryVO> categories, String prefix) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (categories == null || categories.isEmpty()) {
            return result;
        }

        int index = 1;
        for (AssessmentCategoryVO category : categories) {
            Map<String, Object> categoryMap = new HashMap<>();

            // 添加带编号的分类名称
            String numberedPrefix = StringUtils.isEmpty(prefix) ? index + "." : prefix + index + ".";
            categoryMap.put("categoryId", category.getCategoryId());
            categoryMap.put("categoryName", category.getCategoryName());
            categoryMap.put("categoryLevel", category.getCategoryLevel());
            categoryMap.put("numberedCategoryName", numberedPrefix + " " + category.getCategoryName());

            // 处理规则
            List<Map<String, Object>> ruleList = new ArrayList<>();
            if (category.getRules() != null) {
                for (AssessmentRuleVO rule : category.getRules()) {
                    Map<String, Object> ruleMap = new HashMap<>();
                    ruleMap.put("ruleName", rule.getRuleName());
                    ruleMap.put("aiScore", rule.getAiScore());
                    ruleMap.put("manualScore", rule.getManualScore());
                    ruleMap.put("aiScoreAbs", rule.getAiScore() != null ? rule.getAiScore().abs() : BigDecimal.ZERO);
                    ruleMap.put("manualScoreAbs", rule.getManualScore() != null ? rule.getManualScore().abs() : BigDecimal.ZERO);
                    ruleMap.put("reference", rule.getReference());
                    ruleMap.put("aiAssessmentRule", rule.getAiAssessmentRule());
                    ruleMap.put("assessmentRemark", rule.getAssessmentRemark());
                    ruleMap.put("qualityCheckpoints", rule.getQualityCheckpoints());
                    ruleList.add(ruleMap);
                }
            }
            categoryMap.put("rules", ruleList);

            // 递归处理子分类
            List<Map<String, Object>> children = processCategoriesForHtml(
                    category.getChildren(), numberedPrefix);
            categoryMap.put("children", children);

            result.add(categoryMap);
            index++;
        }

        return result;
    }
}