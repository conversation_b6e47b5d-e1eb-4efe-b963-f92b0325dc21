package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilter;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordFilterResponseVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_filter(工单筛选器;)】的数据库操作Mapper
* @createDate 2023-09-19 15:30:11
* @Entity generator.domain.CrmAgentWorkRecordFilter
*/
public interface CrmAgentWorkRecordFilterMapper extends BaseMapper<CrmAgentWorkRecordFilter> {

    /**
     *  工单筛选器列表
     * @param userId 用户ID
     * @return 工单筛选器列表
     */
    @Select("select work_record_filter_id, work_record_filter_name, default_filter from crm_agent_work_record_filter " +
            "            where user_id = #{userId} or default_filter = 1 order by default_filter desc ,create_time desc")
    List<WorkRecordFilterResponseVO> queryWorkRecordFilter( @Param("userId")String userId);
}




