package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAssessmentFormInfo;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentFormInfoVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;

/**
 * 评估表基本信息Service接口
 */
public interface CrmAssessmentFormInfoService extends IService<CrmAssessmentFormInfo> {

    /**
     * 分页查询评估表
     *
     * @param pageParam 分页参数
     * @param formInfo 查询条件
     * @return 分页结果
     */
    IPage<CrmAssessmentFormInfoVo> queryFormInfoPages(IPage<Object> pageParam, CrmAssessmentFormInfo formInfo);

    /**
     * 查询所有启用的评估表
     *
     * @return 评估表列表
     */
    List<CrmAssessmentFormInfo> listEnabledFormInfos();

    /**
     * 保存或更新评估表信息
     *
     * @param formInfoVo 评估表信息VO
     * @return 是否成功
     */
    AjaxResult<CrmAssessmentFormInfo> saveOrUpdateFormInfo(CrmAssessmentFormInfoVo formInfoVo);

    /**
     * 删除评估表
     *
     * @param assessmentId 评估表ID
     * @return 是否成功
     */
    AjaxResult<String> deleteFormInfo(String assessmentId);

    /**
     * 获取评估表详情
     *
     * @param assessmentId 评估表ID
     * @return 评估表详情
     */
    CrmAssessmentFormInfoVo getFormInfoDetail(String assessmentId);


    /**
     * 修改评估表启用状态
     *
     * @param assessmentId 评估表ID
     * @return 是否成功
     */
    String  updateFormStatus(String assessmentId);

    byte[] generateAndSavePdf(String recordId) throws Exception;
}