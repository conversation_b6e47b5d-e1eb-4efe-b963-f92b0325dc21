package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExtDef;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderExtVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_ext_def(工单属性定义表;)】的数据库操作Service
* @createDate 2023-09-20 15:52:31
*/
public interface CrmAgentWorkRecordExtDefService extends IService<CrmAgentWorkRecordExtDef> {

    /**
     * 查询当前登录人所在公司设置的工单扩展属性以及初始化的属性
     * @param queryType 1、正常工单 8、机器人  为8时，则查询带有是否转人工的下拉
     * @return 工单扩展属性以及初始化的属性
     */
    AjaxResult<List<WorkOrderExtVo>> queryList(Integer queryType);

    AjaxResult addWorkOrderExt(WorkOrderExtVo workOrderExtVo);

    AjaxResult updateWorkOrderExt(WorkOrderExtVo workOrderExtVo);

    AjaxResult deleteWorkOrderExt(String workOrderExtDefId,String languageCode);

    AjaxResult updateWorkOrderExtSort(List<WorkOrderExtVo> workOrderExtVoList,String languageCode);

    IPage<CrmAgentWorkRecordExtDef> listWorkOrderExt(IPage<CrmAgentWorkRecordExtDef> page,String languageCode);

    AjaxResult queryWorkOrderExtById(String workOrderExtDefId);

    /**
     *  查询自定义工单属性
     * @return 查询自定义工单属性
     */
    AjaxResult<List<WorkOrderExtVo>> queryDefineExtList();

    List<String> queryExtInfoByCode(String code);

    /**
     *  查询当前用户工单表头
     * @param queryType 查询工单类型类型 1、正常人工 8、机器人
     * @return
     */
    List<WorkOrderExtVo> queryWorkRecordByUserId(String userId, String redisKey,Integer queryType);

    void addUserWorkRecordExt(List<String> workRecordExtDefIds, String redisKey);

    AjaxResult generateDefaultWorkOrderExtProps(String companyId);

    /**
     * 获取当前公司设置的当前浏览器设置的语言下的扩展字段和系统字段，以便表头国际化
     * @return map [code, name]
     */
    Map<String, String> getExtDefMap();

    /**
     * 查询当前用户联络明细表头
     * @return 用户联络明细表头id
     */
    List<WorkOrderExtVo> queryContactDetailByUserId(String userId, String redisKey);

    AjaxResult<List<WorkOrderExtVo>> querySpecialTicketExtDefList(Integer queryType, List<String> extCodeList, List<String> propertyTypeCodeList);

}
