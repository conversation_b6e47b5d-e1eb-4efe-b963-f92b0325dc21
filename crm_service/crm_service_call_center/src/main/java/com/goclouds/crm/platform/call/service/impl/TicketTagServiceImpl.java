package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.TicketTag;
import com.goclouds.crm.platform.call.service.TicketTagService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.domain.call.BatchUpdateTicketTagVo;
import com.goclouds.crm.platform.openfeignClient.domain.call.TicketTagResult;
import com.goclouds.crm.platform.openfeignClient.domain.call.UpdateTicketTagVo;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

/**
 * @Author: sunlinan
 * @Description:
 * @Date: 2025-04-09 14:52
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class TicketTagServiceImpl implements TicketTagService {
    private final RestHighLevelClient restHighLevelClient;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    @Override
    public AjaxResult<List<TicketTagResult>> queryEsTicketTag(String companyId, String ticketId) {
        List<TicketTagResult> resultList = new ArrayList<>();
        try {
            String indexName = ticketIndex + companyId;
            boolean indexExists = headIndexExists(indexName);
            if (!indexExists) {
                createIndex(indexName);
                return AjaxResult.ok(resultList);
            }
            //查询指定工单Id打了哪些标签
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            // 根据前端查询列,查询条件,进行查询条件拼接
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("work_record_id", ticketId));
            searchSourceBuilder.query(boolQueryBuilder);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            long totalCount = response.getHits().getTotalHits().value;
            if (totalCount > 0) {
                SearchHit hit = hits[0];
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                TicketInfoIndex contentIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                List<TicketTag> ticketTagList = contentIndex.getTicketTag();
                if (CollectionUtils.isNotEmpty(ticketTagList)) {
                    for (TicketTag ticketTag : ticketTagList) {
                        TicketTagResult ticketTagResult = new TicketTagResult();
                        BeanUtils.copyProperties(ticketTag, ticketTagResult);
                        resultList.add(ticketTagResult);
                    }
                }
            }
        } catch (Exception e) {
            log.error("工单标签管理-查询Id为{}的工单出现异常：", ticketId, e);
            return AjaxResult.failure(resultList);
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult<Object> updateEsTicketTag(UpdateTicketTagVo updateTicketTag) {
        String companyId = updateTicketTag.getCompanyId();
        String ticketId = updateTicketTag.getTicketId();
        List<TicketTagResult> ticketTagList = updateTicketTag.getTicketTagList();
        try {
            String indexName = ticketIndex + companyId;
            boolean indexExists = headIndexExists(indexName);
            if (!indexExists) {
                createIndex(indexName);
            }
            updateTicketTagsByWorkRecordId(indexName, ticketId, ticketTagList);

        } catch (Exception e) {
            log.error("工单标签管理-更新Id为{}的工单标签出现异常：", ticketId, e);
            return AjaxResult.failure();
        }
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<Object> removeEsTicketTag(String companyId, String ticketId, String removeTagId) {
        try {
            String indexName = ticketIndex + companyId;
            boolean indexExists = headIndexExists(indexName);
            if (!indexExists) {
                createIndex(indexName);
                return AjaxResult.ok();
            }
            // 构建搜索查询，查找 work_record_id 为 999 的文档
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("work_record_id", ticketId));

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);

            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source(searchSourceBuilder);

            // 执行搜索请求
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 遍历搜索结果
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                String documentId = hit.getId();

                // 构建脚本，用于从 nested 字段中移除指定 tag_id 的元素
                String scriptSource = "for (int i = 0; i < ctx._source.ticket_tag.size(); i++) { " +
                        "  if (ctx._source.ticket_tag[i].tag_id == params.tagId) { " +
                        "    ctx._source.ticket_tag.remove(i); " +
                        "    i--; " +
                        "  } " +
                        "}";

                Map<String, Object> scriptParams = new HashMap<>();
                scriptParams.put("tagId", removeTagId);

                Script script = new Script(ScriptType.INLINE, "painless", scriptSource, scriptParams);

                // 构建更新请求
                UpdateRequest updateRequest = new UpdateRequest(indexName, documentId);
                updateRequest.script(script);

                // 执行更新请求
                try {
                    restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                        log.error("es更新工单标签字段数据失败，异常信息：", e);
                        throw new RuntimeException(e);
                    }
                }

                //手动刷新索引（使写入立即可查）
                RefreshRequest refreshRequest = new RefreshRequest(indexName);
                try {
                    restHighLevelClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    log.error("插入强刷索引文档异常", e);
                    throw new RuntimeException(e);
                }
            }

        } catch (IOException e) {
            log.error("工单标签管理-移除工单Id为{}工单中tagId为{}的标签出现异常：", ticketId, removeTagId, e);
            return AjaxResult.failure();
        }
        return AjaxResult.ok();
    }

    //如下方法可以实现，批量删除指定标签后，打的原有的标签不变
    @Override
    public AjaxResult<Object> deleteEsTicketTag(String companyId, String removeTagId) {
        try {
            String indexName = ticketIndex + companyId;
            boolean indexExists = headIndexExists(indexName);
            if (!indexExists) {
                createIndex(indexName);
                return AjaxResult.ok();
            }

            // 创建 UpdateByQueryRequest
            UpdateByQueryRequest request = new UpdateByQueryRequest(indexName);

            // 设置脚本
            Script script = new Script(
                    ScriptType.INLINE,
                    "painless",
                    "if (ctx._source.ticket_tag != null)" +
                            " { ctx._source.ticket_tag.removeIf(tag -> tag.tag_id == params.tag_id); }",
                    Collections.singletonMap("tag_id", removeTagId)
            );
            request.setScript(script);

            // 设置查询条件
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery(
                    "ticket_tag",
                    QueryBuilders.boolQuery().must(
                            QueryBuilders.termQuery("ticket_tag.tag_id", removeTagId)
                    ),
                    ScoreMode.None
            );
            request.setQuery(nestedQuery);

            // 执行请求
            BulkByScrollResponse response = restHighLevelClient.updateByQuery(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("工单标签管理-移除tagId为{}的标签出现异常：", removeTagId, e);
                return AjaxResult.failure();
            }
        }
        return AjaxResult.ok();
    }


    /**
     * 查询索引是否存在
     *
     * @param index 索引名称
     * @return
     */
    private boolean headIndexExists(String index) {
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        } catch (IOException e) {
            log.error("当前索引{}, 是否存在出现异常: {}", index, e);
            return false;
        }
    }


    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        } catch (Exception e) {
            log.error("创建索引报错", e);
        }
    }

    /**
     * 根据work_record_id更新嵌套字段ticket_tag
     *
     * @param indexName     索引名称
     * @param workRecordId  work_record_id值
     * @param ticketTagList 要更新的标签列表
     * @throws IOException
     */
    private void updateTicketTagsByWorkRecordId(String indexName,
                                                String workRecordId,
                                                List<TicketTagResult> ticketTagList) throws IOException {
        // 1. 根据work_record_id查询文档ID
        String documentId = findDocumentIdByWorkRecordId(indexName, workRecordId);

        if (documentId == null) {
            log.info("未找到work_record_id为{}的文档，不进行标签更新操作", workRecordId);
            return;
        }

        // 2. 准备更新请求
        UpdateRequest updateRequest = new UpdateRequest(indexName, documentId);

        // 使用脚本更新(推荐)
        updateWithScript(updateRequest, ticketTagList);

        // 3. 执行更新
        try {
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es更新工单标签字段数据失败，异常信息：", e);
                throw new RuntimeException(e);
            }
        }

        //手动刷新索引（使写入立即可查）
        RefreshRequest refreshRequest = new RefreshRequest(indexName);
        try {
            restHighLevelClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("插入强刷索引文档异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据work_record_id查询文档ID
     */
    private String findDocumentIdByWorkRecordId(String indexName, String workRecordId) throws IOException {
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        // 构建查询条件
        sourceBuilder.query(QueryBuilders.termQuery("work_record_id", workRecordId));
        sourceBuilder.size(1); // 只需要一个结果

        searchRequest.source(sourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        if (searchResponse.getHits().getHits().length == 0) {
            return null;
        }

        // 返回第一个匹配文档的ID
        return searchResponse.getHits().getHits()[0].getId();
    }

    /**
     * 使用脚本更新嵌套字段
     */
    private void updateWithScript(UpdateRequest updateRequest, List<TicketTagResult> ticketTagList) {
        // 构建脚本参数
        Map<String, Object> params = new HashMap<>();
        params.put("tags", convertToNestedFormat(ticketTagList));

        // 构建脚本
        Script script = new Script(
                ScriptType.INLINE,
                "painless",
                "ctx._source.ticket_tag = params.tags",
                params
        );

        updateRequest.script(script);
    }

    /**
     * 将TicketTagResult列表转换为Elasticsearch嵌套格式
     */
    private List<Map<String, Object>> convertToNestedFormat(List<TicketTagResult> ticketTagList) {
        List<Map<String, Object>> nestedList = new ArrayList<>();

        for (TicketTagResult tag : ticketTagList) {
            Map<String, Object> nestedTag = new HashMap<>();
            nestedTag.put("tag_id", tag.getTagId());
            nestedTag.put("tag_name", tag.getTagName());
            nestedList.add(nestedTag);
        }

        return nestedList;
    }

    /**
     * 用于更新es中工单标签中的name
     */
    @Override
    public AjaxResult updateForEsTicketTagName(String companyId, String tagId, String tagContent) {
        String indexName = ticketIndex + companyId;
        boolean indexExists = headIndexExists(indexName);
        if (!indexExists) {
            createIndex(indexName);
            return AjaxResult.ok();
        }
        UpdateByQueryRequest request = new UpdateByQueryRequest(indexName);
        Map<String, Object> params = new HashMap<>();
        params.put("tagId", tagId);
        params.put("newTagName", tagContent);
        StringBuilder scriptBuilder = new StringBuilder();
        scriptBuilder.append("if (ctx._source.ticket_tag != null) {\n")
                .append("    for (int i = 0; i < ctx._source.ticket_tag.length; i++) {\n")
                .append("        if (ctx._source.ticket_tag[i].tag_id == params.tagId) {\n")
                .append("            ctx._source.ticket_tag[i].tag_name = params.newTagName;\n")
                .append("            break;\n")
                .append("        }\n")
                .append("    }\n")
                .append("}");
        String scriptSource = scriptBuilder.toString();

        Script script = new Script(
                ScriptType.INLINE,
                "painless",
                scriptSource,
                params
        );
        NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery(
                "ticket_tag",
                QueryBuilders.boolQuery().must(
                        QueryBuilders.termQuery("ticket_tag.tag_id", params.get("tagId"))
                ),
                ScoreMode.None
        );
        request.setScript(script)
                .setQuery(nestedQuery)
                .setConflicts("proceed");

        // 执行更新
        try {
            BulkByScrollResponse response = restHighLevelClient.updateByQuery(request, RequestOptions.DEFAULT);
            log.info("更新ES中的工单标签name影响文档数: {}", response.getUpdated());
        } catch (IOException e) {
            log.error("更新ES中的工单标签name出现异常:", e);
        }
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<Object> batchUpdateEsTicketTag(BatchUpdateTicketTagVo updateTicketTag) {
        String companyId = updateTicketTag.getCompanyId();
        List<String> removeTagIdList = updateTicketTag.getRemoveTagIdList();
        String tagId = updateTicketTag.getTagId();
        String tagName = updateTicketTag.getTagName();

        String indexName = ticketIndex + companyId;
        boolean indexExists = headIndexExists(indexName);
        if (!indexExists) {
            createIndex(indexName);
            return AjaxResult.ok();
        }

        try {
            updateTags(removeTagIdList, indexName, tagId, tagName);
        } catch (IOException e) {
            log.error("工单标签-批量公开私有标签为标准标签，操作es数据出现异常：", e);
            return AjaxResult.failure();
        }
        return AjaxResult.ok();
    }


    /**
     * 更新ES文档标签
     *
     * @param removeTagIdList 需要移除的tagId列表
     * @return 更新成功的文档ID列表
     */
    public List<String> updateTags(List<String> removeTagIdList, String indexName,
                                   String tagId, String tagName) throws IOException {
        // 1. 查询包含指定tagId的文档
        List<String> docIds = findDocsWithTags(removeTagIdList, indexName);

        // 2. 更新这些文档
        for (String docId : docIds) {
            updateDocTags(docId, removeTagIdList, indexName, tagId, tagName);
        }

        return docIds;
    }

    /**
     * 查询包含指定tagId的文档
     */
    private List<String> findDocsWithTags(List<String> removeTagIdList, String indexName) throws IOException {
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        // 构建查询条件：ticket_tag.tag_id包含在removeTagIdList中的文档
        sourceBuilder.query(QueryBuilders.nestedQuery("ticket_tag",
                QueryBuilders.termsQuery("ticket_tag.tag_id", removeTagIdList),
                ScoreMode.None));

        sourceBuilder.size(10000); // 根据实际情况调整大小
        searchRequest.source(sourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        List<String> docIds = new ArrayList<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            docIds.add(hit.getId());
        }

        return docIds;
    }

    /**
     * 更新单个文档的标签
     */
    private void updateDocTags(String docId, List<String> removeTagIdList,
                               String indexName, String addTagId, String addTagName) {

        UpdateRequest updateRequest = new UpdateRequest(indexName, docId);

        // 使用Map构建更新内容
        Map<String, Object> scriptParams = new HashMap<>();
        scriptParams.put("removeTagIds", removeTagIdList);
        scriptParams.put("newTagId", addTagId);
        scriptParams.put("newTagName", addTagName);

        updateRequest.script(new Script(
                ScriptType.INLINE,
                "painless",
                "ctx._source.ticket_tag.removeIf(tag -> params.removeTagIds.contains(tag.tag_id)); " +
                        "ctx._source.ticket_tag.add(['tag_id': params.newTagId, 'tag_name': params.newTagName]);",
                scriptParams
        ));

        try {
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es修改文档失败，异常信息：", e);
            }
        }
    }
}
