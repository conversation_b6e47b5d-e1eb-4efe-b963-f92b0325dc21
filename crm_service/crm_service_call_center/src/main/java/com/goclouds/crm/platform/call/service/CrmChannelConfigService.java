package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmChannelConfig;
import com.goclouds.crm.platform.call.domain.vo.ChannelConfigMappingDTO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_channel_config(平台渠道配置)】的数据库操作Service
* @createDate 2023-05-25 11:04:45
*/
public interface CrmChannelConfigService extends IService<CrmChannelConfig> {


    List<ChannelConfigMappingDTO> getChannelMappingRules(String instanceId);




}
