package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFile;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordFileMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordFileService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_file(工作记录内容附件)】的数据库操作Service实现
* @createDate 2023-05-25 10:30:34
*/
@Service
public class CrmAgentWorkRecordFileServiceImpl extends ServiceImpl<CrmAgentWorkRecordFileMapper, CrmAgentWorkRecordFile>
    implements CrmAgentWorkRecordFileService {

}




