package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmCallTicketSmartFill;
import com.goclouds.crm.platform.call.service.CrmCallTicketSmartFillService;
import com.goclouds.crm.platform.call.mapper.CrmCallTicketSmartFillMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_call_ticket_smart_fill(智能填单表)】的数据库操作Service实现
* @createDate 2025-04-14 13:57:40
*/
@Service
@RequiredArgsConstructor
public class CrmCallTicketSmartFillServiceImpl extends ServiceImpl<CrmCallTicketSmartFillMapper, CrmCallTicketSmartFill>
    implements CrmCallTicketSmartFillService{
}




