package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAssessmentRule;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentRuleVo;
import java.util.List;

import java.awt.*;


/**
 * 评估规则Service接口
 */
public interface CrmAssessmentRuleService extends IService<CrmAssessmentRule> {

    /**
     * 分页查询评估规则
     *
     * @param pageParam 分页参数
     * @param rule 查询条件
     * @return 分页结果
     */
    IPage<CrmAssessmentRuleVo> queryRulePages(IPage<Object> pageParam, CrmAssessmentRule rule);


    /**
     * 添加规则
     *
     * @param rule 规则信息
     * @return 是否成功
     */
    boolean addRule(CrmAssessmentRule rule);


    Boolean saveOrUpdateRuleInfo(CrmAssessmentRuleVo formInfo);

    Boolean deleteRule(String ruleId);

    CrmAssessmentRuleVo getRuleDetail(String ruleId);

    void removeDraftRulesByAssessmentId(String assessmentId);

    List<CrmAssessmentRuleVo> ruleListByassessmentId(String assessmentId);
}