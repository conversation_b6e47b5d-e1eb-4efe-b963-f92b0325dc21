package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmChannelInfoConfigInst;
import com.goclouds.crm.platform.call.mapper.CrmChannelInfoConfigInstMapper;
import com.goclouds.crm.platform.call.service.CrmChannelInfoConfigInstService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_channel_info_config_inst(渠道详细信息配置)】的数据库操作Service实现
* @createDate 2023-05-25 11:04:45
*/
@Service
public class CrmChannelInfoConfigInstServiceImpl extends ServiceImpl<CrmChannelInfoConfigInstMapper, CrmChannelInfoConfigInst>
    implements CrmChannelInfoConfigInstService {

}




