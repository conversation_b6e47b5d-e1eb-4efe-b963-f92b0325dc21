package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.call.domain.vo.statis.*;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;

public interface ConnectReportService  {
    /**
     * 查询队列纬度座席状态列表指标
     * @return 队列纬度座席状态列表指标
     */
    AjaxResult<IPage<ConnectQueueLatitudeAgentVo>> queryQueueRealTimeIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectQueueLatitudeAgentVo> pageParam);

    /**
     * 查询队列纬度联系人列表指标
     * @return 队列纬度联系人列表指标
     */
    AjaxResult<IPage<ConnectQueueLatitudeContactsVo>> queryQueueContactsIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectQueueLatitudeContactsVo> pageParam);

    /**
     * 查询队列纬度性能列表指标
     * @return 队列纬度性能列表指标
     */
    AjaxResult<IPage<ConnectQueueLatitudePropertyVo>> queryQueuePropertyIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectQueueLatitudePropertyVo> pageParam);

    /**
     * 查询座席纬度座席员列表指标
     * @return 座席纬度座席员列表指标
     */
    AjaxResult<IPage<ConnectAgentLatitudeAgentVo>> queryAgentIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectAgentLatitudeAgentVo> pageParam);

    /**
     * 查询座席纬度联系人列表指标
     * @return 座席纬度联系人列表指标
     */
    AjaxResult<IPage<ConnectAgentLatitudeContactsVo>> queryAgentContactsIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectAgentLatitudeContactsVo> pageParam);

    /**
     * 查询座席纬度性能列表指标
     * @return 座席纬度性能列表指标
     */
    AjaxResult<IPage<ConnectAgentLatitudePropertyVo>> queryAgentPropertyIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectAgentLatitudePropertyVo> pageParam);

    /**
     *  connect座席纬度性能列表
     * @return connect座席纬度性能列表字段属性是否展示
     */
    AjaxResult<ConnectAgentLatitudePropertyAttributeVo> queryAgentPropertyAttribute();

    /**
     *  connect队列纬度性能列表字段
     * @return connect座席纬度性能列表字段属性是否展示
     */
    AjaxResult<ConnectQueueLatitudePropertyAttributeVo> queryQueueAttribute();

    AjaxResult<ConnectAgentLatitudeAgentAttributeVo> queryAgentIndexAttribute();

    /**
     *  修改队列纬度性能默认字段
     * @param propertyAttribute 字段参数
     * @return 修改状态
     */
    AjaxResult<Object> updateQueueAttribute(ConnectQueueLatitudePropertyAttributeVo propertyAttribute);

    /**
     *  修改座席员列表指标字段
     * @param agentAttribute 字段参数
     * @return 修改状态
     */
    AjaxResult<Object> updateAgentIndexAttribute(ConnectAgentLatitudeAgentAttributeVo agentAttribute);

    /**
     *  修改座席员列表性能字段
     * @param agentAttribute 字段参数
     * @return 修改状态
     */
    AjaxResult<Object> updateAgentAttribute(ConnectAgentLatitudePropertyAttributeVo agentAttribute);

    /**
     * 导出队列纬度座席状态列表 list
     * @param connectExportAttribute 请求参数
     * @return
     */
    void exportQueueReal(List<ConnectExportAttributeVO> connectExportAttribute);

    /**
     * 导出队列纬度联系人列表指标 list
     * @param connectExportAttribute 请求参数
     * @return
     */
    void  exportQueueContacts(List<ConnectExportAttributeVO> connectExportAttribute);

    /**
     * 导出队列纬度性能列表指标list
     * @param connectExportAttribute 请求参数
     */
    void exportQueueProperty(List<ConnectExportAttributeVO> connectExportAttribute);

    /**
     * 导出座席纬度座席员列表指标list
     * @param connectExportAttribute 请求参数
     */
    void exportAgentIndex(List<ConnectExportAttributeVO> connectExportAttribute);

    /**
     * 导出座席纬度联系人列表指标list
     * @param connectExportAttribute 请求参数
     */
    void exportAgentContacts(List<ConnectExportAttributeVO> connectExportAttribute);

    /**
     * 导出座席纬度性能列表指标list
     * @param connectExportAttribute 请求参数
     */
    void exportAgentProperty(List<ConnectExportAttributeVO> connectExportAttribute);
}
