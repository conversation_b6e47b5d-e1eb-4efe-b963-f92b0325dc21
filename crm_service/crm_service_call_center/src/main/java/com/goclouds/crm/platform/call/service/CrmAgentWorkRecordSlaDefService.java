package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordSlaDef;
import com.goclouds.crm.platform.call.domain.vo.AddOrUpdateWorkRecordSlaDefRulVo;
import com.goclouds.crm.platform.call.domain.vo.CacheTimeVO;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordSlaDefRulVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import org.apache.ibatis.annotations.Delete;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【crm_agent_work_record_sla_def(工单SLA定义;)】的数据库操作Service
 * @createDate 2023-09-21 16:50:34
 */
public interface CrmAgentWorkRecordSlaDefService extends IService<CrmAgentWorkRecordSlaDef> {

    AjaxResult queryWorkRecordSla();

    AjaxResult addOrUpdateWorkOrderSla(AddOrUpdateWorkRecordSlaDefRulVo workRecordSlaDefRulVo);

    /**
     * @DELETE 已过时-不再使用
     * 根据公司ID、渠道类型、工单类型，查询当前的规则数据，如果没有则返回默认的
     *
     * @param priorityLevelId 优先级ID
     * @return 查询对应的SLA定义
     */
    CrmAgentWorkRecordSlaDef queryLevelSla(String priorityLevelId, String companyId);

    /**
     * 根据公司ID、渠道类型、工单类型，优先级id，查询当前的规则数据，如果没有则返回默认的
     *
     * @return 查询对应的SLA定义
     */
    CrmAgentWorkRecordSlaDef queryLevelSla(String companyId, String channelCode,String workRecordTypeValue,String priorityLevelId);

    AjaxResult removeAllSla(String slaRuleId);

    /**
     * 进行数据缓存
     * @param workRecordId 工单id
     * @param replyType 回复人类型
     * @param now 当前时间
     */
    void redisDataCache(String companyId,String workRecordId, Integer replyType, LocalDateTime now);

    /**
     * 工单的平均时间计算
     * @param workRecordId 工单id
     * @param companyId 公司id
     * @return
     */
    CacheTimeVO calculationTime(String companyId, String workRecordId);

    /**
     * 根据工单id，进行当前工单数据缓存清除
     * @param workRecordId
     */
    void redisDateCacheDelete(String companyId,String workRecordId);
}
