package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmCallAgentStatusDef;
import com.goclouds.crm.platform.call.domain.vo.AgentStatusVO;
import com.goclouds.crm.platform.call.mapper.CrmCallAgentStatusDefMapper;
import com.goclouds.crm.platform.call.service.CrmCallAgentStatusDefService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import javassist.runtime.Desc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【crm_call_agent_status_def(座席状态管理表)】的数据库操作Service实现
 * @createDate 2025-03-10 15:08:34
 */
@Service
public class CrmCallAgentStatusDefServiceImpl extends ServiceImpl<CrmCallAgentStatusDefMapper, CrmCallAgentStatusDef>
        implements CrmCallAgentStatusDefService {

    @Autowired
    private RedisTemplate redisTemplate;

    public static final String KEY = "AGENT_STATUS";

    /**
     * 增加或修改座席状态
     *
     * @param agentStatusVO
     * @return
     */
    @Override
    public AjaxResult addOrUpdateAgentStatus(AgentStatusVO agentStatusVO) {

        if (ObjectUtil.isNull(agentStatusVO.getAgentStatusId())
                ||
                ObjectUtil.isEmpty(agentStatusVO.getAgentStatusId())
                ||
                ObjectUtil.equals(agentStatusVO.getAgentStatusId(), "")
        ) {
            CrmCallAgentStatusDef crmCallAgentStatusDef = BeanUtil.copyProperties(agentStatusVO, CrmCallAgentStatusDef.class);

            //新增
            crmCallAgentStatusDef.setAgentStatusId(
                    UUID.randomUUID().toString().replace("-", "")
            );
            crmCallAgentStatusDef.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            crmCallAgentStatusDef.setCreateTime(new Date());
            crmCallAgentStatusDef.setCreator(SecurityUtil.getUserId());
            crmCallAgentStatusDef.setDataStatus(1);
            crmCallAgentStatusDef.setEnableStatus(1L);
            save(crmCallAgentStatusDef);
        } else {
            //修改
            update(new LambdaUpdateWrapper<CrmCallAgentStatusDef>()
                    .eq(CrmCallAgentStatusDef::getAgentStatusId, agentStatusVO.getAgentStatusId())
                    .set(CrmCallAgentStatusDef::getAgentStatusName, agentStatusVO.getAgentStatusName())
                    .set(CrmCallAgentStatusDef::getDescription, agentStatusVO.getDescription())
                    .set(CrmCallAgentStatusDef::getType, agentStatusVO.getType())
                    .set(CrmCallAgentStatusDef::getEnableStatus, agentStatusVO.getEnableStatus())
                    .set(CrmCallAgentStatusDef::getModifyTime, new Date())
                    .set(CrmCallAgentStatusDef::getModifier, SecurityUtil.getUserId())
            );
        }
        redisOpsValueForAgentStatus();
        return AjaxResult.ok(MessageUtils.get("operate.success"));

    }

    /**
     * 删除座席状态
     *
     * @param agentStatusId
     * @return
     */
    @Override
    public AjaxResult agentStatusDel(String agentStatusId) {
        this.update(new LambdaUpdateWrapper<CrmCallAgentStatusDef>()
                .eq(CrmCallAgentStatusDef::getAgentStatusId, agentStatusId)
                .set(CrmCallAgentStatusDef::getDataStatus, 0)
                .set(CrmCallAgentStatusDef::getModifier, SecurityUtil.getUserId())
                .set(CrmCallAgentStatusDef::getModifyTime, new Date())
        );
        redisOpsValueForAgentStatus();
        return AjaxResult.ok(MessageUtils.get("operate.success"));
    }

    /**
     * 查询并获取当前 未删除、已启用的状态列表
     * 保存到redis中
     *
     * @return
     */
    public void redisOpsValueForAgentStatus() {
        List<CrmCallAgentStatusDef> sourceList = list(
                new LambdaQueryWrapper<CrmCallAgentStatusDef>()
                        .eq(CrmCallAgentStatusDef::getDataStatus, 1)
                        .eq(CrmCallAgentStatusDef::getEnableStatus, 1)
                        .eq(CrmCallAgentStatusDef::getCompanyId,SecurityUtil.getLoginUser().getCompanyId())
        );
        List<AgentStatusVO> agentStatusVOS = BeanUtil.copyToList(sourceList, AgentStatusVO.class);
        redisTemplate.opsForValue().set(KEY+SecurityUtil.getLoginUser().getCompanyId(), agentStatusVOS);
    }

    /**
     * 数据分页查询
     *
     * @return
     */
    @Override
    public IPage<CrmCallAgentStatusDef> queryAgentStatus(IPage<CrmCallAgentStatusDef> pageParam) {
        //暂无限制条件，待扩展
        List<CrmCallAgentStatusDef> init = list(
                new LambdaQueryWrapper<CrmCallAgentStatusDef>()
                        .eq(CrmCallAgentStatusDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
        );
        if (init.size() == 0) {
            initData();
        }
        LambdaQueryWrapper<CrmCallAgentStatusDef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmCallAgentStatusDef::getDataStatus, 1);
        queryWrapper.eq(CrmCallAgentStatusDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId());
        queryWrapper.orderByDesc(CrmCallAgentStatusDef::getCreateTime);
        return this.baseMapper.selectPage(
                pageParam,
                queryWrapper
        );
    }

    public void initData() {
        //首先判断是否已经存在初始化
        if(
                count(
                        new LambdaQueryWrapper<CrmCallAgentStatusDef>()
                                .eq(CrmCallAgentStatusDef::getCompanyId,SecurityUtil.getLoginUser().getCompanyId())
                                .eq(CrmCallAgentStatusDef::getInitStatus,1)
                ) == 0
        ){
            //进行初始化操作，增加两条数据
            //Available  Available state   可路由  初始化
            // Offline Offline state 离线   初始化
            List<CrmCallAgentStatusDef> batch = new ArrayList<>();
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            CrmCallAgentStatusDef available = new CrmCallAgentStatusDef();
            available.setAgentStatusId(
                    UUID.randomUUID().toString().replace("-", "")
            );
            available.setAgentStatusName("Available");
            available.setDescription("Available state");
            available.setEnableStatus(1L);
            available.setType("1");
            available.setInitStatus(1L);
            available.setCompanyId(companyId);
            available.setCreateTime(new Date());
            available.setDataStatus(1);
            batch.add(available);
            CrmCallAgentStatusDef offline = new CrmCallAgentStatusDef();
            offline.setAgentStatusId(
                    UUID.randomUUID().toString().replace("-", "")
            );
            offline.setAgentStatusName("Offline");
            offline.setDescription("Offline state");
            offline.setEnableStatus(1L);
            offline.setType("0");
            offline.setInitStatus(1L);
            offline.setCompanyId(companyId);
            offline.setCreateTime(new Date());
            offline.setDataStatus(1);
            batch.add(offline);
            saveBatch(batch);
        }
        redisOpsValueForAgentStatus();
    }

    @Override
    public void init() {
        initData();
    }
}




