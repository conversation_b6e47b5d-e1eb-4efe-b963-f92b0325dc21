package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilterCondition;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordFilterConditionResponseVO;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordFilterConditionMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordFilterConditionService;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_filter_condition(工单筛选器条件;)】的数据库操作Service实现
* @createDate 2023-09-19 15:10:09
*/
@Service
public class CrmAgentWorkRecordFilterConditionServiceImpl extends ServiceImpl<CrmAgentWorkRecordFilterConditionMapper, CrmAgentWorkRecordFilterCondition>
    implements CrmAgentWorkRecordFilterConditionService {

    @Override
    public List<WorkRecordFilterConditionResponseVO> queryFilterCondition(String workRecordFilterId) {

        return this.baseMapper.queryFilterCondition(workRecordFilterId, SecurityUtil.getLoginUser().getCompanyId(), ServletUtils.getHeaderLanguage());
    }
}




