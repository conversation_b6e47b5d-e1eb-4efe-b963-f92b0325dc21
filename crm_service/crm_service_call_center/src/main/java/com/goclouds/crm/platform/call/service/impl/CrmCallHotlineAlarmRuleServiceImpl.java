package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmCallHotlineAlarmRule;
import com.goclouds.crm.platform.call.service.CrmCallHotlineAlarmRuleService;
import com.goclouds.crm.platform.call.mapper.CrmCallHotlineAlarmRuleMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_call_hotline_alarm_rule(热线指标告警规则表)】的数据库操作Service实现
* @createDate 2025-02-06 13:50:55
*/
@Service
public class CrmCallHotlineAlarmRuleServiceImpl extends ServiceImpl<CrmCallHotlineAlarmRuleMapper, CrmCallHotlineAlarmRule>
    implements CrmCallHotlineAlarmRuleService{

}




