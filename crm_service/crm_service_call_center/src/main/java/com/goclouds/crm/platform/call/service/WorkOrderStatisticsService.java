package com.goclouds.crm.platform.call.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.goclouds.crm.platform.call.domain.vo.AgentStatusSwitchInfoVo;
import com.goclouds.crm.platform.call.domain.vo.statis.SeatingWorkloadNewVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysDeptVo;

import java.util.List;

public interface WorkOrderStatisticsService {

    List<SysDeptVo> queryCallCenterDeptList();

    /**
     *  座席工作量报表汇总
     * @param seatingWorkloadVo 请求参数
     * @return 工作量报表汇总
     */
    AjaxResult<Object> queryAgentWorkloadSummarize(SeatingWorkloadNewVo seatingWorkloadVo);

    /**
     *  机器人工单统计
     * @param seatingWorkloadVo 请求参数
     * @return 机器人工单汇总
     */
    AjaxResult<Object> queryRobotWorkloadSummarize(SeatingWorkloadNewVo seatingWorkloadVo);

    /**
     *  座席工作效率统计
     * @param seatingWorkloadVo 请求参数
     * @return 座席工作效率统计汇总
     */
    AjaxResult<Object> queryAgentWorkingEfficiency(SeatingWorkloadNewVo seatingWorkloadVo);

    /**
     *  座席满意度统计
     * @param seatingWorkloadVo 请求参数
     * @return 座席满意度统计汇总
     */
    AjaxResult<Object> queryAgentSatisfactionLevel(SeatingWorkloadNewVo seatingWorkloadVo);

    AjaxResult agentStatusSwitch(AgentStatusSwitchInfoVo agentStatusSwitchInfoVo) throws JsonProcessingException;
}
