package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAssessmentCategory;
import com.goclouds.crm.platform.call.domain.CrmAssessmentFormVersion;
import com.goclouds.crm.platform.call.domain.CrmAssessmentRule;
import com.goclouds.crm.platform.call.domain.CrmAssessmentRulePoint;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentCategoryVo;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentCategoryMapper;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentFormVersionMapper;
import com.goclouds.crm.platform.call.service.CrmAssessmentCategoryService;
import com.goclouds.crm.platform.call.service.CrmAssessmentRulePointService;
import com.goclouds.crm.platform.call.service.CrmAssessmentRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评估表分类Service实现
 */
@Service
@RequiredArgsConstructor
public class AssessmentCategoryServiceImpl extends ServiceImpl<CrmAssessmentCategoryMapper, CrmAssessmentCategory>
        implements CrmAssessmentCategoryService {

    private final CrmAssessmentFormVersionMapper versionMapper;
    private final CrmAssessmentRuleService ruleService;
    private final CrmAssessmentRulePointService rulePointService;

    @Override
    public IPage<CrmAssessmentCategory> queryCategoryPages(IPage<Object> pageParam, CrmAssessmentCategory category) {
        // 构建分页条件
        Page<CrmAssessmentCategory> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());

        // 构建查询条件
        LambdaQueryWrapper<CrmAssessmentCategory> queryWrapper = new LambdaQueryWrapper<>();

        // 按名称模糊查询
        if (StringUtils.isNotBlank(category.getCategoryName())) {
            queryWrapper.like(CrmAssessmentCategory::getCategoryName, category.getCategoryName());
        }

        // 按版本ID查询
        if (StringUtils.isNotBlank(category.getVersionId())) {
            queryWrapper.eq(CrmAssessmentCategory::getVersionId, category.getVersionId());
        }

        // 按父级ID查询
        if (StringUtils.isNotBlank(category.getParentId())) {
            queryWrapper.eq(CrmAssessmentCategory::getParentId, category.getParentId());
        } else {
            // 如果未指定父级ID，则只查询一级分类（parentId为null的）
            queryWrapper.isNull(CrmAssessmentCategory::getParentId);
        }

        // 按分类等级查询
        if (category.getLevel() != null) {
            queryWrapper.eq(CrmAssessmentCategory::getLevel, category.getLevel());
        }

        // 只查询未删除的数据
        queryWrapper.eq(CrmAssessmentCategory::getDataStatus, 0);

        // 按排序和创建时间排序
        queryWrapper.orderByAsc(CrmAssessmentCategory::getSort);
        queryWrapper.orderByAsc(CrmAssessmentCategory::getCreateTime);

        // 执行查询
        return this.baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<CrmAssessmentCategory> listCategoriesByVersionId(String versionId) {
        // 根据版本ID查询分类列表
        LambdaQueryWrapper<CrmAssessmentCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentCategory::getVersionId, versionId);
        queryWrapper.eq(CrmAssessmentCategory::getDataStatus, 1); // 只查询未删除的数据
        queryWrapper.orderByAsc(CrmAssessmentCategory::getLevel);
        queryWrapper.orderByAsc(CrmAssessmentCategory::getSort);

        return this.list(queryWrapper);
    }


    public List<CrmAssessmentCategory> listCategoriesByAssessmentId(String assessmentId,String versionId) {
        // 根据版本ID查询分类列表
        LambdaQueryWrapper<CrmAssessmentCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentCategory::getAssessmentId, assessmentId);

        List<CrmAssessmentFormVersion> versionList=
                versionMapper.selectList(new LambdaQueryWrapper<CrmAssessmentFormVersion>()
                        .eq(CrmAssessmentFormVersion::getAssessmentId,assessmentId)
                        .orderByDesc(CrmAssessmentFormVersion::getVersionNo));
        if (StringUtils.isNotBlank(versionId)&& !versionList.isEmpty() && !Objects.equals(versionList.get(0).getVersionId(), versionId)) {
            queryWrapper.eq(CrmAssessmentCategory::getVersionId, versionId);
            queryWrapper.eq(CrmAssessmentCategory::getStatus, 0);
        }else {
            queryWrapper.eq(CrmAssessmentCategory::getStatus, 1);
        }
        queryWrapper.eq(CrmAssessmentCategory::getDataStatus, 1); // 只查询未删除的数据
        queryWrapper.orderByAsc(CrmAssessmentCategory::getLevel);
        queryWrapper.orderByAsc(CrmAssessmentCategory::getSort);

        return this.list(queryWrapper);
    }


    @Override
    public List<CrmAssessmentCategory> listCategoriesByParentId(String parentId) {
        // 根据父级ID查询子分类
        LambdaQueryWrapper<CrmAssessmentCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentCategory::getParentId, parentId);
        queryWrapper.eq(CrmAssessmentCategory::getDataStatus, 1); // 只查询未删除的数据
        queryWrapper.orderByAsc(CrmAssessmentCategory::getSort);

        return this.list(queryWrapper);
    }

    @Override
    public List<CrmAssessmentCategory> listTopCategories(String versionId) {
        // 查询顶级分类列表
        LambdaQueryWrapper<CrmAssessmentCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentCategory::getVersionId, versionId);
        queryWrapper.isNull(CrmAssessmentCategory::getParentId).or().eq(CrmAssessmentCategory::getParentId, "");
        queryWrapper.eq(CrmAssessmentCategory::getLevel, 1); // 一级分类
        queryWrapper.eq(CrmAssessmentCategory::getDataStatus, 1); // 只查询未删除的数据
        queryWrapper.orderByAsc(CrmAssessmentCategory::getSort);

        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCategory(CrmAssessmentCategoryVo categoryVo) {
        CrmAssessmentCategory category = new CrmAssessmentCategory();
        BeanUtils.copyProperties(categoryVo, category);

        if (StringUtils.isBlank(category.getCategoryId())) {
            category.setCategoryId(UUID.randomUUID().toString().replace("-", ""));
        }

        category.setCreateTime(LocalDateTime.now());
        category.setDataStatus(1);


        LambdaQueryWrapper<CrmAssessmentCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentCategory::getAssessmentId, category.getAssessmentId());
        queryWrapper.eq(CrmAssessmentCategory::getDataStatus, 1);
        queryWrapper.orderByDesc(CrmAssessmentCategory::getSort);
        queryWrapper.last("limit 1");

        CrmAssessmentCategory lastCategory = this.getOne(queryWrapper);

        int nextSort = 0;
        if (lastCategory != null && lastCategory.getSort() != null) {
            nextSort = lastCategory.getSort() + 1;
        }

        if (category.getSort() == null || category.getSort() <= nextSort) {
            category.setSort(nextSort);
        }

//        List<CrmAssessmentFormVersion> versionList=
//                versionMapper.selectList(new LambdaQueryWrapper<CrmAssessmentFormVersion>()
//                        .eq(CrmAssessmentFormVersion::getAssessmentId,categoryVo.getAssessmentId())
//                        .orderByDesc(CrmAssessmentFormVersion::getVersionNo));
//
//        if (versionList.isEmpty()) {
//            CrmAssessmentCategory newCategory = new CrmAssessmentCategory();
//            BeanUtils.copyProperties(category, newCategory);
//            newCategory.setCategoryId(UUID.randomUUID().toString().replace("-", ""));
//            newCategory.setStatus(0);
//            newCategory.setSort(category.getSort());
//            this.save(newCategory);
//        }
        return this.save(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(CrmAssessmentCategoryVo categoryVo) {
        if (StringUtils.isBlank(categoryVo.getCategoryId())) {
            return false;
        }

        CrmAssessmentCategory category = new CrmAssessmentCategory();
        BeanUtils.copyProperties(categoryVo, category);

        // 设置修改时间
        category.setModifyTime(LocalDateTime.now());

        return this.updateById(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(String categoryId) {
        if (StringUtils.isBlank(categoryId)) {
            return false;
        }

        // 1. 递归删除所有子分类及其关联规则
        deleteCategoryAndChildren(categoryId);

        // 2. 删除当前分类关联的规则
        deleteCategoryRules(categoryId);

        // 3. 逻辑删除当前分类
        CrmAssessmentCategory category = new CrmAssessmentCategory();
        category.setCategoryId(categoryId);
        category.setDataStatus(0);
        category.setModifyTime(LocalDateTime.now());

        return this.updateById(category);
    }

    /**
     * 递归删除分类及其子分类
     */
    private void deleteCategoryAndChildren(String categoryId) {
        // 查询所有子分类
        LambdaQueryWrapper<CrmAssessmentCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentCategory::getParentId, categoryId);
        List<CrmAssessmentCategory> children = this.list(queryWrapper);

        // 递归删除每个子分类
        for (CrmAssessmentCategory child : children) {
            // 先删除子分类的规则
            deleteCategoryRules(child.getCategoryId());

            // 递归删除子分类的子分类
            deleteCategoryAndChildren(child.getCategoryId());

            // 逻辑删除子分类
            CrmAssessmentCategory category = new CrmAssessmentCategory();
            category.setCategoryId(child.getCategoryId());
            category.setDataStatus(0);
            category.setModifyTime(LocalDateTime.now());
            this.updateById(category);
        }
    }

    /**
     * 删除分类关联的所有规则
     */
    private void deleteCategoryRules(String categoryId) {
        // 查询分类下的所有规则
        LambdaQueryWrapper<CrmAssessmentRule> ruleQuery = new LambdaQueryWrapper<>();
        ruleQuery.eq(CrmAssessmentRule::getCategoryId, categoryId);
        List<CrmAssessmentRule> rules = ruleService.list(ruleQuery);

        // 逻辑删除每个规则
        for (CrmAssessmentRule rule : rules) {
            // 删除规则关联的质检点
            LambdaQueryWrapper<CrmAssessmentRulePoint> pointQuery = new LambdaQueryWrapper<>();
            pointQuery.eq(CrmAssessmentRulePoint::getRuleId, rule.getRuleId());
            List<CrmAssessmentRulePoint> points = rulePointService.list(pointQuery);

            for (CrmAssessmentRulePoint point : points) {
                point.setDataStatus(0);
                rulePointService.updateById(point);
            }

            // 逻辑删除规则
            rule.setDataStatus(0);
            rule.setModifyTime(LocalDateTime.now());
            ruleService.updateById(rule);
        }
    }

    @Override
    public CrmAssessmentCategory getCategoryDetail(String categoryId) {
        return this.getById(categoryId);
    }

    @Override
    public List<CrmAssessmentCategoryVo> getCategoryTree(String versionId) {
        List<CrmAssessmentCategory> allCategories = listCategoriesByVersionId(versionId);
        return buildCategoryTree(allCategories);
    }

    @Override
    public List<CrmAssessmentCategoryVo> getCategoryTreeByAssessmentId(CrmAssessmentCategory category) {
        List<CrmAssessmentCategory> allCategories = listCategoriesByAssessmentId(category.getAssessmentId(),category.getVersionId()==null?null:category.getVersionId());
        return buildCategoryTree(allCategories);
    }

    @Override
    public void removeDraftCategoriesByAssessmentId(String assessmentId) {
        LambdaQueryWrapper<CrmAssessmentCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmAssessmentCategory::getAssessmentId, assessmentId)
                .eq(CrmAssessmentCategory::getStatus, 1); // 草稿
        this.remove(wrapper);
    }

    private List<CrmAssessmentCategoryVo> buildCategoryTree(List<CrmAssessmentCategory> allCategories) {
        List<CrmAssessmentCategoryVo> allCategoryVos = allCategories.stream().map(category -> {
            CrmAssessmentCategoryVo vo = new CrmAssessmentCategoryVo();
            BeanUtils.copyProperties(category, vo);
            return vo;
        }).collect(Collectors.toList());

        List<CrmAssessmentCategoryVo> rootCategories = allCategoryVos.stream()
                .filter(vo -> StringUtils.isBlank(vo.getParentId()) || vo.getLevel() == 1)
                .collect(Collectors.toList());

        rootCategories.forEach(root -> buildChildrenTree(root, allCategoryVos));

        return rootCategories;
    }


    /**
     * 递归构建子分类树
     */
    private void buildChildrenTree(CrmAssessmentCategoryVo parent, List<CrmAssessmentCategoryVo> allCategories) {
        List<CrmAssessmentCategoryVo> children = allCategories.stream()
                .filter(vo -> parent.getCategoryId().equals(vo.getParentId()))
                .collect(Collectors.toList());

        if (!children.isEmpty()) {
            parent.setChildren(children);
            // 递归构建每个子分类的子分类
            children.forEach(child -> {
                buildChildrenTree(child, allCategories);
            });
        }
    }
} 