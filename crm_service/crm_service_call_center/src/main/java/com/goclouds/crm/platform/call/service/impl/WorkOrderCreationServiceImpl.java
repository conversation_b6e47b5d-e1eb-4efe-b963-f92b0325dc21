package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.es.TicketExt;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.TicketOperationLog;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordFinalEsResult;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordContentMapper;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordMapper;
import com.goclouds.crm.platform.call.service.*;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.constant.RabbitMqConstants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.domain.QueryCondition;
import com.goclouds.crm.platform.common.domain.message.SendMessageDTO;
import com.goclouds.crm.platform.common.enums.*;
import com.goclouds.crm.platform.common.enums.email.ChannelEmailEnum;
import com.goclouds.crm.platform.common.enums.marketing.CustomerTimeTypeEnum;
import com.goclouds.crm.platform.common.utils.RedisCacheUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.aigc.AigcChatClient;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.client.channel.MediaClient;
import com.goclouds.crm.platform.openfeignClient.client.customer.CustomerClient;
import com.goclouds.crm.platform.openfeignClient.client.rescmgnt.RescmgntClient;
import com.goclouds.crm.platform.openfeignClient.client.system.InternationalClient;
import com.goclouds.crm.platform.openfeignClient.client.system.UserClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.aigc.InboundIntentVo;
import com.goclouds.crm.platform.openfeignClient.domain.aigc.SmartSummaryResultVo;
import com.goclouds.crm.platform.openfeignClient.domain.channel.ConnectMessageVo;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelConfigVO;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelDefVO;
import com.goclouds.crm.platform.openfeignClient.domain.channel.WhatsAppChannelDetailVo;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CreateCustomerVO;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CrmCustomerVO;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CustomerMediaInstVO;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysUserVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.UserDetailsVO;
import com.goclouds.crm.platform.openfeignClient.domain.translate.VoiceTranscribeVO;
import com.goclouds.crm.platform.utils.ElasticsearchUtil;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import com.google.gson.Gson;
import com.ibm.icu.text.SimpleDateFormat;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.engine.VersionConflictEngineException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.sql.Time;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.goclouds.crm.platform.common.domain.QueryCondition.createCondition;
import static com.goclouds.crm.platform.utils.SecurityUtil.getUserId;
import static com.goclouds.crm.platform.utils.SecurityUtil.getUsername;

/**
 * <AUTHOR>
 * @description 创建工单service
 * @createDate 2024-01-17 14:24:35
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkOrderCreationServiceImpl implements WorkOrderCreationService {

    private final CrmCustomerServiceImpl crmCustomerService;
    @Value("${s3.bucket-name}")
    private String bucketName;

    @Value("${s3.region}")
    private String region;

    @Value("${s3.region-type}")
    private String regionType;

    @Value("${es.work-record-content-index}")
    private String workContentIndex;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    @Value("${s3.domain-prefix}")
    private String domainPrefix;

    private final RestHighLevelClient restHighLevelClient;

    private final RedissonClient redissonClient;

    private final CustomerClient customerClient;

    private final MediaClient whatsAppClient;

    private final UserClient userClient;

    private final ChannelClient channelClient;

    private final RabbitTemplate rabbitTemplate;

    private final CrmAgentWorkRecordService crmAgentWorkRecordService;

    private final CrmAgentWorkRecordDetailService crmAgentWorkRecordDetailService;

    private final CrmChannelConfigService crmChannelConfigService;

    private final CrmChannelInfoConfigInstService crmChannelInfoConfigInstService;

    private final CrmAgentWorkRecordFileService crmAgentWorkRecordFileService;

    private final CrmAgentWorkRecordPriorityLevelDefService crmAgentWorkRecordPriorityLevelDefService;

    private final CrmAgentWorkRecordSlaDefService crmAgentWorkRecordSlaDefService;

    private final CrmAgentWorkRecordExtIntsService crmAgentWorkRecordExtIntsService;

    private final CrmAgentWorkRecordRelationService crmAgentWorkRecordRelationService;

    private final CrmAgentWorkRecordOperationLogService crmAgentWorkRecordOperationLogService;

    private final CrmAgentWorkRecordMapper crmAgentWorkRecordMapper;

    private final CrmAgentWorkRecordContentMapper crmAgentWorkRecordContentMapper;

    private final CrmAgentWorkRecordAutoMergeService crmAgentWorkRecordAutoMergeService;

    private final RescmgntClient rescmgntClient;

    private final ElasticsearchUtil elasticsearchUtil;

    private final AigcChatClient aigcChatClient;

    private final StatDataIndexService statDataIndexService;

    private final RedisTemplate redisTemplate;

    private final InternationalClient internationalClient;

    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> createWork(CreateWorkOrderRecordVO createWorkOrderRecordVO) {
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        BeanUtils.copyProperties(createWorkOrderRecordVO, ticketInfoIndex);
        // 定义日志操作描述
        Integer operationLogType = 0;
        SysUserVo loginUser = new SysUserVo();
        if (createWorkOrderRecordVO.getAutoType() == 2) {
            loginUser = SecurityUtil.getLoginUser();
            // 只有是人工工单时，才直接获取当前登录用户的部门id
            ticketInfoIndex.setDeptId(loginUser.getDeptId());
        } else {
            String robotName = getRobotName(createWorkOrderRecordVO.getChannelId());
            loginUser.setUserName(robotName);
            loginUser.setUserId("");
            // 只有为机器人时有公司id
            loginUser.setCompanyId(createWorkOrderRecordVO.getCompanyId());
            // 定义部门id为null
            loginUser.setDeptId("");
        }
        // 定义工单回复
        WorkRecordReplyDTO workRecordReplyDTO = new WorkRecordReplyDTO();
        // 拼接工单参数
        workAttributeHandle(createWorkOrderRecordVO, workRecordReplyDTO, loginUser, ticketInfoIndex);
        if (createWorkOrderRecordVO.getWorkOrderId() != null) {
            ticketInfoIndex.setWorkRecordId(createWorkOrderRecordVO.getWorkOrderId());
        } else {
            ticketInfoIndex.setWorkRecordId(UuidUtils.generateUuid());
        }
        // 如果没有选择座席,那么状态为待分配
        if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getAgentId())) {
            // 添加工单操作日志
//            operationLogDescribe = loginUser.getUserName() + "创建了工单,并且分配给" + ticketInfoIndex.getAgentName() + "工单状态为待客服处理";
            operationLogType = TicketOperationTypeEnum.CREATE_TICKET_ASSIGNED.getCode();
        } else {
            ticketInfoIndex.setStatus(0);
            // 添加工单操作日志
//            operationLogDescribe = loginUser.getUserName() + "创建了工单,工单状态为待分配";
            operationLogType = TicketOperationTypeEnum.CREATE_TICKET_UNDISTRIBUTED.getCode();
        }
        // 添加工单
        ticketInfoIndex.setWordRecordCode(sdf.format(date))
                .setTransferToAgentFlag(0)
                .setCompanyId(loginUser.getCompanyId())
                .setCreateType(createWorkOrderRecordVO.getCreateType())
                .setCreator(loginUser.getUserId())
                .setCreateTime(LocalDateTime.now())
                .setModifyTime(LocalDateTime.now())
                .setDataStatus(1);
        // 如果视频状态为null，则认为是非视频
        if (createWorkOrderRecordVO.getChatVoice() != null) {
            ticketInfoIndex.setChatVoice(createWorkOrderRecordVO.getChatVoice());
        } else {
            ticketInfoIndex.setChatVoice(0);
        }
        // 拓展字段保存
        List<TicketExt> ticketExtList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(createWorkOrderRecordVO.getExtIntsList())) {
            createWorkOrderRecordVO.getExtIntsList().forEach(item -> {
                TicketExt ticketExt = new TicketExt();
                ticketExt.setExtCode(item.getWorkRecordExtCode());
                ticketExt.setExtValue(item.getWorkRecordExtValue());
                ticketExtList.add(ticketExt);
            });
        }
        ticketInfoIndex.setTicketExt(ticketExtList);
        // 如果是转人工工单，则不加操作记录(新版工作台分配)
        if (createWorkOrderRecordVO.getAutoType() != 3) {
            // 取出操作日志
            List<TicketOperationLog> ticketOperationLogList = new ArrayList<>();
            TicketOperationLog ticketOperationLog = new TicketOperationLog();
            ticketOperationLog.setOperatorName(loginUser.getUserName());
            ticketOperationLog.setOperationLogType(operationLogType);
            ticketOperationLog.setOperatorTime(LocalDateTime.now());
            ticketOperationLogList.add(ticketOperationLog);
            ticketInfoIndex.setTicketOperationLog(ticketOperationLogList);
        }
        addTicketInfo(ticketInfoIndex, loginUser.getCompanyId());

        // 手动创建时，进行内容添加
        if (createWorkOrderRecordVO.getCreateType() == 1) {
            //判断扩展属性是否有抄送或密送
            if (createWorkOrderRecordVO.getExtIntsList() != null) {
                List<CreateWorkOrderExtIntsVO> copyAddressExtList = createWorkOrderRecordVO.getExtIntsList().stream().filter(createWorkOrderExtIntsVO ->
                                ("copyAddress").equals(createWorkOrderExtIntsVO.getWorkRecordExtCode()))
                        .collect(Collectors.toList());
                List<CreateWorkOrderExtIntsVO> bccAddressExtList = createWorkOrderRecordVO.getExtIntsList().stream().filter(createWorkOrderExtIntsVO ->
                                ("bccAddress").equals(createWorkOrderExtIntsVO.getWorkRecordExtCode()))
                        .collect(Collectors.toList());
                if (!copyAddressExtList.isEmpty()) {
                    workRecordReplyDTO.setCopyAddress(copyAddressExtList.get(0).getWorkRecordExtValue());
                }
                if (!bccAddressExtList.isEmpty()) {
                    workRecordReplyDTO.setBccAddress(bccAddressExtList.get(0).getWorkRecordExtValue());
                }
            }
            //工作记录内容
            workRecordReplyDTO.setWorkRecordId(ticketInfoIndex.getWorkRecordId());
            workRecordReplyDTO.setContent(createWorkOrderRecordVO.getWorkRecordContent());
            workRecordReplyDTO.setSubject(ticketInfoIndex.getWorkRecordTheme());
            workRecordReplyDTO.setContentFileList(createWorkOrderRecordVO.getContentFileList());
            workRecordReplyDTO.setWorkRecordFileList(createWorkOrderRecordVO.getWorkRecordFileList());
            workRecordReplyDTO.setCompanyId(loginUser.getCompanyId());
            workRecordReplyDTO.setStatus(0);
            workRecordReplyDTO.setAutoType(2);
            // 界面创建工单修改样式， 所以新增了以下字段。 只有前端传值时，才需改变
            if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getCopyAddress())) {
                workRecordReplyDTO.setCopyAddress(createWorkOrderRecordVO.getCopyAddress());
            }
            if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getBccAddress())) {
                workRecordReplyDTO.setBccAddress(createWorkOrderRecordVO.getBccAddress());
            }
            if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getSubject())) {
                workRecordReplyDTO.setSubject(createWorkOrderRecordVO.getSubject());
            }
            if (createWorkOrderRecordVO.getSendingMail() != null) {
                workRecordReplyDTO.setSendingMail(createWorkOrderRecordVO.getSendingMail());
            }
            if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getDesAddress())) {
                workRecordReplyDTO.setDesAddress(createWorkOrderRecordVO.getDesAddress());
            }
            replyMessage(workRecordReplyDTO);
            // 进行工单关联
            if (CollectionUtils.isNotEmpty(createWorkOrderRecordVO.getRelationWorkRecordIdList())) {
                UpdateWorkAssociationVO updateWorkAssociationVO = new UpdateWorkAssociationVO();
                updateWorkAssociationVO.setWorkRecordId(ticketInfoIndex.getWorkRecordId());
                updateWorkAssociationVO.setRelationWorkRecordIdList(createWorkOrderRecordVO.getRelationWorkRecordIdList());
                crmAgentWorkRecordService.updateWorkAssociation(updateWorkAssociationVO);
            }
        }

        return AjaxResult.ok(ticketInfoIndex.getWorkRecordId(), MessageUtils.get("operate.success"));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> autoCreateWork(WorkRecordCreateDTO workRecordRemarkDTO) {
        // 创建Gson实例
        Gson gson = new Gson();

        // 将Java对象转换为JSON字符串
        String json = gson.toJson(workRecordRemarkDTO);
        log.info("打印一下自动工单请求参数:" + json);
        // 如果是登录后转人工，需要把基础信息存入
        if (workRecordRemarkDTO.getAutoType() == 2) {
            // 座席ID，当前用户
            workRecordRemarkDTO.setAgentId(getUserId());
            // 座席名称，当前用户
            workRecordRemarkDTO.setAgentName(getUsername());
            // 获取当前公司id,这个是为判断客户是否存在 所以传入
            workRecordRemarkDTO.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        }

        // 客户资料查询和生成
        CreateCustomerVO createCustomerVO = new CreateCustomerVO();
        createCustomerVO.setMobile(workRecordRemarkDTO.getCustomerContactInfo());
        createCustomerVO.setChannelTypeId(workRecordRemarkDTO.getChannelTypeId());
        createCustomerVO.setChatUserName(workRecordRemarkDTO.getChatUserName());
        createCustomerVO.setChannelId(workRecordRemarkDTO.getChannelId());
        createCustomerVO.setCompanyId(workRecordRemarkDTO.getCompanyId());
        createCustomerVO.setChatMember(workRecordRemarkDTO.getChatMember());
        createCustomerVO.setMemberId(workRecordRemarkDTO.getMemberId());
        createCustomerVO.setPhoneNumber(workRecordRemarkDTO.getPhoneNumber());
        createCustomerVO.setCustomerLabel(workRecordRemarkDTO.getCustomerLabel());
        createCustomerVO.setFingerprintId(workRecordRemarkDTO.getFingerprintId());
        R<CrmCustomerVO> crmCustomer = customerClient.mobileQueryCustomerDetails(createCustomerVO);
        if (crmCustomer.getCode() != 200) {
            return null;
        }
        CrmCustomerVO crmCustomerVO = crmCustomer.getData();
        // 拼接工单参数
        CreateWorkOrderRecordVO createWorkOrderRecordVO = workParameter(workRecordRemarkDTO, crmCustomerVO);
        if (createWorkOrderRecordVO == null) {
            return AjaxResult.failure();
        }
        // 定义工单回复
        WorkRecordReplyDTO workRecordReplyDTO = new WorkRecordReplyDTO();
        workRecordReplyDTO.setStatus(1);

        AjaxResult<Object> work = new AjaxResult<>();
        String ticketId = null;
        // 如果为电话,则直接添加工单
        if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.PHONE.getCode().toString())) {
            work = this.createWork(createWorkOrderRecordVO);
            ticketId = work.getData().toString();
            workRecordReplyDTO.setStatus(0);
            // 如果为在线聊天语音
        } else if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WEB_VIDEO.getCode().toString())) {
            work = this.createWork(createWorkOrderRecordVO);
            ticketId = work.getData().toString();
            workRecordReplyDTO.setStatus(0);
        } else if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.APP_VIDEO.getCode().toString())) {
            work = this.createWork(createWorkOrderRecordVO);
            ticketId = work.getData().toString();
            workRecordReplyDTO.setStatus(0);
        } else {
            Map<String, Object> result = null;
            // 只有为人工工单时,才会根据查询
            // 查询当前客户ID,根据同一个渠道，并且为当前座席处理的是否有正在执行的工单
            if (workRecordRemarkDTO.getAutoType() == 2) {
                // 查询是否开启合并工单
                CrmAgentWorkRecordAutoMerge merge = crmAgentWorkRecordAutoMergeService.getOne(new QueryWrapper<CrmAgentWorkRecordAutoMerge>().lambda()
                        .eq(CrmAgentWorkRecordAutoMerge::getChannelTypeId, workRecordRemarkDTO.getChannelTypeId())
                        .eq(CrmAgentWorkRecordAutoMerge::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                        .eq(CrmAgentWorkRecordAutoMerge::getDataStatus, 1));
                // 如果为1，则是开起合并工单
                if (merge != null && merge.getAutoMergeFlag() == 1) {
                    List<QueryCondition> queryConditions = new ArrayList<>();
                    queryConditions.add(createCondition("customer_id", createWorkOrderRecordVO.getCustomerId()));
                    queryConditions.add(createCondition("status", Arrays.asList("0", "1", "2"), QueryTypeEnum.TERMS));
                    queryConditions.add(createCondition("agent_id", getUserId()));
                    queryConditions.add(createCondition("data_status", 1));
                    queryConditions.add(createCondition("channel_type_id", workRecordRemarkDTO.getChannelTypeId()));
                    queryConditions.add(createCondition("company_id", SecurityUtil.getLoginUser().getCompanyId()));
                    queryConditions.add(createCondition("chat_voice", 0));
                    queryConditions.add(createCondition("channel_config_id", workRecordRemarkDTO.getChannelId()));
                    queryConditions.add(createCondition("create_time", SortOrder.DESC));
                    result = elasticsearchUtil.searchInfoMapQuery(queryConditions, ticketIndex + workRecordRemarkDTO.getCompanyId(), TicketInfoIndex.class);
                }
            }

            // 邮件 待座席处理
            if (result == null) {
                work = this.createWork(createWorkOrderRecordVO);
                workRecordReplyDTO.setStatus(0);
                ticketId = work.getData().toString();
            } else {
                TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
                String id = (String) result.get("id");
                // 如果有工单未完成的，则直接获取到该工单id,并且将工单修改为待座席处理
                ticketId = workRecord.getWorkRecordId();
                workRecord.setStatus(1);
                workRecord.setModifyTime(LocalDateTime.now());
                elasticsearchUtil.updateDocument(ticketIndex + workRecordRemarkDTO.getCompanyId(), id, workRecord);
            }
            // 如果不是邮件类型,则存储回复类型
            if (!workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.EMAIL.getCode().toString())) {
                workRecordReplyDTO.setReplyType(workRecordRemarkDTO.getReplyType());
            }
        }
        //工作记录内容
        workRecordReplyDTO.setDesAddress(workRecordRemarkDTO.getCustomerContactInfo());
        // 公司id
        workRecordReplyDTO.setCompanyId(createWorkOrderRecordVO.getCompanyId());
        // 如果是在线聊天,则存储名称
        if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WEB_CHAT.getCode().toString())) {
            workRecordReplyDTO.setDesAddress(workRecordRemarkDTO.getChatUserName());
        } else if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.EMAIL.getCode().toString())) {
            // 这边主要是为了主动联系客户操作，以防有其他类型邮件，判断只有收件人不为null，才进行存储
            if (StringUtil.isNotEmpty(workRecordRemarkDTO.getCopyAddress())) {
                workRecordReplyDTO.setCopyAddress(workRecordRemarkDTO.getCopyAddress());
            }
            if (StringUtil.isNotEmpty(workRecordRemarkDTO.getBccAddress())) {
                workRecordReplyDTO.setBccAddress(workRecordRemarkDTO.getBccAddress());
            }

        }
        workRecordReplyDTO.setWorkRecordId(ticketId);
        workRecordReplyDTO.setChannelId(workRecordRemarkDTO.getChannelId());
        workRecordReplyDTO.setSubject(createWorkOrderRecordVO.getWorkRecordTheme());
        workRecordReplyDTO.setWorkRecordFileList(createWorkOrderRecordVO.getWorkRecordFileList());
        workRecordReplyDTO.setSendingMail(workRecordRemarkDTO.getSendingMail());
        if (workRecordRemarkDTO.getContactType() != null) {
            // 有值为公司联系客户
            customerClient.updateCustomerTime(createWorkOrderRecordVO.getCustomerId(), CustomerTimeTypeEnum.COMPANY_ACTIVE_TIME.getCode());
        } else {
            // 没值为客户联系公司
            customerClient.updateCustomerTime(createWorkOrderRecordVO.getCustomerId(), CustomerTimeTypeEnum.CUSTOM_ACTIVE_TIME.getCode());
        }
        // 为智能客服工单并且不是电话类型的时候不创建内容，
        if (workRecordRemarkDTO.getAutoType() != 1) {
            workRecordReplyDTO.setContent(createWorkOrderRecordVO.getWorkRecordContent());
            workRecordReplyDTO.setAutoType(workRecordRemarkDTO.getAutoType());
            work = this.autoReplyMessage(workRecordReplyDTO, workRecordRemarkDTO.getContactId(), workRecordRemarkDTO.getConnectId());
        }
        if (workRecordRemarkDTO.getAutoType() == 1 &&
                CrmChannelEnum.PHONE.getCode().toString().equals(workRecordRemarkDTO.getChannelTypeId())) {
            // 附件(工单回复对应附件)
            List<ContentFile> workRecordFileList = new ArrayList<>();
            for (WorkRecordFileDTO workRecordFileDTO : workRecordRemarkDTO.getWorkRecordFileList()) {
                ContentFile contentFile = new ContentFile();
                contentFile.setFileName(workRecordFileDTO.getName());
                contentFile.setFileUrl(workRecordFileDTO.getPath());
                contentFile.setBucketName(workRecordFileDTO.getBucketName());
                workRecordFileList.add(contentFile);
            }
            workRecordReplyDTO.setWorkRecordFileList(workRecordFileList);
            workRecordReplyDTO.setContent(createWorkOrderRecordVO.getWorkRecordContent());
            workRecordReplyDTO.setAutoType(workRecordRemarkDTO.getAutoType());
            workRecordReplyDTO.setChannelTypeId(workRecordRemarkDTO.getChannelTypeId());
            workRecordReplyDTO.setCompanyId(workRecordRemarkDTO.getCompanyId());
            // 翻译语言
            workRecordReplyDTO.setTranslateLanguage(workRecordRemarkDTO.getTranslateLanguage());
            workRecordReplyDTO.setRegion(workRecordRemarkDTO.getRegion());
            workRecordReplyDTO.setAwsAccountId(workRecordRemarkDTO.getAwsAccountId());
            if (workRecordRemarkDTO.getThirdPartyFlag() != null) {
                workRecordReplyDTO.setThirdPartyFlag(workRecordRemarkDTO.getThirdPartyFlag());
            }

            work = this.autoReplyMessage(workRecordReplyDTO, workRecordRemarkDTO.getContactId(), workRecordRemarkDTO.getConnectId());
        }
        try {
            // 如果是机器人工单，则调用联络明细数据
            if(workRecordRemarkDTO.getAutoType() == 1){
                ContactDetailChannelAddVo contactDetailChannelAddVo = new ContactDetailChannelAddVo();
                contactDetailChannelAddVo.setWorkOrderId(work.getData().toString());
                contactDetailChannelAddVo.setStartTime(new Date());
                contactDetailChannelAddVo.setContactId(work.getData().toString());
                if(workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.PHONE.getCode().toString())){
                    contactDetailChannelAddVo.setCallChannel("voice");
                }else{
                    contactDetailChannelAddVo.setCallChannel("chat");
                }
                contactDetailChannelAddVo.setChannelTypeId(workRecordRemarkDTO.getChannelTypeId());
                contactDetailChannelAddVo.setChannelId(workRecordRemarkDTO.getChannelId());
                contactDetailChannelAddVo.setEventType(1);
                contactDetailChannelAddVo.setCompanyId(workRecordRemarkDTO.getCompanyId());
                statDataIndexService.addChannelContactDetail(contactDetailChannelAddVo);
            }
        }catch (Exception e){
            log.error("机器人调用联络明细报错:",e);
        }

        return work;
    }

    public TicketInfoIndex get(String ticketIndexName, String workId, String companyId){
        //工单详情 之前
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndexName, workId, companyId);
        // 创建 ObjectMapper 实例
        ObjectMapper objectMapper = new ObjectMapper();
        if (ticketHit != null) {
            try {
                // 获取到_id
                esId = ticketHit.getId();
                Map<String, Object> source = ticketHit.getSourceAsMap();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错", e);
            }
        }
        return ticketInfoIndex;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> adhesiveAgentWork(WorkRecordCreateDTO workRecordRemarkDTO) {
        // 创建Gson实例
        Gson gson = new Gson();
        // 将Java对象转换为JSON字符串
        String dtoJson = gson.toJson(workRecordRemarkDTO);
        log.info("打印自动创建工单请求参数:{}", dtoJson);
        WorkAdhesiveAgentVo adhesiveAgentVo = new WorkAdhesiveAgentVo();
        // 无论哪种工单，首先判断客户是否存在，不存在则进行创建
        // 客户资料查询和生成
        CreateCustomerVO createCustomerVO = new CreateCustomerVO();
        createCustomerVO.setMobile(workRecordRemarkDTO.getCustomerContactInfo());
        createCustomerVO.setChannelTypeId(workRecordRemarkDTO.getChannelTypeId());
        createCustomerVO.setChatUserName(workRecordRemarkDTO.getChatUserName());
        createCustomerVO.setChannelId(workRecordRemarkDTO.getChannelId());
        createCustomerVO.setCompanyId(workRecordRemarkDTO.getCompanyId());
        createCustomerVO.setChatMember(workRecordRemarkDTO.getChatMember());
        createCustomerVO.setMemberId(workRecordRemarkDTO.getMemberId());
        createCustomerVO.setPhoneNumber(workRecordRemarkDTO.getPhoneNumber());
        createCustomerVO.setCustomerLabel(workRecordRemarkDTO.getCustomerLabel());
        createCustomerVO.setFingerprintId(workRecordRemarkDTO.getFingerprintId());
        R<CrmCustomerVO> crmCustomer = customerClient.mobileQueryCustomerDetails(createCustomerVO);
        if (crmCustomer.getCode() != 200) {
            return null;
        }
        CrmCustomerVO crmCustomerVO = crmCustomer.getData();
        log.info("客户信息{}", crmCustomerVO);
//        if(StringUtil.isNotEmpty(crmCustomerVO.getCustomerLabel())){
//            adhesiveAgentVo.setCustomerLabel(crmCustomerVO.getCustomerLabel());
//            log.info("保存客户标签{}",crmCustomerVO.getCustomerLabel());
//        }
        BeanUtils.copyProperties(crmCustomerVO, adhesiveAgentVo);
        log.info("打印查询客户信息后，复制到返回的内容:{}", adhesiveAgentVo);

        // 定义工单回复
        WorkRecordReplyDTO workRecordReplyDTO = new WorkRecordReplyDTO();
        workRecordReplyDTO.setStatus(1);
        AjaxResult<Object> work = new AjaxResult<>();
        String ticketId = null;
        // 拼接工单参数
        CreateWorkOrderRecordVO createWorkOrderRecordVO = workParameter(workRecordRemarkDTO, crmCustomerVO);
        //针对机器人工单进行二次操作，进行赋值
        //工单详情 之前
        // 因为web聊天以及社媒，都不需要考虑工单id一致的问题，所以直接将工单id设置为null
        createWorkOrderRecordVO.setWorkOrderId(null);
        // 如果是机器人工单，则进行直接创建。非机器人工单，则需要判断类型
        if (workRecordRemarkDTO.getAutoType() == 1) {
            // 拼接工单参数
            work = this.createWork(createWorkOrderRecordVO);
            ticketId = work.getData().toString();
            workRecordReplyDTO.setStatus(0);
        } else {
            // 如果为电话,则直接添加工单，在线聊天语音，app在线视频，则都是直接创建
            if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.PHONE.getCode().toString())
                    || workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WEB_VIDEO.getCode().toString())
                    || workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.APP_VIDEO.getCode().toString())) {
                work = this.createWork(createWorkOrderRecordVO);
                ticketId = work.getData().toString();
                workRecordReplyDTO.setStatus(0);
            } else {
                // 定义社媒类型是否需要校验客户时间 默认0
                Integer mediaTimeCheckCount = 0;
                Date date = null;
                if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.FACEBOOK.getCode().toString())
                        || workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WHATSAPP.getCode().toString())) {
                    mediaTimeCheckCount = CrmChannelEnum.FACEBOOK.getMessageConfine();
                }
                if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getCode().toString())
                        || workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WECHAT_OFFICIAL_ACCOUNT.getCode().toString())) {
                    mediaTimeCheckCount = CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getMessageConfine();
                }
                List<QueryCondition> queryConditions = new ArrayList<>();
                queryConditions.add(createCondition("customer_id", createWorkOrderRecordVO.getCustomerId()));
                queryConditions.add(createCondition("data_status", 1));
                queryConditions.add(createCondition("channel_type_id", workRecordRemarkDTO.getChannelTypeId()));
                queryConditions.add(createCondition("company_id", workRecordRemarkDTO.getCompanyId()));
                queryConditions.add(createCondition("chat_voice", 0));
                queryConditions.add(createCondition("channel_config_id", workRecordRemarkDTO.getChannelId()));
                queryConditions.add(createCondition("create_time", SortOrder.DESC));

                // 如果座席id不为null则需要加上座席id，根据座席id以及用户id查询
                if (StringUtil.isNotEmpty(workRecordRemarkDTO.getAgentId()) && !workRecordRemarkDTO.getAgentId().equals("1001")) {
                    queryConditions.add(createCondition("agent_id", workRecordRemarkDTO.getAgentId()));
                } else {
                    queryConditions.add(createCondition("agent_id", "1001", QueryTypeEnum.MUST_NOT));
                }
                Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, ticketIndex + workRecordRemarkDTO.getCompanyId(), TicketInfoIndex.class);


                // 如果没有工单，则需要创建工单没有座席的工单。如果有工单，则需要判断工单的状态
                if (result == null) {
                    work = this.createWork(createWorkOrderRecordVO);
                    workRecordReplyDTO.setStatus(0);
                    ticketId = work.getData().toString();
                    // 社媒需要进行消息限制 判断是否需要进行更新
                    if (mediaTimeCheckCount != 0) {
                        // 取当前时间为客户第一条回复时间
                        date = new Date();
                    }
                } else {
                    TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
                    String id = (String) result.get("id");
//                    TicketInfoIndex workRecord = ticketInfoIndexList.get(0);
                    // 首先判断工单状态,如果工单是已经完成的，则进行新建工单
                    if (workRecord.getStatus() == 3 || workRecord.getStatus() == 4) {
                        work = this.createWork(createWorkOrderRecordVO);
                        workRecordReplyDTO.setStatus(0);
                        ticketId = work.getData().toString();
                        // 社媒需要进行消息限制 判断是否需要进行更新
                        if (mediaTimeCheckCount != 0) {
                            // 取当前时间为客户第一条回复时间
                            date = new Date();
                        }
                    } else {
                        // 如果有工单未完成的，则直接获取到该工单id,并且将工单修改为待座席处理
                        ticketId = workRecord.getWorkRecordId();
                        //TODO 处理中的工单必须有座席
                        if(StringUtil.isBlank(workRecord.getAgentId())){
                            workRecord.setStatus(0);
                            workRecordReplyDTO.setStatus(0);
                        }else {
                            workRecord.setStatus(1);
                        }
                        workRecord.setModifyTime(LocalDateTime.now());
                        elasticsearchUtil.updateDocument(ticketIndex + workRecordRemarkDTO.getCompanyId(), id, workRecord);
//                        crmAgentWorkRecordService.updateById(crmAgentWorkRecord);
                        // 社媒需要进行消息限制 判断是否需要进行更新
                        if (mediaTimeCheckCount != 0) {
                            if (workRecord.getCustomerFirstMessageTime() != null) {
                                // 将 Date 转换为 LocalDateTime
                                LocalDateTime replyTime = workRecord.getCustomerFirstMessageTime();
                                // 获取当前时间
                                LocalDateTime now = LocalDateTime.now();
                                // 计算当前时间和回复时间的差值
                                long hoursDifference = ChronoUnit.HOURS.between(replyTime, now);
                                // 判断是否超过24小时,超过24小时则设置为当前时间
                                if (hoursDifference >= mediaTimeCheckCount) {
                                    date = new Date();
                                }
                            }
                        }
                    }
                    // 取到上一个工单的座席id
                    adhesiveAgentVo.setAgentId(workRecord.getAgentId());
                    // 取到上一个工单的状态
                    adhesiveAgentVo.setStatus(workRecord.getStatus());
                    adhesiveAgentVo.setTicketType(workRecord.getWorkRecordTypeCode());
                }
                // 如果不是邮件类型,则存储回复类型
                if (!workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.EMAIL.getCode().toString())) {
                    workRecordReplyDTO.setReplyType(workRecordRemarkDTO.getReplyType());
                }
                // 判断当前是否为机器人工单，如果是机器人工单，则需要将机器人工单聊天内容保存到工单中
                if (StringUtil.isNotEmpty(workRecordRemarkDTO.getWorkOrderId())) {
                    //工单详情
                    TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
                    String esId = null;
                    SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex, workRecordRemarkDTO.getWorkOrderId(), workRecordRemarkDTO.getCompanyId());
                    // 创建 ObjectMapper 实例
                    ObjectMapper objectMapper = new ObjectMapper();
                    if (ticketHit != null) {
                        try {
                            // 获取到_id
                            esId = ticketHit.getId();
                            Map<String, Object> source = ticketHit.getSourceAsMap();
                            // 注册 JavaTimeModule
                            objectMapper.registerModule(new JavaTimeModule());
                            String json = objectMapper.writeValueAsString(source);
                            // 将 JSON 字符串转换为 Java 对象
                            ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                        } catch (JsonProcessingException e) {
                            log.error("查询es报错", e);
                        }
                    }
                    if ("1001".equals(ticketInfoIndex.getAgentId())) {
                        String customerFirstReplyTime = copyWorkChatContent(workRecordRemarkDTO.getWorkOrderId(), ticketId, workRecordRemarkDTO.getCompanyId());
                        // 修改机器人工单转人工状态，并且记录机器人转人工id
                        ticketInfoIndex.setTransferToAgentFlag(1);
                        ticketInfoIndex.setAgentTicketId(ticketId);
                        updateRobotTicketFlag(esId, workRecordRemarkDTO.getCompanyId(), ticketInfoIndex, objectMapper);
                        // mediaTimeCheckCount不为0则是社媒需要消息限制，date不为null，则是需要更新（以防消息时间没到限制）
                        if (mediaTimeCheckCount != 0 && date != null && StringUtil.isNotEmpty(customerFirstReplyTime)) {
                            // 使用 DateTimeFormatter 定义日期时间格式
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            // 将字符串解析为 LocalDateTime
                            LocalDateTime localDateTime = LocalDateTime.parse(customerFirstReplyTime, formatter);
                            // 将 LocalDateTime 转换为 Date
                            date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
                        }
//                        RLock lock = redissonClient.getLock(workRecordRemarkDTO.getCompanyId()+ticketId);
//                        try {
//                            lock.lock();
                            TicketInfoIndex after = get(ticketIndex, ticketId, workRecordRemarkDTO.getCompanyId());
                            after.setWorkRecordId(ticketId);
                        //扩展属性 - 如果当前存在则不引用机器人工单的信息
                        if(     ObjectUtil.isEmpty(after.getTicketExt())
                                || ObjectUtil.isNull(after.getTicketExt())
                                || after.getTicketExt().size()!=0
                                && ObjectUtil.isNotEmpty(ticketInfoIndex.getTicketExt())
                                && ObjectUtil.isNotNull(ticketInfoIndex.getTicketExt())
                                && ticketInfoIndex.getTicketExt().size()!=0){
                            after.setTicketExt(ticketInfoIndex.getTicketExt());
                        }
                        if(
                                StringUtil.isBlank(after.getWorkRecordTheme())
                                        &&
                                        StringUtil.isNotBlank(ticketInfoIndex.getWorkRecordTheme())
                        ){
                            after.setWorkRecordTheme(ticketInfoIndex.getWorkRecordTheme());
                        }
                        if(     StringUtil.isBlank(after.getPriorityLevelId())
                                &&
                                StringUtil.isNotBlank(ticketInfoIndex.getPriorityLevelId())
                        ){
                            after.setPriorityLevelId(ticketInfoIndex.getPriorityLevelId());
                        }
                        if(     StringUtil.isBlank(after.getPriorityLevelName())
                                &&
                                StringUtil.isNotBlank(ticketInfoIndex.getPriorityLevelName())
                        ){
                            after.setPriorityLevelName(ticketInfoIndex.getPriorityLevelName());
                        }
                        if(     StringUtil.isBlank(after.getWorkRecordTypeCode())
                                &&
                                StringUtil.isNotBlank(ticketInfoIndex.getWorkRecordTypeCode())
                        ){
                            after.setWorkRecordTypeCode(ticketInfoIndex.getWorkRecordTypeCode());
                        }
                        if(     StringUtil.isBlank(after.getWorkRecordTypeName())
                                &&
                                StringUtil.isNotBlank(ticketInfoIndex.getWorkRecordTypeName())
                        ){
                            after.setWorkRecordTypeName(ticketInfoIndex.getWorkRecordTypeName());
                        }
                            String id = null;
                            SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex, ticketId, workRecordRemarkDTO.getCompanyId());
                            // 创建 ObjectMapper 实例
                            if (ticketHit != null) {
                                // 获取到_id
                                id = hit.getId();
                            }
                            //进行更新
                            log.info("当前修改工单信息=====>{}",after);
                            adhesiveAgentVo.setTicketType(after.getWorkRecordTypeCode());
                            elasticsearchUtil.updateDocument(ticketIndex + workRecordRemarkDTO.getCompanyId(),id,after);
//                        } finally {
//                            lock.unlock();
//                        }

                    }
                }
                //工作记录内容
                workRecordReplyDTO.setDesAddress(workRecordRemarkDTO.getCustomerContactInfo());
                // 如果是在线聊天,则存储名称
                if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WEB_CHAT.getCode().toString())) {
                    workRecordReplyDTO.setDesAddress(workRecordRemarkDTO.getChatUserName());
                }

                workRecordReplyDTO.setWorkRecordId(ticketId);
                workRecordReplyDTO.setChannelId(workRecordRemarkDTO.getChannelId());
                workRecordReplyDTO.setSubject(createWorkOrderRecordVO.getWorkRecordTheme());
                workRecordReplyDTO.setWorkRecordFileList(createWorkOrderRecordVO.getWorkRecordFileList());
                workRecordReplyDTO.setSendingMail(workRecordRemarkDTO.getSendingMail());
                // 公司id
                workRecordReplyDTO.setCompanyId(createWorkOrderRecordVO.getCompanyId());
                if (workRecordRemarkDTO.getContactType() != null) {
                    // 有值为公司联系客户
                    customerClient.updateCustomerTime(createWorkOrderRecordVO.getCustomerId(), CustomerTimeTypeEnum.COMPANY_ACTIVE_TIME.getCode());
                } else {
                    // 没值为客户联系公司
                    customerClient.updateCustomerTime(createWorkOrderRecordVO.getCustomerId(), CustomerTimeTypeEnum.CUSTOM_ACTIVE_TIME.getCode());
                }

                if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getWorkRecordContent())) {
                    workRecordReplyDTO.setContent(createWorkOrderRecordVO.getWorkRecordContent());
                    workRecordReplyDTO.setAutoType(workRecordRemarkDTO.getAutoType());
                    work = this.autoReplyMessage(workRecordReplyDTO, workRecordRemarkDTO.getContactId(), workRecordRemarkDTO.getConnectId());
                }
                // 在最后进行判断 mediaTimeCheckCount不为0则是社媒需要消息限制，date不为null，则是需要更新
                if (mediaTimeCheckCount != 0 && date != null) {
                    SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex, ticketId, workRecordRemarkDTO.getCompanyId());
                    TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
                    if (hit != null) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        // 获取到_id
                        String esId = hit.getId();
                        Map<String, Object> source = hit.getSourceAsMap();
                        String json = null;
                        try {
                            // 注册 JavaTimeModule
                            objectMapper.registerModule(new JavaTimeModule());
                            json = objectMapper.writeValueAsString(source);
                            // 将 JSON 字符串转换为 Java 对象
                            ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                        } catch (JsonProcessingException e) {
                            log.error("社媒查询工单报错，消息限制失败", e);
                        }
                        ticketInfoIndex.setWorkRecordId(ticketId);
                        if (StringUtil.isNotEmpty(ticketInfoIndex.getAgentId())) {
                            ticketInfoIndex.setStatus(1);
                        }
                        ticketInfoIndex.setModifyTime(LocalDateTime.now());
                        ticketInfoIndex.setCustomerFirstMessageTime(date.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());
                        adhesiveAgentVo.setTicketType(ticketInfoIndex.getWorkRecordTypeCode());
                        elasticsearchUtil.updateDocument(ticketIndex + workRecordRemarkDTO.getCompanyId(), esId, ticketInfoIndex);
                    }

                }
            }
        }
        adhesiveAgentVo.setWorkOrderId(ticketId);
        adhesiveAgentVo.setChannelId(workRecordRemarkDTO.getChannelId());
        adhesiveAgentVo.setCompanyId(workRecordRemarkDTO.getCompanyId());
        log.info("打印一下自动创建工单的返回{}", adhesiveAgentVo);
        return AjaxResult.ok(adhesiveAgentVo, MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult<Object> mustCreateWork(WorkRecordCreateDTO workRecordRemarkDTO) {
        // 创建Gson实例
        Gson gson = new Gson();
        // 将Java对象转换为JSON字符串
        String dtoJson = gson.toJson(workRecordRemarkDTO);
        log.info("打印必定创建工单请求参数:{}", dtoJson);
        // 无论哪种工单，首先判断客户是否存在，不存在则进行创建
        // 客户资料查询和生成
        CreateCustomerVO createCustomerVO = new CreateCustomerVO();
        createCustomerVO.setMobile(workRecordRemarkDTO.getCustomerContactInfo());
        createCustomerVO.setChannelTypeId(workRecordRemarkDTO.getChannelTypeId());
        createCustomerVO.setChatUserName(workRecordRemarkDTO.getChatUserName());
        createCustomerVO.setChannelId(workRecordRemarkDTO.getChannelId());
        createCustomerVO.setCompanyId(workRecordRemarkDTO.getCompanyId());
        createCustomerVO.setChatMember(workRecordRemarkDTO.getChatMember());
        createCustomerVO.setMemberId(workRecordRemarkDTO.getMemberId());
        createCustomerVO.setPhoneNumber(workRecordRemarkDTO.getPhoneNumber());
        createCustomerVO.setCustomerLabel(workRecordRemarkDTO.getCustomerLabel());
        createCustomerVO.setFingerprintId(workRecordRemarkDTO.getFingerprintId());
        R<CrmCustomerVO> crmCustomer = customerClient.mobileQueryCustomerDetails(createCustomerVO);
        if (crmCustomer.getCode() != 200) {
            return null;
        }
        CrmCustomerVO crmCustomerVO = crmCustomer.getData();
        // 拼接工单参数
        CreateWorkOrderRecordVO createWorkOrderRecordVO = workParameter(workRecordRemarkDTO, crmCustomerVO);
        // 因为web聊天以及社媒，都不需要考虑工单id一致的问题，所以直接将工单id设置为null
        createWorkOrderRecordVO.setWorkOrderId(null);
        // 定义社媒类型是否需要校验客户时间 默认0
        Integer mediaTimeCheckCount = 0;
        if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.FACEBOOK.getCode().toString())
                || workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WHATSAPP.getCode().toString())) {
            mediaTimeCheckCount = CrmChannelEnum.FACEBOOK.getMessageConfine();
        }
        if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getCode().toString())
                || workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WECHAT_OFFICIAL_ACCOUNT.getCode().toString())) {
            mediaTimeCheckCount = CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getMessageConfine();
        }
        // 不为0则是社媒需要时间限制
        if (mediaTimeCheckCount != 0) {
            // 查询客户在当前渠道下有没有工单
            List<QueryCondition> queryConditions = new ArrayList<>();
            queryConditions.add(createCondition("customer_id", crmCustomerVO.getCustomerId()));
            queryConditions.add(createCondition("data_status", 1));
            queryConditions.add(createCondition("channel_type_id", workRecordRemarkDTO.getChannelTypeId()));
            queryConditions.add(createCondition("company_id", workRecordRemarkDTO.getCompanyId()));
            queryConditions.add(createCondition("chat_voice", 0));
            queryConditions.add(createCondition("channel_config_id", workRecordRemarkDTO.getChannelId()));
            queryConditions.add(createCondition("customer_first_message_time", SortOrder.DESC));
            Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, ticketIndex + workRecordRemarkDTO.getCompanyId(), TicketInfoIndex.class);
            // 如果之前没有数据，则认为首次
            if (result == null) {
                createWorkOrderRecordVO.setCustomerFirstMessageTime(LocalDateTime.now());
            } else {
                TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
                // 如果有数据，但是没有首次客服时间，则也认为是首次
                if (workRecord.getCustomerFirstMessageTime() != null) {
                    // 将 Date 转换为 LocalDateTime
                    LocalDateTime replyTime = workRecord.getCustomerFirstMessageTime();
                    // 获取当前时间
                    LocalDateTime now = LocalDateTime.now();
                    // 计算当前时间和回复时间的差值
                    long hoursDifference = ChronoUnit.HOURS.between(replyTime, now);
                    // 判断是否超过24小时,超过24小时则设置为当前时间
                    if (hoursDifference >= mediaTimeCheckCount) {
                        createWorkOrderRecordVO.setCustomerFirstMessageTime(LocalDateTime.now());
                    }
                } else {
                    createWorkOrderRecordVO.setCustomerFirstMessageTime(LocalDateTime.now());
                }
            }
        }
        AjaxResult<Object> work = this.createWork(createWorkOrderRecordVO);
        String ticketId = work.getData().toString();
        // 判断当前是否为机器人工单，如果是机器人工单，则需要将机器人工单聊天内容保存到工单中
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getWorkOrderId()) && ObjectUtil.isNotNull(workRecordRemarkDTO.getWorkOrderId())) {
            TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
            SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex, workRecordRemarkDTO.getWorkOrderId(), workRecordRemarkDTO.getCompanyId());
            String esId = null;
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            if (ticketHit != null) {
                try {
                    // 获取到_id
                    esId = ticketHit.getId();
                    Map<String, Object> source = ticketHit.getSourceAsMap();
                    // 注册 JavaTimeModule
                    objectMapper.registerModule(new JavaTimeModule());
                    String json = objectMapper.writeValueAsString(source);
                    // 将 JSON 字符串转换为 Java 对象
                    ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                } catch (JsonProcessingException e) {
                    log.error("查询es报错", e);
                }
            }
            if ("1001".equals(ticketInfoIndex.getAgentId())) {
//                RLock lock = redissonClient.getLock(workRecordRemarkDTO.getCompanyId()+ticketId);
//                try {
//                    lock.lock();
                    String customerFirstReplyTime = copyWorkChatContent(workRecordRemarkDTO.getWorkOrderId(), ticketId, workRecordRemarkDTO.getCompanyId());
                    // 修改机器人工单转人工状态，并且记录机器人转人工id
                    ticketInfoIndex.setTransferToAgentFlag(1);
                    ticketInfoIndex.setAgentTicketId(ticketId);
                    updateRobotTicketFlag(esId, workRecordRemarkDTO.getCompanyId(), ticketInfoIndex, objectMapper);

                    TicketInfoIndex after = get(ticketIndex, ticketId, ticketInfoIndex.getCompanyId());
                    //扩展属性 - 如果当前存在则不引用机器人工单的信息
                    if(     ObjectUtil.isEmpty(after.getTicketExt())
                            || ObjectUtil.isNull(after.getTicketExt())
                            || after.getTicketExt().size()!=0
                            && ObjectUtil.isNotEmpty(ticketInfoIndex.getTicketExt())
                            && ObjectUtil.isNotNull(ticketInfoIndex.getTicketExt())
                            && ticketInfoIndex.getTicketExt().size()!=0){
                        after.setTicketExt(ticketInfoIndex.getTicketExt());
                    }
                    if(
                            StringUtil.isBlank(after.getWorkRecordTheme())
                        &&
                            StringUtil.isNotBlank(ticketInfoIndex.getWorkRecordTheme())
                    ){
                        after.setWorkRecordTheme(ticketInfoIndex.getWorkRecordTheme());
                    }
                    if(     StringUtil.isBlank(after.getPriorityLevelId())
                            &&
                            StringUtil.isNotBlank(ticketInfoIndex.getPriorityLevelId())
                    ){
                        after.setPriorityLevelId(ticketInfoIndex.getPriorityLevelId());
                    }
                    if(     StringUtil.isBlank(after.getPriorityLevelName())
                            &&
                            StringUtil.isNotBlank(ticketInfoIndex.getPriorityLevelName())
                    ){
                        after.setPriorityLevelName(ticketInfoIndex.getPriorityLevelName());
                    }
                    if(     StringUtil.isBlank(after.getWorkRecordTypeCode())
                            &&
                            StringUtil.isNotBlank(ticketInfoIndex.getWorkRecordTypeCode())
                    ){
                        after.setWorkRecordTypeCode(ticketInfoIndex.getWorkRecordTypeCode());
                    }
                    if(     StringUtil.isBlank(after.getWorkRecordTypeName())
                            &&
                            StringUtil.isNotBlank(ticketInfoIndex.getWorkRecordTypeName())
                    ){
                        after.setWorkRecordTypeName(ticketInfoIndex.getWorkRecordTypeName());
                    }
                    String id = null;
                    SearchHit tickeAfter = elasticsearchUtil.queryEsTicket(ticketIndex, ticketId, workRecordRemarkDTO.getCompanyId());
                    // 创建 ObjectMapper 实例
                    if (tickeAfter != null) {
                        // 获取到_id
                        id = ticketHit.getId();
                    }
                    log.info("当前修改工单信息=====>{}",after);
                    //进行更新
                    elasticsearchUtil.updateDocument(ticketIndex + workRecordRemarkDTO.getCompanyId(),id,after);
//                } finally {
//                    lock.unlock();
//                }


            }
        }
        if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getWorkRecordContent())) {
            // 定义工单回复
            WorkRecordReplyDTO workRecordReplyDTO = new WorkRecordReplyDTO();
            workRecordReplyDTO.setStatus(0);
            // 如果不是邮件类型,则存储回复类型
            if (!workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.EMAIL.getCode().toString())) {
                workRecordReplyDTO.setReplyType(workRecordRemarkDTO.getReplyType());
            }
            //工作记录内容
            workRecordReplyDTO.setDesAddress(workRecordRemarkDTO.getCustomerContactInfo());
            // 如果是在线聊天,则存储名称
            if (workRecordRemarkDTO.getChannelTypeId().equals(CrmChannelEnum.WEB_CHAT.getCode().toString())) {
                workRecordReplyDTO.setDesAddress(workRecordRemarkDTO.getChatUserName());
            }
            workRecordReplyDTO.setWorkRecordId(ticketId);
            workRecordReplyDTO.setChannelId(workRecordRemarkDTO.getChannelId());
            workRecordReplyDTO.setSubject(createWorkOrderRecordVO.getWorkRecordTheme());
            workRecordReplyDTO.setWorkRecordFileList(createWorkOrderRecordVO.getWorkRecordFileList());
            workRecordReplyDTO.setSendingMail(workRecordRemarkDTO.getSendingMail());
            // 公司id
            workRecordReplyDTO.setCompanyId(createWorkOrderRecordVO.getCompanyId());
            workRecordReplyDTO.setContent(createWorkOrderRecordVO.getWorkRecordContent());
            workRecordReplyDTO.setAutoType(workRecordRemarkDTO.getAutoType());
            work = this.autoReplyMessage(workRecordReplyDTO, workRecordRemarkDTO.getContactId(), workRecordRemarkDTO.getConnectId());
        }
        return AjaxResult.ok(ticketId);
    }

    @Override
    public AjaxResult<Object> ticketLeastAgent(WorkRecordCreateDTO workRecordRemarkDTO) throws IOException {
        // 首先根据部门id，查询出该部门下的所有坐席
        R<List<UserDetailsVO>> userListResponse = userClient.queryUserListByDeptId(workRecordRemarkDTO.getDeptId());
        // 如果没有信息，则返回null
        if (userListResponse == null || userListResponse.getCode() != 200 || userListResponse.getData() == null) {
            return AjaxResult.ok();
        }
        List<UserDetailsVO> userDetailsList = userListResponse.getData();
        List<String> agentList = userDetailsList.stream().map(UserDetailsVO::getUserId).collect(Collectors.toList());

        // 定义索引名称
        String indexName = ticketIndex + workRecordRemarkDTO.getCompanyId();
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 不查询机器人工单
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
        boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));
        BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                .should(QueryBuilders.termQuery("status", 0))
                .should(QueryBuilders.termQuery("status", 1))
                .should(QueryBuilders.termQuery("status", 2))
                .minimumShouldMatch(1);
        // 将 statusQuery 添加到主查询中
        boolQueryBuilder.must(statusQuery);
        boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", workRecordRemarkDTO.getDeptId()));
        // 构建 terms 聚合
        TermsAggregationBuilder termsAgg = AggregationBuilders
                .terms("by_agent_id")
                .field("agent_id")
                .size(10000);

        termsAgg.order(BucketOrder.count(true));
        searchSourceBuilder.query(boolQueryBuilder).size(0);
        searchSourceBuilder.aggregation(termsAgg);
        // 设置请求源
        request.source(searchSourceBuilder);
        // 进行查询
        SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        List<Map<String, Object>> resultList = getMaps(response, agentList);
        // 根据 count 排序，按升序序排列
        resultList = resultList.stream()
                .sorted(Comparator.comparingLong((Map<String, Object> m) -> (Long) m.get("count")))
                .collect(Collectors.toList());
        return AjaxResult.ok(resultList.get(0).get("agent_id"));
    }

    @Override
    public AjaxResult updateReplyTicket(WorkRecordReplyDTO workRecordReplyDTO) {
        // 定义索引名称
        String indexName = workContentIndex+workRecordReplyDTO.getCompanyId();
        // 根据工单回复id修改回复内容
        UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(indexName);
        updateRequest.setQuery(QueryBuilders.termQuery("work_record_content_id", workRecordReplyDTO.getContentId()));
        // 构建脚本参数
        Map<String, Object> params = new HashMap<>();
        StringBuilder scriptBuilder = new StringBuilder();
        params.put("incoming_intent_id", workRecordReplyDTO.getIncomingIntentId());
        scriptBuilder.append("ctx._source.incoming_intent_id = params.incoming_intent_id; ");
        params.put("incoming_intent_name", workRecordReplyDTO.getIncomingIntentName());
        scriptBuilder.append("ctx._source.incoming_intent_name = params.incoming_intent_name; ");
        params.put("intelligent_agent_id", workRecordReplyDTO.getIntelligentAgentId());
        scriptBuilder.append("ctx._source.intelligent_agent_id = params.intelligent_agent_id; ");
        params.put("intelligent_agent_name", workRecordReplyDTO.getIntelligentAgentName());
        scriptBuilder.append("ctx._source.intelligent_agent_name = params.intelligent_agent_name; ");
        // 设置脚本
        updateRequest.setScript(
                new Script(ScriptType.INLINE, "painless",
                        scriptBuilder.toString(),
                        params)
        );
        log.info("打印一下更新工单回复脚本内容:{}", updateRequest);
        // 执行更新
        try {
            BulkByScrollResponse bulkResponse = restHighLevelClient.updateByQuery(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("更新工单回复内容报错", e);
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    /**
     * 消息撤回
     * @param recallVO 撤回消息
     * @return
     */
    @Override
    public AjaxResult messageRecall(MessageRecallVO recallVO) {
        try {
            // 定义索引名称
            String indexName = workContentIndex+recallVO.getCompanyId();
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
            }

            // 插入消息前判断该消息是否存在 根据contentId判断
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("work_record_content_id", recallVO.getContentId()))
                    .must(QueryBuilders.termQuery("work_record_id", recallVO.getWorkRecordId()))
            );
            SearchRequest searchRequest = new SearchRequest(indexName).source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            //当前消息不存在
            if (searchResponse.getHits().getTotalHits().value == 0) {
                return AjaxResult.ok();
            }
            SearchHits hits = searchResponse.getHits();
            String esId = hits.getHits()[0].getId();
            if (hits != null) {
                try {
                    TicketContentIndex ticketContentIndex = new TicketContentIndex();
                    for (SearchHit hit : hits) {
                        Map<String, Object> source = hit.getSourceAsMap();
                        ticketContentIndex = BeanUtil.toBean(source, TicketContentIndex.class);
                    }
//                    if(type == 2){
//                        //进行修改保存 - 逻辑删除
//                    ticketContentIndex.setData_status("0");
//                    }else if(type == 1) {
                        //变更成系统消息 回复内容类型 1、文本 2、图片 3、视频 4、附件 5、音频
                        //增加一个超出的类型用于系统返回 999
                        ticketContentIndex.setData_status("999");
//                    }
                    elasticsearchUtil.updateDocument(indexName,esId,ticketContentIndex);
                } catch (Exception e) {
                    log.error("查询es报错", e);
                }
            }
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es撤回工单聊天数据失败，异常信息：", e);
            }
        }
        return AjaxResult.ok();
    }

    private static List<Map<String, Object>> getMaps(SearchResponse response, List<String> agentList) {
        Map<String, Long> agentIdCountMap = new HashMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (response != null) {
            Aggregations aggregations = response.getAggregations();
            Terms agentAgg = aggregations.get("by_agent_id");
            for (Terms.Bucket bucket : agentAgg.getBuckets()) {
                String agentId = bucket.getKeyAsString();
                long count = bucket.getDocCount();
                agentIdCountMap.put(agentId, count);
            }
        }
        for (String agentId : agentList) {
            Map<String, Object> result = new HashMap<>();
            result.put("agent_id", agentId);
            result.put("count", agentIdCountMap.getOrDefault(agentId, 0L)); // 如果没有该 agent_id，则赋值为 0
            resultList.add(result);
        }
        return resultList;
    }


    /**
     * 修改机器人工单转人工状态
     *
     * @param esId      id标识
     * @param companyId 公司id
     */
    private void updateRobotTicketFlag(String esId, String companyId, TicketInfoIndex ticketInfoIndex, ObjectMapper objectMapper) {

        try {
            // 进行修改保存操作日志
            UpdateRequest updateRequest = new UpdateRequest(ticketIndex + companyId, esId);
            String value = objectMapper.writeValueAsString(ticketInfoIndex);
            updateRequest.doc(value, XContentType.JSON);
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es修改文档失败，异常信息：", e);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 将机器人的聊天内容复制到新的工单中
     *
     * @param rootWorkId 机器人工单id
     * @param workId     工单id
     */
    private String copyWorkChatContent(String rootWorkId, String workId, String companyId) {
        // 定义客户首次回复消息时间
        String customerFirstReplyTime = null;
        // 查询出该工单下的聊天内容
        // 定义索引名称
        String indexName = workContentIndex + companyId;
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("work_record_id", rootWorkId));
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(10000);
        request.source(searchSourceBuilder);
        List<TicketContentVo> inboundIntentList = new ArrayList<>();
        try {
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            if (hits.length > 0) {
                // 定义BulkRequest
                BulkRequest bulkRequest = new BulkRequest(indexName);
                for (SearchHit hit : hits) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    if (hit.getScore() < 0) {
                        continue;
                    }

                    String s = JSON.toJSONString(source);
                    IndexRequest indexRequest = new IndexRequest();
                    TicketContentIndex contentIndex = JSON.parseObject(s, TicketContentIndex.class);
                    contentIndex.setWork_record_content_id(UuidUtils.generateUuid());
                    contentIndex.setWork_record_id(workId);
                    // 当客户首次回复消息时间为null，并且回复类型为客户时，取出回复时间
                    if (contentIndex.getReply_type() == 2 && customerFirstReplyTime == null) {
                        customerFirstReplyTime = contentIndex.getReply_time();
                    }
                    indexRequest.source(JSON.toJSONString(contentIndex), XContentType.JSON);
                    bulkRequest.add(indexRequest);
                    // 将内容及属性保存
                    TicketContentVo intentVo = new TicketContentVo();
                    intentVo.setContent(contentIndex.getContent());
                    intentVo.setReplyType(contentIndex.getReply_type());
                    inboundIntentList.add(intentVo);
                }
                // 批量插入
                BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            }
        } catch (Exception e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("ES保存工单聊天数据失败，异常信息：", e);
            }
        }
        // TODO 发送进线意图AIGC
        addInboundIntent(inboundIntentList, workId, companyId);
        return customerFirstReplyTime;
    }


    private void addInboundIntent(List<TicketContentVo> inboundIntentList, String workId, String companyId) {

        InboundIntentVo inboundIntentVo = new InboundIntentVo();
        // 创建 ObjectMapper 实例
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 使用Stream API进行转换
            List<String> resultList = inboundIntentList.stream()
                    .map(ticketContentVo -> {
                        String prefix;
                        if (ticketContentVo.getReplyType() == 1 || ticketContentVo.getReplyType() == 3) {
                            prefix = "agent:";
                        } else if (ticketContentVo.getReplyType() == 2) {
                            prefix = "customer:";
                        } else {
                            // 处理其他情况，如果有的话
                            prefix = "";
                        }
                        return prefix + ticketContentVo.getContent();
                    })
                    .collect(Collectors.toList());
            // 将 List 转换为 JSON 字符串
            String json = objectMapper.writeValueAsString(resultList);
            inboundIntentVo.setContent(json);
            inboundIntentVo.setTicketId(workId);
            log.info("开始发送进线意图:{}", inboundIntentVo);
            R<SmartSummaryResultVo> smartSummaryResultVoR = aigcChatClient.inboundIntent(companyId, null, inboundIntentVo);
            log.info("进线意图发送成功，接口返回{}", smartSummaryResultVoR);
        } catch (Exception e) {
            log.error("转换json报错:", e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> autoReplyMessage(WorkRecordReplyDTO workRecordReplyDTO, String contactId, String connectId) {
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex, workRecordReplyDTO.getWorkRecordId(), workRecordReplyDTO.getCompanyId());
        if (hit != null) {
            try {
                // 获取到_id
                esId = hit.getId();
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错", e);
            }
        }
        log.info("打印一下请求参数:{}", workRecordReplyDTO);
//        if(workRecordReplyDTO.getAutoType() == 1 &&
//                CrmChannelEnum.PHONE.getCode().toString().equals(workRecordReplyDTO.getChannelTypeId())){
//            // 如果是机器人电话工单，则将区域保存
//            ticketInfoIndex.setRegion(workRecordReplyDTO.getRegion());
//            // 机器人电话工单需要将区域修改保存
//            updateEsTicket(ticketInfoIndex,esId);
//            log.info("修改区域成功，工单id为:"+esId);
//        }

        // 获取渠道配置
        CrmChannelConfig channelConfig = null;
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getChannelId())) {
            channelConfig = crmChannelConfigService.getById(workRecordReplyDTO.getChannelId());
        }
        SysUserVo loginUser = new SysUserVo();
        // 判断是否为机器人(未登录)的回复
        if (workRecordReplyDTO.getAutoType() == 2) {
            loginUser = SecurityUtil.getLoginUser();
        } else {
            if (StringUtil.isNotEmpty(workRecordReplyDTO.getChannelId())) {
                String robotName = getRobotName(workRecordReplyDTO.getChannelId());
                loginUser.setUserName(robotName);
            }
            loginUser.setUserId("");
            loginUser.setCompanyId(workRecordReplyDTO.getCompanyId());
        }
        workRecordReplyDTO.setContactId(contactId);
        // TODO 内容存储
        String workRecordContentId = recordContentSave(workRecordReplyDTO, ticketInfoIndex, connectId, loginUser, 0, esId);
        try {
            // 只有为人工类型的工单，并且回复类型不为null 才会进行存储redis
            if (workRecordReplyDTO.getAutoType() != 1 && workRecordReplyDTO.getReplyType() !=null) {
                crmAgentWorkRecordSlaDefService.redisDataCache(
                        workRecordReplyDTO.getCompanyId(),
                        workRecordReplyDTO.getWorkRecordId(),
                        workRecordReplyDTO.getReplyType(),
                        LocalDateTime.now()
                );
            }
        }catch (Exception e){
            log.error("存储redis报错:",e);
        }
        // 工单回复
//        if(workRecordReplyDTO.getStatus() == 1){
//            workRecord.setStatus(2);
//            workRecord.setModifier(SecurityUtil.getUserId());
//            workRecord.setModifyTime(new Date());
//            crmAgentWorkRecordMapper.updateById(workRecord);
//        }
        // TODO 发送邮件
        AjaxResult<Object> result = sendMail(workRecordReplyDTO, channelConfig, loginUser);
        if (StringUtil.isNotNull(result)) {
            return result;
        }
        return AjaxResult.ok(workRecordReplyDTO.getWorkRecordId(), MessageUtils.get("operate.success"));
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> replyMessage(WorkRecordReplyDTO workRecordReplyDTO) {
        // 创建Gson实例
        Gson gson = new Gson();

        // 将Java对象转换为JSON字符串
        String dtoJson = gson.toJson(workRecordReplyDTO);
        log.info("回复工单json信息:" + dtoJson);

        // 获取渠道配置
        CrmChannelConfig channelConfig = null;
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getChannelId())) {
            channelConfig = crmChannelConfigService.getById(workRecordReplyDTO.getChannelId());
        }
        // 如果公司id为null，则取当前登录用户的公司id
        if (StringUtil.isEmpty(workRecordReplyDTO.getCompanyId())) {
            workRecordReplyDTO.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        }
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex, workRecordReplyDTO.getWorkRecordId(), workRecordReplyDTO.getCompanyId());
        if (hit != null) {
            try {
                // 获取到_id
                esId = hit.getId();
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错", e);
            }
        }
        // 当前工单存在，并且回复类型为客户时，才进行判断
        if (ticketInfoIndex != null && workRecordReplyDTO.getReplyType() != null && workRecordReplyDTO.getReplyType() == 2) {
            // 定义社媒类型是否需要校验客户时间 默认0
            Integer mediaTimeCheckCount = 0;
            if (ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.FACEBOOK.getCode().toString())
                    || ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WHATSAPP.getCode().toString())) {
                mediaTimeCheckCount = CrmChannelEnum.FACEBOOK.getMessageConfine();
            }
            if (ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getCode().toString())
                    || ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WECHAT_OFFICIAL_ACCOUNT.getCode().toString())) {
                mediaTimeCheckCount = CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getMessageConfine();
            }
            // 当mediaTimeCheckCount不为0，则是社媒满足消息限制
            if (mediaTimeCheckCount != 0) {
                if (ticketInfoIndex.getCustomerFirstMessageTime() != null) {
                    // 将 Date 转换为 LocalDateTime
                    LocalDateTime replyTime = ticketInfoIndex.getCustomerFirstMessageTime();
                    // 获取当前时间
                    LocalDateTime now = LocalDateTime.now();
                    // 计算当前时间和回复时间的差值
                    long hoursDifference = ChronoUnit.HOURS.between(replyTime, now);
                    // 判断是否超过24小时,超过24小时则更新首次时间
                    if (hoursDifference >= mediaTimeCheckCount) {
                        ticketInfoIndex.setCustomerFirstMessageTime(LocalDateTime.now());
                        updateEsTicket(ticketInfoIndex, esId);
                    }
                }
            }
        }
        if (ticketInfoIndex == null) {
//            return AjaxResult.failure(MessageUtils.get("error.null.record"));
            //由于robot自动工单那边，创建工单和回复工单是提前设置好了workRecordId，所以此处注释掉上述判空return，进行如下逻辑的修改
            ticketInfoIndex = new TicketInfoIndex();
            ticketInfoIndex.setWorkRecordId(workRecordReplyDTO.getWorkRecordId())
                    .setChannelTypeId(workRecordReplyDTO.getChannelTypeId());

        }

        SysUserVo loginUser = new SysUserVo();
        // 判断是否为机器人(未登录)的回复
        if (workRecordReplyDTO.getAutoType() == 1) {
            String robotName = getRobotName(workRecordReplyDTO.getChannelId());
            loginUser.setUserName(robotName);
            loginUser.setUserId("");
            loginUser.setCompanyId(workRecordReplyDTO.getCompanyId());
        } else {
            loginUser = SecurityUtil.getLoginUser();
        }
        // TODO 内容存储
        String workRecordContentId = recordContentSave(workRecordReplyDTO, ticketInfoIndex, null, loginUser, 1, esId);
        try {
            // 只有为人工类型的工单，并且回复类型不为null 才会进行存储redis
            if(workRecordReplyDTO.getAutoType() != 1 && workRecordReplyDTO.getReplyType() !=null) {
                crmAgentWorkRecordSlaDefService.redisDataCache(
                        workRecordReplyDTO.getCompanyId(),
                        workRecordReplyDTO.getWorkRecordId(),
                        workRecordReplyDTO.getReplyType(),
                        LocalDateTime.now()
                );
            }
        }catch (Exception e){
            log.error("存储redis报错:",e);
        }
        // TODO 发送邮件
        AjaxResult<Object> result = sendMail(workRecordReplyDTO, channelConfig, loginUser);
        if (StringUtil.isNotNull(result)) {
            return result;
        }
        Object o = redisTemplate.opsForValue().get(workRecordReplyDTO.getCompanyId() + workRecordReplyDTO.getWorkRecordId());
        TimeVO timeVO = countGradeTime(
                ticketInfoIndex.getCreateTime(),
                ticketInfoIndex.getChannelTypeId(),
                ticketInfoIndex.getWorkRecordTypeCode(),
                ticketInfoIndex.getPriorityLevelId(),
                ticketInfoIndex.getCompanyId()
        );
        ticketInfoIndex.setShouldResolveTime(timeVO.getShouldResolveTime());
        ticketInfoIndex.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
        ticketInfoIndex.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
        //进行国际化数据装填
        //已经存在过redis缓存无需二次装填
        String day = internationalClient.systemLanguages("天").getData();
        String hour = internationalClient.systemLanguages("时").getData();
        String minute = internationalClient.systemLanguages("分").getData();
        //判断当前工单状态
        if(ObjectUtil.isNotNull(ticketInfoIndex.getSessionStatus()) && ticketInfoIndex.getSessionStatus() == 3){
            //当前为待解决状态
            //座席未回复过
            ScheduleWorkVO scheduleWorkVO = new ScheduleWorkVO();
            scheduleWorkVO.setTimeType(2L);
            scheduleWorkVO.setWorkRecordId(ticketInfoIndex.getWorkRecordId());
            //时间非空判断
            if (ObjectUtil.isNotNull(ticketInfoIndex.getShouldResolveTime())) {
                LocalDateTime dateTime =LocalDateTime.now();
                if (dateTime.compareTo(ticketInfoIndex.getShouldResolveTime()) < 1) {
                    scheduleWorkVO.setTimeStatus(2L);
                    Duration duration = Duration.between(dateTime,ticketInfoIndex.getShouldResolveTime());
                    String convert = convert(duration,day,hour,minute);
                    scheduleWorkVO.setScheduleTime(convert);
                } else {
                    scheduleWorkVO.setTimeStatus(1L);
                    Duration duration = Duration.between(ticketInfoIndex.getShouldResolveTime(),dateTime);
                    String convert = convert(duration,day,hour,minute);
                    scheduleWorkVO.setScheduleTime(convert);
                }
            }
            return AjaxResult.ok(scheduleWorkVO);
        }
        if(ObjectUtil.isNotNull(o) && ObjectUtil.isNotEmpty(o)){
            List<RedisDataCacheVO> redisDataCacheVOList = Convert.toList(RedisDataCacheVO.class, o);
            //判断是否存在过座席回复
            List<RedisDataCacheVO> collect = redisDataCacheVOList.stream().filter(RedisDataCacheVO -> RedisDataCacheVO.getReplyType() == 1).collect(Collectors.toList());
            if(ObjectUtil.isNull(collect) || collect.size() == 0){
                //座席未回复过
                ScheduleWorkVO scheduleWorkVO = new ScheduleWorkVO();
                scheduleWorkVO.setTimeType(1L);
                scheduleWorkVO.setWorkRecordId(ticketInfoIndex.getWorkRecordId());
                if(ObjectUtil.isNotNull(workRecordReplyDTO.getReplyType())){
                    if( workRecordReplyDTO.getReplyType() == 2){
                        //拿redis中的客户首次信息
                        //计算首次响应时间
                        LocalDateTime dateTime = Convert.toLocalDateTime(redisDataCacheVOList.get(0).getTime()).plusSeconds(ticketInfoIndex.getFirstResponseTime());
                        LocalDateTime later = LocalDateTime.now();
                        if (dateTime.compareTo(later) < 1) {
                            scheduleWorkVO.setTimeStatus(1L);
                            Duration duration = Duration.between(dateTime, later);
                            String convert = convert(duration,day,hour,minute);
                            scheduleWorkVO.setScheduleTime(convert);
                        } else {
                            scheduleWorkVO.setTimeStatus(2L);
                            Duration duration = Duration.between(later, dateTime);
                            String convert = convert(duration,day,hour,minute);
                            scheduleWorkVO.setScheduleTime(convert);
                        }
                        return AjaxResult.ok(scheduleWorkVO);
                    }else if(workRecordReplyDTO.getReplyType() ==1){
                        //客服回复不回显时间
                        return AjaxResult.ok(scheduleWorkVO);
                    }
                }
            }else{
                ScheduleWorkVO scheduleWorkVO = new ScheduleWorkVO();
                scheduleWorkVO.setTimeType(1L);
                scheduleWorkVO.setWorkRecordId(ticketInfoIndex.getWorkRecordId());
                //存在过客服回复
                //如果是用户信息
                if(ObjectUtil.isNotNull(workRecordReplyDTO.getReplyType())){
                    if(workRecordReplyDTO.getReplyType() == 1){
                        return AjaxResult.ok(scheduleWorkVO);
                    }else {
                        //是用户
                        //获取redis中的最后条用户时间
                        //计算平均响应时间
                        //用用户发消息时间+下一次平均响应时间
                        List<RedisDataCacheVO> collect1 = redisDataCacheVOList.stream().filter(RedisDataCacheVO -> RedisDataCacheVO.getReplyType() == 2).collect(Collectors.toList());
                        RedisDataCacheVO redisDataCacheVO = collect1.get(collect1.size() - 1);
                        LocalDateTime dateTime = Convert.toLocalDateTime(redisDataCacheVO.getTime()).plusSeconds(ticketInfoIndex.getAvgResponseTime());
                        LocalDateTime later = LocalDateTime.now();
                        if (dateTime.compareTo(LocalDateTime.now()) < 1) {
                            scheduleWorkVO.setTimeStatus(1L);
                            Duration duration = Duration.between(dateTime, later);
                            String convert = convert(duration,day,hour,minute);
                            scheduleWorkVO.setScheduleTime(convert);
                        } else {
                            scheduleWorkVO.setTimeStatus(2L);
                            Duration duration = Duration.between(later, dateTime);
                            String convert = convert(duration,day,hour,minute);
                            scheduleWorkVO.setScheduleTime(convert);
                        }
                        return AjaxResult.ok(scheduleWorkVO);
                    }
                }
                }
        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }



    /**
     * 返回计算好的时间字符串，天、时、分
     * 去除秒字段
     *
     * @param duration
     * @return
     */
    public String convert(Duration duration,String day,String hour,String minute) {
        String date = "";
        Integer seconds = Convert.toInt(duration.getSeconds());
        int days = seconds / (24 * 60 * 60);
        int remainingSeconds = seconds % (24 * 60 * 60);
        int hours = remainingSeconds / (60 * 60);
        remainingSeconds = remainingSeconds % (60 * 60);
        int minutes = remainingSeconds / 60;
        remainingSeconds = remainingSeconds % 60;

        // 若有剩余秒数，进行向上取整
        if (remainingSeconds > 0) {
            minutes++;
        }

        // 处理分钟数满 60 进 1 到小时
        hours += minutes / 60;
        minutes = minutes % 60;

        // 处理小时数满 24 进 1 到天
        days += hours / 24;
        hours = hours % 24;
        String daysString = days == 0 ? "" : days + day;
        String hoursString = hours == 0 ? "" : hours + hour;
        String minutesString = minutes == 0 ? "" : minutes + minute;
        date = daysString+hoursString+minutesString;
        return date;
    }


    /**
     * 处理需要跨服务调用的数据
     *
     * @param createWorkOrderRecordVO 创建请求参数
     * @param workRecordReplyDTO      拼接工单回复参数
     * @param loginUser               登录人信息
     */
    private void workAttributeHandle(CreateWorkOrderRecordVO createWorkOrderRecordVO, WorkRecordReplyDTO workRecordReplyDTO, SysUserVo loginUser, TicketInfoIndex ticketInfoIndex) {
        // 根据工单类型ID查询工单类型
        if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getWorkRecordTypeId())) {
            // 因国际化问题,工单类型使用code进行关联. 前端无需改动,所以名称还是id,手动将其存放至code
            ticketInfoIndex.setWorkRecordTypeCode(createWorkOrderRecordVO.getWorkRecordTypeId());
        }
        // 根据优先级ID查询优先级
        if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getPriorityLevelId())) {
            CrmAgentWorkRecordPriorityLevelDef workLevel = crmAgentWorkRecordPriorityLevelDefService.getById(createWorkOrderRecordVO.getPriorityLevelId());
            ticketInfoIndex.setPriorityLevelName(workLevel.getPriorityLevelCode());
            // 计算终止时间
            TimeVO timeVO = countGradeTime(LocalDateTime.now(),createWorkOrderRecordVO.getChannelTypeId(), createWorkOrderRecordVO.getWorkRecordTypeId(), createWorkOrderRecordVO.getPriorityLevelId(), loginUser.getCompanyId());
            ticketInfoIndex.setShouldResolveTime(timeVO.getShouldResolveTime());
            ticketInfoIndex.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
            ticketInfoIndex.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
        }
        if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getDeptId())) {
            ticketInfoIndex.setDeptId(createWorkOrderRecordVO.getDeptId());
        }
        // 如果没有选择座席,那么状态为待分配
        if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getAgentId())) {
            ticketInfoIndex.setStatus(1);
            ticketInfoIndex.setAcceptType(0);
            // 当坐席不为机器人，才设置分配时间并且查询坐席名称以及部门id
            if (!ticketInfoIndex.getAgentId().equals("1001")) {
                ticketInfoIndex.setInitiationTime(LocalDateTime.now());
                // 根据座席ID查询座席名称
                R<UserDetailsVO> userDetails = userClient.queryUserDetails(createWorkOrderRecordVO.getAgentId());
                if (userDetails.getCode() == AjaxResult.SUCCESS) {
                    ticketInfoIndex.setAgentName(userDetails.getData().getUserName());
                    ticketInfoIndex.setDeptId(userDetails.getData().getDeptId());
                }
            }
        }
        // 判断渠道ID是否为null
        if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getChannelId())) {
            // 定义为渠道类型id,实际为渠道id
            R<CrmChannelConfigVO> channelConfig = channelClient.queryChannelConfig(createWorkOrderRecordVO.getChannelId());
            workRecordReplyDTO.setChannelId(createWorkOrderRecordVO.getChannelId());
            if (channelConfig.getCode() == AjaxResult.SUCCESS) {
                ticketInfoIndex.setChannelConfigId(channelConfig.getData().getChannelId());
                ticketInfoIndex.setChannelConfigName(channelConfig.getData().getName());
                ticketInfoIndex.setChannelTypeId(channelConfig.getData().getChannelType().toString());
                R<CrmChannelDefVO> channelDef = channelClient.queryChannelDef(channelConfig.getData().getChannelType().toString());
                if (channelDef.getCode() == AjaxResult.SUCCESS) {
                    ticketInfoIndex.setChannelTypeName(channelDef.getData().getName());
                }
            }
        }
        // 客户ID查询客户电话
        R<CrmCustomerVO> customerDetails = customerClient.queryCustomerDetails(createWorkOrderRecordVO.getCustomerId());
        if (customerDetails.getCode() == AjaxResult.SUCCESS) {
            CrmCustomerVO customer = customerDetails.getData();
            ticketInfoIndex.setCustomerName(customer.getName());
            // TODO 后续增加渠道时,需要根据不同的渠道存储不同的联系方式
            if (StringUtil.isNotEmpty(ticketInfoIndex.getChannelTypeId())) {
                if (CrmChannelEnum.EMAIL.getCode().equals(Integer.valueOf(ticketInfoIndex.getChannelTypeId()))
                        || CrmChannelEnum.WEB_CHAT.getCode().equals(Integer.valueOf(ticketInfoIndex.getChannelTypeId()))
                        || CrmChannelEnum.APP_CHAT.getCode().equals(Integer.valueOf(ticketInfoIndex.getChannelTypeId()))
                        || CrmChannelEnum.WEB_VIDEO.getCode().equals(Integer.valueOf(ticketInfoIndex.getChannelTypeId()))
                        || CrmChannelEnum.APP_VIDEO.getCode().equals(Integer.valueOf(ticketInfoIndex.getChannelTypeId()))) {
                    ticketInfoIndex.setCustomerTelephone(customer.getEmailAddress());
                }
                if (CrmChannelEnum.PHONE.getCode().equals(Integer.valueOf(ticketInfoIndex.getChannelTypeId()))
                        || CrmChannelEnum.WHATSAPP.getCode().equals(Integer.valueOf(ticketInfoIndex.getChannelTypeId()))) {
                    // 判断电话区号是否为null，不为null则进行拼接
                    if (StringUtil.isNotEmpty(customer.getTelephonePrefixId())) {
                        ticketInfoIndex.setCustomerTelephone(customer.getTelephonePrefixId() + customer.getTelephone());
                    } else {
                        ticketInfoIndex.setCustomerTelephone(customer.getTelephone());
                    }
                }
            }
            if (StringUtil.isNotEmpty(customer.getEmailAddress())) {
                workRecordReplyDTO.setDesAddress(customer.getEmailAddress());
            }
        }
        // 判断渠道类型ID是否为
        if (StringUtil.isNotEmpty(createWorkOrderRecordVO.getChannelTypeId())) {
            ticketInfoIndex.setChannelTypeId(createWorkOrderRecordVO.getChannelTypeId());
            workRecordReplyDTO.setChannelTypeId(createWorkOrderRecordVO.getChannelTypeId());
            R<CrmChannelDefVO> channelDef = channelClient.queryChannelDef(createWorkOrderRecordVO.getChannelTypeId());
            if (channelDef.getCode() == AjaxResult.SUCCESS) {
                ticketInfoIndex.setChannelTypeName(channelDef.getData().getName());
            }
        }
        // 创建工单时，设置消息到达时间
        ticketInfoIndex.setLastMessageDeliveryTime(LocalDateTime.now());
        // 判断是否为自动工单
        if (createWorkOrderRecordVO.getCreateType() == 0) {
            ticketInfoIndex.setStatus(1);
        }
    }

    /**
     * 工单明细信息存储
     *
     * @param workRecord 工单信息
     * @param contactId  contactId
     * @param connectId  connectId
     * @param type       明细类型
     * @param loginUser  登录人信息
     * @return 明细id
     */
    private String recordDetailSave(CrmAgentWorkRecord workRecord, String contactId, String connectId, Integer type, SysUserVo loginUser) {
        CrmAgentWorkRecordDetail workRecordDetail = new CrmAgentWorkRecordDetail();
        workRecordDetail.setWorkRecordDetailId(UuidUtils.generateUuid())
                .setWorkRecordId(workRecord.getWorkRecordId())
                .setContactId(contactId)
                .setConnectId(connectId)
                .setType(type);
        workRecordDetail.init(loginUser);
        crmAgentWorkRecordDetailService.save(workRecordDetail);
        return workRecordDetail.getWorkRecordDetailId();
    }

    /**
     * 工单内容存储
     *
     * @param workRecordReplyDTO 前端请求参数
     * @param ticketInfoIndex    工单信息
     * @param connectId          connectId
     * @param loginUser          登录人信息
     * @param createType         自动创建还是手动创建 0 自动创建  1手动创建
     * @return 工单内容id
     */
    private String recordContentSave(WorkRecordReplyDTO workRecordReplyDTO, TicketInfoIndex ticketInfoIndex, String connectId, SysUserVo loginUser, int createType, String esId) {
        TicketContentIndex ticketContentIndex = new TicketContentIndex();
//        ticketContentIndex.setWork_record_content_id(UuidUtils.generateUuid());
        ticketContentIndex.setWork_record_content_id(StringUtil.isNotBlank(workRecordReplyDTO.getContentId()) ? workRecordReplyDTO.getContentId() : UuidUtils.generateUuid());
        ticketContentIndex.setWork_record_id(ticketInfoIndex.getWorkRecordId());
        //引用聊天记录
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getReferenceContent())) {
            ticketContentIndex.setReference_content(workRecordReplyDTO.getReferenceContent());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getReferenceContentFile())) {
            ticketContentIndex.setReference_content_file(workRecordReplyDTO.getReferenceContentFile());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getReferenceContentId())) {
            ticketContentIndex.setReference_work_record_content_id(workRecordReplyDTO.getReferenceContentId());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getReferenceReplyPerson())) {
            ticketContentIndex.setReference_reply_person(workRecordReplyDTO.getReferenceReplyPerson());
        }
        if (ObjectUtil.isNotEmpty(workRecordReplyDTO.getReferenceContentType()) && ObjectUtil.isNotNull(workRecordReplyDTO.getReferenceContentType())) {
            ticketContentIndex.setReference_content_type(workRecordReplyDTO.getReferenceContentType());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getIncomingIntentId())) {
            ticketContentIndex.setIncoming_intent_id(workRecordReplyDTO.getIncomingIntentId());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getIncomingIntentName())) {
            ticketContentIndex.setIncoming_intent_name(workRecordReplyDTO.getIncomingIntentName());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getIntelligentAgentId())) {
            ticketContentIndex.setIntelligent_agent_id(workRecordReplyDTO.getIntelligentAgentId());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getIntelligentAgentName())) {
            ticketContentIndex.setIntelligent_agent_name(workRecordReplyDTO.getIntelligentAgentName());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getContent())) {
            ticketContentIndex.setContent(workRecordReplyDTO.getContent());
        }
        ticketContentIndex.setConnect_id(connectId);
        ticketContentIndex.setContent_type(workRecordReplyDTO.getContentType());
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getContactId())) {
            ticketContentIndex.setContact_id(workRecordReplyDTO.getContactId());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getTranslateContent())) {
            ticketContentIndex.setTranslate_content(workRecordReplyDTO.getTranslateContent());
        }
        if (StringUtil.isNotEmpty(workRecordReplyDTO.getTranslateLanguage())) {
            ticketContentIndex.setTranslate_language(workRecordReplyDTO.getTranslateLanguage());
        }
        // 回复类型不为null,则进行存储
        if (workRecordReplyDTO.getReplyType() != null) {
            ticketContentIndex.setReply_type(workRecordReplyDTO.getReplyType());
        } else {
            // 如果回复类型为null，并且为邮件的话，设置为默认值
            ticketContentIndex.setReply_type(1);
        }
        if (workRecordReplyDTO.getReplyType() != null && workRecordReplyDTO.getReplyType() == 2) {
            // 设置已读状态为未读
            ticketContentIndex.setRead_status(0);
        } else {
            ticketContentIndex.setRead_status(1);
        }
        List<TicketFile> ticketFileList = new ArrayList<>();
        // 工作记录文件
        if (CollectionUtils.isNotEmpty(workRecordReplyDTO.getWorkRecordFileList())) {
            workRecordReplyDTO.getWorkRecordFileList().forEach(workFile -> {
                TicketFile ticketFile = new TicketFile();
                ticketFile.setFile_name(workFile.getFileName());
                ticketFile.setFile_path(workFile.getFileUrl());
                ticketFile.setBucket_name(workFile.getBucketName());
                ticketFileList.add(ticketFile);
            });
        }
        ticketContentIndex.setTicket_file(ticketFileList);
        // 判断是否为自动工单，不同工单类型邮件收发人不同
        if (createType == 0) {
            ticketContentIndex.setEmail_recipient(getReplyMessageForm(workRecordReplyDTO.getChannelId()));
            ticketContentIndex.setEmail_sender(workRecordReplyDTO.getDesAddress());
            ticketContentIndex.setEmail_cc_to(workRecordReplyDTO.getCopyAddress());
            ticketContentIndex.setEmail_bcc(workRecordReplyDTO.getBccAddress());
        } else {
            if (ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.EMAIL.getCode().toString())) {
                // 当没有客户联系方式时，进行查询
                if (StringUtil.isEmpty(workRecordReplyDTO.getDesAddress())) {
                    R<CrmCustomerVO> customerDetails = customerClient.queryCustomerDetails(ticketInfoIndex.getCustomerId());
                    if (customerDetails.getCode() == AjaxResult.SUCCESS) {
                        CrmCustomerVO data = customerDetails.getData();
                        workRecordReplyDTO.setDesAddress(data.getEmailAddress());
                    }
                }
                ticketContentIndex.setEmail_recipient(workRecordReplyDTO.getDesAddress());
                ticketContentIndex.setEmail_cc_to(workRecordReplyDTO.getCopyAddress());
                ticketContentIndex.setEmail_bcc(workRecordReplyDTO.getBccAddress());
                ticketContentIndex.setEmail_sender(getReplyMessageForm(workRecordReplyDTO.getChannelId()));
                if (workRecordReplyDTO.getStatus() == 1) {
                    ticketInfoIndex.setStatus(2);
                    ticketInfoIndex.setLastMessageReplyTime(LocalDateTime.now());
                    updateEsTicket(ticketInfoIndex, esId);
                }
            }
        }

        // 定义工单状态为待客户回复
        int status = 0;
        // 判断下 回复类型，如果为客户，则将工单状态修改为带座席处理
        if (StringUtil.isNotNull(workRecordReplyDTO.getReplyType())) {
            // 如果是客户 消息到达时间(根据客户回复的最新聊天记录时间进行更新)
            if (workRecordReplyDTO.getReplyType().equals(2)) {
                status = 1;
                ticketInfoIndex.setLastMessageDeliveryTime(LocalDateTime.now());
            }
            if (workRecordReplyDTO.getReplyType().equals(1)) {
                ticketInfoIndex.setLastMessageReplyTime(LocalDateTime.now());
                status = 2;
            }

        }
        // 是否需要执行更新消息到达时间
        boolean isMessage = true;
        // 除了这几种类型，都将名称拼入
        if (!ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.EMAIL.getCode().toString())
                && !ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.PHONE.getCode().toString())
                && !ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WEB_VIDEO.getCode().toString())
                && !ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.APP_VIDEO.getCode().toString())
                && !ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WEB_ONLINE_VOICE.getCode().toString())
                && !ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.APP_ONLINE_VOICE.getCode().toString())) {
            ticketContentIndex.setReply_person(workRecordReplyDTO.getChatUserName());

            if (workRecordReplyDTO.getStatus() == 1 && status != 0 && StringUtil.isNotEmpty(ticketInfoIndex.getAgentId())) {
                ticketInfoIndex.setStatus(status);
                updateEsTicket(ticketInfoIndex, esId);
                isMessage = false;
            }
            if (ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WHATSAPP.getCode().toString())) {
                // 如果回复类型是客户时，才发送whatsApp消息
                if (workRecordReplyDTO.getReplyType().equals(1)) {
                    // connect ccp发送信息
                    if (0 == SecurityUtil.getLoginUser().getOpenSelfChat()) {
                        whatsAppContent(workRecordReplyDTO);
                    }
                }
            }
        }
        if (isMessage) {
            // 修改工单消息回复到达时间
            updateEsTicket(ticketInfoIndex, esId);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date replyTime = workRecordReplyDTO.getReplyTime();
        if (replyTime == null) {
            replyTime = new Date();
            log.info("使用当前时间: {}", replyTime);
        }

        String formattedTime = sdf.format(replyTime);
        ticketContentIndex.setReply_time(formattedTime);
        ticketContentIndex.setReply_millis_time(String.valueOf(replyTime.getTime()));
        // 为手动工单的回复，才需要加上Re
//        if (workRecordReplyDTO.getStatus() == 1 && CrmChannelEnum.EMAIL.getCode().equals(Integer.valueOf(ticketInfoIndex.getChannelTypeId()))) {
//            ticketContentIndex.setEmail_subject("Re:" + (StringUtil.isEmpty(workRecordReplyDTO.getSubject()) ? workRecordReplyDTO.getDesAddress() : workRecordReplyDTO.getSubject()));
//        } else {
//            ticketContentIndex.setEmail_subject((StringUtil.isEmpty(workRecordReplyDTO.getSubject()) ? workRecordReplyDTO.getDesAddress() : workRecordReplyDTO.getSubject()));
//        }
        // 去除Re
            ticketContentIndex.setEmail_subject((StringUtil.isEmpty(workRecordReplyDTO.getSubject()) ? workRecordReplyDTO.getDesAddress() : workRecordReplyDTO.getSubject()));
        // 修改为es存储
        crmAgentWorkRecordService.addAgentWorkRecordContent(ticketContentIndex, loginUser.getCompanyId());
        // 如果是机器人电话工单，则发送MQ，进行语音转录操作
        if (workRecordReplyDTO.getAutoType() == 1 && ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.PHONE.getCode().toString())) {
            if (CollectionUtils.isNotEmpty(ticketContentIndex.getTicket_file())) {
                // 因为录音文件会有多条，所以需要循环的进行转录
                for (TicketFile ticketFile : ticketContentIndex.getTicket_file()) {
                    // 只有小段的进行转录，整体的不进行转录
                    if (ticketFile.getFile_name().contains("-")) {
                        // 翻译语言
                        String translateLanguage = workRecordReplyDTO.getTranslateLanguage();
                        String region = workRecordReplyDTO.getRegion();
                        String awsAccountId = workRecordReplyDTO.getAwsAccountId();
                        VoiceTranscribeVO voiceTranscribeVO = new VoiceTranscribeVO();
                        voiceTranscribeVO.setAwsUserId(awsAccountId);
                        voiceTranscribeVO.setCompanyId(loginUser.getCompanyId());
                        voiceTranscribeVO.setRegion(region);
                        voiceTranscribeVO.setBucketName(ticketFile.getBucket_name());
                        voiceTranscribeVO.setFilePath(ticketFile.getFile_path());
                        voiceTranscribeVO.setLanguage(translateLanguage);
                        voiceTranscribeVO.setFileName(ticketFile.getFile_name());
                        voiceTranscribeVO.setWorkRecordId(ticketContentIndex.getWork_record_id());
                        rescmgntClient.transcribe(voiceTranscribeVO);
                    }
                }
                log.info("机器人语音工单语音转录成功");
            }
            // 调用浩哥创建外部工单
            if (workRecordReplyDTO.getThirdPartyFlag() != null && workRecordReplyDTO.getThirdPartyFlag() == 1) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("dataId", ticketContentIndex.getWork_record_id());
                jsonObject.put("companyId", ticketInfoIndex.getCompanyId());

                //用于道通总结标题和摘要
                String questionKey = Constants.DAOTONG_TRANSCRIBE + workRecordReplyDTO.getCompanyId()
                        .concat(":").concat(workRecordReplyDTO.getContactId());
                String userEmailKey = questionKey.concat(":userEmail");
                String cIdKey = questionKey.concat(":certificateId");

                jsonObject.put("question", RedisCacheUtil.getCacheObject(questionKey));
                jsonObject.put("userEmail", RedisCacheUtil.getCacheObject(userEmailKey));
                jsonObject.put("certificateId", RedisCacheUtil.getCacheObject(cIdKey));
//            jsonObject.put("test",RedisCacheUtil.getCacheObject("test"));

                rabbitTemplate.convertAndSend(RabbitMqConstants.THIRD_PARTY_DATA_SYNC_EXCHANGE, RabbitMqConstants.THIRD_PARTY_DATA_SYNC_SALESFORCE_ROUTING_KEY, jsonObject.toJSONString());

                log.info("调用外部工单MQ成功");
            }
        }
        return ticketContentIndex.getWork_record_content_id();

    }

    private void updateEsTicket(TicketInfoIndex ticketInfoIndex, String esId) {
        try {
            // 定义索引名称
            String indexName = ticketIndex + ticketInfoIndex.getCompanyId();
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            // 注册 JavaTimeModule
            objectMapper.registerModule(new JavaTimeModule());

            // 进行修改保存操作日志
            UpdateRequest updateRequest = new UpdateRequest(indexName, esId);
            String value = objectMapper.writeValueAsString(ticketInfoIndex);
            updateRequest.doc(value, XContentType.JSON);
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            if (e.getMessage().contains("409")) {
                log.error("es修改文档失败，异常信息：", e);
            } else if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es修改文档失败，异常信息：", e);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * whatsApp回复内容特殊处理
     *
     * @param workRecordReplyDTO
     */
    private void whatsAppContent(WorkRecordReplyDTO workRecordReplyDTO) {
        // TODO 判断是客户还是座席， 为座席调用发送消息
        // TODO 查询座席手机号
        R<List<WhatsAppChannelDetailVo>> listR = channelClient.queryWhatsAppChannel();
        if (listR.getCode() == AjaxResult.SUCCESS) {
            List<WhatsAppChannelDetailVo> phoneList = listR.getData().stream().filter(vo -> vo.getChannelId().equals(workRecordReplyDTO.getChannelId()) && vo.getCode().equals("whatsapp_phone_number"))
                    .collect(Collectors.toList());
            if (!phoneList.isEmpty()) {
                WhatsAppChannelDetailVo whatsAppChannelDetailVo = phoneList.get(0);
                ConnectMessageVo connectMessageVo = new ConnectMessageVo();
                connectMessageVo.setCustomerPhone(workRecordReplyDTO.getCustomPhone());
                connectMessageVo.setPhone(whatsAppChannelDetailVo.getName());
                // 定义回复类型
                connectMessageVo.setMessageType(MessageType.getTypeByValue(workRecordReplyDTO.getContentType()));
                connectMessageVo.setMessage(workRecordReplyDTO.getContent());
                String url = workRecordReplyDTO.getContent();
                // 如果类型为附件需要取附件的url
                if (workRecordReplyDTO.getContentType().equals(MessageType.DOCUMENT.getCode()) && !workRecordReplyDTO.getWorkRecordFileList().isEmpty()) {
                    url = domainPrefix + workRecordReplyDTO.getWorkRecordFileList().get(0).getFileUrl();
                }
                connectMessageVo.setFileUrl(url);
                log.info("调用发送WhatsApp的请求参数为：{}", connectMessageVo);
                whatsAppClient.message(connectMessageVo);
            } else {
                log.error("未查询到该channelId下的手机号");
            }
        }
    }


    /**
     * 发送邮件方法
     *
     * @param workRecordReplyDTO 工单参数信息
     * @param channelConfig      平台渠道信息
     * @return 是否需要终止 为null 则无须终止
     */
    private AjaxResult<Object> sendMail(WorkRecordReplyDTO workRecordReplyDTO, CrmChannelConfig channelConfig, SysUserVo loginUser) {
        if (workRecordReplyDTO.getSendingMail() == 1) {

            if (channelConfig != null) {
                if (CrmChannelEnum.EMAIL.getCode().equals(channelConfig.getChannelType()) || (CrmChannelEnum.AMAZON.getCode().equals(channelConfig.getChannelType()) && workRecordReplyDTO.getReplyType() != null && workRecordReplyDTO.getReplyType() == 1 && 0 == SecurityUtil.getLoginUser().getOpenSelfChat())) {
                    // 判断发送邮件数量是否已经到达上限
                    String redisSendEmailCountKey = Constants.SEND_EMAIL_COUNT_PREFIX
                            .concat(LocalDate.now().format(DateTimeFormatter.ofPattern(Constants.DATE_FORMATTER)))
                            .concat(":")
                            .concat(SecurityUtil.getLoginUser().getCompanyId());
                    RLock lock = redissonClient.getLock(Constants.SEND_EMAIL_COUNT_LOCK.concat(SecurityUtil.getLoginUser().getCompanyId()));
                    lock.lock();
                    try {
                        Integer sendEmailCount = RedisCacheUtil.getCacheObject(redisSendEmailCountKey);
                        Integer sendEmailPerDayCount = SecurityUtil.getLoginUser().getSendEmailPerDayCount();
                        if ((Objects.nonNull(sendEmailCount) ? sendEmailCount.intValue() : 0) >= (Objects.nonNull(sendEmailPerDayCount) ? sendEmailPerDayCount.intValue() : 100)) {
                            return AjaxResult.failure(MessageUtils.get("send.email.limit.count"));
                        } else {
                            RedisCacheUtil.incr(redisSendEmailCountKey);
                        }
                    } catch (Exception e) {
                        log.error("发送邮件前上限数量的校验出现问题", e);
                    } finally {
                        lock.unlock();
                    }
                    // 发送邮件
                    SendMessageDTO sendMessageDTO = new SendMessageDTO();
                    sendMessageDTO.setChannelId(workRecordReplyDTO.getChannelId());
                    // 为手动工单的回复，才需要加上Re
//                    if (workRecordReplyDTO.getStatus() == 1) {
//                        sendMessageDTO.setSubject("Re:" + (StringUtil.isEmpty(workRecordReplyDTO.getSubject()) ? workRecordReplyDTO.getDesAddress() : workRecordReplyDTO.getSubject()));
//                    } else {
//                        sendMessageDTO.setSubject("" + (StringUtil.isEmpty(workRecordReplyDTO.getSubject()) ? workRecordReplyDTO.getDesAddress() : workRecordReplyDTO.getSubject()));
//                    }
                    sendMessageDTO.setSubject("" + (StringUtil.isEmpty(workRecordReplyDTO.getSubject()) ? workRecordReplyDTO.getDesAddress() : workRecordReplyDTO.getSubject()));
                    sendMessageDTO.setContent(workRecordReplyDTO.getContent());
                    // 如果是邮件，则直接取邮箱，并且加上抄送人密送人
                    if (CrmChannelEnum.EMAIL.getCode().equals(channelConfig.getChannelType())) {
                        sendMessageDTO.setDesAddress(workRecordReplyDTO.getDesAddress());
                        sendMessageDTO.setCopyAddress(workRecordReplyDTO.getCopyAddress());
                        sendMessageDTO.setBccAddress(workRecordReplyDTO.getBccAddress());
                    }
                    if (CrmChannelEnum.AMAZON.getCode().equals(channelConfig.getChannelType())) {
                        // 根据工单id查询出客户id
                        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
                        SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex, workRecordReplyDTO.getWorkRecordId(), loginUser.getCompanyId());
                        if (ticketHit != null) {
                            try {
                                Map<String, Object> source = ticketHit.getSourceAsMap();
                                // 创建 ObjectMapper 实例
                                ObjectMapper objectMapper = new ObjectMapper();
                                // 注册 JavaTimeModule
                                objectMapper.registerModule(new JavaTimeModule());
                                String json = objectMapper.writeValueAsString(source);
                                // 将 JSON 字符串转换为 Java 对象
                                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                            } catch (JsonProcessingException e) {
                                log.error("查询es报错", e);
                            }
                        }
                        // 如果是AWS站内信，则需要查询具体的值
                        R<CustomerMediaInstVO> mediaInst = customerClient.queryCustomerMediaDetails(ticketInfoIndex.getCustomerId(), "AmazonEmail");
                        if (mediaInst.getCode() == AjaxResult.SUCCESS) {
                            sendMessageDTO.setDesAddress(mediaInst.getData().getValue());
                        }
                    }
                    List<ContentFile> fileList = workRecordReplyDTO.getWorkRecordFileList();
                    log.info("打印一下前端传入的fileList:{}",fileList);
                    // 判断是否有邮件
                    if (CollectionUtils.isNotEmpty(fileList)) {
                        List<Map<String, String>> attachList = new ArrayList<>();
                        fileList.forEach(item -> {
                            log.info("打印一下item值:{}",item);
                            Map<String, String> attachMap = new HashMap<>();
                            AjaxResult ajaxResult = crmAgentWorkRecordService.downloadFile(new ContentFile().setFileUrl(item.getFileUrl()).setBucketName(bucketName));
                            attachMap.put("fileName", item.getFileName());
                            attachMap.put("filePath", String.valueOf(ajaxResult.getData()));
                            attachMap.put("fileObject", item.getFileUrl());
                            attachList.add(attachMap);
                        });
                        sendMessageDTO.setAttachList(attachList);
                    }
                    // 发送邮件mq
                    rabbitTemplate.convertAndSend(RabbitMqConstants.SEND_MESSAGE_EXCHANGE, RabbitMqConstants.SEND_SINGLE_EMAIL_ROUTING_KEY, JSON.toJSONString(sendMessageDTO));
                    // 邮件存储联络明细
                    if (CrmChannelEnum.EMAIL.getCode().equals(channelConfig.getChannelType())){
                        try {
                            ContactDetailChannelAddVo contactDetailChannelAddVo = new ContactDetailChannelAddVo();
                            contactDetailChannelAddVo.setWorkOrderId(workRecordReplyDTO.getWorkRecordId());
                            contactDetailChannelAddVo.setStartTime(new Date());
                            contactDetailChannelAddVo.setCallTime(new Date());
                            contactDetailChannelAddVo.setFirstReplyTime(new Date());
                            contactDetailChannelAddVo.setContactId(workRecordReplyDTO.getWorkRecordId());
                            contactDetailChannelAddVo.setCallChannel("email");
                            contactDetailChannelAddVo.setChannelTypeId(channelConfig.getChannelType().toString());
                            contactDetailChannelAddVo.setChannelId(channelConfig.getChannelId());
                            contactDetailChannelAddVo.setEventType(11);
                            statDataIndexService.addChannelContactDetail(contactDetailChannelAddVo);
                        }catch (Exception e){
                            log.error("机器人调用联络明细报错:",e);
                        }
                    }
                }
            }
        }
        return null;
    }


    /**
     * 根据取到id，取出名称
     *
     * @param channelId channelId
     * @return 名称
     */
    private String getReplyMessageForm(String channelId) {
        // 查渠道id信息
        CrmChannelConfig crmChannelConfig = crmChannelConfigService.getById(channelId);
        if (crmChannelConfig != null) {
            if (CrmChannelEnum.EMAIL.getCode().equals(crmChannelConfig.getChannelType())) {
                // 取出邮件渠道的各种信息
                List<CrmChannelInfoConfigInst> list = crmChannelInfoConfigInstService.list(Wrappers.<CrmChannelInfoConfigInst>lambdaQuery()
                        .eq(CrmChannelInfoConfigInst::getChannelId, crmChannelConfig.getChannelId()));
                for (CrmChannelInfoConfigInst infoConfig : list) {
                    if (ChannelEmailEnum.EMAIl_ACCOUNT.getCode().equals(infoConfig.getCode())) {
                        return infoConfig.getName();

                    }
                }
            }
        }
        return "";
    }

    /**
     * 根据渠道id，取出机器人名称
     *
     * @param channelId channelId
     * @return 名称
     */
    private String getRobotName(String channelId) {
        // 查渠道id信息
        CrmChannelConfig crmChannelConfig = crmChannelConfigService.getById(channelId);
        if (crmChannelConfig != null) {
            // 取出邮件渠道的各种信息
            List<CrmChannelInfoConfigInst> list = crmChannelInfoConfigInstService.list(Wrappers.<CrmChannelInfoConfigInst>lambdaQuery()
                    .eq(CrmChannelInfoConfigInst::getChannelId, crmChannelConfig.getChannelId()));
            for (CrmChannelInfoConfigInst infoConfig : list) {
                if (ChannelConfigEnum.BOT_NAME.getCode().equals(infoConfig.getCode())) {
                    return infoConfig.getName();
                }
            }
        }
        return "robot";
    }

    /**
     * 保存工单附件
     *
     * @param workRecordReplyDTO  请求参数
     * @param workRecord          工单信息
     * @param workRecordDetailId  工单详细id
     * @param workRecordContentId 工单内容id
     * @param loginUser           登录人
     */
    private void createWorkFile(WorkRecordReplyDTO workRecordReplyDTO, CrmAgentWorkRecord workRecord, String workRecordDetailId, String workRecordContentId, SysUserVo loginUser) {
        List<CrmAgentWorkRecordFile> workRecordFileList = new ArrayList<>();

        // 遍历workRecordRemarkDTO.getWorkRecordFileList()，创建工作记录文件
        if (CollectionUtils.isNotEmpty(workRecordReplyDTO.getWorkRecordFileList())) {
            for (ContentFile workFile : workRecordReplyDTO.getWorkRecordFileList()) {
                CrmAgentWorkRecordFile workRecordFile = createWorkRecordFile(workFile, loginUser, workRecord.getWorkRecordId(), workRecordDetailId, workRecordContentId);
                workRecordFileList.add(workRecordFile);
            }
//            if(workRecordReplyDTO.getAutoType() == 2){
//                // 删除Redis中暂时保存的附件、图片信息
//                RedisCacheUtil.deleteObject(workRecordReplyDTO.getWorkRecordFileList().stream().map(file -> Constants.FILE_TEMPORARY_STAGE_PREFIX.concat(file.getFileUrl())).collect(Collectors.toList()));
//            }
        }
        // 删除Redis中暂时保存的附件、图片信息
//        if (CollectionUtil.isNotEmpty(workRecordReplyDTO.getContentFileList())){
//            if(workRecordReplyDTO.getAutoType() == 2) {
//                RedisCacheUtil.deleteObject(workRecordReplyDTO.getContentFileList().stream().map(file -> Constants.FILE_TEMPORARY_STAGE_PREFIX.concat(file.getFileUrl())).collect(Collectors.toList()));
//            }
//        }
        if (workRecordFileList.size() > 0) {
            crmAgentWorkRecordFileService.saveBatch(workRecordFileList);
        }
    }


    /**
     * 把createWorkRecordRemark 生成工作记录附件的方法抽出来
     *
     * @param dto                 文件信息
     * @param loginUser           登录人信息
     * @param workRecordId        工单id
     * @param workRecordDetailId  明细id
     * @param workRecordContentId 内容id
     * @return CrmAgentWorkRecordFile对象
     */
    private CrmAgentWorkRecordFile createWorkRecordFile(ContentFile dto, SysUserVo loginUser, String workRecordId, String workRecordDetailId, String workRecordContentId) {
        CrmAgentWorkRecordFile workRecordFile = new CrmAgentWorkRecordFile();
        workRecordFile.setWorkRecordId(workRecordId)
                .setWorkRecordDetailId(workRecordDetailId)
                .setWorkRecordContentId(workRecordContentId)
                .setWorkRecordFileId(UuidUtils.generateUuid())
                .setFilePath(dto.getFileUrl())
                .setFileName(dto.getFileName())
                .setBucketName(dto.getBucketName())
                .init(loginUser);
        return workRecordFile;
    }


    /**
     * 拼接工单参数
     *
     * @param workRecordRemarkDTO 请求参数
     * @return 拼接后实体
     */
    private CreateWorkOrderRecordVO workParameter(WorkRecordCreateDTO workRecordRemarkDTO, CrmCustomerVO crmCustomerVO) {
        CreateWorkOrderRecordVO createWorkOrderRecordVO = new CreateWorkOrderRecordVO();
        // 只有工单主题为空时才会设置为未命名工单
        createWorkOrderRecordVO.setWorkRecordTheme(TransUtil.trans("未命名工单"));
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getSubject())) {
            createWorkOrderRecordVO.setWorkRecordTheme(workRecordRemarkDTO.getSubject());
        }
        // 优先级为null时，则定义默认的
        if (StringUtil.isEmpty(workRecordRemarkDTO.getPriorityLevelId())) {
            // 优先级id
            createWorkOrderRecordVO.setPriorityLevelId("1001");
            // 优先级名称
            createWorkOrderRecordVO.setPriorityLevelName("P1-低级影响");
        } else {
            createWorkOrderRecordVO.setPriorityLevelId(workRecordRemarkDTO.getPriorityLevelId());
        }
        // 工单类型code不为null时，则保存
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getWorkRecordTypeCode())) {
            createWorkOrderRecordVO.setWorkRecordTypeId(workRecordRemarkDTO.getWorkRecordTypeCode());
        }

        // 如果进线意图id不为null，则就存储
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getIncomingIntentId())) {
            createWorkOrderRecordVO.setIncomingIntentId(workRecordRemarkDTO.getIncomingIntentId());
        }

        if (workRecordRemarkDTO.getAutoType() == 1) {
            String robotName = getRobotName(workRecordRemarkDTO.getChannelId());
            // 座席ID，当前用户
            createWorkOrderRecordVO.setAgentId("1001");
            // 座席名称，当前用户
            createWorkOrderRecordVO.setAgentName(robotName);
            // 当为机器人工单时,才会有公司id. 这个为创建工单时所以传入
            createWorkOrderRecordVO.setCompanyId(workRecordRemarkDTO.getCompanyId());
        } else if (workRecordRemarkDTO.getAutoType() == 2) {
            // 座席ID，当前用户
            createWorkOrderRecordVO.setAgentId(getUserId());
            // 座席名称，当前用户
            createWorkOrderRecordVO.setAgentName(getUsername());
            // 获取当前公司id,这个是为判断客户是否存在 所以传入
            createWorkOrderRecordVO.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        } else {
            // 当座席id、座席名称不为null时则存入
            if (StringUtil.isNotEmpty(workRecordRemarkDTO.getAgentId()) && !workRecordRemarkDTO.getAgentId().equals("1001")) {
                createWorkOrderRecordVO.setAgentId(workRecordRemarkDTO.getAgentId());
                // 如果转人工工单绑定上坐席时，则需要赋值消息到达时间
                createWorkOrderRecordVO.setLastMessageDeliveryTime(LocalDateTime.now());
            }
            if (StringUtil.isNotEmpty(workRecordRemarkDTO.getAgentName()) && !workRecordRemarkDTO.getAgentName().equals("robot")) {
                createWorkOrderRecordVO.setAgentName(workRecordRemarkDTO.getAgentName());
            }
            // 待分配工单，公司id需要前端传入
            createWorkOrderRecordVO.setCompanyId(workRecordRemarkDTO.getCompanyId());
        }

        // 如果connectId不为null，则进行保存
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getConnectId())) {
            createWorkOrderRecordVO.setConnectId(workRecordRemarkDTO.getConnectId());
        }
        // 如果呼入呼出类型不为null，则进行保存
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getCallType())) {
            createWorkOrderRecordVO.setCallType(workRecordRemarkDTO.getCallType());
        }

        // 如果转录语言不为null，则进行保存
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getTicketLanguage())) {
            createWorkOrderRecordVO.setTicketLanguage(workRecordRemarkDTO.getTicketLanguage());
        }
        //是否在线聊天语音
        createWorkOrderRecordVO.setChatVoice(workRecordRemarkDTO.getChatVoice());
        // 客户ID
        createWorkOrderRecordVO.setCustomerId(crmCustomerVO.getCustomerId());
        // 客户名称
        createWorkOrderRecordVO.setCustomerName(crmCustomerVO.getCustomerGroupName());
        // 工单内容
        createWorkOrderRecordVO.setWorkRecordContent(workRecordRemarkDTO.getContent());
        // 附件(工单回复对应附件)
        List<ContentFile> workRecordFileList = new ArrayList<>();
        if (workRecordRemarkDTO.getWorkRecordFileList() != null && workRecordRemarkDTO.getWorkRecordFileList().size() > 0) {
            for (WorkRecordFileDTO workRecordFileDTO : workRecordRemarkDTO.getWorkRecordFileList()) {
                ContentFile contentFile = new ContentFile();
                contentFile.setFileName(workRecordFileDTO.getName());
                contentFile.setFileUrl(workRecordFileDTO.getPath());
                contentFile.setBucketName(bucketName);
                workRecordFileList.add(contentFile);
            }
            createWorkOrderRecordVO.setWorkRecordFileList(workRecordFileList);
        }

        createWorkOrderRecordVO.setChannelTypeId(workRecordRemarkDTO.getChannelTypeId());
        createWorkOrderRecordVO.setChannelId(workRecordRemarkDTO.getChannelId());
        // 定义自动创建工单
        createWorkOrderRecordVO.setCreateType(0);
        // 将机器人创建还是人工创建传入
        createWorkOrderRecordVO.setAutoType(workRecordRemarkDTO.getAutoType());
        // 判断是否有传工单ID
        if (workRecordRemarkDTO.getWorkOrderId() != null) {
            createWorkOrderRecordVO.setWorkOrderId(workRecordRemarkDTO.getWorkOrderId());
        }
        // 处理扩展属性
        List<CreateWorkOrderExtIntsVO> extInts = dealExtInts(workRecordRemarkDTO.getExtendedAttribute());
        createWorkOrderRecordVO.setExtIntsList(extInts);
        // 处理区域
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getRegion())) {
            createWorkOrderRecordVO.setRegion(workRecordRemarkDTO.getRegion());
        }
        // 处理部门信息
        if (StringUtil.isNotEmpty(workRecordRemarkDTO.getDeptId())) {
            createWorkOrderRecordVO.setDeptId(workRecordRemarkDTO.getDeptId());
        }
        return createWorkOrderRecordVO;
    }

    private List<CreateWorkOrderExtIntsVO> dealExtInts(String extendedAttribute) {
        if (StringUtil.isEmpty(extendedAttribute)) {
            return null;
        }
        log.info("extendedAttribute:{}", extendedAttribute);
        List<CreateWorkOrderExtIntsVO> extIntsVOList = new ArrayList<>();
        JSONObject jsonObject = JSON.parseObject(extendedAttribute);
        Set<String> allKeys = jsonObject.keySet();
        for (String key : allKeys) {
            if (key.startsWith("extended_")) {
                String workRecordExtCode = key.replace("extended_", "");
                String workRecordExtValue = jsonObject.getJSONObject(key).getString("value");
                CreateWorkOrderExtIntsVO createWorkOrderExtIntsVO = new CreateWorkOrderExtIntsVO();
                createWorkOrderExtIntsVO.setWorkRecordExtCode(workRecordExtCode);
                createWorkOrderExtIntsVO.setWorkRecordExtValue(workRecordExtValue);
                extIntsVOList.add(createWorkOrderExtIntsVO);
                log.info("workRecordExtCode:{},workRecordExtValue:{}", workRecordExtCode, workRecordExtValue);
            }
        }

        return extIntsVOList;
    }

    /**
     * 方法改变
     * 同步计算当前的
     * 解决时间 用户发送时间+SLa对应的解决时间
     * 响应时间 用户发送时间+Sla对应的首次响应时间
     * 下一次平均响应时间 用户发送时间+Sla对应的下一次平均相响应时间
     *
     * @param channelCode     渠道code
     * @param recordTypeValue 工单value
     * @param priorityLevelId 优先级id
     * @param companyId       公司id
     * @return 返回三种时间的数据集合
     */
    private TimeVO countGradeTime(LocalDateTime createTime,String channelCode, String recordTypeValue, String priorityLevelId, String companyId) {
        TimeVO timeVO = new TimeVO();
        CrmAgentWorkRecordSlaDef recordSlaDef =
                crmAgentWorkRecordSlaDefService.queryLevelSla
                        (companyId, channelCode, recordTypeValue, priorityLevelId);
        //首次响应时间 - 用户首次发消息
        timeVO.setFirstResponseTime(
                convertToSecond(recordSlaDef.getResponseTimeUnit(), recordSlaDef.getResponseTime())
        );
        //下一次响应时间 - 座席进行过工单回复
        timeVO.setAvgResponseTime(
                convertToSecond(recordSlaDef.getNextResponseTimeUnit(), recordSlaDef.getNextResponseTime()));
        //解决时间
        timeVO.setShouldResolveTime(convert(check(createTime, recordSlaDef.getResolveTimeUnit(), recordSlaDef.getResolveTime())));
        return timeVO;
    }

    /**
     * 将时间转换为秒
     * @param unit
     * @param time
     * @return
     */
    private Integer convertToSecond(String unit, Integer time) {
        if(ObjectUtil.isNull(time)){
            return 0;
        }
        Integer dateTime = null;
        switch (unit) {
            case "M":
                dateTime = time*60;
                break;
            case "H":
                dateTime = time*3600;
                break;
            case "D":
                dateTime = time*24*3600;
                break;
            default:
                dateTime = 0;
                break;
        }
        return dateTime;
    }

    public LocalDateTime check(LocalDateTime nowDataTime, String unit, Integer time) {
        LocalDateTime dateTime = null;
        switch (unit) {
            case "M":
                dateTime = nowDataTime.plusMinutes(time);
                break;
            case "H":
                dateTime = nowDataTime.plusHours(time);
                break;
            case "D":
                dateTime = nowDataTime.plusDays(time);
                break;
            default:
                dateTime = nowDataTime;
                break;
        }
        return dateTime;
    }
    public LocalDateTime convert(LocalDateTime time) {
        Date gradeTime = Date.from(time.toInstant(ZoneOffset.ofHours(8)));
        // 将 Date 转换为 Instant
        Instant instant = gradeTime.toInstant();
        // 使用系统默认时区将 Instant 转换为 LocalDateTime
        LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime;
    }

    /**
     * 添加操作记录
     *
     * @param workRecordId         工单id
     * @param operationLogReason   操作原因
     * @param operationLogDescribe 操作记录描述
     */
    private void addOperationLog(String workRecordId, String operationLogReason, String operationLogDescribe, Integer operationLogType, SysUserVo loginUser) {
        CrmAgentWorkRecordOperationLog crmAgentWorkRecordOperationLog = new CrmAgentWorkRecordOperationLog();
        crmAgentWorkRecordOperationLog.setOperationLogId(UuidUtils.generateUuid())
                .setWorkRecordId(workRecordId)
                .setOperationLogDescribe(operationLogDescribe)
                .setDataStatus(1)
                .setOperatorName(loginUser.getUserName()).setCreator(loginUser.getUserId()).setCreateTime(new Date()).setOperationLogType(operationLogType);
        // 操作原因不为null则添加
        if (operationLogReason != null) {
            crmAgentWorkRecordOperationLog.setOperationLogReason(operationLogReason);
        }
        crmAgentWorkRecordOperationLogService.save(crmAgentWorkRecordOperationLog);
    }


    /**
     * 保存工单信息
     *
     * @param ticketInfoIndex 工单内容
     * @param companyId       公司id
     */
    private void addTicketInfo(TicketInfoIndex ticketInfoIndex, String companyId) {
        log.info("为了避免重复工单创建，进行日志记录，创建工单参数 ====={}",ticketInfoIndex);
        RLock lock;
        try {
             lock = redissonClient.getLock(ticketInfoIndex.getWorkRecordId() + companyId);
        } finally {

        }
        try {
            if(ObjectUtil.isNotNull(lock) && ObjectUtil.isNotEmpty(lock)){
                lock.lock();
            }
            // 定义索引名称
            String indexName = ticketIndex + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
            }

            //在这里进行处理，首先进行工单判断，查询是否存在当前工单，没有则添加，否则替换
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder builder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            //只需要对用户agent_id和当前工单的类型（1、2进行判断）
            builder.query(
                    boolQueryBuilder.must(
                            QueryBuilders.termQuery("work_record_id",ticketInfoIndex.getWorkRecordId())
                    )
            );
            builder.from(0).size(9999);
            request.source(builder);
            SearchResponse search = null;
            try {
                search = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            List<TicketInfoIndex> target = new ArrayList<>();
            String id = "";
            for (SearchHit hit : search.getHits().getHits()) {
                id = hit.getId();
                Map<String, Object> source = hit.getSourceAsMap();
                TicketInfoIndex result = BeanUtil.toBean(source, TicketInfoIndex.class);
                target.add(
                        result
                );
            }
            //判断当前工单是否存在
            if(ObjectUtil.isNotEmpty(target) && ObjectUtil.isNotNull(target) && target.size()!=0){
                //进行修改替换
                TicketInfoIndex ticketInfoIndex1 = target.get(0);
                log.info("当前工单已经存在，存在工单信息 ====={}，当前工单信息====={}",ticketInfoIndex1,ticketInfoIndex);
                BeanUtil.copyProperties(ticketInfoIndex,ticketInfoIndex1);
                //进行替换
                elasticsearchUtil.updateDocument(ticketIndex + companyId,id,ticketInfoIndex1);
            }else {
                log.info("当前工单属于新创建，工单信息 ====={}",ticketInfoIndex);
                // 定义索引请求
                IndexRequest indexRequest = new IndexRequest(indexName);
                ObjectMapper objectMapper = new ObjectMapper();

                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());

                String value = objectMapper.writeValueAsString(ticketInfoIndex);
                indexRequest.source(value, XContentType.JSON);
                // 进行添加
                IndexResponse response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
                log.info(String.valueOf(response.getResult()));
            }
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es保存工单聊天数据失败，异常信息：", e);
            }
        }finally {
            if(ObjectUtil.isNotNull(lock) && ObjectUtil.isNotEmpty(lock)){
                lock.unlock();
            }
        }
    }


    /**
     * 验证索引是否存在
     *
     * @param index 索引名称
     * @return 存在状态
     * @throws IOException
     */
    private boolean headIndexExists(String index) {
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        } catch (Exception e) {
            log.error("验证索引失败:", e);
        }
        return false;
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        } catch (Exception e) {
            log.error("创建索引报错", e);
        }
    }
}
