package com.goclouds.crm.platform.call.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordSlaDef;
import com.goclouds.crm.platform.call.domain.CrmImRoutingMain;
import com.goclouds.crm.platform.call.domain.vo.RoutingChannelVo;
import com.goclouds.crm.platform.call.domain.vo.RoutingRuleEnumVo;
import com.goclouds.crm.platform.call.domain.vo.workbench.RoutingRuleEnumGroupVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;

public interface CrmImRoutingMainService extends IService<CrmImRoutingMain> {

    //查询渠道规则列表
    IPage<RoutingChannelVo> queryList(RoutingChannelVo vo, Integer pageNumber, Integer pageSize);

    AjaxResult saveRuleInfo(RoutingChannelVo vo);

    List<RoutingRuleEnumGroupVo> getAllEnumDetails();

    RoutingChannelVo queryRuleDetails(String routingId);

    AjaxResult delRuleInfo(RoutingChannelVo vo);

    void updateRoutingOrder(List<RoutingChannelVo> routingList);
}
