package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordPriorityLevelDef;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordLevelVO;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordPriorityLevelDefMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordPriorityLevelDefService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_priority_level_def(工单优先级定义表;)】的数据库操作Service实现
* @createDate 2023-09-21 16:09:26
*/
@Service
public class CrmAgentWorkRecordPriorityLevelDefServiceImpl extends ServiceImpl<CrmAgentWorkRecordPriorityLevelDefMapper, CrmAgentWorkRecordPriorityLevelDef>
    implements CrmAgentWorkRecordPriorityLevelDefService {

    @Override
    public List<WorkRecordLevelVO> queryWorkRecordLevel() {

        return this.baseMapper.queryWorkRecordLevel();
    }
}




