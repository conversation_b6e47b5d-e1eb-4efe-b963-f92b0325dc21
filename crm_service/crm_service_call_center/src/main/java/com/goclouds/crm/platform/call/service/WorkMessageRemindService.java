package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.call.domain.messageRemind.TranslateLanguageVo;
import com.goclouds.crm.platform.call.domain.messageRemind.request.*;
import com.goclouds.crm.platform.call.domain.messageRemind.response.QueryMessageRemindTimeListResponse;
import com.goclouds.crm.platform.call.domain.messageRemind.response.QueryMessageRemindingResponse;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
 * <AUTHOR> pengliang.sun
 * @description :
 */
public interface WorkMessageRemindService {

    AjaxResult<IPage<QueryMessageRemindingResponse>> queryMessageReminding(IPage<Object> pageParam,QueryMessageRemindingRequest request);

    AjaxResult addMessageReminding(AddMessageRemindingRequest request);

    AjaxResult updateMessageReminding(UpdateMessageRemindingRequest request);

    void removeMessageReminding(RemoveMessageRemindingRequest request);

    void translateMessageReminding(TranslateMessageRemindingRequest request);

    AjaxResult<QueryMessageRemindTimeListResponse> queryMessageRemindingList(String companyId, String language);

    void mqAiTranslateMessageReminding(TranslateLanguageVo languageVo);

}
