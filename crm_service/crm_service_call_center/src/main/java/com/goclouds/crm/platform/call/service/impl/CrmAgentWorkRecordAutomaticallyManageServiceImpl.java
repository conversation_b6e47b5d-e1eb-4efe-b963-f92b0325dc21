package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordAutomaticallyManage;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordTypeDef;
import com.goclouds.crm.platform.call.domain.CrmSlaRecordChannel;
import com.goclouds.crm.platform.call.domain.CrmWorkRecordChannel;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordAutomaticallyManageMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordAutomaticallyManageService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordTypeDefService;
import com.goclouds.crm.platform.call.service.CrmWorkRecordChannelService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.client.system.InternationalClient;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelConfigVO;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelDefForSlaVO;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import lombok.RequiredArgsConstructor;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【crm_agent_work_record_automatically_manage(工单自动管理表)】的数据库操作Service实现
 * @createDate 2025-04-08 09:35:51
 */
@Service
@RequiredArgsConstructor
public class CrmAgentWorkRecordAutomaticallyManageServiceImpl extends ServiceImpl<CrmAgentWorkRecordAutomaticallyManageMapper, CrmAgentWorkRecordAutomaticallyManage>
        implements CrmAgentWorkRecordAutomaticallyManageService {

    private final CrmWorkRecordChannelService crmWorkRecordChannelService;

    private final CrmAgentWorkRecordTypeDefService crmAgentWorkRecordTypeDefService;


    private final ChannelClient channelClient;

    private final InternationalClient internationalClient;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    private final RestHighLevelClient restHighLevelClient;


    private final RedissonClient redissonClient;


    /**
     * 通过传入的分页参数，进行工单关闭管理数据返回
     *
     * @param pageParam
     * @param type      1 - 工单自动结束会话   2 - 工单自动关闭
     * @return
     */
    @Override
    public AjaxResult<IPage<QueryWorkOrderManageVO>> listWorkManage(IPage<CrmAgentWorkRecordAutomaticallyManage> pageParam, Long type) {
        //进行顺序重拍
        List<CrmAgentWorkRecordAutomaticallyManage> list = this.list(
                new LambdaQueryWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getCloseType, type)
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getDataStatus, 1)
                        .orderByAsc(CrmAgentWorkRecordAutomaticallyManage::getOrder)
        );
        if(ObjectUtil.isNotNull(list) && ObjectUtil.isNotEmpty(list)&& list.size()!=0){
            Long order = 1L;
            for (CrmAgentWorkRecordAutomaticallyManage agentWorkRecordAutomaticallyManage : list) {
                update(
                        new LambdaUpdateWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                                .eq(CrmAgentWorkRecordAutomaticallyManage::getWorkRecordCloseId, agentWorkRecordAutomaticallyManage.getWorkRecordCloseId())
                                .set(CrmAgentWorkRecordAutomaticallyManage::getOrder, order)
                );
                order++;
            }
        }
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        LambdaQueryWrapper<CrmAgentWorkRecordAutomaticallyManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAgentWorkRecordAutomaticallyManage::getCloseType, type);
        queryWrapper.eq(CrmAgentWorkRecordAutomaticallyManage::getDataStatus, 1);
        queryWrapper.eq(CrmAgentWorkRecordAutomaticallyManage::getCompanyId, companyId);
        queryWrapper.orderByAsc(CrmAgentWorkRecordAutomaticallyManage::getOrder);
        IPage<CrmAgentWorkRecordAutomaticallyManage> pageDate = this.baseMapper.selectPage(
                pageParam,
                queryWrapper
        );
        if (ObjectUtil.isNull(pageDate.getRecords()) || pageDate.getRecords().size() == 0) {
            //进行初始化，所有渠道和工单，首次按照7天进行处理
            RLock lock = redissonClient.getLock(companyId + type);
            try {
                lock.lock();
                String uuid = UUID.randomUUID().toString().replace("-", "");
                CrmAgentWorkRecordAutomaticallyManage crmAgentWorkRecordAutomaticallyManage = new CrmAgentWorkRecordAutomaticallyManage();
                crmAgentWorkRecordAutomaticallyManage.setWorkRecordCloseId(uuid);
                if(type == 1){
                    //结束会话
                    crmAgentWorkRecordAutomaticallyManage.setTime(24);
                    crmAgentWorkRecordAutomaticallyManage.setTimeUnit("H");
                }else {
                    //关闭工单
                    crmAgentWorkRecordAutomaticallyManage.setTime(7);
                    crmAgentWorkRecordAutomaticallyManage.setTimeUnit("D");
                }
                crmAgentWorkRecordAutomaticallyManage.setCloseType(type);
                //初始状态 - 默认为不结束
                crmAgentWorkRecordAutomaticallyManage.setCloseStatus(0L);
                //初始化 - 默认排序为1
                crmAgentWorkRecordAutomaticallyManage.setOrder(1L);
                crmAgentWorkRecordAutomaticallyManage.setCreator(SecurityUtil.getUserId());
                crmAgentWorkRecordAutomaticallyManage.setCreateTime(new Date());
                crmAgentWorkRecordAutomaticallyManage.setInitStatus(1L);
                crmAgentWorkRecordAutomaticallyManage.setCompanyId(companyId);
                crmAgentWorkRecordAutomaticallyManage.setDataStatus(1);
                save(crmAgentWorkRecordAutomaticallyManage);
            } finally {
                lock.unlock();
            }
            this.baseMapper.selectPage(
                    pageParam,
                    queryWrapper
            );
            List<QueryWorkOrderManageVO> result = new ArrayList<>();
            pageDate.getRecords().forEach(
                    item -> {
                        String unit = "";
                        switch (item.getTimeUnit()) {
                            case "D":
                                unit = internationalClient.systemLanguages("天").getData();
                                break;
                            case "H":
                                unit = internationalClient.systemLanguages("小时").getData();
                                break;
                            case "M":
                                unit = internationalClient.systemLanguages("分").getData();
                        }
                        //进行数据填充
                        QueryWorkOrderManageVO queryWorkOrderManageVO = BeanUtil.copyProperties(item, QueryWorkOrderManageVO.class);
                        queryWorkOrderManageVO.setWorkManageId(item.getWorkRecordCloseId());
                        if(queryWorkOrderManageVO.getCloseStatus() == 0){
                            if (type == 1) {
                                //自动结束会话
                                String end = internationalClient.systemLanguages("未开启自动结束会话").getData();
                                queryWorkOrderManageVO.setWorkDetails(end);
                            } else {
                                //自动关闭工单
                                String close = internationalClient.systemLanguages("未开启自动结束工单").getData();
                                queryWorkOrderManageVO.setWorkDetails(close);
                            }

                        }else {
                            if (type == 1) {
                                //自动结束会话
                                String end = internationalClient.systemLanguages("客户超过{}未发消息，结束会话").getData();
                                String replace = end.replace("{}", item.getTime() + unit);
                                queryWorkOrderManageVO.setWorkDetails(replace);
                            } else {
                                //自动关闭工单
                                String close = internationalClient.systemLanguages("客户结束会话超过{}，工单状态标记为已解决").getData();
                                String replace = close.replace("{}", item.getTime() + unit);
                                queryWorkOrderManageVO.setWorkDetails(replace);
                            }
                        }
                        result.add(queryWorkOrderManageVO);
                    }
            );
            IPage<QueryWorkOrderManageVO> iPage = new Page<>();
            BeanUtils.copyProperties(pageDate, iPage);
            //返回页面
            iPage.setRecords(result);
            return AjaxResult.ok(iPage);
        }
        List<QueryWorkOrderManageVO> result = new ArrayList<>();
        pageDate.getRecords().forEach(
                item -> {
                    //进行数据填充
                    QueryWorkOrderManageVO queryWorkOrderManageVO = BeanUtil.copyProperties(item, QueryWorkOrderManageVO.class);
                    queryWorkOrderManageVO.setWorkManageId(item.getWorkRecordCloseId());
                    //进行渠道信息拼接
                    List<CrmWorkRecordChannel> source = crmWorkRecordChannelService.list(
                            new LambdaQueryWrapper<CrmWorkRecordChannel>()
                                    .eq(CrmWorkRecordChannel::getWorkRecordManageId, item.getWorkRecordCloseId())
                                    .eq(CrmWorkRecordChannel::getCompanyId, companyId)
                                    .eq(CrmWorkRecordChannel::getDataStatus, 1)
                    );
                    //对  source - channel 数据进行分组
                    List<CrmWorkRecordChannel> channelList = source.stream().filter(
                            CrmWorkRecordChannel -> CrmWorkRecordChannel.getType().equals(com.goclouds.crm.platform.call.domain.CrmSlaRecordChannel.TYPE.CHANNEL_TYPE)
                    ).collect(Collectors.toList());
                    //获取当前渠道的code值
                    List<String> collect = channelList.stream().map(CrmWorkRecordChannel::getTypeValue).collect(Collectors.toList());
                    List<CrmChannelConfigVO> listR = new ArrayList<>();
                    if(ObjectUtil.isNotNull(collect) && collect.size() != 0){
                         listR = channelClient.queryChannelConfiglDefList(collect).getData();
                    }
                    List<WorkManageChannelParentVO> channelListResult = new ArrayList<>();
                    if(ObjectUtil.isNotNull(listR) && listR.size() != 0){
                        List<WorkManageChannelVO> workManageChannelVOS = BeanUtil.copyToList(listR, WorkManageChannelVO.class);
                        List<Integer> collect3 = workManageChannelVOS.stream().map(WorkManageChannelVO::getChannelType).collect(Collectors.toList());
                        List<String> collect4 = collect3.stream().map(String::valueOf).collect(Collectors.toList());
                        List<CrmChannelDefForSlaVO> data = channelClient.queryChannelDefList(collect4).getData();
                        Map<String, List<CrmChannelDefForSlaVO>> code = data.stream().collect(Collectors.groupingBy(CrmChannelDefForSlaVO::getCode));
                        Map<Integer, List<WorkManageChannelVO>> collect2 = workManageChannelVOS.stream().collect(Collectors.groupingBy(WorkManageChannelVO::getChannelType));
                        for (Map.Entry<Integer, List<WorkManageChannelVO>> integerListEntry : collect2.entrySet()) {
                            WorkManageChannelParentVO vo = new WorkManageChannelParentVO();
                            vo.setCode(Convert.toStr(integerListEntry.getKey()));
                            //对名称进行国际化
                            vo.setName(TransUtil.trans(code.get(Convert.toStr(integerListEntry.getKey())).get(0).getName()));
                            vo.setChannelVOList(
                                    BeanUtil.copyToList(integerListEntry.getValue(),WorkManageChannelVO.class)
                            );
                            channelListResult.add(vo);
                        }
                    }
                    queryWorkOrderManageVO.setChannelList(channelListResult);
                    //对  source - workRecord 数据进行分组
                    List<CrmWorkRecordChannel> workRecordList = source.stream().filter(
                            CrmWorkRecordChannel -> CrmWorkRecordChannel.getType().equals(com.goclouds.crm.platform.call.domain.CrmSlaRecordChannel.TYPE.WORK_RECORD_TYPE)
                    ).collect(Collectors.toList());
                    if(ObjectUtil.isNotNull(workRecordList) && workRecordList.size() != 0){
                        //工单的value值
                        List<String> collect1 = workRecordList.stream().map(CrmWorkRecordChannel::getTypeValue).collect(Collectors.toList());
                        queryWorkOrderManageVO.setWorkRecordList(
                                BeanUtil.copyToList(
                                        crmAgentWorkRecordTypeDefService.list(
                                                new LambdaQueryWrapper<CrmAgentWorkRecordTypeDef>()
                                                        .in(CrmAgentWorkRecordTypeDef::getWorkRecordTypeValue, collect1)
                                                        .eq(CrmAgentWorkRecordTypeDef::getLanguageCode, ServletUtils.getHeaderLanguage())
                                                        .eq(CrmAgentWorkRecordTypeDef::getDataStatus, 1)
                                        ),
                                        WorkManageWorkRecordVO.class
                                )
                        );
                    }else {
                        queryWorkOrderManageVO.setWorkRecordList(new ArrayList<WorkManageWorkRecordVO>());
                    }
                    //进行详情数据拼接 - 进行国际化
                    //已经存在过redis缓存无需二次装填
                    String unit = "";
                    switch (item.getTimeUnit()) {
                        case "D":
                            unit = internationalClient.systemLanguages("天").getData();
                            break;
                        case "H":
                            unit = internationalClient.systemLanguages("小时").getData();
                            break;
                        case "M":
                            unit = internationalClient.systemLanguages("分").getData();
                    }
                    if(queryWorkOrderManageVO.getCloseStatus() == 0){
                        if (type == 1) {
                            //自动结束会话
                            String end = internationalClient.systemLanguages("未开启自动结束会话").getData();
                            queryWorkOrderManageVO.setWorkDetails(end);
                        } else {
                            //自动关闭工单
                            String close = internationalClient.systemLanguages("未开启自动结束工单").getData();
                            queryWorkOrderManageVO.setWorkDetails(close);
                        }
                    }else {
                        if (type == 1) {
                            //自动结束会话
                            String end = internationalClient.systemLanguages("客户超过{}未发消息，结束会话").getData();
                            String replace = end.replace("{}", item.getTime() + unit);
                            queryWorkOrderManageVO.setWorkDetails(replace);
                        } else {
                            //自动关闭工单
                            String close = internationalClient.systemLanguages("客户结束会话超过{}，工单状态标记为已解决").getData();
                            String replace = close.replace("{}", item.getTime() + unit);
                            queryWorkOrderManageVO.setWorkDetails(replace);
                        }
                    }
                    result.add(queryWorkOrderManageVO);
                }
        );
        IPage<QueryWorkOrderManageVO> iPage = new Page<>();
        BeanUtils.copyProperties(pageDate, iPage);
        //返回页面
        iPage.setRecords(result);
        return AjaxResult.ok(iPage);
    }

    @Override
    public AjaxResult addOrUpdateWorkOrderManage(AddOrUpdateWorkOrderManageVO addOrUpdateWorkOrderManageVO) {

        //规则的新增以及修改
        if (ObjectUtil.isEmpty(addOrUpdateWorkOrderManageVO.getWorkRecordCloseId()) ||
                ObjectUtil.isNull(addOrUpdateWorkOrderManageVO.getWorkRecordCloseId())
        ) {
            Map<String, Object> map = checkCanChoose(addOrUpdateWorkOrderManageVO.getChannelCodes(), addOrUpdateWorkOrderManageVO.getWorkRecordTypeValues(), addOrUpdateWorkOrderManageVO.getWorkRecordCloseId(), addOrUpdateWorkOrderManageVO.getCloseType());
            if (!Convert.toBool(map
                    .get("canCreate"))) {
                return returnData(map);
            }
            //规则新增
            //进行初始化，所有渠道和工单，首次按照5小时进行处理
            String uuid = UUID.randomUUID().toString().replace("-", "");
            CrmAgentWorkRecordAutomaticallyManage crmAgentWorkRecordAutomaticallyManage = new CrmAgentWorkRecordAutomaticallyManage();
            BeanUtils.copyProperties(addOrUpdateWorkOrderManageVO, crmAgentWorkRecordAutomaticallyManage);
            crmAgentWorkRecordAutomaticallyManage.setWorkRecordCloseId(uuid);
            crmAgentWorkRecordAutomaticallyManage.setCloseStatus(1L);
            crmAgentWorkRecordAutomaticallyManage.setInitStatus(0L);
            crmAgentWorkRecordAutomaticallyManage.setCreator(SecurityUtil.getUserId());
            crmAgentWorkRecordAutomaticallyManage.setCreateTime(new Date());
            crmAgentWorkRecordAutomaticallyManage.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            crmAgentWorkRecordAutomaticallyManage.setDataStatus(1);
            //进行顺序添加，需要获取当前拥有的数量
            CrmAgentWorkRecordAutomaticallyManage crmAgentWorkRecordAutomaticallyManage1 = list(
                    new LambdaQueryWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getDataStatus, 1)
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getInitStatus, 1)
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getCloseType, addOrUpdateWorkOrderManageVO.getCloseType())
            ).get(0);
            //新增的永远在倒数第二个
            crmAgentWorkRecordAutomaticallyManage.setOrder(crmAgentWorkRecordAutomaticallyManage1.getOrder());
            save(crmAgentWorkRecordAutomaticallyManage);
            //每次都让默认规则的排序为最后一位
            update(
                    new LambdaUpdateWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getWorkRecordCloseId,crmAgentWorkRecordAutomaticallyManage1.getWorkRecordCloseId())
                            .set(CrmAgentWorkRecordAutomaticallyManage::getOrder,crmAgentWorkRecordAutomaticallyManage1.getOrder()+1)
            );
            //渠道工单信息添加
            List<String> channelCodes = addOrUpdateWorkOrderManageVO.getChannelCodes();
            List<CrmWorkRecordChannel> crmWorkRecordChannels = new ArrayList<>();
            for (String channelCode : channelCodes) {
                CrmWorkRecordChannel channel = new CrmWorkRecordChannel();
                channel.setWorkRecordManageId(uuid);
                channel.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                channel.setType(CrmSlaRecordChannel.TYPE.CHANNEL_TYPE);
                channel.setTypeValue(channelCode);
                channel.setDataStatus(1);
                channel.setCreator(SecurityUtil.getUserId());
                channel.setCreateTime(new Date());
                crmWorkRecordChannels.add(channel);
            }
            //工单
            List<String> workRecordTypeValues = addOrUpdateWorkOrderManageVO.getWorkRecordTypeValues();
            for (String workRecordTypeValue : workRecordTypeValues) {
                CrmWorkRecordChannel recordTypeValue = new CrmWorkRecordChannel();
                recordTypeValue.setWorkRecordManageId(uuid);
                recordTypeValue.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                recordTypeValue.setType(CrmSlaRecordChannel.TYPE.WORK_RECORD_TYPE);
                recordTypeValue.setTypeValue(workRecordTypeValue);
                recordTypeValue.setDataStatus(1);
                recordTypeValue.setCreator(SecurityUtil.getUserId());
                recordTypeValue.setCreateTime(new Date());
                crmWorkRecordChannels.add(recordTypeValue);
            }
            crmWorkRecordChannelService.saveBatch(crmWorkRecordChannels);
        } else {
            //规则修改：需要进一步判断所选的渠道或工单是否唯一
            Map<String, Object> map = checkCanChoose(addOrUpdateWorkOrderManageVO.getChannelCodes(), addOrUpdateWorkOrderManageVO.getWorkRecordTypeValues(), addOrUpdateWorkOrderManageVO.getWorkRecordCloseId(), addOrUpdateWorkOrderManageVO.getCloseType());
            if (!Convert.toBool(map
                    .get("canCreate"))) {
                return returnData(map);
            }
            //关闭基本详情
            update(
                    new LambdaUpdateWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getWorkRecordCloseId, addOrUpdateWorkOrderManageVO.getWorkRecordCloseId())
                            .set(CrmAgentWorkRecordAutomaticallyManage::getTime, addOrUpdateWorkOrderManageVO.getTime())
                            .set(CrmAgentWorkRecordAutomaticallyManage::getTimeUnit, addOrUpdateWorkOrderManageVO.getTimeUnit())
                            .set(CrmAgentWorkRecordAutomaticallyManage::getCloseStatus, addOrUpdateWorkOrderManageVO.getCloseStatus())
                            .set(CrmAgentWorkRecordAutomaticallyManage::getModifier, SecurityUtil.getUserId())
                            .set(CrmAgentWorkRecordAutomaticallyManage::getModifyTime, LocalDateTime.now())
            );
            //工单-渠道对应表
            //首先全部删除
            crmWorkRecordChannelService.update(
                    new LambdaUpdateWrapper<CrmWorkRecordChannel>()
                            .eq(CrmWorkRecordChannel::getWorkRecordManageId, addOrUpdateWorkOrderManageVO.getWorkRecordCloseId())
                            .set(CrmWorkRecordChannel::getDataStatus, 0)
            );
            //进行添加
            //渠道工单信息添加
            List<String> channelCodes = addOrUpdateWorkOrderManageVO.getChannelCodes();
            List<CrmWorkRecordChannel> crmWorkRecordChannels = new ArrayList<>();
            for (String channelCode : channelCodes) {
                CrmWorkRecordChannel channel = new CrmWorkRecordChannel();
                channel.setWorkRecordManageId(addOrUpdateWorkOrderManageVO.getWorkRecordCloseId());
                channel.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                channel.setType(CrmSlaRecordChannel.TYPE.CHANNEL_TYPE);
                channel.setTypeValue(channelCode);
                channel.setDataStatus(1);
                channel.setCreator(SecurityUtil.getUserId());
                channel.setCreateTime(new Date());
                crmWorkRecordChannels.add(channel);
            }
            //工单
            List<String> workRecordTypeValues = addOrUpdateWorkOrderManageVO.getWorkRecordTypeValues();
            for (String workRecordTypeValue : workRecordTypeValues) {
                CrmWorkRecordChannel recordTypeValue = new CrmWorkRecordChannel();
                recordTypeValue.setWorkRecordManageId(addOrUpdateWorkOrderManageVO.getWorkRecordCloseId());
                recordTypeValue.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                recordTypeValue.setType(CrmSlaRecordChannel.TYPE.WORK_RECORD_TYPE);
                recordTypeValue.setTypeValue(workRecordTypeValue);
                recordTypeValue.setDataStatus(1);
                recordTypeValue.setCreator(SecurityUtil.getUserId());
                recordTypeValue.setCreateTime(new Date());
                crmWorkRecordChannels.add(recordTypeValue);
            }
            crmWorkRecordChannelService.saveBatch(crmWorkRecordChannels);
        }
        return AjaxResult.ok(null,MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult removeAll(String id) {
        update(
                new LambdaUpdateWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getWorkRecordCloseId, id)
                        .set(CrmAgentWorkRecordAutomaticallyManage::getDataStatus, 0)
        );
        crmWorkRecordChannelService.update(
                new LambdaUpdateWrapper<CrmWorkRecordChannel>()
                        .eq(CrmWorkRecordChannel::getWorkRecordManageId, id)
                        .set(CrmWorkRecordChannel::getDataStatus, 0)
        );
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult<QueryAllRecordAndCloseManageVO> queryAllRecordAndCloseManage(String companyId, String workRecordId) {
        QueryAllRecordAndCloseManageVO queryAllRecordAndCloseManageVO = new QueryAllRecordAndCloseManageVO();
        queryAllRecordAndCloseManageVO.setWorkRecordId(workRecordId);
        queryAllRecordAndCloseManageVO.setTime(0);
        String indexName = ticketIndex + companyId;
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        SearchSourceBuilder builder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
       //只判断对应的工单id
        builder.query(
                boolQueryBuilder.must(
                        QueryBuilders.termQuery("work_record_id",workRecordId)
                )
        );
        builder.from(0).size(10);
        request.source(builder);
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            //查询异常不做处理
        }
        String channelCode = null;
        String recordCode = null;
        for (SearchHit hit : response.getHits().getHits()) {
            Map<String, Object> source = hit.getSourceAsMap();
            TicketInfoIndex result = BeanUtil.toBean(source, TicketInfoIndex.class);
            //因为是唯一的限制，只可能出现一个工单
             channelCode = result.getChannelConfigId();
             recordCode = result.getWorkRecordTypeCode();
        }
        //通过公司id去关联表中获取对应数据
        List<CrmAgentWorkRecordAutomaticallyManage> list = list(
                new LambdaQueryWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                        .in(CrmAgentWorkRecordAutomaticallyManage::getCompanyId, companyId)
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getDataStatus, 1)
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getCloseType, 1)
        );
        if(ObjectUtil.isNull(list) || list.size() == 0){
            //进行一个初始化操作  - 只针对与结束对话
            RLock lock = redissonClient.getLock(companyId+1);
            try {
                lock.lock();
                //进行一次查询
                //有了就不继续创建
                List<CrmAgentWorkRecordAutomaticallyManage> check = list(
                        new LambdaQueryWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                                .in(CrmAgentWorkRecordAutomaticallyManage::getCompanyId, companyId)
                                .eq(CrmAgentWorkRecordAutomaticallyManage::getDataStatus, 1)
                                .eq(CrmAgentWorkRecordAutomaticallyManage::getCloseType, 1)
                );
                if(ObjectUtil.isNull(check) || check.size() == 0){
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    CrmAgentWorkRecordAutomaticallyManage crmAgentWorkRecordAutomaticallyManage = new CrmAgentWorkRecordAutomaticallyManage();
                    crmAgentWorkRecordAutomaticallyManage.setWorkRecordCloseId(uuid);
                    crmAgentWorkRecordAutomaticallyManage.setTime(24);
                    crmAgentWorkRecordAutomaticallyManage.setTimeUnit("H");
                    crmAgentWorkRecordAutomaticallyManage.setCloseType(1L);
                    //默认状态设置为不开启
                    crmAgentWorkRecordAutomaticallyManage.setCloseStatus(0L);
                    //初始化 - 默认排序为1
                    crmAgentWorkRecordAutomaticallyManage.setOrder(1L);
                    crmAgentWorkRecordAutomaticallyManage.setCreator("system");
                    crmAgentWorkRecordAutomaticallyManage.setCreateTime(new Date());
                    crmAgentWorkRecordAutomaticallyManage.setInitStatus(1L);
                    crmAgentWorkRecordAutomaticallyManage.setCompanyId(companyId);
                    crmAgentWorkRecordAutomaticallyManage.setDataStatus(1);
                    save(crmAgentWorkRecordAutomaticallyManage);
                }
            } finally {
                lock.unlock();
            }
            //默认创建的不存在结束会话时间
//            queryAllRecordAndCloseManageVO.setTime(24*3600);
        }else {
            //进行二次查询 - 如果其中存在启用状态为未启用的
            List<CrmAgentWorkRecordAutomaticallyManage> typeOne = list(
                    new LambdaQueryWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                            .in(CrmAgentWorkRecordAutomaticallyManage::getCompanyId, companyId)
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getDataStatus, 1)
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getCloseType, 1)
                            .eq(CrmAgentWorkRecordAutomaticallyManage::getCloseStatus, 1)
            );
            if(ObjectUtil.isNotNull(typeOne) && ObjectUtil.isNotEmpty(typeOne) && typeOne.size() != 0){
                if(typeOne.size() == 1){
                    CrmAgentWorkRecordAutomaticallyManage init = typeOne.get(0);
                    if(init.getInitStatus() == 1){
                        Integer unit = 0;
                        switch (init.getTimeUnit()) {
                            case "D":
                                unit = init.getTime()*24*3600;
                                break;
                            case "H":
                                unit = init.getTime()*3600;
                                break;
                            case "M":
                                unit = init.getTime()*60;
                        }
                        //只可能有一个是符合规范的
                        queryAllRecordAndCloseManageVO.setTime(unit);
                    }else {
                        List<String> id = list.stream().map(CrmAgentWorkRecordAutomaticallyManage::getWorkRecordCloseId).collect(Collectors.toList());
                        //需要进行工单和渠道信息拼接
                        List<CrmWorkRecordChannel> data = crmWorkRecordChannelService.list(
                                new LambdaQueryWrapper<CrmWorkRecordChannel>()
                                        .in(CrmWorkRecordChannel::getWorkRecordManageId, id)
                                        .eq(CrmWorkRecordChannel::getDataStatus, 1)
                                        .eq(CrmWorkRecordChannel::getCompanyId, companyId)
                        ).stream().distinct().collect(Collectors.toList());
                        Map<String, List<CrmWorkRecordChannel>> collect = data.stream().collect(Collectors.groupingBy(CrmWorkRecordChannel::getWorkRecordManageId));
                        for (Map.Entry<String, List<CrmWorkRecordChannel>> stringListEntry : collect.entrySet()) {
                            //获取所有的工单类型
                            List<CrmWorkRecordChannel> recordList = stringListEntry.getValue().stream().filter(CrmWorkRecordChannel -> CrmWorkRecordChannel.getType().equals(2L)).collect(Collectors.toList());
                            List<String> collect1 = recordList.stream().map(CrmWorkRecordChannel -> CrmWorkRecordChannel.getTypeValue()).collect(Collectors.toList());
                            //获取所有的渠道类型
                            List<CrmWorkRecordChannel> channelList = stringListEntry.getValue().stream().filter(CrmWorkRecordChannel -> CrmWorkRecordChannel.getType().equals(1L)).collect(Collectors.toList());
                            List<String> collect2 = channelList.stream().map(CrmWorkRecordChannel -> CrmWorkRecordChannel.getTypeValue()).collect(Collectors.toList());
                            if(collect1.contains(recordCode) && collect2.contains(channelCode)){
                                CrmAgentWorkRecordAutomaticallyManage crmAgentWorkRecordAutomaticallyManage = list.stream().filter(
                                        CrmAgentWorkRecordAutomaticallyManage -> CrmAgentWorkRecordAutomaticallyManage.getWorkRecordCloseId().equals(stringListEntry.getKey())
                                ).collect(Collectors.toList()).get(0);
                                Integer unit = 0;
                                switch (crmAgentWorkRecordAutomaticallyManage.getTimeUnit()) {
                                    case "D":
                                        unit = crmAgentWorkRecordAutomaticallyManage.getTime()*24*3600;
                                        break;
                                    case "H":
                                        unit = crmAgentWorkRecordAutomaticallyManage.getTime()*3600;
                                        break;
                                    case "M":
                                        unit = crmAgentWorkRecordAutomaticallyManage.getTime()*60;
                                }
                                //只可能有一个是符合规范的
                                queryAllRecordAndCloseManageVO.setTime(unit);
                            }
                        }
                        //判断当前是否是空数据
                        if(ObjectUtil.isNull(queryAllRecordAndCloseManageVO.getTime()) || ObjectUtil.isEmpty(queryAllRecordAndCloseManageVO.getTime())){
                            CrmAgentWorkRecordAutomaticallyManage crmAgentWorkRecordAutomaticallyManage = list.stream().filter(
                                    CrmAgentWorkRecordAutomaticallyManage -> CrmAgentWorkRecordAutomaticallyManage.getInitStatus().equals(1L)
                            ).collect(Collectors.toList()).get(0);
                            if(crmAgentWorkRecordAutomaticallyManage.getCloseStatus() == 1){
                                //启用状态
                                //进行默认值填充 - 按照默认规则
                                Integer unit = 0;
                                switch (crmAgentWorkRecordAutomaticallyManage.getTimeUnit()) {
                                    case "D":
                                        unit = crmAgentWorkRecordAutomaticallyManage.getTime()*24*3600;
                                        break;
                                    case "H":
                                        unit = crmAgentWorkRecordAutomaticallyManage.getTime()*3600;
                                        break;
                                    case "M":
                                        unit = crmAgentWorkRecordAutomaticallyManage.getTime()*60;
                                }
                                //只可能有一个是符合规范的
                                queryAllRecordAndCloseManageVO.setTime(unit);
                            }
                        }
                    }
                }else {
                    List<String> id = list.stream().map(CrmAgentWorkRecordAutomaticallyManage::getWorkRecordCloseId).collect(Collectors.toList());
                    //需要进行工单和渠道信息拼接
                    List<CrmWorkRecordChannel> data = crmWorkRecordChannelService.list(
                            new LambdaQueryWrapper<CrmWorkRecordChannel>()
                                    .in(CrmWorkRecordChannel::getWorkRecordManageId, id)
                                    .eq(CrmWorkRecordChannel::getDataStatus, 1)
                                    .eq(CrmWorkRecordChannel::getCompanyId, companyId)
                    ).stream().distinct().collect(Collectors.toList());
                    Map<String, List<CrmWorkRecordChannel>> collect = data.stream().collect(Collectors.groupingBy(CrmWorkRecordChannel::getWorkRecordManageId));
                    for (Map.Entry<String, List<CrmWorkRecordChannel>> stringListEntry : collect.entrySet()) {
                        //获取所有的工单类型
                        List<CrmWorkRecordChannel> recordList = stringListEntry.getValue().stream().filter(CrmWorkRecordChannel -> CrmWorkRecordChannel.getType().equals(2L)).collect(Collectors.toList());
                        List<String> collect1 = recordList.stream().map(CrmWorkRecordChannel -> CrmWorkRecordChannel.getTypeValue()).collect(Collectors.toList());
                        //获取所有的渠道类型
                        List<CrmWorkRecordChannel> channelList = stringListEntry.getValue().stream().filter(CrmWorkRecordChannel -> CrmWorkRecordChannel.getType().equals(1L)).collect(Collectors.toList());
                        List<String> collect2 = channelList.stream().map(CrmWorkRecordChannel -> CrmWorkRecordChannel.getTypeValue()).collect(Collectors.toList());
                        if(collect1.contains(recordCode) && collect2.contains(channelCode)){
                            CrmAgentWorkRecordAutomaticallyManage crmAgentWorkRecordAutomaticallyManage = list.stream().filter(
                                    CrmAgentWorkRecordAutomaticallyManage -> CrmAgentWorkRecordAutomaticallyManage.getWorkRecordCloseId().equals(stringListEntry.getKey())
                            ).collect(Collectors.toList()).get(0);
                            Integer unit = 0;
                            switch (crmAgentWorkRecordAutomaticallyManage.getTimeUnit()) {
                                case "D":
                                    unit = crmAgentWorkRecordAutomaticallyManage.getTime()*24*3600;
                                    break;
                                case "H":
                                    unit = crmAgentWorkRecordAutomaticallyManage.getTime()*3600;
                                    break;
                                case "M":
                                    unit = crmAgentWorkRecordAutomaticallyManage.getTime()*60;
                            }
                            //只可能有一个是符合规范的
                            queryAllRecordAndCloseManageVO.setTime(unit);
                        }
                    }
                    //判断当前是否是空数据
                    if(queryAllRecordAndCloseManageVO.getTime() == 0){
                        CrmAgentWorkRecordAutomaticallyManage crmAgentWorkRecordAutomaticallyManage = list.stream().filter(
                                CrmAgentWorkRecordAutomaticallyManage -> CrmAgentWorkRecordAutomaticallyManage.getInitStatus().equals(1L)
                        ).collect(Collectors.toList()).get(0);
                        if(crmAgentWorkRecordAutomaticallyManage.getCloseStatus() == 1){
                            //启用状态
                            //进行默认值填充 - 按照默认规则
                            Integer unit = 0;
                            switch (crmAgentWorkRecordAutomaticallyManage.getTimeUnit()) {
                                case "D":
                                    unit = crmAgentWorkRecordAutomaticallyManage.getTime()*24*3600;
                                    break;
                                case "H":
                                    unit = crmAgentWorkRecordAutomaticallyManage.getTime()*3600;
                                    break;
                                case "M":
                                    unit = crmAgentWorkRecordAutomaticallyManage.getTime()*60;
                            }
                            //只可能有一个是符合规范的
                            queryAllRecordAndCloseManageVO.setTime(unit);
                        }
                    }
                }
            }
        }
        return AjaxResult.ok(queryAllRecordAndCloseManageVO);
    }

    /**
     * 不同的工单可以选择相同的渠道
     *
     * @param channelSource
     * @param recordTypeSource
     * @param workOrderId
     * @return
     */
    public Map<String, Object> checkCanChoose(List<String> channelSource, List<String> recordTypeSource, String workOrderId, Long type) {
        //增加通过类型从而进行二次筛选
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        Map<String, Object> result = new HashMap<>();
        List<String> channel = channelSource;
        List<String> recordType = recordTypeSource;
        List<String> idList = list(
                new LambdaQueryWrapper<CrmAgentWorkRecordAutomaticallyManage>()
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getCloseType, type)
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getCompanyId, companyId)
                        .eq(CrmAgentWorkRecordAutomaticallyManage::getDataStatus, 1)
        ).stream().map(CrmAgentWorkRecordAutomaticallyManage::getWorkRecordCloseId).collect(Collectors.toList());
        if (ObjectUtil.isNull(idList) || idList.size() == 0) {
            //当前没有渠道-工单数据，可以创建
            result.put("canCreate", true);
        } else {
            //获取当前所有已使用的渠道和工单类型
            LambdaQueryWrapper<CrmWorkRecordChannel> crmWorkRecordChannelLambdaQueryWrapper = new LambdaQueryWrapper<>();
            crmWorkRecordChannelLambdaQueryWrapper.eq(CrmWorkRecordChannel::getCompanyId, companyId);
            crmWorkRecordChannelLambdaQueryWrapper.eq(CrmWorkRecordChannel::getDataStatus, 1);
            crmWorkRecordChannelLambdaQueryWrapper.in(CrmWorkRecordChannel::getWorkRecordManageId, idList);
            List<CrmWorkRecordChannel> list = crmWorkRecordChannelService.list(
                    crmWorkRecordChannelLambdaQueryWrapper
            );
            if (ObjectUtil.isNull(list) || list.size() == 0) {
                //当前没有渠道-工单数据，可以创建
                result.put("canCreate", true);
            }else {
                //整理每个规则的所携带的渠道和工单数据
                ////进行筛选 - 分别匹配获取对应的交集
                Map<String, List<CrmWorkRecordChannel>> collect = new HashMap<>();
                if (ObjectUtil.isNull(workOrderId)||ObjectUtil.equals(workOrderId,"")) {
                    collect = list.stream().collect(Collectors.groupingBy(CrmWorkRecordChannel::getWorkRecordManageId));
                } else {
                    collect = list
                            .stream().filter(CrmWorkRecordChannel -> !CrmWorkRecordChannel.getWorkRecordManageId().equals(workOrderId)).collect(Collectors.toList())
                            .stream().collect(Collectors.groupingBy(CrmWorkRecordChannel::getWorkRecordManageId));
                }
                //获取每个规则下的工单和渠道进行map添加
                Map<String, List<String>> map = new HashMap<>();
                for (Map.Entry<String, List<CrmWorkRecordChannel>> stringListEntry : collect.entrySet()) {
                    //获取所有的工单类型
                    List<CrmWorkRecordChannel> recordList = stringListEntry.getValue().stream().filter(CrmWorkRecordChannel -> CrmWorkRecordChannel.getType().equals(2L)).collect(Collectors.toList());
                    //获取所有的渠道类型
                    List<CrmWorkRecordChannel> channelList = stringListEntry.getValue().stream().filter(CrmWorkRecordChannel -> CrmWorkRecordChannel.getType().equals(1L)).collect(Collectors.toList());
                    recordList.forEach(
                            item -> {
                                //获取渠道的所有名称
                                List<String> channelNameList = channelList.stream().map(CrmWorkRecordChannel::getTypeValue).collect(Collectors.toList());
                                List<String> strings = map.get(item.getTypeValue());
                                if (ObjectUtil.isNotNull(strings) && strings.size() != 0) {
                                    strings.addAll(channelNameList);
                                    //进行去重
                                    strings = strings.stream().distinct().collect(Collectors.toList());
                                } else {
                                    map.put(item.getTypeValue(), channelNameList);
                                }
                            }
                    );
                }
                //针对用户的传值进行计算，
                List<String> canRecord = new ArrayList<>();
                List<String> canNoRecord = new ArrayList<>();
                List<String> canChannel = new ArrayList<>();
                List<String> canNoChannel = new ArrayList<>();
                recordType.forEach(
                        item -> {
                            List<String> strings = map.get(item);
                            if (ObjectUtil.isNull(strings) || strings.size() == 0) {
                                //没有进行过对应工单创建，
                                canRecord.add(item);
                            } else {
                                //获取当前工单类型中存在的渠道类型，去交集，获取不可可创建的渠道类型
                                List<String> channelNoCanCreate = strings.stream().filter(
                                        a -> channel.contains(a)
                                ).collect(Collectors.toList());
                                if (ObjectUtil.isNull(channelNoCanCreate) || channelNoCanCreate.size() == 0) {
                                    canRecord.add(item);
                                } else {
                                    canNoRecord.add(item);
                                    canNoChannel.addAll(channelNoCanCreate);
                                }
                            }
                        }
                );
                if (canNoChannel.size() == 0) {
                    canChannel = channelSource;
                }
                if (canRecord.size() == recordTypeSource.size() && canChannel.size() == channelSource.size()) {
                    result.put("canCreate", true);
                } else {
                    result.put("canCreate", false);
                }

                if (canRecord.size() == recordTypeSource.size()) {
                    result.put("recordType", null);
                } else {
                    //获取差集
                    //不能创建
                    List<String> recordTypeNotCanCreateName = crmAgentWorkRecordTypeDefService.list(
                            new LambdaQueryWrapper<CrmAgentWorkRecordTypeDef>()
                                    .in(CrmAgentWorkRecordTypeDef::getWorkRecordTypeValue, canNoRecord)
                                    .eq(CrmAgentWorkRecordTypeDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                                    .eq(CrmAgentWorkRecordTypeDef::getLanguageCode, ServletUtils.getHeaderLanguage())
                                    .eq(CrmAgentWorkRecordTypeDef::getDataStatus, 1)
                    ).stream().map(CrmAgentWorkRecordTypeDef::getWorkRecordTypeName).collect(Collectors.toList());
                    result.put("recordType", recordTypeNotCanCreateName);
                }

                if (canChannel.size() == channelSource.size()) {
                    result.put("channel", null);
                } else {
                    //获取交集
                    List<String> channelNotCanCreateName = null;
                    if (canNoChannel.size() == 0) {
                        channelNotCanCreateName = channelClient.queryChannelConfiglDefList(channelSource).getData().stream().map(CrmChannelConfigVO::getName).collect(Collectors.toList());
                        result.put("channel", channelNotCanCreateName);
                    } else {
                        channelNotCanCreateName = channelClient.queryChannelConfiglDefList(canNoChannel).getData().stream().map(CrmChannelConfigVO::getName).collect(Collectors.toList());
                        result.put("channel", channelNotCanCreateName);
                    }
                }
            }
        }
        return result;
    }

    public AjaxResult returnData(Map<String, Object> map) {
        Object channel = map.get("channel");
        Object recordType = map.get("recordType");
        if (ObjectUtil.isNotNull(channel) && ObjectUtil.isNotNull(recordType)) {
            return AjaxResult.failure(MessageUtils.get("agent.agent.sal.rul.channel.record.creat.fail",
                    new Object[]{
                            map.get("channel"), map.get("recordType")
                    }));
        } else if (ObjectUtil.isNotNull(channel) && ObjectUtil.isNull(recordType)) {
            return AjaxResult.failure(MessageUtils.get("agent.agent.sal.rul.channel.creat.fail",
                    new Object[]{
                            map.get("channel")
                    }));
        } else if (ObjectUtil.isNull(channel) && ObjectUtil.isNotNull(recordType)) {
            return AjaxResult.failure(MessageUtils.get("agent.agent.sal.rul.record.creat.fail",
                    new Object[]{
                            map.get("recordType")
                    }));
        }
        return AjaxResult.ok();
    }

}




