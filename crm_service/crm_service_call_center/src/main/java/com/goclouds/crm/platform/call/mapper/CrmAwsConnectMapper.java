package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.goclouds.crm.platform.call.domain.CrmAwsConnect;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_aws_connect(aws账号关联connect实例)】的数据库操作Mapper
* @createDate 2023-05-23 13:53:36
* @Entity generator.domain.CrmAwsConnect
*/
public interface CrmAwsConnectMapper extends BaseMapper<CrmAwsConnect> {

    @Select("select t1.*, t2.user_id from crm_aws_connect t1 " +
            "left join sys_user_connect t2 on t1.connect_id = t2.aws_connect_id and t1.data_status = 1 and t2.data_status = 1 " +
            "${ew.customSqlSegment} ")
    List<CrmAwsConnect> queryAllConnectList(@Param(Constants.WRAPPER) Wrapper<String> wrapper);
}




