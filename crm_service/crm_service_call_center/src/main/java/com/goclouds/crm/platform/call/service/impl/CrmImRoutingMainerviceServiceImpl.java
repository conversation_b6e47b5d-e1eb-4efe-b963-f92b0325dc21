package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.vo.RoutingChannelVo;
import com.goclouds.crm.platform.call.domain.vo.RoutingRuleEnumVo;
import com.goclouds.crm.platform.call.domain.vo.workbench.RoutingRuleEnumGroupVo;
import com.goclouds.crm.platform.call.mapper.CrmImRoutingChannelMapper;
import com.goclouds.crm.platform.call.mapper.CrmImRoutingChannelUniqueMapper;
import com.goclouds.crm.platform.call.mapper.CrmImRoutingMainMapper;
import com.goclouds.crm.platform.call.mapper.CrmImRoutingRuleMapper;
import com.goclouds.crm.platform.call.service.CrmImRoutingMainService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.RoutingRuleEnum;
import com.goclouds.crm.platform.common.enums.RoutingRuleGroupEnum;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
public class CrmImRoutingMainerviceServiceImpl extends ServiceImpl<CrmImRoutingMainMapper, CrmImRoutingMain> implements CrmImRoutingMainService {

    private final static Integer DEL_STATUS=0;

    private final CrmImRoutingMainMapper imRoutingMainMapper;

    private final CrmImRoutingChannelMapper routingChannelMapper;

    private final CrmImRoutingRuleMapper routingRuleMapper;

    private final CrmImRoutingChannelUniqueMapper routingChannelUniqueMapper;


    @Override
    public IPage<RoutingChannelVo> queryList(RoutingChannelVo vo, Integer pageNumber, Integer pageSize) {

        String companyId = SecurityUtil.getLoginUser().getCompanyId();

        LambdaQueryWrapper<CrmImRoutingMain> queryWrapper = new LambdaQueryWrapper<CrmImRoutingMain>()
                .eq(CrmImRoutingMain::getCompanyId, companyId)
                .eq(CrmImRoutingMain::getDelStatus, DEL_STATUS)
                .orderByAsc(CrmImRoutingMain::getDefaultRouting)
                .orderByAsc(CrmImRoutingMain::getRoutingSeq);

        if (StringUtils.isNotBlank(vo.getRoutingId())) {
            queryWrapper.eq(CrmImRoutingMain::getRoutingId, vo.getRoutingId());
        }

        Page<CrmImRoutingMain> page = new Page<>(pageNumber, pageSize);
        IPage<CrmImRoutingMain> crmImRoutingPage = this.baseMapper.selectPage(page, queryWrapper);

        if (ObjectUtils.isEmpty(crmImRoutingPage) || crmImRoutingPage.getRecords().isEmpty()) {
            return new Page<>(pageNumber, pageSize);
        }

        // 拼接数据 加载渠道数据、规则数据和个性化配置
        List<RoutingChannelVo> routingMains = crmImRoutingPage.getRecords().stream()
                .map(routing -> {

                    RoutingChannelVo channelVo = new RoutingChannelVo();
                    BeanUtils.copyProperties(routing, channelVo);

                    loadRoutingData(channelVo);
                    return channelVo;
                })
                .collect(Collectors.toList());

        IPage<RoutingChannelVo> resultPage = new Page<>(pageNumber, pageSize);
        resultPage.setRecords(routingMains);
        resultPage.setTotal(crmImRoutingPage.getTotal());
        resultPage.setCurrent(crmImRoutingPage.getCurrent());
        resultPage.setSize(crmImRoutingPage.getSize());

        return resultPage;
    }

    private void loadRoutingData(RoutingChannelVo routing) {
        // 加载渠道数据
        routing.setRoutingChannel(routingChannelMapper.selectList(new LambdaQueryWrapper<CrmImRoutingChannel>()
                .eq(CrmImRoutingChannel::getRoutingId, routing.getRoutingId())
                .eq(CrmImRoutingChannel::getDelStatus, DEL_STATUS)
                .orderByAsc(CrmImRoutingChannel::getChannelSeq)));

        // 加载规则数据
        List<CrmImRoutingRule> routingRuleList = routingRuleMapper.selectList(new LambdaQueryWrapper<CrmImRoutingRule>()
                .eq(CrmImRoutingRule::getRoutingId, routing.getRoutingId()));

        if (!routingRuleList.isEmpty()) {
            routing.setRoutingRule(routingRuleList);
            routingRuleList.forEach(routingRule -> {
                if (StringUtils.isNotBlank(routingRule.getDistributionIdsName())) {
                    routingRule.setDistributionIdsNameList(Arrays.asList(routingRule.getDistributionIdsName().split(",")));
                }
                loadUniqueData(routingRule);
            });
        }
    }

    private void loadUniqueData(CrmImRoutingRule routingRule) {
        // 加载个性化配置
        List<CrmImRoutingChannelUnique> uniquesList = routingChannelUniqueMapper.selectList(new LambdaQueryWrapper<CrmImRoutingChannelUnique>()
                .eq(CrmImRoutingChannelUnique::getRoutingId, routingRule.getRoutingId())
                .eq(CrmImRoutingChannelUnique::getRoutingRuleId, routingRule.getRoutingRuleId()));

        uniquesList.forEach(uniques -> {
            if (StringUtils.isNotBlank(uniques.getConditionValuesName())) {
                uniques.setConditionValuesNameList(Arrays.asList(uniques.getConditionValuesName().split(",")));
            }
        });
        routingRule.setUniquesList(uniquesList);
    }

    @Override
    public RoutingChannelVo queryRuleDetails(String routingId) {
        // 查询 CrmImRoutingMain 数据
        CrmImRoutingMain crmImRoutingMain = this.baseMapper.selectById(routingId);
        if (crmImRoutingMain == null) {
            return null;
        }

        RoutingChannelVo routingChannelVo = new RoutingChannelVo();
        BeanUtils.copyProperties(crmImRoutingMain, routingChannelVo);

        // 加载渠道数据、规则数据和个性化配置
        loadRoutingData(routingChannelVo);

        return routingChannelVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveRuleInfo(RoutingChannelVo vo) {

        if (ObjectUtil.isEmpty(vo)) {
            return AjaxResult.failure(MessageUtils.get("rule.save.error"));
        }

        //去重校验
        Set<String> conditionValuesSet = new HashSet<>();
        boolean hasDuplicates = vo.getRoutingRule().stream().anyMatch(rule ->
                rule.getUniquesList() != null && !rule.getUniquesList().isEmpty() &&
                        rule.getUniquesList().stream().anyMatch(unique -> {
                            List<String> allList = Arrays.asList(unique.getConditionValues().split(","));
                            return allList.stream().anyMatch(value -> !conditionValuesSet.add(value));
                        })
        );
        if (hasDuplicates) {
            return AjaxResult.failure(MessageUtils.get("rule.save.error"));
        }

        List<CrmImRoutingRule> ruleList = vo.getRoutingRule();
        List<CrmImRoutingChannel> crmImRoutingChannel = vo.getRoutingChannel();

        CrmImRoutingMain crmImRoutingMain = new CrmImRoutingMain();
        BeanUtils.copyProperties(vo, crmImRoutingMain);

        //新增
        if (StringUtils.isBlank(vo.getRoutingId())) {

            List<CrmImRoutingMain> routingMains = this.baseMapper.selectList(new LambdaQueryWrapper<CrmImRoutingMain>()
                    .eq(CrmImRoutingMain::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                    .eq(CrmImRoutingMain::getDelStatus, DEL_STATUS)
                    .eq(CrmImRoutingMain::getDefaultRouting, 0)
                    .orderByDesc(CrmImRoutingMain::getRoutingSeq));

            //计算排序值
            CrmImRoutingMain crmImRouting = routingMains.isEmpty() ? null : routingMains.get(0);
            crmImRoutingMain.setRoutingSeq(crmImRouting != null && crmImRouting.getRoutingSeq() != null
                    ? crmImRouting.getRoutingSeq() + 1
                    : 1);

            this.baseMapper.insert(crmImRoutingMain.setCreateBy(SecurityUtil.getUserId())
                    .setCreateTime(new Date())
                    .setDelStatus(DEL_STATUS)
                    .setCompanyId(SecurityUtil.getLoginUser().getCompanyId())
                    .setRoutingId(UUID.randomUUID().toString().replace("-", "")));

            //计算 默认规则 排序值
            CrmImRoutingMain imRoutingMain = this.baseMapper.selectOne(new LambdaQueryWrapper<CrmImRoutingMain>()
                    .eq(CrmImRoutingMain::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                    .eq(CrmImRoutingMain::getDelStatus, DEL_STATUS)
                    .eq(CrmImRoutingMain::getDefaultRouting, 1));
            if (imRoutingMain != null) {
                imRoutingMain.setRoutingSeq(crmImRoutingMain.getRoutingSeq() + 1);
                this.baseMapper.updateById(imRoutingMain);
            }
        } else {
            crmImRoutingMain.setUpdateTime(new Date());
            crmImRoutingMain.setUpdateBy(SecurityUtil.getUserId());
            this.baseMapper.updateById(crmImRoutingMain);
        }

        // 保存规则列表
        if (CollectionUtils.isNotEmpty(ruleList)) {
            saveOrUpdateRuleList(ruleList, crmImRoutingMain);
        }

        // 更新路由渠道信息
        updateRoutingChannels(crmImRoutingChannel, crmImRoutingMain);

        return AjaxResult.ok(MessageUtils.get("rule.save.success"));
    }


    private void saveOrUpdateRuleList(List<CrmImRoutingRule> ruleList, CrmImRoutingMain crmImRoutingMain) {
        routingRuleMapper.deleteByRoutingId(crmImRoutingMain.getRoutingId());


        ruleList.forEach(rule -> {
            if (rule.getDistributionIdsNameList()!=null) {
                rule.setDistributionIdsName(String.join(",", rule.getDistributionIdsNameList()));
            }

                rule.setRoutingRuleId(generateUUID());
                rule.setRoutingId(crmImRoutingMain.getRoutingId());
                rule.setCreateBy(SecurityUtil.getUserId());
                rule.setCreateTime(new Date());
                rule.setRoutingRuleSeq(determineRuleSeq(crmImRoutingMain.getRoutingId()));
                routingRuleMapper.insert(rule);

            updateRuleUniqueList(rule, crmImRoutingMain.getRoutingId());
        });
    }

    private int determineRuleSeq(String routingId) {
        List<CrmImRoutingRule> routingRules = routingRuleMapper.selectList(new LambdaQueryWrapper<CrmImRoutingRule>()
                .eq(CrmImRoutingRule::getRoutingId, routingId)
                .orderByDesc(CrmImRoutingRule::getRoutingRuleSeq));
        return routingRules.isEmpty() ? 1 : routingRules.get(0).getRoutingRuleSeq() + 1;
    }

    private void updateRuleUniqueList(CrmImRoutingRule rule, String routingId) {
        routingChannelUniqueMapper.deleteChannelUnique(rule.getRoutingRuleId());

        if (rule.getUniquesList()!=null && !rule.getUniquesList().isEmpty()) {
            rule.getUniquesList().forEach(unique -> {
                if (unique.getConditionValuesNameList()!=null && !unique.getConditionValuesNameList().isEmpty()) {
                    unique.setConditionValuesName(String.join(",", unique.getConditionValuesNameList()));
                }
                unique.setRuleUniqueId(generateUUID());
                unique.setRoutingRuleId(rule.getRoutingRuleId());
                unique.setRoutingId(routingId);
                unique.setCreateBy(SecurityUtil.getUserId());
                unique.setCreateTime(new Date());
                routingChannelUniqueMapper.insert(unique);
            });
        }
    }

    private void updateRoutingChannels(List<CrmImRoutingChannel> crmImRoutingChannel, CrmImRoutingMain crmImRoutingMain) {
        routingChannelMapper.update(new CrmImRoutingChannel().setDelStatus(1),
                new LambdaQueryWrapper<CrmImRoutingChannel>().eq(CrmImRoutingChannel::getRoutingId, crmImRoutingMain.getRoutingId()));

        if (CollectionUtils.isNotEmpty(crmImRoutingChannel)) {

            for (int i = 0; i < crmImRoutingChannel.size(); i++) {
                CrmImRoutingChannel channel=crmImRoutingChannel.get(i);
                channel.setRoutingChannelId(generateUUID());
                channel.setRoutingId(crmImRoutingMain.getRoutingId());
                channel.setCreateTime(new Date());
                channel.setChannelSeq(i+1);
                routingChannelMapper.insert(channel);
            }
        }

    }

    private String generateUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }


    // 返回一个List，包含所有枚举项的详细信息
    public List<RoutingRuleEnumGroupVo> getAllEnumDetails() {
        List<RoutingRuleEnumVo> collect = Arrays.stream(RoutingRuleEnum.values())
                .map(enumValue -> {
                    // 创建 RoutingRuleEnumVo 对象并赋值
                    RoutingRuleEnumVo vo = new RoutingRuleEnumVo();
                    vo.setGroupCode(enumValue.getGroupCode());
                    vo.setCode(enumValue.getDescription());
                    vo.setName(TransUtil.trans(enumValue.getName()));
                    return vo;
                })
                .collect(Collectors.toList());

        //将返回值进行处理，根据分组进行分组
        // 使用Collectors.groupingBy将collect按groupCode分组
        Map<Integer, List<RoutingRuleEnumVo>> grouped = collect.stream()
                .collect(Collectors.groupingBy(RoutingRuleEnumVo::getGroupCode));

        // 将分组结果转换为List<RoutingRuleEnumGroupVo>
        List<RoutingRuleEnumGroupVo> resultList = new ArrayList<>();
        for (Map.Entry<Integer, List<RoutingRuleEnumVo>> entry : grouped.entrySet()) {
            Integer groupCode = entry.getKey();
            //这里需要从RoutingRuleEnumVo列表中获取groupName，假设第一个元素的groupName代表整个组的groupName
            String groupName = RoutingRuleGroupEnum.getGroupNameByCode(groupCode);
            List<RoutingRuleEnumVo> routingRuleList = entry.getValue();
            resultList.add(
                    new RoutingRuleEnumGroupVo()
                            .setGroupCode(groupCode)
                            .setGroupName(TransUtil.trans(groupName))
                            .setRoutingRuleList(routingRuleList));
        }
        return resultList;
    }

    @Override
    public AjaxResult delRuleInfo(RoutingChannelVo vo) {
        if(vo.getRoutingId()==null){
            return  AjaxResult.failure(MessageUtils.get("rule.delete.error"));
        }
        CrmImRoutingMain crmImRoutingMain = this.baseMapper.selectById(vo.getRoutingId());
        if(crmImRoutingMain.getDefaultRouting()!=null && crmImRoutingMain.getDefaultRouting() ==1){
            return  AjaxResult.failure(MessageUtils.get("rule.delete.error"));
        }
        crmImRoutingMain.setRoutingId(vo.getRoutingId());
        crmImRoutingMain.setDelStatus(1);
        crmImRoutingMain.setUpdateTime(new Date());
        crmImRoutingMain.setUpdateBy(SecurityUtil.getUserId());
        this.baseMapper.updateById(crmImRoutingMain);
        //重置排序
        this.imRoutingMainMapper.updateOrder(crmImRoutingMain.getRoutingSeq(),crmImRoutingMain.getCompanyId());
        return  AjaxResult.ok(MessageUtils.get("rule.delete.success"));
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRoutingOrder(List<RoutingChannelVo> routingList) {
        if (CollectionUtils.isEmpty(routingList)) {
            return;  // 如果列表为空，直接返回
        }

        // 获取最小的 routingSeq 值
        Integer minRoutingSeq = routingList.stream()
                .min(Comparator.comparingInt(RoutingChannelVo::getRoutingSeq))
                .map(RoutingChannelVo::getRoutingSeq)
                .orElse(0);  // 如果为空，则使用 0 作为默认值

        List<CrmImRoutingMain> existingList = this.baseMapper.selectList(new LambdaQueryWrapper<CrmImRoutingMain>()
                .eq(CrmImRoutingMain::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                .eq(CrmImRoutingMain::getDelStatus, 0));

        if (CollectionUtils.isEmpty(existingList)) {
            return;
        }

        // 为每个 RoutingId 赋值新序号，避免重复创建 Map
        Map<String, Integer> routingSeqMap = new HashMap<>();
        for (int i = 0; i < routingList.size(); i++) {
            routingSeqMap.put(routingList.get(i).getRoutingId(), i + minRoutingSeq);
        }

        List<CrmImRoutingMain> saveList = new ArrayList<>(existingList.size());

        // 对查询到的 CrmImRoutingMain 进行重新排序
        for (CrmImRoutingMain crmImRoutingMain : existingList) {
            // 根据 routingSeqMap 设置新的 RoutingSeq
            Integer newSeq = routingSeqMap.get(crmImRoutingMain.getRoutingId());
            if (newSeq != null) {  // 确保 routingSeqMap 中包含当前的 routingId
                crmImRoutingMain.setRoutingSeq(newSeq);
                crmImRoutingMain.setUpdateBy(SecurityUtil.getUserId());
                crmImRoutingMain.setUpdateTime(new Date());
                saveList.add(crmImRoutingMain);
            }
        }
        // 批量更新数据
        if (!saveList.isEmpty()) {
            this.updateBatchById(saveList);
        }
    }


}




