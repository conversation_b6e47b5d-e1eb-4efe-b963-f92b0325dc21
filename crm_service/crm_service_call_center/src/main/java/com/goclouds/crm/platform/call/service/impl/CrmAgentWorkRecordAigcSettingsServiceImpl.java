package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordAigcSettings;
import com.goclouds.crm.platform.call.domain.vo.AigcSettingsVO;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordAigcSettingsMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordAigcSettingsService;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class CrmAgentWorkRecordAigcSettingsServiceImpl
        extends ServiceImpl<CrmAgentWorkRecordAigcSettingsMapper, CrmAgentWorkRecordAigcSettings>
        implements CrmAgentWorkRecordAigcSettingsService {

    @Override
    public AigcSettingsVO getSettings() {
       return getAigcSettingsInfo(SecurityUtil.getLoginUser().getCompanyId());
    }

    @Override
    public AigcSettingsVO getAigcSettingsInfo(String companyId) {
        // 查询数据库中是否有对应的设置记录
        CrmAgentWorkRecordAigcSettings crmAgentWorkRecordAigcSettings = this.getOne(new LambdaQueryWrapper<CrmAgentWorkRecordAigcSettings>()
                .eq(CrmAgentWorkRecordAigcSettings::getCompanyId, companyId));

        if (crmAgentWorkRecordAigcSettings == null) {
            return new AigcSettingsVO();
        }

        AigcSettingsVO aigcSettingsVO = new AigcSettingsVO();
        BeanUtils.copyProperties(crmAgentWorkRecordAigcSettings, aigcSettingsVO);
        // 返回设置对象
        return aigcSettingsVO;
    }

    @Override
    public String saveOrUpdate(AigcSettingsVO settings) {
        // 获取当前用户的公司ID和用户ID
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String userId = SecurityUtil.getUserId();

        // 查询是否存在对应的记录
        CrmAgentWorkRecordAigcSettings workRecordAigcSettings = this.getOne(new LambdaQueryWrapper<CrmAgentWorkRecordAigcSettings>()
                .eq(CrmAgentWorkRecordAigcSettings::getCompanyId, companyId));

        // 创建一个新的 AigcSettings 对象
        CrmAgentWorkRecordAigcSettings aigcSettings = new CrmAgentWorkRecordAigcSettings();
        BeanUtils.copyProperties(settings, aigcSettings);

        // 如果记录已存在，进行更新操作
        if (workRecordAigcSettings != null) {
            aigcSettings.setId(workRecordAigcSettings.getId());
            aigcSettings.setUpdatedBy(userId);
            aigcSettings.setUpdatedAt(LocalDateTime.now());
            this.updateById(aigcSettings);
            return  MessageUtils.get("save.success");
        }

        // 如果记录不存在，进行保存操作
        aigcSettings.setCompanyId(companyId);
        aigcSettings.setCreatedAt(LocalDateTime.now());
        aigcSettings.setCreatedBy(userId);
        this.save(aigcSettings);
        return  MessageUtils.get("save.success");
    }

}