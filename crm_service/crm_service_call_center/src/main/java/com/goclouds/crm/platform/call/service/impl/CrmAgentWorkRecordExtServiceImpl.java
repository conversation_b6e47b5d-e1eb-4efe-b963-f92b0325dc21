package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExt;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordExtMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordExtService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_ext(工作记录扩展属性表)】的数据库操作Service实现
* @createDate 2023-05-25 10:30:27
*/
@Service
public class CrmAgentWorkRecordExtServiceImpl extends ServiceImpl<CrmAgentWorkRecordExtMapper, CrmAgentWorkRecordExt>
    implements CrmAgentWorkRecordExtService {

    @Override
    public List<String> getQueryWorkId(QueryWrapper<String> queryWrapperExt) {

       return this.baseMapper.getQueryWorkId(queryWrapperExt);
    }
}




