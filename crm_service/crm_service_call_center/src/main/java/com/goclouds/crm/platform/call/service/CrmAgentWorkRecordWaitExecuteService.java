package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordWaitExecute;
import com.goclouds.crm.platform.call.domain.vo.UpdateWaitExecuteVO;
import com.goclouds.crm.platform.call.domain.vo.AddWaitExecuteVO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_wait_execute(工单待办事项)】的数据库操作Service
* @createDate 2023-10-20 17:36:05
*/
public interface CrmAgentWorkRecordWaitExecuteService extends IService<CrmAgentWorkRecordWaitExecute> {

    /**
     * 更改待办事项状态
     * @param updateWaitExecuteVO 请求参数
     * @return 返回状态
     */
    AjaxResult<Object> updateWaitExecute(UpdateWaitExecuteVO updateWaitExecuteVO);

    /**
     * 新增待办事项
     * @param addWaitExecuteVO 请求参数
     * @return 返回状态
     */
    AjaxResult<Object> addWaitExecute(AddWaitExecuteVO addWaitExecuteVO);

}
