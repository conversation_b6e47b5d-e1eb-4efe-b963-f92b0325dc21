package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordOperationLog;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderRemarksVO;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderSatisfactionVo;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordOperationLogVO;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.domain.call.PhoneWorkOrderSatisfactionVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_operation_log(工单操作日志记录;)】的数据库操作Service
* @createDate 2023-09-28 14:33:26
*/
public interface CrmAgentWorkRecordOperationLogService extends IService<CrmAgentWorkRecordOperationLog> {

    /**
     *  查询工单操作记录
     * @param workRecordId 工单id
     * @return 查询工单操作记录
     */
    AjaxResult<List<WorkRecordOperationLogVO>> workOrderOperationRecords(String workRecordId);

    /**
     *
     * @param workRecordId
     * @return
     */
    AjaxResult<List<WorkOrderRemarksVO>> queryByTicketRemarks(String workRecordId);

//    AjaxResult ticketSatisfaction(WorkOrderSatisfactionVo workOrderSatisfactionVo);

    /**
     * 查询备注列表 分页
     * @param pageParam
     * @param workRecordId
     * @return
     */
    AjaxResult<IPage<WorkOrderRemarksVO>> queryByTicketRemarksPage(IPage<Object> pageParam, String workRecordId);

    //工单满意度评价，改为存储到es中
    AjaxResult<Object> ticketSatisfactionToEs(WorkOrderSatisfactionVo workOrderSatisfactionVo) throws Exception ;

    AjaxResult<Object> phoneTicketSatisfaction(PhoneWorkOrderSatisfactionVo phoneWorkOrderSatisfactionVo) throws Exception;
}
