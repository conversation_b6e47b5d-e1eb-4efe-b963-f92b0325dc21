package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmCallPushMessageConfig;
import com.goclouds.crm.platform.call.service.CrmCallPushMessageConfigService;
import com.goclouds.crm.platform.call.mapper.CrmCallPushMessageConfigMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_call_push_message_config(推送消息配置表)】的数据库操作Service实现
* @createDate 2025-02-19 17:01:29
*/
@Service
public class CrmCallPushMessageConfigServiceImpl extends ServiceImpl<CrmCallPushMessageConfigMapper, CrmCallPushMessageConfig>
    implements CrmCallPushMessageConfigService{

}




