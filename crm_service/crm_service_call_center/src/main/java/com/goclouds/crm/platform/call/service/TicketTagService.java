package com.goclouds.crm.platform.call.service;

import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.domain.call.BatchUpdateTicketTagVo;
import com.goclouds.crm.platform.openfeignClient.domain.call.TicketTagResult;
import com.goclouds.crm.platform.openfeignClient.domain.call.UpdateTicketTagVo;

import java.util.List;

/**
 * @author: sunlinan
 * @description:
 * @date: 2025-04-09 14:51
 **/
public interface TicketTagService {
    AjaxResult<List<TicketTagResult>> queryEsTicketTag(String companyId, String ticketId);

    AjaxResult<Object> updateEsTicketTag(UpdateTicketTagVo updateTicketTag);

    AjaxResult<Object> removeEsTicketTag(String companyId, String ticketId, String tagId);

    AjaxResult<Object> deleteEsTicketTag(String companyId, String tagId);

    AjaxResult<Object> updateForEsTicketTagName(String companyId, String tagId, String tagContent);

    AjaxResult<Object> batchUpdateEsTicketTag(BatchUpdateTicketTagVo updateTicketTag);
}
