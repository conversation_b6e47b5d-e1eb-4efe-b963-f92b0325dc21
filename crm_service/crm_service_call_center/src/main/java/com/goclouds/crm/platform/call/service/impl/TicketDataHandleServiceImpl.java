package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.es.*;
import com.goclouds.crm.platform.call.service.*;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.ibm.icu.text.SimpleDateFormat;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TicketDataHandleServiceImpl implements TicketDataHandleService {

    private final CrmAgentWorkRecordService crmAgentWorkRecordService;

    private final CrmAgentWorkRecordDetailService crmAgentWorkRecordDetailService;

    private final CrmAgentWorkRecordContentService crmAgentWorkRecordContentService;

    private final CrmAgentWorkRecordFileService crmAgentWorkRecordFileService;

    private final CrmAgentWorkRecordExtIntsService crmAgentWorkRecordExtIntsService;

    private final CrmAgentWorkRecordOperationLogService crmAgentWorkRecordOperationLogService;

    private final CrmAgentWorkRecordSatisfactionService crmAgentWorkRecordSatisfactionService;

    private final CrmAgentWorkRecordConcernedService crmAgentWorkRecordConcernedService;

    private final CrmAgentWorkRecordRelationService crmAgentWorkRecordRelationService;

    private final RestHighLevelClient restHighLevelClient;

    @Value("${es.work-record-content-index}")
    private String workContentIndex;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    @Value("${es.ticket-relation-index}")
    private String ticketRelationIndex;

    @Override
    public void ticketDataTransfer(String companyId){
        log.info("开始进行工单的补充");
        // 定义保存索引
        String indexName = ticketIndex + companyId;

        String ticketRelationIndexName = ticketRelationIndex + companyId;

        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
        }

        // 查询索引是否存在
        boolean ticketRelationIndexExists = headIndexExists(ticketRelationIndexName);
        // 索引不存在直接创建索引
        if (!ticketRelationIndexExists) {
            createIndex(ticketRelationIndexName);
        }

        BulkRequest ticketBulkRequest = new BulkRequest(indexName);

        BulkRequest ticketRelationBulkRequest = new BulkRequest(ticketRelationIndexName);

        ObjectMapper objectMapper = new ObjectMapper();
        // 注册 JavaTimeModule
        objectMapper.registerModule(new JavaTimeModule());

        //查询当前公司下的工单数据
        LambdaQueryWrapper<CrmAgentWorkRecord> wrapper = new QueryWrapper<CrmAgentWorkRecord>().lambda()
                .eq(CrmAgentWorkRecord::getCompanyId, companyId)
                .eq(CrmAgentWorkRecord::getDataStatus, 1);
        // 查询出需要补充的工单数据
        List<CrmAgentWorkRecord> companyWorkRecordList = crmAgentWorkRecordService.list(wrapper);

        // 根据工单id进行分组，方便取出工单信息
        Map<String, List<CrmAgentWorkRecord>> workRecordMap = companyWorkRecordList.stream()
                .collect(Collectors.groupingBy(CrmAgentWorkRecord::getWorkRecordId));
        // 取出所有工单id
        List<String> workIdList = companyWorkRecordList.stream().map(CrmAgentWorkRecord::getWorkRecordId).collect(Collectors.toList());

        // 查询出该工单下的所有拓展字段
        List<CrmAgentWorkRecordExtInts> recordExtIntsList = crmAgentWorkRecordExtIntsService.list(new QueryWrapper<CrmAgentWorkRecordExtInts>().lambda()
                .eq(CrmAgentWorkRecordExtInts::getDataStatus, 1)
                .in(CrmAgentWorkRecordExtInts::getWorkRecordId, workIdList));
        // 根据工单id进行分组，方便取出工单信息
        Map<String, List<CrmAgentWorkRecordExtInts>> recordExtIntsMap = recordExtIntsList.stream()
                .collect(Collectors.groupingBy(CrmAgentWorkRecordExtInts::getWorkRecordId));


        // 查询出该工单下的所有操作日志
        List<CrmAgentWorkRecordOperationLog> operationLogList = crmAgentWorkRecordOperationLogService.list(new QueryWrapper<CrmAgentWorkRecordOperationLog>().lambda()
                .eq(CrmAgentWorkRecordOperationLog::getDataStatus, 1)
                .in(CrmAgentWorkRecordOperationLog::getWorkRecordId, workIdList));
        // 根据工单id进行分组，方便取出工单信息
        Map<String, List<CrmAgentWorkRecordOperationLog>> operationLogMap = operationLogList.stream()
                .collect(Collectors.groupingBy(CrmAgentWorkRecordOperationLog::getWorkRecordId));

        // 查询出该工单的满意度评价
        List<CrmAgentWorkRecordSatisfaction> recordSatisfactionList = crmAgentWorkRecordSatisfactionService.list(new QueryWrapper<CrmAgentWorkRecordSatisfaction>().lambda()
                .eq(CrmAgentWorkRecordSatisfaction::getDataStatus, 1)
                .in(CrmAgentWorkRecordSatisfaction::getWorkRecordId, workIdList));
        // 根据工单id进行分组，方便取出工单信息
        Map<String, List<CrmAgentWorkRecordSatisfaction>> recordSatisfactionMap = recordSatisfactionList.stream()
                .collect(Collectors.groupingBy(CrmAgentWorkRecordSatisfaction::getWorkRecordId));


        // 查询出该工单的关注关系
        List<CrmAgentWorkRecordConcerned> recordConcernedList = crmAgentWorkRecordConcernedService.list(new QueryWrapper<CrmAgentWorkRecordConcerned>().lambda()
                .eq(CrmAgentWorkRecordConcerned::getDataStatus, 1)
                .in(CrmAgentWorkRecordConcerned::getWorkRecordId, workIdList));
        // 根据工单id进行分组，方便取出工单信息
        Map<String, List<CrmAgentWorkRecordConcerned>> recordConcernedMap = recordConcernedList.stream()
                .collect(Collectors.groupingBy(CrmAgentWorkRecordConcerned::getWorkRecordId));

        // 查询出工单的关联关系
        List<CrmAgentWorkRecordRelation> recordRelationList = crmAgentWorkRecordRelationService.list(new QueryWrapper<CrmAgentWorkRecordRelation>().lambda()
                .in(CrmAgentWorkRecordRelation::getWorkRecordId, workIdList)
                .or()
                .in(CrmAgentWorkRecordRelation::getRelationWorkRecordId, workIdList));


        companyWorkRecordList.forEach(ticket->{
            TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
            BeanUtils.copyProperties(ticket, ticketInfoIndex);

            if(ticket.getInitiationTime()!=null){
                ticketInfoIndex.setInitiationTime(ticket.getInitiationTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getResolveTime()!=null){
                ticketInfoIndex.setResolveTime(ticket.getResolveTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getShouldResolveTime()!=null){
                ticketInfoIndex.setShouldResolveTime(ticket.getShouldResolveTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getFirstResponseTime()!=null){
//                ticketInfoIndex.setFirstResponseTime(ticket.getFirstResponseTime().toInstant()
//                        .atZone(ZoneId.systemDefault())
//                        .toLocalDateTime());
            }
            if(ticket.getFinalResponseTime()!=null){
                ticketInfoIndex.setFinalResponseTime(ticket.getFinalResponseTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getShouldResponseTime()!=null){
                ticketInfoIndex.setShouldResponseTime(ticket.getShouldResponseTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getTerminateTime()!=null){
                ticketInfoIndex.setTerminateTime(ticket.getTerminateTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getTransferTime()!=null){
                ticketInfoIndex.setTransferTime(ticket.getTransferTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getTimeOfDuration()!=null){
                ticketInfoIndex.setTimeOfDuration(ticket.getTimeOfDuration().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getModifyTime()!=null){
                ticketInfoIndex.setModifyTime(ticket.getModifyTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getCreateTime()!=null){
                ticketInfoIndex.setCreateTime(ticket.getCreateTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getLastMessageDeliveryTime()!=null){
                ticketInfoIndex.setLastMessageDeliveryTime(ticket.getLastMessageDeliveryTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getLastMessageReplyTime()!=null){
                ticketInfoIndex.setLastMessageReplyTime(ticket.getLastMessageReplyTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            if(ticket.getCustomerFirstMessageTime()!=null){
                ticketInfoIndex.setCustomerFirstMessageTime(ticket.getCustomerFirstMessageTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }

            // 取出来该工单下的扩展字段保存
            List<CrmAgentWorkRecordExtInts> ticketRecordExt = recordExtIntsMap.get(ticket.getWorkRecordId());
            if(CollectionUtils.isNotEmpty(ticketRecordExt)){
                List<TicketExt> ticketExtList = new ArrayList<>();
                ticketRecordExt.forEach(ext->{
                    TicketExt ticketExt = new TicketExt();
                    ticketExt.setExtCode(ext.getWorkRecordExtCode());
                    ticketExt.setExtValue(ext.getWorkRecordExtValue());
                    ticketExtList.add(ticketExt);
                });
                ticketInfoIndex.setTicketExt(ticketExtList);
            }
            // 取出该工单下的操作日志保存
            List<CrmAgentWorkRecordOperationLog> recordOperationLogList = operationLogMap.get(ticket.getWorkRecordId());
            if(CollectionUtils.isNotEmpty(recordOperationLogList)){
                List<TicketOperationLog> ticketOperationLogList = new ArrayList<>();
                recordOperationLogList.forEach(operationLog->{
                    TicketOperationLog ticketOperationLog = new TicketOperationLog();
                    BeanUtils.copyProperties(operationLog, ticketOperationLog);
                    if(operationLog.getCreateTime()!=null){
                        ticketOperationLog.setOperatorTime(operationLog.getCreateTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());
                    }
                    ticketOperationLogList.add(ticketOperationLog);
                });
                ticketInfoIndex.setTicketOperationLog(ticketOperationLogList);
            }

            // 取出该工单下的满意度评价
            List<CrmAgentWorkRecordSatisfaction> satisfactionList = recordSatisfactionMap.get(ticket.getWorkRecordId());
            if(CollectionUtils.isNotEmpty(satisfactionList)){
                List<TicketSatisfaction> ticketSatisfactionList = new ArrayList<>();
                satisfactionList.forEach(satisfaction->{
                    TicketSatisfaction ticketSatisfaction = new TicketSatisfaction();
                    BeanUtils.copyProperties(satisfaction, ticketSatisfaction);
                    if(satisfaction.getCommentTime()!=null){
                        ticketSatisfaction.setCommentTime(satisfaction.getCommentTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());
                    }
                    if(satisfaction.getRating()!=null){
                        ticketSatisfaction.setRating(satisfaction.getRating().doubleValue());
                    }
                    ticketSatisfactionList.add(ticketSatisfaction);
                });
                ticketInfoIndex.setTicketSatisfaction(ticketSatisfactionList);
            }

            // 取出该工单的关注关系表
            List<CrmAgentWorkRecordConcerned> concernedList = recordConcernedMap.get(ticket.getWorkRecordId());
            if(CollectionUtils.isNotEmpty(concernedList)){
                List<TicketFollow> ticketFollowList = new ArrayList<>();
                concernedList.forEach(concerned->{
                    TicketFollow ticketFollow = new TicketFollow();
                    ticketFollow.setUserId(concerned.getUserId());
                    if(concerned.getCreateTime()!=null){
                        ticketFollow.setFollowTime(concerned.getCreateTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime());
                    }
                    ticketFollowList.add(ticketFollow);
                });
                ticketInfoIndex.setTicketFollow(ticketFollowList);
            }
            try {
                String value = objectMapper.writeValueAsString(ticketInfoIndex);
                IndexRequest indexRequest = new IndexRequest().source(value, XContentType.JSON);
                ticketBulkRequest.add(indexRequest);
            } catch (JsonProcessingException e) {
                log.error("工单信息转换json报错",e);
            }

        });



        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        recordRelationList.forEach(item->{
            TicketRelation ticketRelation = new TicketRelation();
            ticketRelation.setRelation_id(item.getRelationId());
            ticketRelation.setWork_record_id(item.getWorkRecordId());
            ticketRelation.setRelation_work_record_id(item.getRelationWorkRecordId());
            ticketRelation.setCreator(item.getCreator());
            ticketRelation.setModifier(item.getModifier());
            ticketRelation.setData_status(item.getDataStatus());
            if(item.getCreateTime()!=null){
                ticketRelation.setCreate_time(dateFormat.format(item.getCreateTime()));
            }
            if(item.getModifyTime()!=null){
                ticketRelation.setModify_time(item.getModifyTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime());
            }
            try {

                String value = objectMapper.writeValueAsString(ticketRelation);
                IndexRequest indexRequest = new IndexRequest().source(value, XContentType.JSON);
                ticketRelationBulkRequest.add(indexRequest);
            } catch (JsonProcessingException e) {
                log.error("工单关联关系转换json报错",e);
            }
        });

        if(ticketBulkRequest.numberOfActions()>0){
            log.info("工单数据大于0，进行添加");
            // 当前公司的工单，进行批量加入
            try {
                restHighLevelClient.bulk(ticketBulkRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                    log.error("ES保存工单数据失败，异常信息：", e);
                }
            }
        }
        log.info("工单数据补充成功");

        if(ticketRelationBulkRequest.numberOfActions()>0) {
            log.info("工单关联数据大于0，进行添加");
            // 当前公司的工单关联关系，进行批量加入
            try {
                restHighLevelClient.bulk(ticketRelationBulkRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                    log.error("ES保存工单关联关系数据失败，异常信息：", e);
                }
            }
        }
        log.info("工单关联数据补充成功");


    }



    @Override
    public void ticketCommentDataTransfer(String companyId) {
        log.info("开始补充工单聊天数据");
        //查询当前公司下的工单数据
        LambdaQueryWrapper<CrmAgentWorkRecord> wrapper = new QueryWrapper<CrmAgentWorkRecord>().lambda()
                .eq(CrmAgentWorkRecord::getCompanyId, companyId)
                .eq(CrmAgentWorkRecord::getDataStatus, 1);
        // 查询出需要补充的工单数据
        List<CrmAgentWorkRecord> companyWorkRecord = crmAgentWorkRecordService.list(wrapper);
        // 定义保存索引
        String index = workContentIndex + companyId;

        // 查询索引是否存在
        boolean ticketRelationIndexExists = headIndexExists(index);
        // 索引不存在直接创建索引
        if (!ticketRelationIndexExists) {
            createIndex(index);
        }

        // 根据工单id进行分组，方便取出工单信息
        Map<String, List<CrmAgentWorkRecord>> workRecordMap = companyWorkRecord.stream()
                .collect(Collectors.groupingBy(CrmAgentWorkRecord::getWorkRecordId));
        // 取出所有工单id
        List<String> workIdList = companyWorkRecord.stream().map(CrmAgentWorkRecord::getWorkRecordId).collect(Collectors.toList());


        // 查询connect表、详情表、附件表
        // 根据工单id查询，查询出工单connect关系
        List<CrmAgentWorkRecordDetail> detailList = crmAgentWorkRecordDetailService.list(new QueryWrapper<CrmAgentWorkRecordDetail>().lambda()
                .in(CrmAgentWorkRecordDetail::getWorkRecordId, workIdList)
                .eq(CrmAgentWorkRecordDetail::getDataStatus, 1));


        // 工单详情回复
        List<CrmAgentWorkRecordContent> contentList = crmAgentWorkRecordContentService.list(new QueryWrapper<CrmAgentWorkRecordContent>().lambda()
                .eq(CrmAgentWorkRecordContent::getDataStatus, 1));
        // 将工单回复根据detail进行分组
        Map<String, List<CrmAgentWorkRecordContent>> contentMap = contentList.stream()
                .collect(Collectors.groupingBy(CrmAgentWorkRecordContent::getWorkRecordDetailId));


        // 查询工单附件
        List<CrmAgentWorkRecordFile> fileList = crmAgentWorkRecordFileService.list(new QueryWrapper<CrmAgentWorkRecordFile>().lambda()
                .eq(CrmAgentWorkRecordFile::getDataStatus, 1));
        // 将工单附件进行分组
        Map<String, List<CrmAgentWorkRecordFile>> fileMap = fileList.stream()
                .collect(Collectors.groupingBy(CrmAgentWorkRecordFile::getWorkRecordContentId));

        BulkRequest bulkRequest = new BulkRequest(index);

        // 循环connect关系表
        detailList.forEach(item ->{
            // 根据connect关系id,取出对应回复详情的list
            List<CrmAgentWorkRecordContent> list = contentMap.get(item.getWorkRecordDetailId());
            list.forEach(content ->{
                TicketContentIndex ticketContentIndex = new TicketContentIndex();
                // 定义标识
                ticketContentIndex.setWork_record_content_id(UuidUtils.generateUuid());
                // 定义工单id
                ticketContentIndex.setWork_record_id(item.getWorkRecordId());
                // 回复内容
                if(StringUtil.isNotEmpty(content.getContent())){
                    ticketContentIndex.setContent(content.getContent());
                }
                // 联系id
                if(StringUtil.isNotEmpty(item.getContactId())){
                    ticketContentIndex.setContact_id(item.getContactId());
                }
                // 实例id
                if(StringUtil.isNotEmpty(item.getConnectId())){
                    ticketContentIndex.setConnect_id(item.getConnectId());
                }
                // 定义回复内容类型
                ticketContentIndex.setContent_type(content.getContentType());
                // 回复类型为null 则是手动的创建工单，定义类型为客服
                if(content.getReplyType() == null){
                    ticketContentIndex.setReply_type(1);
                }else {
                    ticketContentIndex.setReply_type(content.getReplyType());
                }
                // 取到工单信息
                List<CrmAgentWorkRecord> workRecords = workRecordMap.get(item.getWorkRecordId());
                CrmAgentWorkRecord workRecord = workRecords.get(0);
                // 将之前的文本定义转成json
                String workFiled1 = content.getWorkFiled1();
                JSONObject jsonObject = JSONObject.parseObject(workFiled1);

                // 邮件主题 需要判断工单是否是邮件类型，如果为邮件类型，则取工单主题
                if(workRecord.getChannelTypeId().equals(CrmChannelEnum.EMAIL.getCode().toString())){
                    // 邮件主题
                    ticketContentIndex.setEmail_subject(jsonObject.get("subject").toString());
                    // 邮件发送人
                    if(jsonObject.get("from")!=null){
                        ticketContentIndex.setEmail_sender(jsonObject.get("from").toString());
                    }
                    // 邮件接收人
                    if(jsonObject.get("to")!=null){
                        ticketContentIndex.setEmail_recipient(jsonObject.get("to").toString());
                    }
                }
                // 回复人
                if(jsonObject.get("from")!=null){
                    ticketContentIndex.setReply_person(jsonObject.get("from").toString());
                }else{
                    ticketContentIndex.setReply_person(jsonObject.get("subject").toString());
                }
                // 回复时间
                ticketContentIndex.setReply_time(jsonObject.get("sendTime").toString());
                ticketContentIndex.setReply_millis_time(Convert.toStr(Convert.toLocalDateTime(jsonObject.get("sendTime").toString()).toInstant(ZoneOffset.UTC).toEpochMilli()));
                // 定义附件内容
                List<TicketFile> ticketFileList = new ArrayList<>();
                List<CrmAgentWorkRecordFile> compareFileList = fileMap.get(content.getWorkRecordContentId());
                if(CollectionUtils.isNotEmpty(compareFileList)){
                    compareFileList.forEach(file->{
                        TicketFile ticketFile = new TicketFile();
                        ticketFile.setFile_path(file.getFilePath());
                        ticketFile.setBucket_name(file.getBucketName());
                        ticketFile.setFile_name(file.getFileName());
                        ticketFileList.add(ticketFile);
                    });
                }
                // 如果有附件，则进行添加
                if(CollectionUtils.isNotEmpty(ticketFileList)){
                    ticketContentIndex.setTicket_file(ticketFileList);
                }
                // 读取状态默认为已读
                ticketContentIndex.setRead_status(1);
                String jsonString = JSON.toJSONString(ticketContentIndex, SerializerFeature.WriteMapNullValue);
                IndexRequest indexRequest = new IndexRequest().source(jsonString, XContentType.JSON);
                bulkRequest.add(indexRequest);
            });
        });
        if(bulkRequest.numberOfActions()>0){
            log.info("工单聊天数据进行添加");
            try {
                // 每一条connect详情，进行一次批量插入
                restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                    log.error("ES保存工单聊天数据失败，异常信息：", e);
                }
            }
        }
        log.info("工单聊天数据补充成功");

    }


    /**
     *  验证索引是否存在
     * @param index 索引名称
     * @return 存在状态
     * @throws IOException
     */
    private boolean headIndexExists(String index){
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        }catch (Exception e){
            log.error("验证索引失败:",e);
        }
        return false;
    }

    /**
     *  创建索引
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName){
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        }catch (Exception e){
            log.error("创建索引报错",e);
        }
    }
}
