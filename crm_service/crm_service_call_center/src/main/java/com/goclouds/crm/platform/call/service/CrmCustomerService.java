package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmCustomer;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * 客户资料(CrmCustomer)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-23 18:57:57
 */
public interface CrmCustomerService extends IService<CrmCustomer> {

    /**
     * <p>
     *     根据客户id查询客户信息
     * </p>
     * @param customerContactInfo 客户联系方式
     * @param channelId 渠道id
     * @return customer id
     */
    String existsCustomer(String customerContactInfo,String channelId);

}

