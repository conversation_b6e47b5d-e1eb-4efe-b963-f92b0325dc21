package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilter;
import com.goclouds.crm.platform.call.domain.vo.CreateOrModifyFilterVO;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordFilterResponseVO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_filter(工单筛选器;)】的数据库操作Service
* @createDate 2023-09-19 15:30:11
*/
public interface CrmAgentWorkRecordFilterService extends IService<CrmAgentWorkRecordFilter> {

    /**
     *  查询筛选器列表
     * @return 筛选器列表
     */
    List<WorkRecordFilterResponseVO> queryWorkRecordFilter();

    /**
     * 创建筛选器
     * @param createOrModifyFilterVO 请求参数
     * @return 返回状态
     */
    AjaxResult<Object> createFilter(CreateOrModifyFilterVO createOrModifyFilterVO);

    /**
     * 修改筛选器
     * @param createOrModifyFilterVO 请求参数
     * @return 返回状态
     */
    AjaxResult<Object> modifyFilter(CreateOrModifyFilterVO createOrModifyFilterVO);
}
