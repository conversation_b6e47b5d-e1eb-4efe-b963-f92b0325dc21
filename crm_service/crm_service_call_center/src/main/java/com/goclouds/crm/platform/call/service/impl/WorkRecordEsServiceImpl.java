package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordEsResult;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordFinalEsResult;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordTypeDefService;
import com.goclouds.crm.platform.call.service.WorkRecordEsService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.enums.CrmChannelTypeIconEnum;
import com.goclouds.crm.platform.common.enums.TicketStatusEnum;
import com.goclouds.crm.platform.common.enums.UserRoleEnum;
import com.goclouds.crm.platform.common.utils.DateUtils;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.channel.ChannelDefVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysRoleVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysUserVo;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import com.ibm.icu.text.SimpleDateFormat;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: sunlinan
 * @Description: 需求修改，之前从mysql查询数据，后续改成了从es中查询（首页功能）
 * @Date: 2024-11-20 10:49
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkRecordEsServiceImpl implements WorkRecordEsService {
    private final ChannelClient channelClient;
    private final RestHighLevelClient restHighLevelClient;
    private final CrmAgentWorkRecordTypeDefService crmAgentWorkRecordTypeDefService;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    //便于前端适配德语，此处固定将部分状态返回值，新增一个英文返回
    private static final String STATUS_TIMEOUT_EN = "Timeout";
    private static final String STATUS_RESOLVED_EN = "Resolved";
    private static final String STATUS_PROCESSING_EN = "Processing";
    private static final String STATUS_PENDING_EN = "Pending";
    private static final String STATUS_OTHERS_EN = "Others";


    @Override
    public AjaxResult queryChannelWorkOrder(ChannelWorkOrderVo channelWorkOrderVo) throws Exception {
        List<Map<String, Object>> doubleMapResultList = new ArrayList<>();
        Integer timeRangeCode = channelWorkOrderVo.getTimeRangeCode();

        Map<String, String> commonConditionMap = getQueryCommonCondition(timeRangeCode);
        String startTime = commonConditionMap.get("startTime");
        String endTime = commonConditionMap.get("endTime");
        String companyId = commonConditionMap.get("companyId");
        String roleId = commonConditionMap.get("roleId");

        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            return AjaxResult.ok(doubleMapResultList);
        }
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1);

        //渠道类型条件
//        boolQueryBuilder.must(QueryBuilders.existsQuery("channel_type_id")); //isNotNull
//        boolQueryBuilder.mustNot(QueryBuilders.termQuery("channel_type_id", "")); //ne

        //需求修改，查询所有状态
     /*   //如果状态是3，那么就是已解决，如果是1和2，还要判断一下是否超时，如果超时了，就是已超时，否则就是处理中
        // 如果是管理员和座席管理员，还需要考虑待分配状态 0-待分配
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery();
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId) || UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
            statusQueryBuilder.should(QueryBuilders.termQuery("status", 0));
        }
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 1));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 2));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 3));
        statusQueryBuilder.minimumShouldMatch(1);
        boolQueryBuilder.must(statusQueryBuilder);*/

        // 时间范围条件
        if (timeRangeCode != 5) {
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startTime).lte(endTime));
            }
        }
        searchSourceBuilder.query(boolQueryBuilder);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        List<WorkOrderRecordEsResult> crmAgentWorkRecordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult result = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, result);
                crmAgentWorkRecordList.add(result);
            }
        }
        if (CollectionUtils.isNotEmpty(crmAgentWorkRecordList)) {
            //将Facebook、WhatsApp、Twitter、Line、聊天都划分到"在线聊天"（前端页面展示的时候，暂定：1-邮件 2-电话 3-在线聊天）
            for (WorkOrderRecordEsResult work : crmAgentWorkRecordList) {
                String channelTypeId = work.getChannelTypeId();
                if (StringUtil.isEmpty(channelTypeId)) { //如下判断是为了避免测试的时候出现假数据不规范的问题，导致空指针，生产环境不会出现类似问题
                    work.setChannelTypeId(STATUS_OTHERS_EN);
                    work.setChannelTypeName(STATUS_OTHERS_EN);
                }
            }
            List<ChannelWorkOrderVo> channelWorkOrderVoList = new ArrayList<>();
            LocalDateTime currentTime = LocalDateTime.now();
            for (WorkOrderRecordEsResult work : crmAgentWorkRecordList) {
                //先判断工单状态，再分组求和（计算百分比）将状态结果临时赋值一个状态，便于分组
                ChannelWorkOrderVo channelWorkOrderVoResult = new ChannelWorkOrderVo();
                channelWorkOrderVoResult.setChannelTypeId(work.getChannelTypeId());
                channelWorkOrderVoResult.setChannelTypeName(work.getChannelTypeName());
                LocalDateTime terminationDateTime = work.getShouldResolveTime();
                // 如果是未解决状态,判断时间
                if (work.getStatus() == 1 || work.getStatus() == 2) {
                    // 当前时间大于终止时间>>>超时 或 当前时间等于终止时间>>>超时
                    if (currentTime.isAfter(terminationDateTime) || currentTime.isEqual(terminationDateTime)) {
                        //已超时
                        channelWorkOrderVoResult.setResolveStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_TIMEOUT_EN)));
                        channelWorkOrderVoResult.setResolveStatusEn(TicketStatusEnum.STATUS_TIMEOUT.getCode());
                    }
                    // 当前时间小于终止时间>>>处理中
                    if (currentTime.isBefore(terminationDateTime)) {
                        channelWorkOrderVoResult.setResolveStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_PROCESSING_EN)));
                        channelWorkOrderVoResult.setResolveStatusEn(TicketStatusEnum.STATUS_PROCESSING.getCode());
                    }
                }else if (work.getStatus() == 3) {
                    //已解决
                    channelWorkOrderVoResult.setResolveStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_RESOLVED_EN)));
                    channelWorkOrderVoResult.setResolveStatusEn(TicketStatusEnum.STATUS_RESOLVED.getCode());
                }else if (work.getStatus() == 0) {
                    //待分配
                    channelWorkOrderVoResult.setResolveStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_PENDING_EN)));
                    channelWorkOrderVoResult.setResolveStatusEn(TicketStatusEnum.STATUS_PENDING.getCode());
                }else{
                    //todo 需求修改后，查询所有状态，除了上述状态，其余的，先临时处理为Others，因为不在前端展示，这样前端的整体比例之和可能大于100%，组长说这样是可以的
                    channelWorkOrderVoResult.setResolveStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_OTHERS_EN)));
                    channelWorkOrderVoResult.setResolveStatusEn(TicketStatusEnum.STATUS_OTHERS.getCode());
                }

                channelWorkOrderVoList.add(channelWorkOrderVoResult);
            }
            //版本1
//            doubleMapResultList = getChannelData1(channelWorkOrderVoList);
            //为了前端页面展示好看，需要填充一些为0的数据(后续还可能改成如上方法)
            Object channelData2 = getChannelData2(channelWorkOrderVoList, roleId);
            return AjaxResult.ok(channelData2);
        }
        return AjaxResult.ok(doubleMapResultList);
    }

    @Override
    public AjaxResult queryWorkOrderTypeDetail(ChannelWorkOrderVo channelWorkOrderVo) throws Exception {
        Integer timeRangeCode = channelWorkOrderVo.getTimeRangeCode();
        Map<String, String> commonConditionMap = getQueryCommonCondition(timeRangeCode);
        String startTime = commonConditionMap.get("startTime");
        String endTime = commonConditionMap.get("endTime");
        String companyId = commonConditionMap.get("companyId");

        List<Map<String, Object>> resultMapList = new ArrayList<>();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            return AjaxResult.ok(resultMapList);
        }
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1);

        // 时间范围条件
        if (timeRangeCode != 5) {
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startTime).lte(endTime));
            }
        }

        searchSourceBuilder.query(boolQueryBuilder);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        List<WorkOrderRecordEsResult> crmAgentWorkRecordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult result = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, result);
                crmAgentWorkRecordList.add(result);
            }
        }

        if (CollectionUtils.isNotEmpty(crmAgentWorkRecordList)) {
            //将工单类型进行国际化
            String headerLanguage = ServletUtils.getHeaderLanguage();
            List<WorkRecordTypeVO> workRecordTypeVOList = crmAgentWorkRecordTypeDefService.queryWorkRecordTypeByLanguage(headerLanguage);
            Map<String, String> workRecordTypeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(workRecordTypeVOList)) {
                //根据类型进行分组
                workRecordTypeMap = workRecordTypeVOList.stream().collect(Collectors.toMap(WorkRecordTypeVO::getWorkRecordTypeValue, WorkRecordTypeVO::getWorkRecordTypeName));
            }
            for (WorkOrderRecordEsResult crmAgentWorkRecord : crmAgentWorkRecordList) {
                String workRecordTypeCode = crmAgentWorkRecord.getWorkRecordTypeCode();
                if (StringUtil.isEmpty(workRecordTypeCode)) {
                    crmAgentWorkRecord.setWorkRecordTypeName("No type");
                } else {
                    String typeName = workRecordTypeMap.get(workRecordTypeCode);
                    if (StringUtil.isEmpty(typeName)) {
                        crmAgentWorkRecord.setWorkRecordTypeName("No type");
                    } else {
                        crmAgentWorkRecord.setWorkRecordTypeName(typeName);
                    }
                }
            }
            //如果属性值为空，用如下方式进行分组，将为空的划分到无工单类型中
            Map<String, List<WorkOrderRecordEsResult>> collectMap = crmAgentWorkRecordList.stream()
                    .collect(Collectors.groupingBy(obj -> obj.getWorkRecordTypeName() != null ? obj.getWorkRecordTypeName() : "No type"));
            //您可以通过以下代码来实现Map<String, List<Object>>根据List数量进行排序，取值前五个，其他的数量单独汇总在一起的功能：
            Map<String, List<WorkOrderRecordEsResult>> topFiveMap = getTopFiveMap(collectMap); //这个返回的结果是无序的
            int entryCount = 0;
            BigDecimal sumPercent = BigDecimal.ZERO;
            for (Map.Entry<String, List<WorkOrderRecordEsResult>> innerEntry : topFiveMap.entrySet()) {
                String key = innerEntry.getKey();
                List<WorkOrderRecordEsResult> eachTypeList = innerEntry.getValue();
                int size = eachTypeList.size();
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("eachTypeCount", size);
                resultMap.put("typeName", key);
                //计算百分比，整数位
                double x = Double.parseDouble(String.valueOf(size));
                double y = Double.parseDouble(String.valueOf(totalCount));
                double percentage = x / y;
                //将小数保留四位并且不进行四舍五入
                DecimalFormat df = new DecimalFormat("#.####");
                df.setRoundingMode(RoundingMode.DOWN);
                String formatted4 = df.format(percentage);
                BigDecimal result = new BigDecimal(formatted4);
                if (entryCount == collectMap.size() - 1) {
                    //最后一个项需要用1减，从而保证所有的百分比相加是100%
                    result = new BigDecimal(1).subtract(sumPercent);
                }
                sumPercent = sumPercent.add(result);
                //每种类型所占百分比
                BigDecimal resultPercent = result.multiply(new BigDecimal(100));
                //保留两位小数
                BigDecimal bigDecimal = resultPercent.setScale(2, RoundingMode.DOWN);
                resultMap.put("eachTypePercent", bigDecimal);
                resultMapList.add(resultMap);
                entryCount = entryCount + 1;
            }
        }
        return AjaxResult.ok(resultMapList);
    }

    @Override
    public AjaxResult queryWorkOrderEffect() throws Exception {
        List<Map<String, Object>> resultMapList = new ArrayList<>();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            return AjaxResult.ok(resultMapList);
        }
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1);

        //工单状态条件
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery();
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 1));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 2));
        boolQueryBuilder.must(statusQueryBuilder);

        //渠道类型条件
//        boolQueryBuilder.must(QueryBuilders.existsQuery("channel_type_id")); //isNotNull
//        boolQueryBuilder.mustNot(QueryBuilders.termQuery("channel_type_id", "")); //ne

        searchSourceBuilder.query(boolQueryBuilder);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        List<WorkOrderRecordEsResult> crmAgentWorkRecordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult result = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, result);
                crmAgentWorkRecordList.add(result);
            }
        }

        if (CollectionUtils.isNotEmpty(crmAgentWorkRecordList)) {
            //用分钟进行比较
            long oneDayTime = 60 * 24;
            long twoDayTime = 60 * 24 * 2;
            long threeDayTime = 60 * 24 * 3;
            long oneWeekTime = 60 * 24 * 7;
            LocalDateTime currentTime = LocalDateTime.now();
            List<WorkOrderEffectVo> workOrderEffectVoList = new ArrayList<>();
            for (WorkOrderRecordEsResult work : crmAgentWorkRecordList) {
                //先判断工单状态，再分组求和（计算百分比）将状态结果临时赋值一个状态，便于分组
                LocalDateTime terminationDateTime = work.getShouldResolveTime();
                // 当前时间大于终止时间>>>超时 或 当前时间等于终止时间>>>超时
                if (currentTime.isAfter(terminationDateTime) || currentTime.isEqual(terminationDateTime)) {
                    //超时的条件下，来判断超时了多久，计算时间差，如下差值如果小于0表示没有超时
                    Duration duration = Duration.between(terminationDateTime, currentTime);
                    long minutes = duration.toMinutes(); // 获取分钟数
                    if (minutes > 0) {
                        //超时类型 1-超时24小时 2-超时48小时 3-超时72小时 4-超时一周
                        if (minutes > oneWeekTime) {
                            WorkOrderEffectVo workOrderEffectVo = new WorkOrderEffectVo();
                            workOrderEffectVo.setWorkRecordId(work.getWorkRecordId());
                            workOrderEffectVo.setTimeoutTypeCode(4);
                            workOrderEffectVo.setTimeoutTypeName(TransUtil.trans("超时一周未解决"));
                            workOrderEffectVoList.add(workOrderEffectVo);
                            continue;
                        }
                        if (minutes > threeDayTime) {
                            WorkOrderEffectVo workOrderEffectVo = new WorkOrderEffectVo();
                            workOrderEffectVo.setWorkRecordId(work.getWorkRecordId());
                            workOrderEffectVo.setTimeoutTypeCode(3);
                            workOrderEffectVo.setTimeoutTypeName(TransUtil.trans("超时72小时未解决"));
                            workOrderEffectVoList.add(workOrderEffectVo);
                            continue;
                        }
                        if (minutes > twoDayTime) {
                            WorkOrderEffectVo workOrderEffectVo = new WorkOrderEffectVo();
                            workOrderEffectVo.setWorkRecordId(work.getWorkRecordId());
                            workOrderEffectVo.setTimeoutTypeCode(2);
                            workOrderEffectVo.setTimeoutTypeName(TransUtil.trans("超时48小时未解决"));
                            workOrderEffectVoList.add(workOrderEffectVo);
                            continue;
                        }
                        if (minutes > oneDayTime) {
                            WorkOrderEffectVo workOrderEffectVo = new WorkOrderEffectVo();
                            workOrderEffectVo.setWorkRecordId(work.getWorkRecordId());
                            workOrderEffectVo.setTimeoutTypeCode(1);
                            workOrderEffectVo.setTimeoutTypeName(TransUtil.trans("超时24小时未解决"));
                            workOrderEffectVoList.add(workOrderEffectVo);
                        } else {
                            WorkOrderEffectVo workOrderEffectVo = new WorkOrderEffectVo();
                            workOrderEffectVo.setWorkRecordId(work.getWorkRecordId());
                            workOrderEffectVo.setTimeoutTypeCode(5);
                            workOrderEffectVo.setTimeoutTypeName(TransUtil.trans("超时24小时内未解决"));
                            workOrderEffectVoList.add(workOrderEffectVo);
                        }
                    }
                }
            }
            //汇总超时情况
            if (CollectionUtils.isNotEmpty(workOrderEffectVoList)) {
                Map<String, List<WorkOrderEffectVo>> collectMap = workOrderEffectVoList.stream().collect(Collectors.groupingBy(WorkOrderEffectVo::getTimeoutTypeName));

                int entryCount = 0;
                BigDecimal sumPercent = BigDecimal.ZERO;
                for (Map.Entry<String, List<WorkOrderEffectVo>> innerEntry : collectMap.entrySet()) {
                    String key = innerEntry.getKey();
                    List<WorkOrderEffectVo> eachEffectList = innerEntry.getValue();
                    int size = eachEffectList.size();
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("eachEffectCount", size);
                    resultMap.put("effectName", key);
                    //计算百分比，整数位
                    double x = Double.parseDouble(String.valueOf(size));
                    double y = Double.parseDouble(String.valueOf(totalCount));
                    double percentage = x / y;
                    //将小数保留四位并且不进行四舍五入
                    DecimalFormat df = new DecimalFormat("#.####");
                    df.setRoundingMode(RoundingMode.DOWN);
                    String formatted4 = df.format(percentage);
                    BigDecimal result = new BigDecimal(formatted4);
                    if (entryCount == collectMap.size() - 1) {
                        //最后一个项需要用1减，从而保证所有的百分比相加是100%
                        result = new BigDecimal(1).subtract(sumPercent);
                    }
                    sumPercent = sumPercent.add(result);
                    //每种类型所占百分比
                    BigDecimal resultPercent = result.multiply(new BigDecimal(100));
                    //保留两位小数
                    BigDecimal bigDecimal = resultPercent.setScale(2, RoundingMode.DOWN);
                    //保留两位小数
                    resultMap.put("eachEffectPercent", bigDecimal);
                    resultMapList.add(resultMap);
                    entryCount = entryCount + 1;
                }
            }
        }
        return AjaxResult.ok(resultMapList);
    }

    @Override
    public IPage<WorkOrderRecordFinalEsResult> queryPendingWorkOrder(IPage<Object> page) throws Exception {
        List<WorkOrderRecordFinalEsResult> results = new ArrayList<>();
        //查询待处理的工单
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), 0L);
            resultPage.setRecords(results);
            return resultPage;
        }
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1);
        //工单状态条件
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery();
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 1));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 2));
        boolQueryBuilder.must(statusQueryBuilder);
        //添加排序条件
        searchSourceBuilder.sort(SortBuilders.fieldSort("create_time").order(SortOrder.DESC));

        searchSourceBuilder.query(boolQueryBuilder);

        //计算当前页的起始下标
        long start = (page.getCurrent() - 1) * page.getSize();
        // 分页查询
        searchSourceBuilder.from((int) start).size((int) page.getSize());
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordFinalEsResult workOrderRecordFinalEsResult = new WorkOrderRecordFinalEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, workOrderRecordFinalEsResult);
                results.add(workOrderRecordFinalEsResult);
            }
            // 计算工单时间 根据状态来判断
            ticketDataHandle(results, 1);
            IPage<WorkOrderRecordFinalEsResult> pageResult = new Page<>(page.getCurrent(), page.getSize(), totalCount);
            pageResult.setRecords(results);
            return pageResult;
        }
        IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), 0L);
        resultPage.setRecords(results);
        return resultPage;
    }

    @Override
    public IPage<WorkOrderRecordFinalEsResult> queryWorkOrderToBeAllocated(IPage<Object> page) throws Exception {
        //查询待分配的工单
        List<WorkOrderRecordFinalEsResult> results = new ArrayList<>();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), 0L);
            resultPage.setRecords(results);
            return resultPage;
        }
        SearchRequest searchRequest = new SearchRequest(indexName);
        //待分配
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(0);
        //工单状态条件
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery();
        statusQueryBuilder.must(QueryBuilders.termQuery("status", 0));
        boolQueryBuilder.must(statusQueryBuilder);
        //添加排序条件
        searchSourceBuilder.sort(SortBuilders.fieldSort("create_time").order(SortOrder.DESC));

        searchSourceBuilder.query(boolQueryBuilder);

        //计算当前页的起始下标
        long start = (page.getCurrent() - 1) * page.getSize();
        // 分页查询
        searchSourceBuilder.from((int) start).size((int) page.getSize());
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);

                WorkOrderRecordFinalEsResult workOrderRecordFinalEsResult = new WorkOrderRecordFinalEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, workOrderRecordFinalEsResult);
                results.add(workOrderRecordFinalEsResult);
            }
            // 计算工单时间 根据状态来判断
            ticketDataHandle(results, 1);
            IPage<WorkOrderRecordFinalEsResult> pageResult = new Page<>(page.getCurrent(), page.getSize(), totalCount);
            pageResult.setRecords(results);
            return pageResult;
        }
        IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), 0L);
        resultPage.setRecords(results);
        return resultPage;
    }

    @Override
    public AjaxResult queryWorkOrderOverstock() throws Exception {
        List<Map<String, Object>> resultMapList = new ArrayList<>();
        //查询未解决的数量
        //如果是按照未解决数量排序，那么就是筛选状态为1或者2的
        //如果是按照占比排序，那么筛选完了状态为1或者2的，还要筛选所有的，作为分母计算占比
        //先查询出所有数据，然后用lambda处理筛选
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            return AjaxResult.ok(resultMapList);
        }
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1);
        //排除座席名字为null的，因为这类是未分配的工单（查询agent_name字段不为null，并且不为空字符串的数据）
        // 添加 agent_name 不为空且不为空字符串的条件
//        boolQueryBuilder.must(QueryBuilders.existsQuery("agent_name")); // isNotNull
//        boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_name", "")); // ne("")

        //
        searchSourceBuilder.query(boolQueryBuilder);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        if (totalCount == 0) {
            return AjaxResult.ok(resultMapList);
        }
        List<WorkOrderRecordEsResult> crmAgentWorkRecordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult result = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, result);
                crmAgentWorkRecordList.add(result);
            }
        }
        Map<String, List<WorkOrderRecordEsResult>> collectMap = crmAgentWorkRecordList.stream()
                .collect(Collectors.groupingBy(obj -> obj.getAgentName() != null ? obj.getAgentName() : STATUS_OTHERS_EN));
        for (Map.Entry<String, List<WorkOrderRecordEsResult>> innerEntry : collectMap.entrySet()) {
            String key = innerEntry.getKey();
            //将如下结果进行汇总，筛选未解决的（即status为1或者是2的）
            List<WorkOrderRecordEsResult> recordList = innerEntry.getValue();
            long unResolveCount = recordList.stream().map(WorkOrderRecordEsResult::getStatus).filter(status -> 1 == status || 2 == status).count();
            Integer allStatusCount = recordList.size();
            //计算未解决数量占比
            //计算百分比，整数位
            BigDecimal x = new BigDecimal(unResolveCount);
            BigDecimal y = new BigDecimal(allStatusCount);
            BigDecimal percentage = x.divide(y, 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal resultPercent = percentage.multiply(new BigDecimal(100));
            BigDecimal bigDecimal = resultPercent.setScale(0, RoundingMode.DOWN);
            Map<String, Object> eachMap = new HashMap<>();
            eachMap.put("agentName", key);
            eachMap.put("unResolveCount", unResolveCount);
            eachMap.put("unResolvePercent", bigDecimal);
            resultMapList.add(eachMap);
        }
        return AjaxResult.ok(resultMapList);

    }

    @Override
    public AjaxResult queryRobotWorkOrder(RobotWorkOrderVo robotWorkOrderVo) throws Exception {
        List<RobotWorkOrderResult> resultList = new ArrayList<>();
        Integer timeRangeCode = robotWorkOrderVo.getTimeRangeCode();
        Map<String, String> commonConditionMap = getQueryCommonCondition(timeRangeCode);
        String startTime = commonConditionMap.get("startTime");
        String endTime = commonConditionMap.get("endTime");
        String companyId = SecurityUtil.getLoginUser().getCompanyId();

        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            return AjaxResult.ok(resultList, MessageUtils.get("operate.success"));
        }

        //查询渠道类型 (这个是数据库的初始化表，一般不会为空)
        List<ChannelDefVo> channelDefVoList = new ArrayList<>();
        R<List<ChannelDefVo>> listR = channelClient.innerChannelType();
        if (AjaxResult.SUCCESS == listR.getCode()) {
            channelDefVoList = listR.getData();
        }
        //数据中存了渠道类型名，为了避免定义表的命名发生变化，还是用定义表查询出来的渠道类型名，为后续数据处理做准备
        Map<String, String> channelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(channelDefVoList)) {
            // 使用Collectors.toMap处理重复的code
            channelMap = channelDefVoList.stream()
                    .collect(Collectors.toMap(ChannelDefVo::getCode, ChannelDefVo::getName, (name1, name2) -> name1));
        }

        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 添加公共查询条件
//        boolQueryBuilder.must(QueryBuilders.termQuery("data_status", Constants.NORMAL));
//        boolQueryBuilder.must(QueryBuilders.termQuery("company_id", companyId));
        boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", "1001"));
        // 时间范围条件
        if (timeRangeCode != 5) {
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startTime).lte(endTime));
            }
        }
        //渠道类型条件
//        boolQueryBuilder.must(QueryBuilders.existsQuery("channel_type_id")); //isNotNull
//        boolQueryBuilder.mustNot(QueryBuilders.termQuery("channel_type_id", "")); //ne


        searchSourceBuilder.query(boolQueryBuilder);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        List<WorkOrderRecordEsResult> crmAgentWorkRecordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult result = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, result);
                crmAgentWorkRecordList.add(result);
            }
            //将上述内容进行分组，汇总各个渠道的数量
//            Map<String, Long> countMap = crmAgentWorkRecordList.stream()
//                    .filter(record -> record != null && record.getChannelTypeId() != null) // 过滤空对象和空code
//                    .collect(Collectors.groupingBy(WorkOrderRecordEsResult::getChannelTypeId, Collectors.counting()));
            Map<String, Integer> countMap = crmAgentWorkRecordList.stream()
                    .filter(record -> record != null && record.getChannelTypeId() != null)
                    .collect(Collectors.groupingBy(WorkOrderRecordEsResult::getChannelTypeId, Collectors.summingInt(x -> 1)));

            for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
                String channelTypeId = entry.getKey();
                Integer count = entry.getValue();

//                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                RobotWorkOrderResult robotWorkOrderResult = new RobotWorkOrderResult();
                robotWorkOrderResult
                        .setChannelTypeId(Integer.parseInt(entry.getKey()))
                        .setChannelTypeName(TransUtil.trans(channelMap.get(channelTypeId)))
                        .setTotalCount(count)
                        .setIcon(CrmChannelTypeIconEnum.getIconByCode(robotWorkOrderResult.getChannelTypeId()));
                resultList.add(robotWorkOrderResult);
            }
            return AjaxResult.ok(resultList, MessageUtils.get("operate.success"));
        }
        return AjaxResult.ok(resultList, MessageUtils.get("operate.success"));
    }


    //组装从es查询数据的公共条件 （待分配的工单是没有座席和部门的）
    //如果工单状态是待分配，那么agent_id和dept_id是为空的，需要加上这个判断
    //workOrderStatus 此处给这个值的定位：0-待分配 1-其他状态
    //首页那些板块，除了「机器人工单」那个板块，其他的都是统计非机器人工单的，所以下边这公共筛选条件，都需要排除1001（1001是机器人工单的代号）
    private BoolQueryBuilder packageCommonSearchBuilder(Integer workOrderStatus) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 添加公共查询条件
//        boolQueryBuilder.must(QueryBuilders.termQuery("data_status", Constants.NORMAL));

        SysUserVo loginUser = SecurityUtil.getLoginUser();
        String roleId = loginUser.getRoleList().get(0).getRoleId();
        String companyId = loginUser.getCompanyId();

        //公用条件：都需要加上如下公司筛选条件
//        boolQueryBuilder.must(QueryBuilders.termQuery("company_id", companyId));
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            // 管理员：companyId
//            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
            // 座席管理员：deptId
            String deptId = loginUser.getDeptId();
            if (workOrderStatus == 1) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
            }
//            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
        } else if (UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            // 座席：userId
            String userId = loginUser.getUserId();
//            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));
            if (workOrderStatus == 1) {
                boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", userId));
            }
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
        }
        return boolQueryBuilder;
    }

    //如下是临时版本（后续有了真实数据可能还需要用getChannelData1）
    private Object getChannelData2(List<ChannelWorkOrderVo> channelWorkOrderVoList, String roleId) {
        List<Map<String, Object>> channelData1 = getChannelData1(channelWorkOrderVoList);
        //如果需要展示某些渠道，就往如下targetKeyList中追加
        //没对接的内容，先不用出现在如下内容中，比如：Twitter、Telegram、Google Play
        List<Integer> targetKeyList = Stream.of(
                CrmChannelEnum.EMAIL.getCode(),
                CrmChannelEnum.WHATSAPP.getCode(),
                CrmChannelEnum.PHONE.getCode(),
                CrmChannelEnum.WEB_CHAT.getCode(),
                CrmChannelEnum.APP_CHAT.getCode(),
                CrmChannelEnum.AMAZON.getCode(),
                CrmChannelEnum.FACEBOOK.getCode(),
                CrmChannelEnum.INSTAGRAM.getCode(),
                CrmChannelEnum.LINE.getCode(),
                CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getCode(),
                CrmChannelEnum.WECHAT_OFFICIAL_ACCOUNT.getCode(),
                CrmChannelEnum.WEB_VIDEO.getCode(),
                CrmChannelEnum.APP_VIDEO.getCode(),
                CrmChannelEnum.WEB_ONLINE_VOICE.getCode(),
                CrmChannelEnum.APP_ONLINE_VOICE.getCode(),
                CrmChannelEnum.SHOPIFY.getCode(),
                CrmChannelEnum.WECHAT_MINI_PROGRAM.getCode(),
                CrmChannelEnum.TIKTOK_SHOP.getCode(),
                CrmChannelEnum.DISCORD.getCode()
        ).collect(Collectors.toList());
        Set<String> setTmp = new HashSet<>();
        if (CollectionUtils.isEmpty(channelData1)) {
            //如果是管理员和座席管理员，还需要考虑待分配状态 待分配
            JSONArray jsonArr;
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId) || UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                //处理中、已解决、已超时、待分配
                jsonArr = getAllChannelResolveStatusFillZeroJson(roleId, targetKeyList);
            } else {
                //处理中、已解决、已超时
                jsonArr = getAllChannelResolveStatusFillZeroJson(roleId, targetKeyList);
            }
            return jsonArr;
        } else {
            String jsonStr = JSONUtil.toJsonStr(channelData1);
            JSONArray jsonArray = JSONArray.parseArray(jsonStr);
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId) || UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                setTmp.add(TicketStatusEnum.STATUS_TIMEOUT.getCode());
                setTmp.add(TicketStatusEnum.STATUS_RESOLVED.getCode());
                setTmp.add(TicketStatusEnum.STATUS_PROCESSING.getCode());
                setTmp.add(TicketStatusEnum.STATUS_PENDING.getCode());
            } else {
                setTmp.add(TicketStatusEnum.STATUS_TIMEOUT.getCode());
                setTmp.add(TicketStatusEnum.STATUS_RESOLVED.getCode());
                setTmp.add(TicketStatusEnum.STATUS_PROCESSING.getCode());
            }
            // 使用 Map 来存储渠道类型和数据标志
            Map<Integer, Integer> channelFlags = new HashMap<>();
            for (Integer eachChannelTypeId : targetKeyList) {
                channelFlags.put(eachChannelTypeId, 0);
            }

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject eachJSONObject = jsonArray.getJSONObject(i);
                String channelTypeCode = eachJSONObject.getString("channelTypeCode");
                Integer channelTypeId = Integer.parseInt(channelTypeCode);
                eachJSONObject.put("icon", CrmChannelTypeIconEnum.getIconByCode(channelTypeId));

                Integer getResult = channelFlags.get(channelTypeId);
                if (getResult != null) {
                    channelFlags.put(channelTypeId, 1);
                    handleDiffChannelTypeData(eachJSONObject, setTmp);
                }
            }

            // 遍历所有渠道类型，如果数据标志为 0，则添加填充数据
            for (Integer eachChannelTypeId : targetKeyList) {
                if (channelFlags.get(eachChannelTypeId) == 0) {
                    String json = getDiffChannelWorkOrderFillZeroResult(eachChannelTypeId, TransUtil.trans(CrmChannelEnum.getNameByCode(eachChannelTypeId)), roleId);
                    JSONObject jsonObject = JSONObject.parseObject(json);
                    jsonArray.add(jsonObject);
                }
            }

            return jsonArray;
        }
    }

    //渠道工单：之前的版本
    private List<Map<String, Object>> getChannelData1(List<ChannelWorkOrderVo> channelWorkOrderVoList) {
        List<Map<String, Object>> doubleMapResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelWorkOrderVoList)) {
            //多条件分组（用国际化之后的channelTypeName）
            Map<String, Map<String, List<ChannelWorkOrderVo>>> doubleMap = channelWorkOrderVoList.stream()
//                    .collect(Collectors.groupingBy(ChannelWorkOrderVo::getChannelTypeName,
                    .collect(Collectors.groupingBy(p -> TransUtil.trans(p.getChannelTypeName()) + "#" + p.getChannelTypeId(),
                            Collectors.groupingBy(ChannelWorkOrderVo::getResolveStatusEn)));
            for (Map.Entry<String, Map<String, List<ChannelWorkOrderVo>>> outerEntry : doubleMap.entrySet()) {
//                String channelNameKey = outerEntry.getKey();
                String channelUnionKey = outerEntry.getKey();
                String[] split = channelUnionKey.split("#");
                String channelTypeName = split[0];
                String channelTypeId = split[1];
                Map<String, List<ChannelWorkOrderVo>> eachChannelMap = outerEntry.getValue();
                //如果您有一个 Map<String, List<Object>> 类型的对象，您可以通过以下方式计算其所有列表对象的总数（如下totalCount是指定渠道下所有）
                int totalCount = eachChannelMap.values().stream().mapToInt(List::size).sum();
                Map<String, Object> doubleMapResult = new HashMap<>();
                List<Map<String, Object>> resultMapList = new ArrayList<>();
                int innerEntryCount = 0;
                BigDecimal sumPercent = BigDecimal.ZERO;
                for (Map.Entry<String, List<ChannelWorkOrderVo>> innerEntry : eachChannelMap.entrySet()) {
                    String key = innerEntry.getKey();
                    List<ChannelWorkOrderVo> eachStatusWorkOrderList = innerEntry.getValue();
                    int size = eachStatusWorkOrderList.size();
                    Map<String, Object> resultMap = new HashMap<>();
                    //工单状态
                    resultMap.put("resolveStatusEn", key);
                    resultMap.put("resolveStatus", MessageUtils.get(TicketStatusEnum.getNameByCode(key)));
                    //每种工单状态个数
                    resultMap.put("eachStatusCount", size);
                    //计算百分比，整数位
                    BigDecimal x = new BigDecimal(size);
                    BigDecimal y = new BigDecimal(totalCount);
                    BigDecimal percentage = x.divide(y, 2, BigDecimal.ROUND_HALF_UP);
                    //将小数保留两位并且不进行四舍五入
                    DecimalFormat df = new DecimalFormat("#.##");
                    df.setRoundingMode(RoundingMode.DOWN);
                    String formatted4 = df.format(percentage);
                    BigDecimal result = new BigDecimal(formatted4);
                    if (innerEntryCount == eachChannelMap.size() - 1) {
                        //最后一个项需要用1减，从而保证所有的百分比相加是100%
                        result = new BigDecimal(1).subtract(sumPercent);
                    }
                    sumPercent = sumPercent.add(result);
                    //每种类型所占百分比
                    BigDecimal resultPercent = result.multiply(new BigDecimal(100));
                    //保留0位小数
                    BigDecimal bigDecimal = resultPercent.setScale(0, RoundingMode.DOWN);
                    resultMap.put("eachStatusPercent", bigDecimal);
                    resultMapList.add(resultMap);
                    innerEntryCount = innerEntryCount + 1;
                }
                doubleMapResult.put("eachChannelTotalCount", totalCount + "");
                doubleMapResult.put("channelResultMapList", resultMapList);
                doubleMapResult.put("channelTypeName", channelTypeName);
                //返回渠道类型代码，用于前端页面图标展示
                doubleMapResult.put("channelTypeCode", channelTypeId);
                doubleMapResultList.add(doubleMapResult);
            }
        }
        if (CollectionUtils.isNotEmpty(doubleMapResultList)) {
            // 调用排序方法，传入要比较的数据量的key
            // 排序后的doubleMapResultList即为按照指定数据量从大到小排序的结果
            sortByMapValue(doubleMapResultList, "eachChannelTotalCount");
        }
        return doubleMapResultList;
    }

    //所有渠道数据，以及状态填充，数据量为0
    private JSONArray getAllChannelResolveStatusFillZeroJson(String roleId, List<Integer> targetKeyList) {
        JSONArray jsonArray = new JSONArray();
        for (Integer eachChannelCode : targetKeyList) {
            String json = getDiffChannelWorkOrderFillZeroResult(eachChannelCode, TransUtil.trans(CrmChannelEnum.getNameByCode(eachChannelCode)), roleId);
            JSONObject jsonObject = JSONObject.parseObject(json);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    private void handleDiffChannelTypeData(JSONObject eachJSONObject, Set<String> setTmp) {
        //处理中
        String resolveStatus1 = JSON.toJSONString(getDiffChannelWorkOrderFillZeroStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_PROCESSING_EN)),STATUS_PROCESSING_EN));
        //已解决
        String resolveStatus2 = JSON.toJSONString(getDiffChannelWorkOrderFillZeroStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_RESOLVED_EN)),STATUS_RESOLVED_EN));
        //已超时
        String resolveStatus3 = JSON.toJSONString(getDiffChannelWorkOrderFillZeroStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_TIMEOUT_EN)),STATUS_TIMEOUT_EN));
        //待分配
        String resolveStatus4 = JSON.toJSONString(getDiffChannelWorkOrderFillZeroStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_PENDING_EN)),STATUS_PENDING_EN));

        JSONArray jsonArr = eachJSONObject.getJSONArray("channelResultMapList");
        Set<String> set = new HashSet<>();
        set.addAll(setTmp);
        Set<Object> resolveStatus = jsonArr.stream().map(vo -> ((JSONObject) vo).get("resolveStatusEn")).collect(Collectors.toSet());
        set.removeAll(resolveStatus);
        if (CollectionUtils.isNotEmpty(set)) {
            for (String str : set) {
                if (str.equals(TicketStatusEnum.STATUS_PROCESSING.getCode())) {
                    JSONObject jsonObject = JSONObject.parseObject(resolveStatus1);
                    jsonArr.add(jsonObject);
                }
                if (str.equals(TicketStatusEnum.STATUS_RESOLVED.getCode())) {
                    JSONObject jsonObject = JSONObject.parseObject(resolveStatus2);
                    jsonArr.add(jsonObject);
                }
                if (str.equals(TicketStatusEnum.STATUS_TIMEOUT.getCode())) {
                    JSONObject jsonObject = JSONObject.parseObject(resolveStatus3);
                    jsonArr.add(jsonObject);
                }
                if (str.equals(TicketStatusEnum.STATUS_PENDING.getCode())) {
                    JSONObject jsonObject = JSONObject.parseObject(resolveStatus4);
                    jsonArr.add(jsonObject);
                }
            }
        }
    }

    //状态填充，数据量为0
    private ChannelWorkOrderStatusVo getDiffChannelWorkOrderFillZeroStatus(String resolveStatus,String resolveStatusEn) {
        ChannelWorkOrderStatusVo build = ChannelWorkOrderStatusVo.builder()
                .resolveStatus(resolveStatus)
                .resolveStatusEn(resolveStatusEn)
                .eachStatusCount(0)
                .eachStatusPercent(0).build();
        return build;
    }

    //指定渠道数据，以及状态填充，数据量为0
    private String getDiffChannelWorkOrderFillZeroResult(Integer channelTypeCode, String channelTypeName, String roleId) {
        List<ChannelWorkOrderStatusVo> channelResultMapList = new ArrayList<>();
        ChannelWorkOrderStatusVo build1 = getDiffChannelWorkOrderFillZeroStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_TIMEOUT_EN)),STATUS_TIMEOUT_EN);
        ChannelWorkOrderStatusVo build2 = getDiffChannelWorkOrderFillZeroStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_RESOLVED_EN)),STATUS_RESOLVED_EN);
        ChannelWorkOrderStatusVo build3 = getDiffChannelWorkOrderFillZeroStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_PROCESSING_EN)),STATUS_PROCESSING_EN);
        channelResultMapList.add(build1);
        channelResultMapList.add(build2);
        channelResultMapList.add(build3);

        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId) || UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
            ChannelWorkOrderStatusVo build4 = getDiffChannelWorkOrderFillZeroStatus(MessageUtils.get(TicketStatusEnum.getNameByCode(STATUS_PENDING_EN)),STATUS_PENDING_EN);
            channelResultMapList.add(build4);
        }
        ChannelTypeVo build = ChannelTypeVo.builder()
                .channelTypeCode(channelTypeCode)
                .channelTypeName(channelTypeName)
                .channelResultMapList(channelResultMapList)
                .icon(CrmChannelTypeIconEnum.getIconByCode(channelTypeCode))
                .eachChannelTotalCount(0).build();
        return JSON.toJSONString(build);
    }

    private void sortByMapValue(List<Map<String, Object>> list, final String key) {
        Collections.sort(list, (o1, o2) -> {
            Comparable value1 = (Comparable) o1.get(key);
            Comparable value2 = (Comparable) o2.get(key);
            return value2.compareTo(value1);
        });
    }

    private Map<String, String> getQueryCommonCondition(Integer timeRangeCode) {
        Map<String, String> resultMap = new HashMap<>();
        SysUserVo loginUser = SecurityUtil.getLoginUser();
        SysRoleVo sysRoleVo = loginUser.getRoleList().get(0);
        String roleId = sysRoleVo.getRoleId();
        if (timeRangeCode == null) {
            resultMap.put("roleId", roleId);
            return resultMap;
        }
        //1-今日 2-昨日 3-近48小时 4-近7日 5-全部
        //根据入参，获取不同的时间作为查询条件
        String startTime = null;
        String endTime = null;
        //tips:计算时间不用前端给后台传的时间了，直接用原来的代码，用服务器时间
        if (timeRangeCode == 1) {
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formatDate = sdf.format(date);
            startTime = formatDate + " 00:00:00";
            endTime = formatDate + " 23:59:59";
        } else if (timeRangeCode == 2) {
            Date date = new Date();
            Date dateResult = DateUtils.addDays(date, -1);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            startTime = sdf.format(dateResult) + " 00:00:00";
            endTime = sdf.format(dateResult) + " 23:59:59";
        } else if (timeRangeCode == 3) {
            Date date = new Date();
            Date dateResult = DateUtils.addHours(date, -47);
//            Date dateResult = DateUtils.addDays(date, -2);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH");
            startTime = sdf.format(dateResult) + ":00:00";
            endTime = sdf.format(date) + ":59:59";
        } else if (timeRangeCode == 4) {
            Date date = new Date();
            Date dateResult = DateUtils.addDays(date, -6);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            startTime = sdf.format(dateResult) + " 00:00:00";
            endTime = sdf.format(date) + " 23:59:59";
        }
        resultMap.put("roleId", roleId);
        resultMap.put("startTime", startTime);
        resultMap.put("endTime", endTime);
        resultMap.put("companyId", loginUser.getCompanyId());
        return resultMap;
    }

    /**
     * 验证索引是否存在
     *
     * @param index 索引名称
     * @return 存在状态
     * @throws IOException
     */
    private boolean headIndexExists(String index) {
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        } catch (Exception e) {
            log.error("验证索引失败:", e);
        }
        return false;
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        } catch (Exception e) {
            log.error("创建索引报错", e);
        }
    }

    // 取前五个值，汇总其他值到一个List中，即top5+others
    private Map<String, List<WorkOrderRecordEsResult>> getTopFiveMap(Map<String, List<WorkOrderRecordEsResult>> map) {
        Map<String, List<WorkOrderRecordEsResult>> sortMap = sortByListSize(map);
        Map<String, List<WorkOrderRecordEsResult>> top5AndOthers = new HashMap<>();
        List<WorkOrderRecordEsResult> otherValues = new ArrayList<>();
        int count = 0;

        for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : sortMap.entrySet()) {
            count++;
            if (count <= 5) {
                top5AndOthers.put(entry.getKey(), entry.getValue());
            } else {
                otherValues.addAll(entry.getValue());
            }
        }
        if (CollectionUtils.isNotEmpty(otherValues)) {
            top5AndOthers.put(STATUS_OTHERS_EN, otherValues);
        }
        return top5AndOthers;
    }

    // 根据List数量进行排序
    private Map<String, List<WorkOrderRecordEsResult>> sortByListSize(Map<String, List<WorkOrderRecordEsResult>> map) {
        List<Map.Entry<String, List<WorkOrderRecordEsResult>>> list = new ArrayList<>(map.entrySet());
        // 降序排序
        list.sort((o1, o2) -> o2.getValue().size() - o1.getValue().size());
        Map<String, List<WorkOrderRecordEsResult>> result = new LinkedHashMap<>();
        for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : list) {
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }

    /**
     * 将查询到的工单,进行数据处理
     *
     * @param list      工单数据
     * @param queryType 查询类型 1 界面查询  2 导出查询
     * @return 拼接好的工单数据
     */
    private void ticketDataHandle(List<WorkOrderRecordFinalEsResult> list, Integer queryType) {
        // 计算工单时间 根据状态来判断
        for (WorkOrderRecordFinalEsResult work : list) {
            LocalDateTime currentTime = LocalDateTime.now();
//            ZoneId zoneId = ZoneId.systemDefault();
//            LocalDateTime terminationDateTime = LocalDateTime.ofInstant(work.getShouldResolveTime().toInstant(), zoneId);
            LocalDateTime terminationDateTime = work.getShouldResolveTime();
            LocalDateTime resolveTime = work.getResolveTime();
            // 如果是未解决状态,判断时间
            if (work.getStatus() == 0 || work.getStatus() == 1 || work.getStatus() == 2) {
                // 终止时间小于当前时间>>>超时 或 终止时间等于当前时间>>>超时
                if (terminationDateTime.isBefore(currentTime) || terminationDateTime.isEqual(currentTime)) {
                    String computingTime = computingTime(currentTime, terminationDateTime);
                    if (queryType == 1) {
                        work.setServiceObjectives("<span>" + TransUtil.trans("超出") + "<span class=\"serviceObjectivesText1\">" + computingTime + "</span>" + TransUtil.trans("未解决") + "</span>");
                    } else {
                        work.setServiceObjectives(TransUtil.trans("超出") + computingTime + TransUtil.trans("未解决"));
                    }
                    work.setServiceStatus(1);
                }

                // 终止时间大于当前时间>>>未超时
                if (terminationDateTime.isAfter(currentTime)) {
                    String computingTime = computingTime(terminationDateTime, currentTime);
                    if (queryType == 1) {
                        work.setServiceObjectives("<span>" + TransUtil.trans("需要") + "<span class=\"serviceObjectivesText2\">" + computingTime + "</span>" + TransUtil.trans("内解决") + "</span>");
                    } else {
                        work.setServiceObjectives(TransUtil.trans("需要") + computingTime + TransUtil.trans("内解决"));
                    }
                    work.setServiceStatus(2);
                }
            }
            if (work.getStatus() == 3) {
                // 判断是否超时
                if (terminationDateTime.isBefore(resolveTime) || resolveTime.isEqual(terminationDateTime)) {
                    if (queryType == 1) {
                        work.setServiceObjectives("<span class=\"serviceObjectivesText4\">" + TransUtil.trans("超时解决") + "</span>");
                    } else {
                        work.setServiceObjectives(TransUtil.trans("超时解决"));
                    }
                    work.setServiceStatus(3);
                } else {
                    if (queryType == 1) {
                        work.setServiceObjectives("<span class=\"serviceObjectivesText2\">" + TransUtil.trans("按时解决") + "</span>");
                    } else {
                        work.setServiceObjectives(TransUtil.trans("按时解决"));
                    }
                    work.setServiceStatus(4);
                }
            }
            if (work.getStatus() == 4) {
                if (queryType == 1) {
                    work.setServiceObjectives("<span>--</span>");
                } else {
                    work.setServiceObjectives("--");
                }
                work.setServiceStatus(5);
            }

            if (work.getStatus() == 5) {
                if (queryType == 1) {
                    work.setServiceObjectives("<span class=\"serviceObjectivesText2\">" + TransUtil.trans("已转单") + "</span>");
                } else {
                    work.setServiceObjectives(TransUtil.trans("已转单"));
                }
                work.setServiceStatus(6);
            }

            if (StringUtil.isNotEmpty(work.getAgentId()) && work.getAgentId().equals("1001")) {
                if (queryType == 1) {
                    work.setServiceObjectives("<span class=\"serviceObjectivesText2\">" + MessageUtils.get("work.automatic.solve") + "</span>");
                } else {
                    work.setServiceObjectives(MessageUtils.get("work.automatic.solve"));
                }
            }
            // 根据名称查询出 对应的国际化
            work.setChannelTypeName(TransUtil.trans(work.getChannelTypeName()));
        }
    }

    /**
     * 计算开始时间于结束时间相差时间
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 相差时间
     */
    private String computingTime(LocalDateTime startTime, LocalDateTime endTime) {

        LocalDateTime tempDateTime = LocalDateTime.from(startTime);

        long days = tempDateTime.until(endTime, ChronoUnit.DAYS);
        tempDateTime = tempDateTime.plusDays(days);


        long hours = tempDateTime.until(endTime, ChronoUnit.HOURS);
        tempDateTime = tempDateTime.plusHours(hours);

        long minutes = tempDateTime.until(endTime, ChronoUnit.MINUTES);
        tempDateTime = tempDateTime.plusMinutes(minutes);

        long seconds = tempDateTime.until(endTime, ChronoUnit.SECONDS);
        String dateTime = " " + days + " " + TransUtil.trans("天") + " " +
                hours + " " + TransUtil.trans("小时") + " ";
        if (days == 0) {
            dateTime = " " + hours + " " + TransUtil.trans("小时") + " ";
        }


        return dateTime.replace("-", "");
    }


}
