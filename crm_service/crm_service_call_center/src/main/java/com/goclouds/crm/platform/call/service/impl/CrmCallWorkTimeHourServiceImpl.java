package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmCallWorkTimeHour;
import com.goclouds.crm.platform.call.mapper.CrmCallWorkTimeHourMapper;
import com.goclouds.crm.platform.call.service.CrmCallWorkTimeHourService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_call_work_time_hour(工作日时间配置)】的数据库操作Service实现
* @createDate 2025-02-07 16:58:25
*/
@Service
public class CrmCallWorkTimeHourServiceImpl extends ServiceImpl<CrmCallWorkTimeHourMapper, CrmCallWorkTimeHour>
    implements CrmCallWorkTimeHourService{

}




