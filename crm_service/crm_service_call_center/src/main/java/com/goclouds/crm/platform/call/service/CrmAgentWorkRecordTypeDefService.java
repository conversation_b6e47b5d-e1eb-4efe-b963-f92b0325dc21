package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordTypeDef;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordLevelVO;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordSlaDefVo;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordTypeVO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_type_def(工单类型定义表)】的数据库操作Service
* @createDate 2023-09-21 16:09:26
*/
public interface CrmAgentWorkRecordTypeDefService extends IService<CrmAgentWorkRecordTypeDef> {

    List<WorkRecordTypeVO> queryWorkRecordType();

    List<WorkRecordTypeVO> queryWorkRecordTypeByLanguage(String languageCode);

    AjaxResult updateWorkOrderType(List<WorkRecordTypeVO> workRecordTypeVOList,String languageCode);

    AjaxResult deleteWorkOrderType(String workRecordTypeId);

    CrmAgentWorkRecordTypeDef gatWorkRecordTypeInfo(String workRecordTypeId);
}
