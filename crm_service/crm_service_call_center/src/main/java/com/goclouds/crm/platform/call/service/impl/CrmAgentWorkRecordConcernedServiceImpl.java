package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordConcerned;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordOperationLog;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordConcernedMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordConcernedService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordOperationLogService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.TicketOperationTypeEnum;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.goclouds.crm.platform.utils.SecurityUtil.getUserId;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_concerned(工单关注关系表)】的数据库操作Service实现
* @createDate 2023-09-27 11:23:18
*/
@Service
@RequiredArgsConstructor
public class CrmAgentWorkRecordConcernedServiceImpl extends ServiceImpl<CrmAgentWorkRecordConcernedMapper, CrmAgentWorkRecordConcerned>
    implements CrmAgentWorkRecordConcernedService {
    @Autowired
    @Lazy
    private CrmAgentWorkRecordOperationLogService crmAgentWorkRecordOperationLogService;

    @Override
    public AjaxResult<Object> concernedWorkOrder(String workRecordId, Integer dataStatus) {
        // 添加工单操作日志
        String operationLogDescribe = null;
        Integer operationLogType = 0;
        // 如果dataStatus为1则是添加,为0则是删除
        if(dataStatus == 1){
            CrmAgentWorkRecordConcerned concerned = new CrmAgentWorkRecordConcerned();
            concerned.setConcernedId(UuidUtils.generateUuid())
                    .setWorkRecordId(workRecordId)
                    .setUserId(SecurityUtil.getUserId())
                    .setDataStatus(1)
                    .setCreator(SecurityUtil.getUserId())
                    .setCreateTime(new Date());
            this.baseMapper.insert(concerned);
            operationLogDescribe = SecurityUtil.getUsername()+"关注了此工单";
            operationLogType = TicketOperationTypeEnum.CONCERNED_TICKET.getCode();
        }else{
            CrmAgentWorkRecordConcerned recordConcerned = this.baseMapper.selectOne(new QueryWrapper<CrmAgentWorkRecordConcerned>().lambda()
                    .eq(CrmAgentWorkRecordConcerned::getWorkRecordId, workRecordId)
                    .eq(CrmAgentWorkRecordConcerned::getUserId, SecurityUtil.getUserId())
                    .eq(CrmAgentWorkRecordConcerned::getDataStatus,1));
            if(recordConcerned != null){
                recordConcerned.setDataStatus(0);
                this.baseMapper.updateById(recordConcerned);
            }
            operationLogDescribe = SecurityUtil.getUsername()+"取消关注了此工单";
            operationLogType = TicketOperationTypeEnum.UNFOLLOW_TICKET.getCode();
        }
        CrmAgentWorkRecordOperationLog crmAgentWorkRecordOperationLog = new CrmAgentWorkRecordOperationLog();
        crmAgentWorkRecordOperationLog.setOperationLogId(UuidUtils.generateUuid())
                .setWorkRecordId(workRecordId)
                .setOperationLogDescribe(operationLogDescribe)
                .setDataStatus(1).setOperatorName(SecurityUtil.getLoginUser().getUserName()).setCreator(getUserId()).setCreateTime(new Date()).setOperationLogType(operationLogType);
        crmAgentWorkRecordOperationLogService.save(crmAgentWorkRecordOperationLog);

        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }


    /**
     * 查询用户关注的工单信息
     * @param userId
     * @return
     */
    @Override
    public List<String> getConcernedWorkOrder(String userId) {
        LambdaQueryWrapper<CrmAgentWorkRecordConcerned> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CrmAgentWorkRecordConcerned::getUserId, userId);
        queryWrapper.eq(CrmAgentWorkRecordConcerned::getDataStatus, 1);
        List<CrmAgentWorkRecordConcerned> list = this.list(queryWrapper);
        return list.stream().map(CrmAgentWorkRecordConcerned::getWorkRecordId).collect(Collectors.toList());
    }
}




