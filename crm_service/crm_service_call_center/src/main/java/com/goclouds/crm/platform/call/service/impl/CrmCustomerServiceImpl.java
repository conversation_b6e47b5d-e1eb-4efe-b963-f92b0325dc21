package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmCustomer;
import com.goclouds.crm.platform.call.domain.SysUser;
import com.goclouds.crm.platform.call.mapper.CrmCustomerMapper;
import com.goclouds.crm.platform.call.service.CrmCustomerService;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysUserVo;
import com.goclouds.crm.platform.utils.SecurityUtil;
import org.springframework.stereotype.Service;

/**
 * 客户资料(CrmCustomer)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-23 18:57:58
 */
@Service("crmCustomerService")
public class CrmCustomerServiceImpl extends ServiceImpl<CrmCustomerMapper, CrmCustomer> implements CrmCustomerService {

    @Override
    public String existsCustomer(String customerContactInfo,String channelId){
        String customerId = null;
        if(StringUtils.isNotBlank(customerContactInfo)){
            customerId = this.getCustomerIdByCustomerKey(customerContactInfo, channelId);
        }
        return customerId;
    }

    /**
     * 通过客户key获取客户id
     * @param customerContactInfo 客户联系方式
     * @param channelId 渠道id
     * @return
     */
    private String getCustomerIdByCustomerKey(String customerContactInfo,String channelId) {
        SysUserVo loginUser = SecurityUtil.getLoginUser();

        // 邮件渠道的，用邮箱查
        if(CrmChannelEnum.EMAIL.getCode().equals(Integer.parseInt(channelId))){
            QueryWrapper<CrmCustomer> crmCustomerQueryWrapper = new QueryWrapper<>();
            crmCustomerQueryWrapper.eq("email_address", customerContactInfo);
            crmCustomerQueryWrapper.eq("data_status", 1);
            crmCustomerQueryWrapper.eq("company_id", loginUser.getCompanyId());
            CrmCustomer crmCustomer = this.baseMapper.selectOne(crmCustomerQueryWrapper);
            return crmCustomer == null ? null : crmCustomer.getCustomerId();
        }
        return null;
    }


}

