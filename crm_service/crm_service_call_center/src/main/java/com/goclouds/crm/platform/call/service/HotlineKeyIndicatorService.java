package com.goclouds.crm.platform.call.service;

import com.goclouds.crm.platform.call.domain.hotline.*;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysUserBaseInfoVo;

import java.util.List;

/**
 * @author: sunlinan
 * @description: 热线关键指标
 * @date: 2024-12-23 16:54
 **/
public interface HotlineKeyIndicatorService {
    AjaxResult saveHotlineRule(HotlineKeyIndicatorRule hotlineKeyIndicatorRule);

    HotlineKeyIndicatorRule queryHotlineRule();

    AjaxResult<InboundResult> queryInboundResult(HotlineRequest hotlineRequest);

    AjaxResult<ConnectionRateResult> queryConnectionRateResult(ConnectionRateRequest hotlineRequest);

    AjaxResult<TotalCallsResult> queryTotalCallsResult(HotlineRequest hotlineRequest);

    AjaxResult<CallLossResult> queryCallLossResult(CallLossRequest hotlineRequest);

    AjaxResult<AvgSatisfactionResult> queryAvgSatisfactionResult(AvgSatisfactionRequest hotlineRequest);

    AjaxResult<AvgCallTimeResult> queryAvgCallTimeResult(AvgCallTimeRequest hotlineRequest);

    AjaxResult<TeamBusinessIndicatorsResult> queryTeamBusinessNoTrend(HotlineRequest hotlineRequest);

    AjaxResult<TeamBusinessIndicatorsResult> queryTeamBusinessTrendPartOne(HotlineRequest hotlineRequest);

    AjaxResult<TeamBusinessIndicatorsResult> queryTeamBusinessTrendPartTwo(HotlineRequest hotlineRequest);

    AjaxResult<TeamBusinessIndicatorsResult> queryTeamBusinessTrendPartThree(HotlineRequest hotlineRequest);

    AjaxResult<List<SysUserBaseInfoVo>> queryUserInfoListByCompanyId();

    AjaxResult saveHotlineAlarmEmailSetting(String userIds);

    AjaxResult<String> queryHotlineAlarmEmailSetting();
}
