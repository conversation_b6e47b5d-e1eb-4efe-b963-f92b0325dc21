package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordOperationLog;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordWaitExecute;
import com.goclouds.crm.platform.call.domain.vo.UpdateWaitExecuteVO;
import com.goclouds.crm.platform.call.domain.vo.AddWaitExecuteVO;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordWaitExecuteMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordOperationLogService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordWaitExecuteService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.TicketOperationTypeEnum;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.goclouds.crm.platform.utils.SecurityUtil.getUserId;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_wait_execute(工单待办事项)】的数据库操作Service实现
* @createDate 2023-10-20 17:36:05
*/
@Service
@RequiredArgsConstructor
public class CrmAgentWorkRecordWaitExecuteServiceImpl extends ServiceImpl<CrmAgentWorkRecordWaitExecuteMapper, CrmAgentWorkRecordWaitExecute>
    implements CrmAgentWorkRecordWaitExecuteService {

    private final CrmAgentWorkRecordOperationLogService crmAgentWorkRecordOperationLogService;
    @Override
    public AjaxResult<Object> updateWaitExecute(UpdateWaitExecuteVO updateWaitExecuteVO) {
        // 根据待办id查询数据
        CrmAgentWorkRecordWaitExecute recordWaitExecute = this.baseMapper.selectById(updateWaitExecuteVO.getWaitExecuteId());
        String operationLogDescribe = null;
        Integer operationLogType = 0;
        // 判断是完成还是删除
        if(updateWaitExecuteVO.getStatus() == 1){
            recordWaitExecute.setWaitExecuteStatus(1);
            operationLogDescribe = SecurityUtil.getUsername()+"完成待办事项:"+recordWaitExecute.getWaitExecuteEvent();
            operationLogType = TicketOperationTypeEnum.COMPLETE_TICKET_WAIT_EXECUTE.getCode();
        }else if(updateWaitExecuteVO.getStatus() == 0){
            recordWaitExecute.setWaitExecuteStatus(0);
            operationLogDescribe = SecurityUtil.getUsername()+"取消完成待办事项:"+recordWaitExecute.getWaitExecuteEvent();
            operationLogType = TicketOperationTypeEnum.CANCELLATION_TICKET_COMPLETE.getCode();
        }else{
            recordWaitExecute.setDataStatus(0);
            operationLogDescribe = SecurityUtil.getUsername()+"删除待办事项:"+recordWaitExecute.getWaitExecuteEvent();
            operationLogType = TicketOperationTypeEnum.DELETE_TICKET_WAIT_EXECUTE.getCode();
        }
        // 添加工单操作日志
        addOperationLog(recordWaitExecute.getWorkRecordId(),recordWaitExecute.getWaitExecuteEvent(),operationLogDescribe,operationLogType);
        this.updateById(recordWaitExecute);
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult<Object> addWaitExecute(AddWaitExecuteVO addWaitExecuteVO) {
        CrmAgentWorkRecordWaitExecute waitExecute = new CrmAgentWorkRecordWaitExecute();
        BeanUtils.copyProperties(addWaitExecuteVO, waitExecute);
        waitExecute.setWaitExecuteId(UuidUtils.generateUuid());
        waitExecute.setCreator(getUserId());
        waitExecute.setCreateTime(new Date());
        waitExecute.setDataStatus(1);
        this.baseMapper.insert(waitExecute);
        // 添加工单操作日志
        String operationLogDescribe = SecurityUtil.getUsername()+"新增待办事项:"+waitExecute.getWaitExecuteEvent();
        addOperationLog(addWaitExecuteVO.getWorkRecordId(),waitExecute.getWaitExecuteEvent(),operationLogDescribe, TicketOperationTypeEnum.CREATE_TICKET_WAIT_EXECUTE.getCode());
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }


    /**
     *  添加操作记录
     * @param workRecordId 工单id
     * @param operationLogReason 操作原因
     * @param operationLogDescribe 操作记录描述
     */
    private void addOperationLog(String workRecordId, String operationLogReason, String operationLogDescribe, Integer operationLogType){
        CrmAgentWorkRecordOperationLog crmAgentWorkRecordOperationLog = new CrmAgentWorkRecordOperationLog();
        crmAgentWorkRecordOperationLog.setOperationLogId(UuidUtils.generateUuid())
                .setWorkRecordId(workRecordId)
                .setOperationLogDescribe(operationLogDescribe)
                .setOperatorName(SecurityUtil.getLoginUser().getUserName())
                .setDataStatus(1).setCreator(getUserId()).setCreateTime(new Date()).setOperationLogType(operationLogType);
        // 操作原因不为null则添加
        if(operationLogReason != null){
            crmAgentWorkRecordOperationLog.setOperationLogReason(operationLogReason);
        }
        crmAgentWorkRecordOperationLogService.save(crmAgentWorkRecordOperationLog);
    }
}




