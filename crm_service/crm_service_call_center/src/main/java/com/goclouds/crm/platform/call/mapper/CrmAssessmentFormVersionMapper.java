package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goclouds.crm.platform.call.domain.CrmAssessmentFormVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 评估表版本Mapper接口
 * 2025-05-14 11:19:52
 * zk
 */
@Mapper
public interface CrmAssessmentFormVersionMapper extends BaseMapper<CrmAssessmentFormVersion> {

    @Select("SELECT MAX(rule_sort) FROM crm_ticket_assessment_rule WHERE version_id = #{versionId}")
    Integer selectMaxRuleSort(@Param("versionId") String versionId);
}