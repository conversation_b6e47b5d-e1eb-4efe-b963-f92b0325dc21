package com.goclouds.crm.platform.call.strategy;

import com.goclouds.crm.platform.call.processer.AddCustomerTicketSmartFillProcesser;
import com.goclouds.crm.platform.call.processer.AddWorkOrderTicketSmartFillProcesser;

import java.util.Arrays;

/**
 * <AUTHOR> pengliang.sun
 * @description :
 */
public enum AigcTicketSmartFillEnum {
    WORK_ORDER("work_order", "工单智能填充策略", AddWorkOrderTicketSmartFillProcesser.class),
    CUSTOMER("customer", "客户智能填充策略", AddCustomerTicketSmartFillProcesser.class);

    private final String code;          // 策略编码
    private final String description;   // 策略描述
    private final Class<? extends AigcTicketSmartFillStrategy> processorClass; // 对应处理器类

    AigcTicketSmartFillEnum(String code, String description,
                            Class<? extends AigcTicketSmartFillStrategy> processorClass) {
        this.code = code;
        this.description = description;
        this.processorClass = processorClass;
    }

    // 根据code获取枚举实例
    public static AigcTicketSmartFillEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.code.equals(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的策略编码: " + code));
    }

    // Getters
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public Class<? extends AigcTicketSmartFillStrategy> getProcessorClass() {
        return processorClass;
    }
}
