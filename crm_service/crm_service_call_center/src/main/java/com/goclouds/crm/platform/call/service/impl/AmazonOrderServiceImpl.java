package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.goclouds.crm.platform.call.domain.AmazonOrder.AmazonOrderRequest;
import com.goclouds.crm.platform.call.domain.AmazonOrder.AmazonOrderResponse;
import com.goclouds.crm.platform.call.domain.AmazonOrder.AmazonTokenRequest;
import com.goclouds.crm.platform.call.domain.AmazonOrder.AmazonTokenResponse;
import com.goclouds.crm.platform.call.domain.CrmChannelInfoConfigInst;
import com.goclouds.crm.platform.call.service.AmazonOrderService;
import com.goclouds.crm.platform.call.service.CrmChannelInfoConfigInstService;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.utils.RedisCacheUtil;
import com.goclouds.crm.platform.openfeignClient.client.call.AmazonOrderCilent;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.client.customer.CustomerClient;
import com.goclouds.crm.platform.openfeignClient.domain.AmazonOrder.*;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CustomerMediaInstVO;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AmazonOrderServiceImpl implements AmazonOrderService {

    @Resource
    private AmazonOrderCilent amazonOrderCilent;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private CrmChannelInfoConfigInstService crmChannelInfoConfigInstService;

    @Resource
    private ChannelClient channelClient;

    @Value("${amazon.client.id}")
    private String client_id;

    @Value("${amazon.client.secret}")
    private String client_secret;

    @Value("${amazon.redirect.uri}")
    private String redirect_uri;

    @Resource
    private CustomerClient customerClient;

    @Override
    public AmazonOrderRpcResponse amazonOrderList(AmazonOrderRequest amazonOrderRequest) {
        AmazonOrderRpcResponse amazonOrderResponse = new AmazonOrderRpcResponse();
        String customerId = amazonOrderRequest.getCustomerId();
        R<CustomerMediaInstVO> amazonEmail = customerClient.queryCustomerMediaDetails(customerId, "AmazonEmail");
        log.info("查询获取的邮箱账户信息:{}", JSONObject.toJSONString(amazonEmail));
        String value = amazonEmail.getData().getValue();
        if (StringUtils.isEmpty(value)) {
            log.info("相关邮箱账户信息为null：[{}]", value);
            return amazonOrderResponse;
        }
        amazonOrderRequest.setUserEmail(value);
        // 判断是否存在redis，若存在 直接返回
        String userEmail = amazonOrderRequest.getUserEmail();
        String cacheObject = RedisCacheUtil.getCacheObject(userEmail);
        if (StringUtils.isNotBlank(cacheObject)) {
            log.info("redis 中存在相关邮箱账户订单信息：[{}]", userEmail);
            return JSONObject.parseObject(cacheObject, AmazonOrderRpcResponse.class);
        }
        String workRecordId = amazonOrderRequest.getWorkRecordId();
        String indexName = "ticket_info_index_" + SecurityUtil.getLoginUser().getCompanyId();
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("work_record_id", workRecordId));
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(10000);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        // 进行查询
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        SearchHit[] hits = response.getHits().getHits();
        log.info("hits:[{}]", (Object) hits);
        Map<String, Object> source = hits[0].getSourceAsMap();
        // 获取工单配置id
        String channelId = source.get("channel_config_id").toString();
        log.info("channelId:[{}]", channelId);
        LambdaQueryWrapper<CrmChannelInfoConfigInst> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmChannelInfoConfigInst::getChannelId, channelId);
        queryWrapper.eq(CrmChannelInfoConfigInst::getCode, "refresh_token");
        queryWrapper.eq(CrmChannelInfoConfigInst::getDataStatus, 1);
        List<CrmChannelInfoConfigInst> list = crmChannelInfoConfigInstService.list(queryWrapper);
        log.info("refresh_token:[{}]", JSONObject.toJSONString(list));
        if(CollectionUtils.isEmpty(list)) {
            return amazonOrderResponse;
        }
        String refreshToken = list.get(0).getName();
        if (RedisCacheUtil.getCacheObject(refreshToken) == null) {
            log.info("redis 中不存在相关refreshToken账户accessToken信息：[{}]", refreshToken);
            // token过期，根据refreshToken 刷新access token
            AmazonTokenRpcRequest amazonTokenRpcRequest = new AmazonTokenRpcRequest();
            amazonTokenRpcRequest.setGrant_type("refresh_token");
            amazonTokenRpcRequest.setClient_id(client_id);
            amazonTokenRpcRequest.setClient_secret(client_secret);
            amazonTokenRpcRequest.setRedirect_uri(redirect_uri);
            amazonTokenRpcRequest.setRefresh_token(refreshToken);
            log.info("亚马逊根据refresh_token刷新查询token,amazonTokenRpcRequest:[{}]", JSONObject.toJSONString(amazonTokenRpcRequest));
            R<AmazonTokenRpcResponse> amazonTokenRpcResponse = null;
            try {
                amazonTokenRpcResponse = amazonOrderCilent.amazonToken(amazonTokenRpcRequest);
            } catch (Exception e) {
                log.info("亚马逊根据refresh_token刷新查询token--异常：【{}】", e.getMessage());
                throw new RuntimeException(e);
            }
            log.info("亚马逊根据refresh_token刷新查询token,response:[{}]", JSONObject.toJSONString(amazonTokenRpcResponse));
            if (amazonTokenRpcResponse.getCode() == 200) {
                log.info("redis 中存入相关refreshToken账户accessToken信息：[{}]", refreshToken);
                RedisCacheUtil.setCacheObject(refreshToken,
                        amazonTokenRpcResponse.getData().getAccess_token(), 60, TimeUnit.MINUTES);
            }
        }
        String accessToken = RedisCacheUtil.getCacheObject(refreshToken);
        LambdaQueryWrapper<CrmChannelInfoConfigInst> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CrmChannelInfoConfigInst::getChannelId, channelId);
        lambdaQueryWrapper.eq(CrmChannelInfoConfigInst::getCode, "marketplace_id");
        lambdaQueryWrapper.eq(CrmChannelInfoConfigInst::getDataStatus, 1);
        List<CrmChannelInfoConfigInst> marketPlaceIdList = crmChannelInfoConfigInstService.list(queryWrapper);
        log.info("marketPlaceIdList:[{}]", JSONObject.toJSONString(marketPlaceIdList));
        if(CollectionUtils.isEmpty(marketPlaceIdList)) {
            return amazonOrderResponse;
        }
        String marketplaceIds = marketPlaceIdList.get(0).getName();
//        R<String> stringR = channelClient.queryAmazonMarketRegionUrl(marketplaceIds);
        LambdaQueryWrapper<CrmChannelInfoConfigInst> baseUrlWrapper = new LambdaQueryWrapper<>();
        baseUrlWrapper.eq(CrmChannelInfoConfigInst::getChannelId, channelId);
        baseUrlWrapper.eq(CrmChannelInfoConfigInst::getCode, "base_url");
        baseUrlWrapper.eq(CrmChannelInfoConfigInst::getDataStatus, 1);
        List<CrmChannelInfoConfigInst> baseUrlList = crmChannelInfoConfigInstService.list(baseUrlWrapper);
        log.info("baseUrlList:[{}]", JSONObject.toJSONString(baseUrlList));
        if(CollectionUtils.isEmpty(baseUrlList)) {
            return amazonOrderResponse;
        }
        String url = baseUrlList.get(0) + "/orders/v0/orders";
        AmazonOrderRpcRequest rpcRequest = new AmazonOrderRpcRequest();
        rpcRequest.setBuyerEmail(userEmail);
        rpcRequest.setRefreshToken(refreshToken);
        rpcRequest.setMaxResultsPerPage("100");
        // 计算半年前的日期
        LocalDate sixMonthsAgo = LocalDate.now().minusMonths(6);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = sixMonthsAgo.format(formatter);
        rpcRequest.setCreatedAfter(formattedDate);
        rpcRequest.setMarketplaceIds(marketplaceIds);
        rpcRequest.setUrl(url);
        rpcRequest.setAccessToken(accessToken);
        log.info("亚马逊rpc订单,rpcRequest:[{}]", JSONObject.toJSONString(rpcRequest));
        R<AmazonOrderRpcResponse> responseR = null;
        try {
            responseR = amazonOrderCilent.amazonOrderList(rpcRequest);
        } catch (Exception e) {
            log.info("亚马逊rpc订单异常responseR--:[{}]", e.getMessage());
            throw new RuntimeException(e);
        }
        log.info("亚马逊rpc订单,responseR:[{}]", JSONObject.toJSONString(responseR));
        if (responseR.getCode() != 200) {
            return amazonOrderResponse;
        }
        if(CollectionUtils.isEmpty( responseR.getData().getOrders())) {
            return amazonOrderResponse;
        }
        responseR.getData().getOrders().forEach(order -> {
            BigDecimal[] totalAmount = {BigDecimal.ZERO};
            List<Orders> ordersList = new ArrayList<>();
            AmazonOrderDetailRpcRequest amazonOrderDetailRpcRequest = new AmazonOrderDetailRpcRequest();
            amazonOrderDetailRpcRequest.setOrderId(order.getAmazonOrderId());
            amazonOrderDetailRpcRequest.setAccessToken(accessToken);
            amazonOrderDetailRpcRequest.setUrl(baseUrlList.get(0)  + "/orders/v0/orders/");
            log.info("亚马逊rpcAmazonOrderDetail,amazonOrderDetailRpcRequest:[{}]", JSONObject.toJSONString(amazonOrderDetailRpcRequest));
            R<AmazonOrderDetailRpcResponse> amazonOrderDetailRpcResponseR = null;
            try {
                amazonOrderDetailRpcResponseR = amazonOrderCilent.rpcAmazonOrderDetail(amazonOrderDetailRpcRequest);
            } catch (Exception e) {
                log.info("亚马逊rpc订单异常amazonOrderDetailRpcResponseR--:[{}]", e.getMessage());
                throw new RuntimeException(e);
            }
            log.info("亚马逊rpcAmazonOrderDetail,amazonOrderDetailRpcResponseR:[{}]", JSONObject.toJSONString(amazonOrderDetailRpcResponseR));
            AmazonOrderDetailRpcResponse amazonOrderDetailRpcResponse = amazonOrderDetailRpcResponseR.getData();
            if(Objects.isNull(amazonOrderDetailRpcResponse)) {
                return;
            }
            // 物流等级
            String shipServiceLevel = amazonOrderDetailRpcResponse.getShipServiceLevel();
            order.setShipServiceLevel(shipServiceLevel);
            AmazonOrderItemDetailRpcRequest orderItemDetailRpcRequest = new AmazonOrderItemDetailRpcRequest();
            orderItemDetailRpcRequest.setOrderId(order.getAmazonOrderId());
            orderItemDetailRpcRequest.setAccessToken(accessToken);
            amazonOrderDetailRpcRequest.setUrl(baseUrlList.get(0)  + "/orders/v0/orders/");
            log.info("亚马逊rpcAmazonOrderDetail,amazonOrderDetailRpcRequest:[{}]", JSONObject.toJSONString(amazonOrderDetailRpcRequest));
            R<AmazonOrderItemDetailRpcResponse> amazonOrderItemDetail = null;
            try {
                amazonOrderItemDetail = amazonOrderCilent.rpcAmazonOrderItemDetail(orderItemDetailRpcRequest);
            } catch (Exception e) {
                log.info("亚马逊rpc订单异常amazonOrderItemDetail--:[{}]", e.getMessage());
                throw new RuntimeException(e);
            }
            log.info("亚马逊rpcAmazonOrderDetail,amazonOrderItemDetail:[{}]", JSONObject.toJSONString(amazonOrderItemDetail));
            if(Objects.isNull(amazonOrderItemDetail)) {
                return;
            }
            if(CollectionUtils.isEmpty(amazonOrderItemDetail.getData().getOrderItems())) {
                return;
            }
            List<AmazonOrderItemDetailRpcResponse.OrderItem> orderItems = amazonOrderItemDetail.getData().getOrderItems();
            orderItems.forEach(item -> {
                Orders orders = new Orders();
                // skuId
                String sellerSKU = item.getSellerSKU();
                orders.setSellerSKU(sellerSKU);
                // ASIN
                String asin = item.getAsin();
                orders.setAsin(asin);
                // 商品名称
                String title = item.getTitle();
                orders.setTitle(title);
                AmazonOrderItemDetailRpcResponse.ProductInfo productInfo = item.getProductInfo();
                // 商品数量
                String numberOfItems = productInfo.getNumberOfItems();
                orders.setNumberOfItems(numberOfItems);
                AmazonOrderItemDetailRpcResponse.ItemPrice itemPrice = item.getItemPrice();
                BigDecimal amount = itemPrice.getAmount();
                orders.setAmount(amount);
                totalAmount[0] = totalAmount[0].add(amount != null ? amount : BigDecimal.ZERO);
                String currencyCode = itemPrice.getCurrencyCode();
                orders.setCurrencyCode(currencyCode);
                ordersList.add(orders);
            });
            order.setTotalAmount(totalAmount[0]);
            if (totalAmount[0].compareTo(BigDecimal.ZERO) > 0) {
                order.setCurrencyCode(ordersList.get(0).getCurrencyCode());
            }
            order.setOrderList(ordersList);
        });
        amazonOrderResponse.setOrders(responseR.getData().getOrders());
        amazonOrderResponse.setNextToken(responseR.getData().getNextToken());
        //将订单信息存入redis中
        RedisCacheUtil.setCacheObject(userEmail,JSONObject.toJSONString(amazonOrderResponse),60, TimeUnit.MINUTES);
        log.info("redis 存入相关邮箱账户60分钟订单信息：[{}],订单：[{}]", userEmail, JSONObject.toJSONString(amazonOrderResponse));
        return amazonOrderResponse;
    }

    @Override
    public AmazonTokenResponse amazonToken(AmazonTokenRequest request) {
        AmazonTokenResponse amazonTokenResponse = new AmazonTokenResponse();
        String lwaCode = request.getLwaCode();
        AmazonTokenRpcRequest amazonTokenRpcRequest = new AmazonTokenRpcRequest();
        amazonTokenRpcRequest.setCode(lwaCode);
        amazonTokenRpcRequest.setGrant_type("authorization_code");
        amazonTokenRpcRequest.setClient_id(client_id);
        amazonTokenRpcRequest.setClient_secret(client_secret);
        amazonTokenRpcRequest.setRedirect_uri(redirect_uri);
        log.info("亚马逊查询token,amazonTokenRpcRequest:[{}]", JSONObject.toJSONString(amazonTokenRpcRequest));
        R<AmazonTokenRpcResponse> response = null;
        try {
            response = amazonOrderCilent.amazonToken(amazonTokenRpcRequest);
        } catch (Exception e) {
            log.info("亚马逊根据refresh_token刷新查询token--异常：【{}】",e.getMessage());
            throw new RuntimeException(e);
        }
        log.info("亚马逊查询token,response:[{}]", JSONObject.toJSONString(response));
        if (response.getCode() != 200) {
            return amazonTokenResponse;
        }
        String refreshToken = response.getData().getRefresh_token();
        amazonTokenResponse.setRefreshToken(refreshToken);
        //将token存入redis中
        RedisCacheUtil.setCacheObject(refreshToken, amazonTokenResponse.getAccessToken(),50,TimeUnit.MINUTES);
        log.info("redis 存入相关邮箱账户token信息50分钟--refreshToken：[{}],token：[{}]", refreshToken, JSONObject.toJSONString(amazonTokenResponse));
        return amazonTokenResponse;
    }
}
