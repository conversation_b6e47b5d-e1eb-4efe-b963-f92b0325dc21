package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goclouds.crm.platform.call.domain.messageRemind.MessageRemind;
import com.goclouds.crm.platform.call.domain.messageRemind.MessageRemindES;
import com.goclouds.crm.platform.call.domain.messageRemind.TranslateLanguageVo;
import com.goclouds.crm.platform.call.domain.messageRemind.request.*;
import com.goclouds.crm.platform.call.domain.messageRemind.response.ChildTranslateMessageReminding;
import com.goclouds.crm.platform.call.domain.messageRemind.response.QueryMessageRemindTimeListResponse;
import com.goclouds.crm.platform.call.domain.messageRemind.response.QueryMessageRemindingResponse;
import com.goclouds.crm.platform.call.domain.messageRemind.response.QueryMessageRemindingTimeResponse;
import com.goclouds.crm.platform.call.service.WorkMessageRemindService;
import com.goclouds.crm.platform.common.constant.RabbitMqConstants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.AIGCModule;
import com.goclouds.crm.platform.common.enums.ResultCodeEnum;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.aigc.AigcChatClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.call.RpcTranslateLanguageVo;
import com.goclouds.crm.platform.utils.AIGCRequestCheckUtil;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.ShardSearchFailure;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.internal.InternalSearchResponse;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.Nullable;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> pengliang.sun
 * @description :
 */
@Slf4j
@Service
public class WorkMessageRemindServiceImpl implements WorkMessageRemindService {

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private AigcChatClient aigcChatClient;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private AIGCRequestCheckUtil aigcRequestCheckUtil;

    private final static String MESSAGE_REMINDING_INDEX_PREFIX = "message_reminding_index_";

    private String embIndexName() {
        return MESSAGE_REMINDING_INDEX_PREFIX + SecurityUtil.getLoginUser().getCompanyId();
    }


    @Override
    public AjaxResult<IPage<QueryMessageRemindingResponse>> queryMessageReminding(IPage<Object> pageParam, QueryMessageRemindingRequest request) {
        //计算当前页的起始下标
        long start = (pageParam.getCurrent() - 1) * pageParam.getSize();
        String indexName = embIndexName();
        log.info("问题提醒查询索引：【{}】", indexName);
        try {
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在则直接返回空
            if (!indexExists) {
                return AjaxResult.ok();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //获取索引
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtil.isNotNull(request.getMessage()) && StringUtil.isNotEmpty(request.getMessage())) {
            // 根据问题查询
            boolQueryBuilder.must(QueryBuilders.matchQuery("message", request.getMessage()));
        }
        boolQueryBuilder.must(QueryBuilders.matchQuery("message_type", request.getMessageType()));
        // 对列表中的每个语言/标签创建一个 termQuery，并将其添加到 should 子句中
        BoolQueryBuilder orQueryBuilder = QueryBuilders.boolQuery();
        for (String language : request.getLanguageList()) {
            orQueryBuilder.should(QueryBuilders.termQuery("language", language));
        }
        if (StringUtil.isNotEmpty(request.getLanguageList())) {
            boolQueryBuilder.filter(orQueryBuilder);
        }
        boolQueryBuilder.mustNot(QueryBuilders.existsQuery("father_message_id"));
        searchSourceBuilder.sort("wait_time", SortOrder.DESC);
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder).from((int) start).size((int) pageParam.getSize());
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        // 进行查询
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        // 计算操作耗时（毫秒）
        SearchHit[] hits = response.getHits().getHits();
        long total = response.getHits().getTotalHits().value;
        List<QueryMessageRemindingResponse> answerKnowledgeListVos = new ArrayList<>();
        IPage<QueryMessageRemindingResponse> iPage = new Page<>();
        long hitStratime = System.currentTimeMillis();
        for (SearchHit hit : hits) {
            QueryMessageRemindingResponse knowledgeListVo = new QueryMessageRemindingResponse();
            Map<String, Object> source = hit.getSourceAsMap();
            long createTime = Long.parseLong(source.get("create_time").toString());
            // id
            knowledgeListVo.setMessageId((String) source.get("message_id"));
            // 创建人
            knowledgeListVo.setCreator((String) source.get("creator"));
            // 问题
            knowledgeListVo.setMessage((String) source.get("message"));
            // 创建时间
            knowledgeListVo.setCreateTime(new Date(createTime));

            knowledgeListVo.setLanguage((String) source.get("language"));
            // 添加翻译词条
            knowledgeListVo.setTranslationStatus((Integer) source.get("translation_status"));

            knowledgeListVo.setBeforeLanguage((String) source.get("before_language"));

            knowledgeListVo.setFatherMessageId((String) source.get("father_message_id"));
            // 根据枚举返回前端具体参数
            String timeType = source.get("time_type").toString();
            // 方式2：提供默认值
            knowledgeListVo.setTimeType(timeType);
            knowledgeListVo.setWaitTime((String) source.get("wait_time"));

            knowledgeListVo.setTranslationLanguage((String) source.get("translation_language"));

            knowledgeListVo.setMessageType((Integer) source.get("message_type"));

            answerKnowledgeListVos.add(knowledgeListVo);
        }
        List<QueryMessageRemindingResponse> answerKnowledgeListVoList = new ArrayList<>();
        try {
            List<QueryMessageRemindingResponse> sortedList = getMessageRemindingList(answerKnowledgeListVos);
            // 按转换后的分钟值升序排序
            answerKnowledgeListVoList = sortedList.stream()
                    .sorted((vo1, vo2) -> {
                        int time1 = convertToMinutes(vo1.getWaitTime(), vo1.getTimeType());
                        int time2 = convertToMinutes(vo2.getWaitTime(), vo2.getTimeType());
                        return Integer.compare(time1,time2 );
                    })
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.error("收集子集合错误，原因为【{}】", e.getMessage());
            throw new RuntimeException(e);
        }
        iPage.setRecords(answerKnowledgeListVoList);
        // 之前是total，因为算上了子级翻译，所以采用父级集合大小
        iPage.setTotal(total);
        return AjaxResult.ok(iPage);
    }

    private int convertToMinutes(String waitTimeStr, String timeType) {
        // 提取数字部分（处理类似 "15h" 或 "30m" 的情况）
        String numericPart = waitTimeStr.replaceAll("[^0-9]", "");
        if (numericPart.isEmpty()) return 0;

        int waitTime = Integer.parseInt(numericPart);

        switch (timeType.toLowerCase()) {
            case "day":   return waitTime * 24 * 60;
            case "hour":  return waitTime * 60;
            case "min":   return waitTime;
            default:      return 0;
        }
    }

    /**
     * 采用迭代器遍历
     *
     * @param queryMessageRemindingList
     * @return
     */
    private List<QueryMessageRemindingResponse> getMessageRemindingList(List<QueryMessageRemindingResponse> queryMessageRemindingList) throws IOException {
        // 2. 收集所有父级的 parentMessageIds，用于查询子级
        Set<String> parentMessageIds = queryMessageRemindingList.stream()
                .map(QueryMessageRemindingResponse::getMessageId)
                .collect(Collectors.toSet());
        String indexName = embIndexName();
        if (!parentMessageIds.isEmpty()) {
            // 3. 查询子级文档（fathergroupqid 在父级 parentMessageIds 列表中）
            SearchRequest childSearchRequest = new SearchRequest(indexName);
            SearchSourceBuilder childSearchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termsQuery("father_message_id", parentMessageIds));
            childSearchSourceBuilder.query(boolQueryBuilder).size(1000);
            // 执行子级查询
            childSearchRequest.source(childSearchSourceBuilder);
            SearchResponse childResponse = restHighLevelClient.search(childSearchRequest, RequestOptions.DEFAULT);
            List<ChildTranslateMessageReminding> childList = parseChildHits(childResponse.getHits());
            // 4. 将子级按 getFatherMessageId 分组，方便关联到父级
            Map<String, List<ChildTranslateMessageReminding>> childMap = childList.stream()
                    .collect(Collectors.groupingBy(
                            vo -> Optional.ofNullable(vo.getFatherMessageId()).orElse(""),
                            Collectors.toList()
                    ));
            // 5. 将子级数据填充到父级的 translateLanguageList 中
            queryMessageRemindingList.forEach(parent -> {
                List<ChildTranslateMessageReminding> children = childMap.getOrDefault(parent.getMessageId(), new ArrayList<>());
                parent.setChildTranslateMessageRemindingList(children);
            });
        }
        return queryMessageRemindingList;
    }

    private List<ChildTranslateMessageReminding> parseChildHits(SearchHits hits) {
        List<ChildTranslateMessageReminding> result = new ArrayList<>();
        for (SearchHit hit : hits) {
            Map<String, Object> source = hit.getSourceAsMap();
            ChildTranslateMessageReminding vo = new ChildTranslateMessageReminding();
            long createTime = Long.parseLong(source.get("create_time").toString());
            // id
            vo.setMessageId((String) source.get("message_id"));
            // 创建人
            vo.setCreator((String) source.get("creator"));
            // 问题
            vo.setMessage((String) source.get("message"));
            // 创建时间
            vo.setCreateTime(new Date(createTime));

            vo.setLanguage((String) source.get("language"));
            // 添加翻译词条
            vo.setTranslationStatus((Integer) source.get("translation_status"));

            vo.setBeforeLanguage((String) source.get("before_language"));

            vo.setMessageType((Integer) source.get("message_type"));

            vo.setFatherMessageId((String) source.get("father_message_id"));

            vo.setTimeType((String) source.get("time_type"));

            vo.setWaitTime((String) source.get("wait_time"));

            vo.setTranslationLanguage((String) source.get("translation_language"));
            result.add(vo);
        }
        return result;
    }

    /**
     * 查询索引是否存在
     *
     * @param index 索引名称
     * @return
     * @throws IOException
     */
    private boolean headIndexExists(String index) throws IOException {
        GetIndexRequest req = new GetIndexRequest(index);
        boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
        log.info("当前索引{}, 是否存在: {}", index, exists);
        return exists;
    }

    @Override
    public AjaxResult addMessageReminding(AddMessageRemindingRequest request) {
        log.info("新增消息提醒入参:[{}]", JSONObject.toJSONString(request));
        MessageRemind addEsVoList = convertToES(request);
        // 默认值 未翻译
        addEsVoList.setTranslationStatus(0);
        log.info("新增消息提醒，生成 ES 入参:[{}]", addEsVoList);
        if (StringUtil.isNotEmpty(addEsVoList.getMessage())) {
            Boolean isExist = addMessageRemindingEs(addEsVoList);
            if (isExist) {
                return AjaxResult.failure(MessageUtils.get("work.message.reminding.add.fail"));
            }
            return AjaxResult.ok(MessageUtils.get("operate.success"));
        }
        return AjaxResult.failure(MessageUtils.get("operate.failure"));
    }

    private Boolean addMessageRemindingEs(MessageRemind messageRemind) {
        // 获取需要添加索引名称
        String indexName = embIndexName();
        // 首先进行ES索引判断
        boolean indexExists = false;
        try {
            indexExists = headIndexExists(indexName);
        } catch (IOException e) {
            log.error("检查索引是否存在报错", e);
            throw new RuntimeException(e);
        }
        // 索引不存在则进行创建
        if (!indexExists) {
            createMessageRemindIndex(indexName);
        }
        SearchResponse countResponse = getCountResponse(indexName, messageRemind.getMessage(), null,
                messageRemind.getWaitTime(),messageRemind.getTimeType(), messageRemind.getMessageType());
        if (countResponse.getHits().getTotalHits().value > 0) {
            return Boolean.TRUE;
        }
        // 定义批量请求
        BulkRequest bulkRequest = new BulkRequest(indexName);
        // 遍历newAddAnswerEsVoList，为每一条数据创建一个IndexRequest并添加到bulkRequest中
        // 定义单条索引请求
        IndexRequest singleIndexRequest = new IndexRequest(indexName);
        // 将单个AddAnswerEsVo对象转为json
        String value = null;
        try {
            value = new ObjectMapper().writeValueAsString(messageRemind);
        } catch (JsonProcessingException e) {
            log.error("存入消息提醒json转换报错：[{}]", e);
            throw new RuntimeException(e);
        }
        log.info("向ES中添加文档：{}", value);
        singleIndexRequest.source(value, XContentType.JSON);
        // 添加单条索引请求到批量请求
        bulkRequest.add(singleIndexRequest);
        log.info("消息提醒数据插入es数据完成:[{}]", bulkRequest);
        try {
            // 执行批量索引操作 es版本和spring boot版本有冲突问题，添加成功，返回200，
            BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK")) {
                log.error("es新增文档失败，异常信息：", e);
                throw new RuntimeException(e);
            }
        }
        // 2. 手动刷新索引（使写入立即可查）
        RefreshRequest refreshRequest = new RefreshRequest(indexName);
        try {
            restHighLevelClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.info("插入强刷索引文档异常");
            throw new RuntimeException(e);
        }
        return Boolean.FALSE;
    }

    private SearchResponse getCountResponse(String indexName, String message,
                                            String messageId, String waitTime, String timeType, Integer messageType) {
        log.info("新增消息提醒索引为：【{}】,消息为:【{}】,消息id：【{}】,提醒时间间隔：【{}】,时间类型：【{}】，消息提醒类型：【{}】",
                indexName, message, messageId,waitTime, timeType, messageType);
        if (StringUtil.isNotEmpty(messageId)) {
            // messageId不为null 说明是更新，子语言更新操作不进行校验
            SearchResponse internalResponse = null;
            try {
                internalResponse = updateCheckMessage(indexName, messageId);
            } catch (IOException e) {
                log.error("校验更新消息提醒是否为字语言报错：【{}】", e);
                throw new RuntimeException(e);
            }
            if (internalResponse != null) {
                return internalResponse;
            }
        }
        SearchRequest countRequest = new SearchRequest(indexName);
        SearchSourceBuilder countSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("message_type", messageType))
                // 1. father_message_id必须为null
                .must(QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.existsQuery("father_message_id")));
        // 2. 按条件添加mustNot排除
        if (StringUtil.isNotEmpty(messageId)) {
            boolQuery.mustNot(QueryBuilders.termQuery("message_id", messageId));
        }
            BoolQueryBuilder messageBoolQuery = QueryBuilders.boolQuery();
            messageBoolQuery.should(QueryBuilders.termsQuery("message.keyword", message));
                BoolQueryBuilder timeBoolQuery = QueryBuilders.boolQuery();
                timeBoolQuery.must(QueryBuilders.termQuery("time_type", timeType));
                timeBoolQuery.must(QueryBuilders.termQuery("wait_time", waitTime));
            messageBoolQuery.should(timeBoolQuery);
        boolQuery.filter(messageBoolQuery);
        countSourceBuilder.query(boolQuery);
        countSourceBuilder.size(0);
        countRequest.source(countSourceBuilder);
        log.info("查询问题是否重复DSL语句：【{}】",countRequest);
        SearchResponse search = null;
        try {
            search = restHighLevelClient.search(countRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("查询提醒消息是否存在重复报错", e);
            throw new RuntimeException(e);
        }
        return search;
    }

    @Nullable
    private SearchResponse updateCheckMessage(String indexName, String messageId) throws IOException {
        // 如果为子语言校验直接
        Boolean flag = sonIsExist(indexName, messageId);
        if (flag) {
            SearchHits emptyHits = new SearchHits(
                    new SearchHit[0],
                    new TotalHits(0, TotalHits.Relation.EQUAL_TO),
                    0f
            );
            InternalSearchResponse internalResponse = new InternalSearchResponse(
                    emptyHits,
                    null,
                    null,
                    null,
                    false,
                    null,
                    1
            );
            return new SearchResponse(
                    internalResponse,
                    null,
                    1,
                    1,
                    0,
                    0,
                    ShardSearchFailure.EMPTY_ARRAY,
                    SearchResponse.Clusters.EMPTY
            );
        }
        return null;
    }

    private Boolean sonIsExist(String indexName, String messageId) throws IOException {
        SearchRequest sonRequest = new SearchRequest(indexName);
        SearchSourceBuilder sonSourceBuilder = new SearchSourceBuilder();
        // 1. 根据消息id查看修改的消息是否为子语言，若为子语言之间return 不进行问题校验
        BoolQueryBuilder sonBoolQuery = QueryBuilders.boolQuery();
        if (StringUtil.isNotEmpty(messageId)) {
            sonBoolQuery.must(QueryBuilders.termQuery("message_id", messageId));
        }
        sonSourceBuilder.query(sonBoolQuery);
        sonRequest.source(sonSourceBuilder);
        SearchResponse statusResponse = restHighLevelClient.search(sonRequest, RequestOptions.DEFAULT);
        log.info("更新查询dsl：【{}】",sonRequest);
        // 计算操作耗时（毫秒）
        SearchHit[] hits = statusResponse.getHits().getHits();
        //father_message_id 为null的情况只有父亲语言，才会出现，子语言所有的father_message_id不为null，为上一个messageId的值
        if (hits[0].getSourceAsMap().get("father_message_id") == null) {
            return Boolean.FALSE;
        }
        // 如果father_message_id不是空的，说明是子语言，return true 不进行问题校验重复
        return Boolean.TRUE;
    }


    private void createMessageRemindIndex(String indexName) {
        CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
        CreateIndexResponse createIndexResponse = null;
        try {
            createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("消息提醒创建索引异常：【{}】,索引名：【{}】", e.getMessage(),indexName);
            throw new RuntimeException(e);
        }
        boolean acknowledged = createIndexResponse.isAcknowledged();
        log.info("创建索引完成：{}", acknowledged);
    }

    private MessageRemind convertToES(AddMessageRemindingRequest request) {
        MessageRemind messageRemind = new MessageRemind();
        messageRemind.setMessageId(UUID.randomUUID().toString());
        messageRemind.setMessage(request.getMessage());
        messageRemind.setLanguage(request.getLanguage());
        messageRemind.setWaitTime(request.getWaitTime());
        messageRemind.setTimeType(request.getTimeType());
        messageRemind.setMessageType(request.getMessageType());
        messageRemind.setCreateTime(new Date());
        messageRemind.setUpdateTime(new Date());
        messageRemind.setCreator(SecurityUtil.getLoginUser().getUserName());
        return messageRemind;
    }

    @Override
    public AjaxResult updateMessageReminding(UpdateMessageRemindingRequest request) {
        log.info("更新消息提醒入参:[{}]", JSONObject.toJSONString(request));
        String indexName = embIndexName();
        SearchResponse countResponse = getCountResponse(indexName, request.getMessage(),
                request.getMessageId(), request.getWaitTime(), request.getTimeType(),request.getMessageType());
        if (countResponse.getHits().getTotalHits().value > 0) {
            return AjaxResult.failure(MessageUtils.get("work.message.reminding.update.fail"));
        }
        Map<String, Object> fieldsToUpdate = new HashMap<>();
        fieldsToUpdate.put("message", request.getMessage());
        fieldsToUpdate.put("language", request.getLanguage());
        fieldsToUpdate.put("wait_time", request.getWaitTime());
        fieldsToUpdate.put("time_type", request.getTimeType());
        fieldsToUpdate.put("update_time", new Date());
        BulkByScrollResponse bulkByScrollResponse = partialUpdateByMessageId(indexName, request.getMessageId(), fieldsToUpdate);
        if(!aigcRequestCheckUtil.requestCheck(SecurityUtil.getLoginUser().getCompanyId(), AIGCModule.Agent_Copilot.getCode())){
            log.info("调用 aiAgent AIGC计费校验失败 不可调用{}",SecurityUtil.getLoginUser().getCompanyId());
            RefreshRequest refreshRequest = new RefreshRequest(indexName);
            try {
                restHighLevelClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
            } catch (Exception e) {
                log.info("修改数据强刷索引文档异常");
                throw new RuntimeException(e);
            }
            return AjaxResult.failure(null, ResultCodeEnum.AI_AGENT_USAGE_LIMIT);
        }
        // 更新结束后判断是否需要进行翻译
        if (request.getIsTranslateFlag()==1 && bulkByScrollResponse.getUpdated()>0){
            updateMessageRemindingTranslateStatus(request.getMessageId(), indexName, 1, null);
            List<TranslateLanguageVo> translateLanguageList = new ArrayList<>();
            TranslateLanguageVo translateLanguageVo = new TranslateLanguageVo();
            translateLanguageVo.setMessageId(request.getMessageId());
            translateLanguageVo.setBeforeTranslateLanguage(request.getLanguage());
            translateLanguageVo.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            String translationtanguage = request.getTranslationLanguage();
            String[] split = translationtanguage.split(",");
            List<String> translateList = Arrays.asList(split);
            translateLanguageVo.setTranslateList(translateList);
            translateLanguageList.add(translateLanguageVo);
            String jsonString = JSON.toJSONString(translateLanguageList);
            // 过滤后到翻译集合
            rabbitTemplate.convertAndSend(RabbitMqConstants.TRANSLATE_MESSAGE_REMINDING_EXCHANGE,
                    RabbitMqConstants.TRANSLATE_MESSAGE_REMINDING_ROUTING_KEY, jsonString);
        }
        // 2. 手动刷新索引（使写入立即可查）
        RefreshRequest refreshRequest = new RefreshRequest(indexName);
        try {
            restHighLevelClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.info("修改数据强刷索引文档异常");
            throw new RuntimeException(e);
        }
        return AjaxResult.ok(MessageUtils.get("operate.success"));
    }



    /**
     * 更新翻译状态
     *
     * @param messageId
     * @param indexName
     * @param translateStatus
     */
    public void updateMessageRemindingTranslateStatus(String messageId, String indexName, Integer translateStatus,String translationLanguage) {
        // 创建 UpdateByQueryRequest
        UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(indexName);
        // 构建组合查询条件
        updateRequest.setQuery(QueryBuilders.termQuery("message_id", messageId));
        // 构建脚本参数
        Map<String, Object> params = new HashMap<>();
        StringBuilder scriptBuilder = new StringBuilder();
        // 评估类型设置为 自动评估
        params.put("translation_status", translateStatus);
        scriptBuilder.append("ctx._source.translation_status = params.translation_status; ");
        if(translationLanguage!=null){
            params.put("translation_language", translationLanguage);
            scriptBuilder.append("ctx._source.translation_language = params.translation_language; ");
        }
        // 设置脚本
        updateRequest.setScript(
                new Script(ScriptType.INLINE, "painless",
                        scriptBuilder.toString(),
                        params)
        );
        log.info("打印一下脚本内容:{}", updateRequest);
        // 执行更新
        try {
            BulkByScrollResponse bulkResponse = restHighLevelClient.updateByQuery(updateRequest, RequestOptions.DEFAULT);
            // 等待任务完成
            if (bulkResponse.getUpdated() > 0) {
                RefreshRequest refreshRequest = new RefreshRequest(indexName);
                restHighLevelClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
            }
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("更新翻译状态报错", e);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 根据 messageId 部分更新字段
     *
     * @param indexName 索引名称
     * @param messageId 业务ID（message_id字段值）
     * @param fieldsToUpdate 要更新的字段Map
     * @return 更新结果
     */
    public BulkByScrollResponse partialUpdateByMessageId(String indexName, String messageId,
                                                         Map<String, Object> fieldsToUpdate) {
        try {
            UpdateByQueryRequest request = new UpdateByQueryRequest(indexName);
            request.setQuery(QueryBuilders.termQuery("message_id", messageId));
            request.setRefresh(true);
            StringBuilder scriptBuilder = new StringBuilder();
            Map<String, Object> params = new HashMap<>();
            int paramIndex = 1;
            for (Map.Entry<String, Object> entry : fieldsToUpdate.entrySet()) {
                String paramName = "val" + paramIndex++;
                scriptBuilder.append("ctx._source.")
                        .append(entry.getKey())
                        .append("=params.")
                        .append(paramName)
                        .append(";");
                params.put(paramName, entry.getValue());
            }
            request.setScript(new Script(
                    ScriptType.INLINE,
                    "painless",
                    scriptBuilder.toString(),
                    params
            ));
            BulkByScrollResponse response = restHighLevelClient.updateByQuery(request, RequestOptions.DEFAULT);
            if (response.getUpdated() == 0) {
                log.warn("未找到匹配的文档，messageId: {}", messageId);
                throw new RuntimeException("未找到messageId为" + messageId + "的文档");
            }
            return response;
        } catch (IOException e) {
            throw new RuntimeException("更新文档失败", e);
        }
    }

    @Override
    public void removeMessageReminding(RemoveMessageRemindingRequest request) {
        // 获取需要删除索引名称
        String indexName = embIndexName();
        log.info("需要删除的MessageIds：{}", request.getMessageIds());
        String stringMessageIdList = request.getMessageIds();
        String[] split = stringMessageIdList.split(",");
        List<String> messageIdList = Arrays.asList(split);
        for (String messageId : messageIdList) {
            // 删除父数据
            DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(indexName);
            QueryBuilder queryBuilder = QueryBuilders.termsQuery("message_id", messageId);
            BoolQueryBuilder filterQueryBuilder = QueryBuilders.boolQuery().must(queryBuilder);
            try {
                deleteByQueryRequest.setQuery(filterQueryBuilder);
                BulkByScrollResponse bulkByScrollResponse = restHighLevelClient.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
            } catch (Exception e) {
                String message = e.getMessage();
                if (!message.contains("OK")) {
                    log.error("es删除父文档失败，异常信息：", e);
                    throw new RuntimeException(e);
                }
            }
            // 删除子数据
            DeleteByQueryRequest delete = new DeleteByQueryRequest(indexName);
            QueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("father_message_id", messageId);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery().must(termsQueryBuilder);
            try {
                delete.setQuery(boolQueryBuilder);
                BulkByScrollResponse deleteByQuery = restHighLevelClient.deleteByQuery(delete, RequestOptions.DEFAULT);
            } catch (Exception e) {
                String message = e.getMessage();
                if (!message.contains("OK")) {
                    log.error("es删除子文档失败，异常信息：", e);
                    throw new RuntimeException(e);
                }
            }
        }
        // 2. 手动刷新索引（使写入立即可查）
        RefreshRequest refreshRequest = new RefreshRequest(indexName);
        try {
            restHighLevelClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.info("删除数据强刷索引文档异常");
            throw new RuntimeException(e);
        }
    }

    @Override
    public void translateMessageReminding(TranslateMessageRemindingRequest request) {
        log.info("消息提醒智能翻译入参：【{}】",JSONObject.toJSONString(request));
        String indexName = embIndexName();
        //公司id
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        List<TranslateMessage> groupData = request.getTranslateMessageList();
        // 过滤掉符合条件的元素
        List<TranslateMessage> filteredList = groupData.stream()
                .filter(item -> item.getTranslationStatus() != 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredList)) {
            log.info("进行翻译的问题全为翻译中");
            return;
        }
        List<TranslateLanguageVo> translateLanguageList = new ArrayList<>();
        List<String> translateTypeList = request.getTranslateLanguageList();
        filteredList.forEach(group -> {
            // 从 translateTypeList 中移除 languageList 中包含的元素
            List<String> filteredTranslateTypeList = translateTypeList.stream()
                    .filter(translateType -> !translateType.equals(group.getLanguage())) // 严格相等比较
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filteredTranslateTypeList)){
                log.info("准备翻译的语言全部为原语言，不进行翻译");
                // 跳过本次循环
                return;
            }
            // 如果存在没有过滤的翻译，进行遍历更新翻译
            log.info("更新翻译 getMessageId:【{}】,indexName：【{}】,状态", group.getMessageId(), indexName);
            updateMessageRemindingTranslateStatus(group.getMessageId(), indexName, 1,null);
            TranslateLanguageVo translateLanguageVo = new TranslateLanguageVo();
            translateLanguageVo.setCompanyId(companyId);
            translateLanguageVo.setTranslateList(filteredTranslateTypeList);
            translateLanguageVo.setMessageId( group.getMessageId());
            String language = group.getLanguage();
            translateLanguageVo.setBeforeTranslateLanguage(language);
            translateLanguageList.add(translateLanguageVo);
        });
        String jsonString = JSON.toJSONString(translateLanguageList);
        // 过滤后到翻译集合
        rabbitTemplate.convertAndSend(RabbitMqConstants.TRANSLATE_MESSAGE_REMINDING_EXCHANGE,
                RabbitMqConstants.TRANSLATE_MESSAGE_REMINDING_ROUTING_KEY, jsonString);
        // 2. 手动刷新索引（使写入立即可查）
        RefreshRequest refreshRequest = new RefreshRequest(indexName);
        try {
            restHighLevelClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.info("插入强刷索引文档异常");
            throw new RuntimeException(e);
        }
    }

    @Override
    public AjaxResult<QueryMessageRemindTimeListResponse> queryMessageRemindingList(String companyId, String language) {
        log.info("返回信息提醒列表入参公司id：【{}】,语言为：【{}】,类型为：【{}】", companyId,language);
        String indexName =  MESSAGE_REMINDING_INDEX_PREFIX + companyId;
        log.info("问题提醒返回具体内容查询索引：【{}】", indexName);
        QueryMessageRemindTimeListResponse messageRemindResponse = new QueryMessageRemindTimeListResponse();
        try {
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在则直接返回空
            if (!indexExists) {
                return AjaxResult.ok(messageRemindResponse);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.should(QueryBuilders.termQuery("language", language));
        queryBuilder.minimumShouldMatch(1);
        boolQueryBuilder.filter(queryBuilder);
        searchSourceBuilder.sort("create_time", SortOrder.DESC);
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder).size(10000);
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        // 进行查询
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        // 计算操作耗时（毫秒）
        SearchHit[] hits=null;
        hits = response.getHits().getHits();
        // 当精确查找没找到时，采用前缀兜底
        if (hits.length == 0) {
            SearchRequest searchRequest1 = new SearchRequest();
            searchRequest1.indices(indexName);
            //构建请求
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder sBoolQuery = QueryBuilders.boolQuery();
            BoolQueryBuilder squeryBuilder = QueryBuilders.boolQuery();
            String[] split = language.split("-");
            String preLanguage = split[0];
            squeryBuilder.should(QueryBuilders.termQuery("language", preLanguage));
            squeryBuilder.minimumShouldMatch(1);
            sBoolQuery.filter(squeryBuilder);
            sourceBuilder.sort("create_time", SortOrder.DESC);
            // 查询条件
            sourceBuilder.query(sBoolQuery).size(10000);
            sourceBuilder.trackTotalHits(true);
            searchRequest1.source(sourceBuilder);
            // 进行查询
            SearchResponse response1 = null;
            try {
                response1 = restHighLevelClient.search(searchRequest1, RequestOptions.DEFAULT);
                hits = response1.getHits().getHits();
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new RuntimeException(e);
            }
        }
        List<QueryMessageRemindingTimeResponse> messageRemindingList = new ArrayList<>();
        for (SearchHit hit : hits) {
            QueryMessageRemindingTimeResponse queryMessageRemindingResponse = new QueryMessageRemindingTimeResponse();
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            queryMessageRemindingResponse.setMessageId((String) sourceAsMap.get("message_id"));
            queryMessageRemindingResponse.setLanguage((String) sourceAsMap.get("language"));
            queryMessageRemindingResponse.setMessage((String) sourceAsMap.get("message"));
            long waitSeconds = convertToSeconds(sourceAsMap.get("wait_time").toString(), sourceAsMap.get("time_type").toString());
            queryMessageRemindingResponse.setWaitTimeSeconds(waitSeconds);
            queryMessageRemindingResponse.setMessageType((Integer)sourceAsMap.get("message_type"));
            messageRemindingList.add(queryMessageRemindingResponse);
        }
        messageRemindResponse.setMessageRemindingList(messageRemindingList);
        return AjaxResult.ok(messageRemindResponse);
    }

    public long convertToSeconds(String waitTime, String timeType) {
        if (waitTime == null || timeType == null) {
            throw new IllegalArgumentException("waitTime and timeType cannot be null");
        }
        long waitTimeValue = Long.parseLong(waitTime);
        switch (timeType.toLowerCase()) {
            case "day":
                return waitTimeValue * 24 * 60 * 60;
            case "hour":
                return waitTimeValue * 60 * 60;
            case "min":
                return waitTimeValue * 60;
            default:
                throw new IllegalArgumentException("Unsupported time type: " + timeType);
        }
    }


    /**
     * mq异步消息通知
     */
    @Override
    public void mqAiTranslateMessageReminding(TranslateLanguageVo languageVo) {
        Integer translateStatus = 2;
        // 定义需要保存的语言
        String translationLanguage = null;
        // 定义需要保存的数据
        String indexName = MESSAGE_REMINDING_INDEX_PREFIX +languageVo.getCompanyId();
        //获取索引
        SearchRequest request = new SearchRequest(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("message_id",languageVo.getMessageId()));
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder).size(10000);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        // 进行查询
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("翻译查询异常：【{}】",e.getMessage());
            throw new RuntimeException(e);
        }
        // 拿出数据,转换返回格式
        SearchHit[] hits = response.getHits().getHits();
        Map<String, Object> source = hits[0].getSourceAsMap();
        MessageRemind messageRemind = new MessageRemind();
        messageRemind.setMessage(source.get("message").toString());
        messageRemind.setCreator((String)source.get("creator"));
        Object transLationtanguageObject = source.get("translation_language");
        if(Objects.nonNull(transLationtanguageObject)){
            translationLanguage = (String) transLationtanguageObject;
        }
        log.info("翻译前已有的翻译语言：【{}】",translationLanguage);
        //用来存重复元素
        List<String> duplicatesLanguage = new ArrayList<>();
        // 处理需要修改语言
        if(StringUtil.isEmpty(translationLanguage)){
            // 如果原语言为null，则直接将现在的加上
            translationLanguage = String.join(",", languageVo.getTranslateList());
        }else{
            // 如果原语言不为null，则转换为set
            Set<String> set = new LinkedHashSet<>(Arrays.asList(translationLanguage.split(",")));
            // 遍历 需要新增语言的list，处理
            for (String item : languageVo.getTranslateList()) {
                if (set.contains(item)) {
                    // 记录重复
                    duplicatesLanguage.add(item);
                } else {
                    // 添加新元素
                    set.add(item);
                }
            }
            log.info("重复翻译语言：【{}】",duplicatesLanguage);
            translationLanguage = String.join(",", set);
            log.info("重新进行翻译语言：【{}】",translationLanguage);
        }
        RpcTranslateLanguageVo rpcTranslateLanguageVo = new RpcTranslateLanguageVo();
        rpcTranslateLanguageVo.setMessage(messageRemind.getMessage());
        try {
            List<MessageRemindES> messageRemindESList = new ArrayList<>();
            for (String language : languageVo.getTranslateList()) {
                rpcTranslateLanguageVo.setTargetLanguage(language);
                R<String> objectR = aigcChatClient.translateMessageReminding(languageVo.getCompanyId(), null, rpcTranslateLanguageVo);
                log.info("打印一下翻译后的内容:{}", objectR);
                if (objectR.getCode() == AjaxResult.SUCCESS && Objects.nonNull(objectR.getData())) {
                    MessageRemindES messageRemindES = new MessageRemindES();
                    JSONObject questions = JSONObject.parseObject(objectR.getData(), JSONObject.class);
                    messageRemindES.setMessageId(UUID.randomUUID().toString());
                    messageRemindES.setFatherMessageId(source.get("message_id").toString());
                    messageRemindES.setCreator((String)source.get("creator"));
                    messageRemindES.setMessageType((Integer) source.get("message_type"));
                    messageRemindES.setTranslationStatus(0);
                    messageRemindES.setMessage(questions.get("message").toString());
                    messageRemindES.setLanguage(language);
                    messageRemindES.setWaitTime((String)source.get("wait_time"));
                    messageRemindES.setTimeType((String) source.get("time_type"));
                    messageRemindES.setBeforeLanguage(source.get("language").toString());
                    messageRemindES.setCreateTime(new Date());
                    messageRemindES.setUpdateTime(new Date());
                    messageRemindESList.add(messageRemindES);
                }
                else{
                    translateStatus = 3;
                    translationLanguage = null;
                }
            }
            if(translateStatus == 2){
                // 在添加前，删除重复语言的数据
                // 如果重复的数组大于0，则进行删除原数据
                if(!duplicatesLanguage.isEmpty()){
                    deleteMessageEs(duplicatesLanguage,languageVo.getMessageId(),indexName);
                    log.info("删除旧语言数据成功");
                }
                batchAddmessageRemindEs(messageRemindESList, indexName);
            }
        }catch (Exception e){
            translateStatus = 3;
            translationLanguage = null;
            log.info("处理翻译报错:",e);
        }
        updateMessageRemindingTranslateStatus(languageVo.getMessageId(), indexName,  translateStatus, translationLanguage);
    }

    private void batchAddmessageRemindEs(List<MessageRemindES> messageRemindESList, String indexName) {
        // 定义批量请求
        BulkRequest bulkRequest = new BulkRequest(indexName);
        // 遍历addAnswerEsVoList，为每一条数据创建一个IndexRequest并添加到bulkRequest中
        for (MessageRemindES addAnswerEmbEsVo : messageRemindESList) {
            // 定义单条索引请求
            IndexRequest singleIndexRequest = new IndexRequest(indexName);
            // 将单个AddAnswerEsVo对象转为json
            String value = null;
            try {
                value = new ObjectMapper().writeValueAsString(addAnswerEmbEsVo);
            } catch (JsonProcessingException e) {
                log.error("翻译子语言插入es转换json异常：【{}】",e.getMessage());
                throw new RuntimeException(e);
            }
            singleIndexRequest.source(value, XContentType.JSON);
            // 添加单条索引请求到批量请求
            bulkRequest.add(singleIndexRequest);
        }
        try {
            // 执行批量索引操作 es版本和spring boot版本有冲突问题，添加成功，返回200，
            BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK")) {
                log.error("es新增文档失败，异常信息：", e);
                throw new RuntimeException(e);
            }
        }
    }

    private void deleteMessageEs(List<String> duplicatesLanguage, String messageId, String indexName) {
        for (String language :duplicatesLanguage) {
            // 创建DeleteByQueryRequest
            DeleteByQueryRequest request = new DeleteByQueryRequest(indexName);
            request.setQuery(QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("father_message_id", messageId))
                    .must(QueryBuilders.matchQuery("language", language)));
            // 执行删除操作
            try {
                BulkByScrollResponse response = restHighLevelClient.deleteByQuery(request, RequestOptions.DEFAULT);
            } catch (IOException e) {
                log.error("删除旧翻译语言报错");
                throw new RuntimeException(e);
            }
        }
    }
}
