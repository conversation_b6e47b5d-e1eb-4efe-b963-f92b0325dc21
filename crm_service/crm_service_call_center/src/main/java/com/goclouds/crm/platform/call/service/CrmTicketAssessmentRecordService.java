package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecord;
import com.goclouds.crm.platform.call.domain.dto.AssessmentBuildDataDTO;
import com.goclouds.crm.platform.call.domain.dto.AssessmentTicketChatListDTO;
import com.goclouds.crm.platform.call.domain.dto.CrmTicketAssessmentRecordPageFormDTO;
import com.goclouds.crm.platform.call.domain.vo.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 智能质检评估记录表 - 业务逻辑类
 */
public interface CrmTicketAssessmentRecordService extends IService<CrmTicketAssessmentRecord> {

    /**
     * 获取工单最新评估记录。
     *
     * @param ticketId           工单ID
     * @param assessorId         评估人ID
     * @param assessmentRecordId 评估记录ID
     * @return 评估记录信息
     */
    AssessmentBriefForRecordVO latestAssessment(String ticketId, String assessorId, String assessmentRecordId);

    /**
     * 获取可用的评估表列表
     *
     * @param ticketType      工单类型
     * @param channelConfigId 渠道ID
     * @return 评估表列表
     */
    List<AssessmentBriefVO> availableAssessments(String ticketType, String channelConfigId);

    /**
     * 获取评估记录详细信息
     *
     * @param recordId 评估记录ID
     * @return 评估记录数据
     */
    AssessmentDetailVO assessmentDetailRecord(String recordId);

    /**
     * 获取评估的规则详情
     *
     * @param recordId 评估记录ID
     * @return 评估信息和规则列表
     */
    AssessmentRecordRuleDetailVO assessmentDetailRule(String recordId);

    /**
     * 评估记录分页列表
     *
     * @param pageParam                   分页参数
     * @param assessmentRecordPageFormDto 查询条件入参
     * @return 评估记录列表
     */
    IPage<CrmTicketAssessmentRecordPageVO> assessmentRecordPages(IPage<Object> pageParam, CrmTicketAssessmentRecordPageFormDTO assessmentRecordPageFormDto);

    /**
     * 评估完成
     *
     * @param recordId 评估记录ID
     * @return True / False
     */
    Boolean assessmentCompleted(String recordId);


    /**
     * 生成评估记录数据并返回评估规则数据
     *
     * @return 评估记录数据
     */
    AssessmentDetailVO generateAssessmentRecord(AssessmentBuildDataDTO assessmentBuildDataDTO);

    /**
     * 开始执行质检任务
     *
     * @param recordId 评估记录ID
     * @return 评估结果true/false
     */
    Boolean startAssessmentTask(String recordId);

    /**
     * 开始执行某条记录中的规则打分任务
     *
     * @param recordRuleItemId 评估记录规则项ID
     * @param ticketChatList   工单聊天记录
     * @return True / False
     */
    Boolean startAssessmentRuleTask(String recordRuleItemId, List<AssessmentTicketChatListDTO> ticketChatList);

    /**
     * 处理更新人工分数，用于更新AI分数事件 - WEBSocket使用
     *
     * @param assessmentRecordId         评估记录ID
     * @param assessmentRecordRuleItemId 评估记录规则项ID
     * @param manualScore                人工分数
     * @param scoreRule                  评估机制（1加分，2减分）
     * @return 总分数
     */
    BigDecimal handleManualScoreUpdated(String assessmentRecordId, String assessmentRecordRuleItemId, BigDecimal manualScore, Integer scoreRule);

    /**
     * 处理更新记录规则项的备注事件 - WEBSocket使用
     *
     * @param assessmentRecordId         评估记录ID
     * @param assessmentRecordRuleItemId 评估记录规则项ID
     * @param assessmentRemark           记录规则备注
     * @return True / False
     */
    Boolean handleAssessmentRemarkAdded(String assessmentRecordId, String assessmentRecordRuleItemId, String assessmentRemark);

    /**
     * 处理人工打分事件 - WEBSocket使用
     *
     * @param assessmentRecordId         评估记录ID
     * @param assessmentRecordRuleItemId 评估记录规则项ID
     * @param manualOptionId             人工选择项ID
     * @param manualOptionName           人工选择项名称
     * @param manualScore                人工分数
     * @param scoreRule                  评估机制（1加分，2减分）
     * @return 总分数
     */
    BigDecimal handleManualScoreSubmitted(String assessmentRecordId, String assessmentRecordRuleItemId, String manualOptionId, String manualOptionName, BigDecimal manualScore, Integer scoreRule);

    /**
     * 查询未评估完成的规则数量
     *
     * @param recordId 评估记录ID
     * @return 未评估的规则数量
     */
    Integer getUnAssessmentRuleCount(String recordId);
}
