package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExt;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_ext(工作记录扩展属性表)】的数据库操作Service
* @createDate 2023-05-25 10:30:27
*/
public interface CrmAgentWorkRecordExtService extends IService<CrmAgentWorkRecordExt> {


    List<String> getQueryWorkId(QueryWrapper<String> queryWrapperExt);
}
