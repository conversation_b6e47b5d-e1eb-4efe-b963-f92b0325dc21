package com.goclouds.crm.platform.call.service.impl;

import com.goclouds.crm.platform.call.domain.WorkRecordFileRequest;
import com.goclouds.crm.platform.call.service.WorkRecordFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.*;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Objects;


/**
 * <AUTHOR> pengliang.sun
 * @description :
 */
@Service
@Slf4j
public class WorkRecordFileServiceImpl implements WorkRecordFileService {

    @Resource
    private RestTemplate restTemplate;

    @Override
    public void workRecordFile(WorkRecordFileRequest request, HttpServletResponse httpResponse) {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.ALL));
        RequestEntity<Void> requestEntity = RequestEntity.get(URI.create(request.getFileUrl()))
                .headers(headers)
                .build();
        try {
            restTemplate.execute(requestEntity.getUrl(), HttpMethod.GET,
                    nu -> {},
                    (ClientHttpResponse clientResponse) -> {
                        // 将远程响应头复制到前端响应
                        // 1. 处理Content-Type
                        MediaType contentType = clientResponse.getHeaders().getContentType();
                        if (contentType != null) {
                            httpResponse.setContentType(contentType.toString());
                        } else {
                            String guessedType = URLConnection.guessContentTypeFromName(request.getFileUrl());
                            httpResponse.setContentType(guessedType != null ? guessedType : "application/octet-stream");
                        }

                        // 2. 处理Content-Disposition
                        String fileName = extractFileName(request.getFileUrl());
                        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");
                        httpResponse.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                                "inline; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);

                        // 3. 流式传输数据
                        try (InputStream input = clientResponse.getBody();
                             OutputStream output = httpResponse.getOutputStream()) {
                            byte[] buffer = new byte[8192];
                            int bytesRead;
                            while ((bytesRead = input.read(buffer)) != -1) {
                                output.write(buffer, 0, bytesRead);
                            }
                        }
                        return null;
                    });

        } catch (RestClientException e) {
            httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try {
                httpResponse.getWriter().write("文件传输失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }


    private String extractFileName(String fileUrl) {
        try {
            return Paths.get(new URL(fileUrl).getPath()).getFileName().toString();
        } catch (MalformedURLException e) {
            return "download";
        }
    }
}
