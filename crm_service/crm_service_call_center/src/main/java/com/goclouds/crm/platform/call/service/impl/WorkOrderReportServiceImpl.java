package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.CrmAwsAccount;
import com.goclouds.crm.platform.call.domain.CrmAwsConnect;
import com.goclouds.crm.platform.call.domain.TicketContentIndex;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.TicketSatisfaction;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.statis.*;
import com.goclouds.crm.platform.call.service.*;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.domain.QueryCondition;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.enums.QueryTypeEnum;
import com.goclouds.crm.platform.common.exception.ServiceException;
import com.goclouds.crm.platform.common.utils.DateUtils;
import com.goclouds.crm.platform.common.utils.ElasticsearchUtils;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.client.channel.ConnectsServiceClient;
import com.goclouds.crm.platform.openfeignClient.client.platform.ConnectClient;
import com.goclouds.crm.platform.openfeignClient.client.system.CompanyClient;
import com.goclouds.crm.platform.openfeignClient.client.system.DeptClient;
import com.goclouds.crm.platform.openfeignClient.client.system.TelephoneBillComputeClient;
import com.goclouds.crm.platform.openfeignClient.client.system.UserClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.channel.ConnectInfoVo;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelConfigVO;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmCustomerSourceChanelVo;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.SearchContactsParam;
import com.goclouds.crm.platform.openfeignClient.domain.system.TelephoneBillComputeVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.TelephoneBillDetailIndex;
import com.goclouds.crm.platform.openfeignClient.domain.system.TelephoneBillSkuDefinitionIndex;
import com.goclouds.crm.platform.openfeignClient.domain.system.UserDetailsVO;
import com.goclouds.crm.platform.utils.ElasticsearchUtil;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.google.common.collect.Lists;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.goclouds.crm.platform.common.domain.QueryCondition.createCondition;

/**
 * @description:
 * @author: sunlinan
 * @date: 2024-06-13 16:56
 **/

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkOrderReportServiceImpl implements WorkOrderReportService {


    @Value("${es.data-detail-index}")
    private String dataDetailsIndex;

    @Value("${es.agent-index}")
    private String agentIndex;

    @Value("${es.queue-index}")
    private String queueIndex;

    @Value("${es.work-record-content-index}")
    private String workContentIndex;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;
    /**
     * 定义kinesis数据存储索引
     */
    private final static String KINESIS_DATA_INDEX_PREFIX = "kinesis_data_index_";

    private final ConnectsServiceClient connectsServiceClient;
    private final ConnectClient connectClient;
    private final RestHighLevelClient restHighLevelClient;
    private final CrmAwsAccountService crmAwsAccountService;
    private final CrmAwsConnectService crmAwsConnectService;
    private final CompanyClient companyClient;
    private final DeptClient deptClient;

    private final ElasticsearchUtil elasticsearchUtil;

    private final CrmAgentWorkRecordSlaDefService crmAgentWorkRecordSlaDefService;

    private final UserClient userClient;

    private final ChannelClient channelClient;
    private final TelephoneBillComputeClient telephoneBillComputeClient;
    private final ElasticsearchUtils elasticsearchUtils;
    private final RedissonClient redissonClient;

    private final static String TELEPHONE_BILL_SKU_INDEX = "telephone_bill_sku_definition_index";
    private final static String TELEPHONE_BILL_DETAIL_INDEX = "telephone_bill_detail_index_";
    private final static String KINESIS_DATA_PHONE_INBOUND = "INBOUND";
    private final static String KINESIS_DATA_PHONE_OUTBOUND = "OUTBOUND";
    private final static String PHONE_SKU_MONTHLY_FEE = "MONTHLY_FEE";

    @Override
    public AjaxResult<Object> kinesisDataMonitor(Object kinesisData) {
        try {
            if(kinesisData == null || kinesisData == ""){
                log.info("处理Kinesis数据获取的接口入参对象是空，数据打印为：{}", kinesisData);
                return null;
            }
            // 将数据转换为json
            String jsonString = JSONUtil.toJsonStr(kinesisData);
            // 转换为Json，取出对应的records
            JSONObject jsonObject = new JSONObject(jsonString);
            // 获取records数组
            JSONArray recordsArray = jsonObject.getJSONArray("records");
            // 遍历数组并提取data字段的值
            for (int i = 0; i < recordsArray.size(); i++) {
                JSONObject record = recordsArray.getJSONObject(i);
                String data = record.get("data").toString();
                byte[] decodedBytes = Base64.getDecoder().decode(data);
                String kinesisJson = new String(decodedBytes);
                // 取出awsAccountId 查询对应的公司ID
                JSONObject awsJson = new JSONObject(kinesisJson);
                String eventType = null;
                // 定义流状态， 1为座席流，2为联系流
                int flowStatus = 1;
                String contactId = null;
                String eventTimestamp = null;
                try {
                    eventType = awsJson.get("EventType").toString();
                    // 判断数据是否为心跳，为心跳则不做处理
                    if(eventType.equals("HEART_BEAT")){
                        log.debug("kinesis,事件,心跳");
                        return null;
                    }
                    eventTimestamp = awsJson.get("EventTimestamp").toString();
//                    log.info("处理Kinesis数据获取的接口入参对象转化为json是：{}", kinesisJson);
                }catch (Exception e){
                    flowStatus = 2;
                    contactId = awsJson.get("ContactId").toString();
                    log.info("没有eventType的数据:{}", kinesisJson);
                }
                String awsAccountId = awsJson.get("AWSAccountId").toString();
                CrmAwsAccount awsAccount = crmAwsAccountService.getById(awsAccountId);
                if(awsAccount == null){
                    return null;
                }
                // 其他类型的，则进行数据保存
                kinesisDataStorage(kinesisJson,awsAccount.getCompanyId(), flowStatus, contactId, eventTimestamp, awsAccountId);
            }

        } catch (Exception e) {
            log.error("查询实时列表数据失败{}", e.getMessage());
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
        return null;
    }

    @Override
    public AjaxResult<Object> kinesisDataClean() {
        try {
            // 定义太平洋时区
            ZoneId pacificZoneId = ZoneId.of("America/Los_Angeles");
            // 获取昨天的0点到今天的0点的时间范围
//            LocalDateTime startTime = LocalDate.now().minusDays(1).atStartOfDay();
//            LocalDateTime endTime = LocalDate.now().atStartOfDay();
            // 获取太平洋时区的开始时间（前一天的开始）
//            ZonedDateTime pacificStartTime = LocalDate.now(pacificZoneId).minusDays(1).atStartOfDay(pacificZoneId);
//            // 获取太平洋时区的结束时间（今天的开始）
//            ZonedDateTime pacificEndTime = LocalDate.now(pacificZoneId).atStartOfDay(pacificZoneId);


            // 获取当前时间在太平洋时区的表示
            ZonedDateTime pacificCurrentTime = ZonedDateTime.now(pacificZoneId);
            // 获取前六个小时的时间在太平洋时区的表示
            ZonedDateTime pacificPreviousDayTime = pacificCurrentTime.minusHours(6);

            // 格式化日期时间
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 查询出所有的awsConnect
            List<CrmAwsConnect> connectList = crmAwsConnectService.list(new QueryWrapper<CrmAwsConnect>().lambda().eq(CrmAwsConnect::getDataStatus, 1));
            // 获取到所有的公司ID 方便查询统计数据
            R<List<String>> listR = companyClient.allCompanyList();
            if (listR.getCode() == AjaxResult.SUCCESS && Objects.nonNull(listR.getData())) {
//                // 循环查询对应公司的ES数据
                for (String companyId : listR.getData()){
                    // 获取到索引名称
                    String indexName = embIndexName(companyId);
                    // 查询索引是否存在
                    boolean indexExists = headIndexExists(indexName);
                    // 索引不存在直接跳过当前循环
                    if (!indexExists) {
                        continue;
                    }
                    // 定义存储座席纬度所需数据
                    List<AgentTimeVo> agentTimeVoList = new ArrayList<>();
                    // 定义队列时长数据
                    List<QueueTimeVo> queueTimeList = new ArrayList<>();
                    // 定义放弃队列数据
                    List<QueueTimeVo> withdrawList = new ArrayList<>();
                    // 保存数据明细
                    dataDetailedSave(pacificPreviousDayTime, pacificCurrentTime, indexName, companyId, formatter, agentTimeVoList, queueTimeList, withdrawList, connectList);
                    // 保存座席数据
                    agentDataSave(pacificPreviousDayTime, pacificCurrentTime, indexName, companyId, formatter,agentTimeVoList);
                    // 队列保存
                    queueDataSave(queueTimeList, withdrawList,companyId);
                }
            }
            log.debug("kinesis数据清洗成功");
        }catch (Exception e){
            log.error("kinesis数据清洗报错:{0}", e);
        }


        return null;
    }



    /**
     *  座席表格数据存储
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param indexName 公司索引名称
     * @param companyId 公司ID
     * @param formatter 时间格式化类型
     * @param agentTimeVoList 座席通话计算基本数据
     * @throws Exception
     */
    private void agentDataSave(ZonedDateTime startTime, ZonedDateTime endTime, String indexName, String companyId, DateTimeFormatter formatter,List<AgentTimeVo> agentTimeVoList) throws Exception{
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 创建RangeQueryBuilder实例并设置范围条件
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("EventTimestamp")
                .gte(startTime)
                .lte(endTime);
        boolQueryBuilder.must(rangeQueryBuilder);
        // 查询更换状态为关闭的
        boolQueryBuilder.must(QueryBuilders.termQuery("CurrentAgentSnapshot.AgentStatus.Type.keyword", "OFFLINE"));
        // 将BoolQueryBuilder设置到SearchSourceBuilder
        searchSourceBuilder.query(boolQueryBuilder);
        // 将SearchSourceBuilder设置到SearchRequest
        request.source(searchSourceBuilder);
        SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        // 取出查询数据
        SearchHit[] hits = response.getHits().getHits();
        // 定义在线时长
        List<AgentTimeVo> agentTimeList = new ArrayList<>();
        for (SearchHit hit : hits) {
            String sourceString = hit.getSourceAsString();
            AgentTimeVo agentTimeVo = new AgentTimeVo();
            // 使用org.json库解析JSON字符串
            JSONObject jsonObject = new JSONObject(sourceString);
            // 开始时间
            JSONObject previousAgentSnapshot = jsonObject.getJSONObject("PreviousAgentSnapshot");
            JSONObject previousAgentStatus = previousAgentSnapshot.getJSONObject("AgentStatus");
            String previousTimestamp = previousAgentStatus.get("StartTimestamp").toString();
            ZonedDateTime previousTimes = ZonedDateTime.parse(previousTimestamp, DateTimeFormatter.ISO_ZONED_DATE_TIME);
            // 结束时间
            JSONObject CurrentAgentSnapshot = jsonObject.getJSONObject("CurrentAgentSnapshot");
            JSONObject currentAgentStatus = CurrentAgentSnapshot.getJSONObject("AgentStatus");
            String currentStartTimestamp = currentAgentStatus.get("StartTimestamp").toString();
            ZonedDateTime currentStartTime = ZonedDateTime.parse(currentStartTimestamp, DateTimeFormatter.ISO_ZONED_DATE_TIME);
            // 计算两个日期时间之间的持续时间
            Duration duration = Duration.between(previousTimes, currentStartTime);
//            statDataDetails.setTotal_time(String.format("%02d:%02d:%02d", duration.toHours(), duration.toMinutes() % 60, duration.getSeconds() % 60));
            JSONObject configuration = CurrentAgentSnapshot.getJSONObject("Configuration");
            agentTimeVo.setAWSAccountId(jsonObject.get("AWSAccountId").toString());
            agentTimeVo.setAgentName(configuration.get("FirstName").toString());
            agentTimeVo.setDuration(duration);
            agentTimeList.add(agentTimeVo);
        }
        // 将通话联系数据，进行汇总，汇总为座席所需要的
        List<AgentAccumulateTimeVo> accumulateTimeList = contactDataSummary(agentTimeVoList);

        // 将座席更新状态的数据 根据 AWSAccountId 进行分组
        Map<String, List<AgentTimeVo>> groupedByAWSAccountId = agentTimeList.stream()
                .collect(Collectors.groupingBy(AgentTimeVo::getAWSAccountId));
        // 计算在线时长
        groupedByAWSAccountId.forEach((awsAccountId, list) -> {
            // 计算累计在线时长
            Duration duration = Duration.ZERO;
            for (AgentTimeVo agentTimeVo: list){
                duration = duration.plus(agentTimeVo.getDuration());
            }
            // 存储到对应的对象中
            for(AgentAccumulateTimeVo accumulateTimeVo : accumulateTimeList){
                if(awsAccountId.equals(accumulateTimeVo.getAWSAccountId())){
                    accumulateTimeVo.setDuration(duration);
                }
            }
        });
        // 计算座席所展示数据，进行保存到ES
        calculateAgentData(accumulateTimeList, companyId);

    }


    /**
     *  将通话联系数据，进行汇总，汇总为座席所需要的
     * @param agentTimeVoList 通话数据
     * @return 座席所需要基本数据
     */
    private List<AgentAccumulateTimeVo> contactDataSummary(List<AgentTimeVo> agentTimeVoList){
        // 将通话时间的数据进行分组
        // 根据 AWSAccountId 进行分组
        Map<String, List<AgentTimeVo>> groupedByAccountId = agentTimeVoList.stream()
                .collect(Collectors.groupingBy(AgentTimeVo::getAWSAccountId));
        List<AgentAccumulateTimeVo> accumulateTimeList = new ArrayList<>();
        // 打印分组结果
        groupedByAccountId.forEach((awsAccountId, list) -> {
            AgentAccumulateTimeVo agentAccumulateTimeVo = new AgentAccumulateTimeVo();
            agentAccumulateTimeVo.setAWSAccountId(awsAccountId);
            // 存储实例别名
            agentAccumulateTimeVo.setConnectAlias(list.get(0).getConnectAlias());
            agentAccumulateTimeVo.setAgentName(list.get(0).getAgentName());
            // 定义累计接待时长
            // 创建两个 Duration 对象
            // 累计接待时长
            Duration receptionDuration = Duration.ZERO;
            // 累计ACW时长
            Duration acwDuration = Duration.ZERO;
            for (AgentTimeVo agentTimeVo: list){
                receptionDuration = receptionDuration.plus(agentTimeVo.getReceptionDuration());
                acwDuration = acwDuration.plus(agentTimeVo.getAcwDuration());
            }
            agentAccumulateTimeVo.setReceptionDuration(receptionDuration);
            agentAccumulateTimeVo.setAcwDuration(acwDuration);
            // TODO 不能直接根据数据来判断次数， 这个为排队人数
            agentAccumulateTimeVo.setReceptionNumber(list.size());
            // 将  acw时长 转换为秒数
            long totalAcwDuration = acwDuration.getSeconds();
            // 时长除以次数即为平均avg时间
            agentAccumulateTimeVo.setAvgAcwDuration(Duration.ofSeconds( totalAcwDuration / list.size()));
            accumulateTimeList.add(agentAccumulateTimeVo);
        });
        return accumulateTimeList;
    }

    /**
     *  计算座席所展示数据，进行保存到ES
     * @param accumulateTimeList 获取到的座席基本数据
     * @param companyId 公司ID
     * @throws Exception
     */
    private void calculateAgentData(List<AgentAccumulateTimeVo> accumulateTimeList, String companyId) throws Exception{
        // 定义批量请求
        BulkRequest bulkRequest = new BulkRequest(agentIndex);
        // 开始计算，保存到ES中
        for(AgentAccumulateTimeVo accumulateTimeVo : accumulateTimeList){
            KinesisAgentDataVO kinesisAgentDataVO = new KinesisAgentDataVO();
            kinesisAgentDataVO.setAgent_name(accumulateTimeVo.getAgentName());
            kinesisAgentDataVO.setConnect_alias(accumulateTimeVo.getConnectAlias());
            Duration duration = accumulateTimeVo.getDuration();
            if(duration!=null){
                // 在线时间
                kinesisAgentDataVO.setAccumulated_online_duration(duration.getSeconds());
                Duration durationMinus = duration.minus(accumulateTimeVo.getReceptionDuration());
                if(accumulateTimeVo.getAcwDuration()!=null){
                    // 计算出空闲时间
                    Duration idleDuration = durationMinus.minus(accumulateTimeVo.getAcwDuration());
                    kinesisAgentDataVO.setAccumulated_idle_duration(idleDuration.getSeconds());
                }else{
                    kinesisAgentDataVO.setAccumulated_idle_duration(0);
                }

                // 累计接待时长
                kinesisAgentDataVO.setAccumulated_reception_duration(accumulateTimeVo.getReceptionDuration()==null?0:accumulateTimeVo.getReceptionDuration().getSeconds());
                // TODO 未响应数量暂时没获取到
                kinesisAgentDataVO.setUnresponsive_quantity(0);
                // 接待联系人数量
                kinesisAgentDataVO.setReception_contacts_quantity(accumulateTimeVo.getReceptionNumber()==null?0:accumulateTimeVo.getReceptionNumber());
                // 平均联系后续工作时间
                kinesisAgentDataVO.setAvg_working_hours_after_contact(accumulateTimeVo.getAvgAcwDuration()==null?0:accumulateTimeVo.getAvgAcwDuration().getSeconds());
                if(accumulateTimeVo.getReceptionDuration()!=null&&accumulateTimeVo.getReceptionNumber()!=null){
                    // 平均客户保持时间
                    long seconds = accumulateTimeVo.getReceptionDuration().getSeconds();
                    long customerRetentionLong = seconds / accumulateTimeVo.getReceptionNumber();
                    Duration customerRetentionDuration = Duration.ofSeconds(customerRetentionLong);
                    kinesisAgentDataVO.setAvg_customer_retention_time(customerRetentionDuration.getSeconds());
                }else{
                    kinesisAgentDataVO.setAvg_customer_retention_time(0);
                }

                kinesisAgentDataVO.setCreate_time((System.currentTimeMillis()-3600000) / 1000);
                kinesisAgentDataVO.setModify_time((System.currentTimeMillis()-3600000) / 1000);
                kinesisAgentDataVO.setCompany_id(companyId);
                // 将单个AddAnswerEsVo对象转为json
                String value = new ObjectMapper().writeValueAsString(kinesisAgentDataVO);
                // 定义单条索引请求
                IndexRequest singleIndexRequest = new IndexRequest(agentIndex);
                singleIndexRequest.source(value, XContentType.JSON);
                // 添加单条索引请求到批量请求
                bulkRequest.add(singleIndexRequest);
            }
        }
        if (bulkRequest.numberOfActions() != 0) {
            // 进行批量保存ES
            batchSaveEs(bulkRequest);
        }
    }

    private void calculateQueueData(List<QueueAccumulateTimeVo> accumulateTimeList, String companyId) throws Exception{
        // 定义批量请求
        BulkRequest bulkRequest = new BulkRequest(queueIndex);
        for (QueueAccumulateTimeVo queueAccumulateTime :accumulateTimeList){
            KinesisQueueDataVO kinesisQueueDataVO = new KinesisQueueDataVO();
            kinesisQueueDataVO.setQueue_name(queueAccumulateTime.getQueueName());
            kinesisQueueDataVO.setConnect_alias(queueAccumulateTime.getConnectAlias());
            kinesisQueueDataVO.setAvg_working_hours_after_contact(queueAccumulateTime.getAvgAcwDuration()==null?0:queueAccumulateTime.getAvgAcwDuration().getSeconds());
            kinesisQueueDataVO.setAvg_agent_interaction_time(queueAccumulateTime.getAvgAgentInteractionDuration()==null?0:queueAccumulateTime.getAvgAgentInteractionDuration().getSeconds());
            kinesisQueueDataVO.setAvg_customer_retention_time(queueAccumulateTime.getCustomMaintainDuration()==null?0:queueAccumulateTime.getCustomMaintainDuration().getSeconds());
            kinesisQueueDataVO.setAvg_queue_abandonment_time(queueAccumulateTime.getQueueAbandonmentDuration()==null?0:queueAccumulateTime.getQueueAbandonmentDuration().getSeconds());
            kinesisQueueDataVO.setAvg_queue_waiting_time(queueAccumulateTime.getQueueWaitDuration()==null?0:queueAccumulateTime.getQueueWaitDuration().getSeconds());
            kinesisQueueDataVO.setAbandon_contact_quantity(queueAccumulateTime.getAbandonmentCount()==null?0:queueAccumulateTime.getAbandonmentCount());
            kinesisQueueDataVO.setQueued_contacts_quantity(queueAccumulateTime.getWaitNumber());
            kinesisQueueDataVO.setCreate_time((System.currentTimeMillis()-3600000) / 1000);
            kinesisQueueDataVO.setModify_time((System.currentTimeMillis()-3600000) / 1000);
            kinesisQueueDataVO.setCompany_id(companyId);
            // 将单个AddAnswerEsVo对象转为json
            String value = new ObjectMapper().writeValueAsString(kinesisQueueDataVO);
            // 定义单条索引请求
            IndexRequest singleIndexRequest = new IndexRequest(queueIndex);
            singleIndexRequest.source(value, XContentType.JSON);
            // 添加单条索引请求到批量请求
            bulkRequest.add(singleIndexRequest);
        }
        if (bulkRequest.numberOfActions() != 0) {
            // 进行批量保存ES
            batchSaveEs(bulkRequest);
        }
    }

    /**
     * 统计队列数据
     * @param queueTimeList 接听队列数据
     * @param withdrawList 放弃队列数据
     * @param companyId 公司ID
     * @throws Exception
     */
    private void queueDataSave(List<QueueTimeVo> queueTimeList, List<QueueTimeVo> withdrawList, String companyId) throws Exception{
        // 定义存储队列数据所需List
        List<QueueAccumulateTimeVo> accumulateTimeList = new ArrayList<>();
        // 进行统计队列数据
        // 根据 队列名称 进行分组
        Map<String, List<QueueTimeVo>> groupedByQueueTimeList = queueTimeList.stream()
                .collect(Collectors.groupingBy(QueueTimeVo::getQueueId));

        // 根据 队列名称 进行分组
        Map<String, List<QueueTimeVo>> groupedByWithdrawList = withdrawList.stream()
                .collect(Collectors.groupingBy(QueueTimeVo::getQueueId));
        // 打印分组结果
        groupedByQueueTimeList.forEach((queueId, list) -> {
            QueueAccumulateTimeVo accumulateTime = new QueueAccumulateTimeVo();
            // 定义累计接待时长
            Duration receptionDuration = Duration.ZERO;
            // 累计ACW时长
            Duration acwDuration = Duration.ZERO;
            // 定义累计等待时间
            Duration waitDuration = Duration.ZERO;
            for(QueueTimeVo queueTime :list){
                receptionDuration = receptionDuration.plus(queueTime.getReceptionDuration());
                acwDuration = acwDuration.plus(queueTime.getAcwDuration());
                waitDuration = waitDuration.plus(queueTime.getWaitDuration());
            }
            // 循环放弃的数据
            groupedByWithdrawList.forEach((accumulateQueueId, accumulateList) -> {
                // 如果名称相等，则进行存储
                if(accumulateQueueId.equals(queueId)){
                    // 累计放弃时长
                    Integer withdrawSecond = 0;
                    for(QueueTimeVo queueTime :accumulateList){
                        withdrawSecond = withdrawSecond +queueTime.getWithdrawSecond();
                    }
                    // 将放弃时长除以放弃人数，计算出平均放弃时间
                    Duration duration = Duration.ZERO;
                    if(withdrawSecond != 0){
                        duration = Duration.ofSeconds(withdrawSecond / accumulateList.size());
                    }
                    accumulateTime.setAbandonmentCount(accumulateList.size());
                    accumulateTime.setQueueAbandonmentDuration(duration);
                }
            });
            // 取出累计acw的秒数
            long acwDurationSeconds = acwDuration.getSeconds();
            Duration avgAcwduration = Duration.ofSeconds(acwDurationSeconds / list.size());
            // 取出累计接待的秒数(客户互动)
            long receptionDurationSeconds = receptionDuration.getSeconds();
            Duration avgReceptionDuration = Duration.ofSeconds(receptionDurationSeconds / list.size());
            // 取出累计等待秒数
            long waitDurationSeconds = waitDuration.getSeconds();
            Duration avgwaitDuration = Duration.ofSeconds(waitDurationSeconds / list.size());
            accumulateTime.setQueueName(list.get(0).getQueueName());
            accumulateTime.setConnectAlias(list.get(0).getConnectAlias());
            accumulateTime.setAvgAcwDuration(avgAcwduration);
            // 累计互动秒数除以座席数量即为座席平均互动描述
            Map<String, List<QueueTimeVo>> collect = list.stream().collect(Collectors.groupingBy(QueueTimeVo::getAgentName));
            Duration duration = Duration.ofSeconds(receptionDurationSeconds / collect.size());
            accumulateTime.setAvgAgentInteractionDuration(duration);
            accumulateTime.setCustomMaintainDuration(avgReceptionDuration);
            accumulateTime.setQueueWaitDuration(avgwaitDuration);
            accumulateTime.setWaitNumber(list.size());
            accumulateTimeList.add(accumulateTime);
        });
        // 计算保存到ES中
        calculateQueueData(accumulateTimeList, companyId);
    }

    /**
     *  数据明细 存储
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param indexName 索引名称
     * @param companyId 公司ID
     * @param formatter 格式化时间
     * @throws Exception
     */
    private void dataDetailedSave(ZonedDateTime startTime, ZonedDateTime endTime, String indexName, String companyId, DateTimeFormatter formatter, List<AgentTimeVo> agentTimeList, List<QueueTimeVo> queueTimeList, List<QueueTimeVo> withdrawList, List<CrmAwsConnect> connectList) throws Exception{
        // 定义本地时区
        ZoneId localZone = ZoneId.systemDefault();

        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 创建RangeQueryBuilder实例并设置范围条件
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("Queue.DequeueTimestamp")
                .gte(startTime)
                .lte(endTime);
        // 将RangeQueryBuilder添加到BoolQueryBuilder
        boolQueryBuilder.must(rangeQueryBuilder);
        // 将BoolQueryBuilder设置到SearchSourceBuilder
        searchSourceBuilder.query(boolQueryBuilder);
        // 将SearchSourceBuilder设置到SearchRequest
        request.source(searchSourceBuilder);
        SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        // 取出查询数据
        SearchHit[] hits = response.getHits().getHits();
        // 定义批量请求
        BulkRequest bulkRequest = new BulkRequest(dataDetailsIndex);
        for (SearchHit hit : hits) {
            // 定义单条索引请求
            IndexRequest singleIndexRequest = new IndexRequest(dataDetailsIndex);
            KinesisStatDataDetailsVO statDataDetails = new KinesisStatDataDetailsVO();
            String sourceString = hit.getSourceAsString();
            // 使用org.json库解析JSON字符串
            JSONObject jsonObject = new JSONObject(sourceString);
            if(!jsonObject.containsKey("Agent")){
                JSONObject queue = jsonObject.getJSONObject("Queue");
                // 放弃时长
                String withdrawDuration = queue.get("Duration").toString();
                QueueTimeVo queueTimeVo = new QueueTimeVo();
                queueTimeVo.setWithdrawSecond(Integer.valueOf(withdrawDuration));
                // 队列名称
                String queueName = queue.get("Name").toString();
                // 取出队列arn
                String queueArn = queue.get("ARN").toString();
                // 拆分字符串并提取UUID
                String[] parts = queueArn.split("/");
                // 获取到实例ID
                String instanceId = parts[1];
                // 获取到队列ID
                String queueId = parts[3];
                queueTimeVo.setQueueName(queueName);
                queueTimeVo.setQueueId(queueId);
                Optional<CrmAwsConnect> matchedConnect = connectList.stream()
                        .filter(connect -> instanceId.equals(connect.getConnectId()))
                        .findFirst();
                String connectAlias = null;
                if (matchedConnect.isPresent()) {
                    CrmAwsConnect connect = matchedConnect.get();
                    connectAlias = connect.getConnectAlias();
                }
                queueTimeVo.setConnectAlias(connectAlias);
                withdrawList.add(queueTimeVo);
                continue;
            }
            JSONObject agent = jsonObject.getJSONObject("Agent");
            // 联系ID
            statDataDetails.setContact_id(jsonObject.get("ContactId").toString());
            // 呼入时间
            String agentTimestamp = agent.get("ConnectedToAgentTimestamp").toString();
            ZonedDateTime agentTimestampTime = ZonedDateTime.parse(agentTimestamp, DateTimeFormatter.ISO_ZONED_DATE_TIME);
            // 将 ZonedDateTime 对象转换为本地时区的 ZonedDateTime 对象
            ZonedDateTime localAgentTimestampTime = agentTimestampTime.withZoneSameInstant(localZone);
            statDataDetails.setCall_time(localAgentTimestampTime.format(formatter));
            // 挂断时间
            String startTimestamp = agent.get("AfterContactWorkStartTimestamp").toString();
            ZonedDateTime startTimestampTime = ZonedDateTime.parse(startTimestamp, DateTimeFormatter.ISO_ZONED_DATE_TIME);
            // 将 ZonedDateTime 对象转换为本地时区的 ZonedDateTime 对象
            ZonedDateTime localStartTimestampTime = startTimestampTime.withZoneSameInstant(localZone);
            statDataDetails.setEnd_time(localStartTimestampTime.format(formatter));
            // ACW时间
            String endTimestamp = agent.get("AfterContactWorkEndTimestamp").toString();
            ZonedDateTime endTimestampTime = ZonedDateTime.parse(endTimestamp, DateTimeFormatter.ISO_ZONED_DATE_TIME);
            // 将 ZonedDateTime 对象转换为本地时区的 ZonedDateTime 对象
            ZonedDateTime localEndTimestampTime = endTimestampTime.withZoneSameInstant(localZone);
            statDataDetails.setAcw_time(localEndTimestampTime.format(formatter));
            // 通话时长
            // 计算两个日期时间之间的持续时间
            Duration duration = Duration.between(agentTimestampTime,startTimestampTime);
            statDataDetails.setTotal_time(String.format("%02d:%02d:%02d", duration.toHours(), duration.toMinutes() % 60, duration.getSeconds() % 60));
            // 互动时长 秒
            Integer agentInteractionDuration = Integer.valueOf(agent.get("AgentInteractionDuration").toString());
            Duration agentInterDuration = Duration.ofSeconds(agentInteractionDuration);
            statDataDetails.setInteraction_time(String.format("%02d:%02d:%02d", agentInterDuration.toHours(), agentInterDuration.toMinutes() % 60, agentInterDuration.getSeconds() % 60));
            // 队列等待时长 秒
            Integer customerHold = Integer.valueOf(agent.get("CustomerHoldDuration").toString());
            Duration customerHoldDuration = Duration.ofSeconds(customerHold);
            statDataDetails.setQueue_wait_time(String.format("%02d:%02d:%02d", customerHoldDuration.toHours(), customerHoldDuration.toMinutes() % 60, customerHoldDuration.getSeconds() % 60));
            // 呼入渠道
            String channel = jsonObject.get("Channel").toString();
            statDataDetails.setCall_channel(channel);
            // 接待座席
            String userName = agent.get("Username").toString();
            statDataDetails.setReception_agent(userName);
            // TODO 座席组 没数据
            agent.get("HierarchyGroups");
            // 队列
            JSONObject queue = jsonObject.getJSONObject("Queue");
            // 队列名称
            String queueName = queue.get("Name").toString();
            statDataDetails.setQueue_name(queueName);
            // 取出队列arn
            String queueArn = queue.get("ARN").toString();
            // 拆分字符串并提取UUID
            String[] parts = queueArn.split("/");
            // 获取到实例ID
            String instanceId = parts[1];
            // 获取到队列ID
            String queueId = parts[3];
            Optional<CrmAwsConnect> matchedConnect = connectList.stream()
                    .filter(connect -> instanceId.equals(connect.getConnectId()))
                    .findFirst();
            String connectAlias = null;
            if (matchedConnect.isPresent()) {
                CrmAwsConnect connect = matchedConnect.get();
                connectAlias = connect.getConnectAlias();
            }
            // ACW时长 秒
            Integer afterContactWork = Integer.valueOf(agent.get("AfterContactWorkDuration").toString());
            Duration afterContactWorkDuration = Duration.ofSeconds(afterContactWork);
            statDataDetails.setAcw_duration(String.format("%02d:%02d:%02d", afterContactWorkDuration.toHours(), afterContactWorkDuration.toMinutes() % 60, afterContactWorkDuration.getSeconds() % 60));
            // 根据客户标识查询对应工单编号
            String contactId = jsonObject.get("ContactId").toString();
//            WorkRecordDetailVo workRecordDetail =  workRecordService.queryContactId(contactId);
//            // 查询到对应的工单编号
//            if(workRecordDetail != null){
//                statDataDetails.setWork_order_number(workRecordDetail.getWordRecordCode());
//            }
            statDataDetails.setCreate_time((System.currentTimeMillis()-3600000) / 1000);
            statDataDetails.setModify_time((System.currentTimeMillis()-3600000) / 1000);
            statDataDetails.setCompany_id(companyId);
            statDataDetails.setConnect_alias(connectAlias);
            // 延迟时间
            Integer holdDuration = Integer.valueOf(agent.get("LongestHoldDuration").toString());
            Duration onHoldDuration = Duration.ofSeconds(holdDuration);
            statDataDetails.setOn_hold_time(String.format("%02d:%02d:%02d", onHoldDuration.toHours(), onHoldDuration.toMinutes() % 60, onHoldDuration.getSeconds() % 60));
            // 延迟次数
            String  numberOfHolds = agent.get("NumberOfHolds").toString();
            statDataDetails.setOn_hold_number(numberOfHolds);
            // 是否为转接
            String initiationMethod = jsonObject.get("InitiationMethod").toString();
            // 如果为TRANSFER，则就是转接
            if(initiationMethod.equals("TRANSFER")){
                statDataDetails.setIs_switch("1");
            }else{
                statDataDetails.setIs_switch("0");
            }
            // 挂断类型
            String disconnectReason = jsonObject.get("DisconnectReason").toString();
            statDataDetails.setHanging_type(disconnectReason);

            // 将单个AddAnswerEsVo对象转为json
            String value = new ObjectMapper().writeValueAsString(statDataDetails);
            singleIndexRequest.source(value, XContentType.JSON);
            // 添加单条索引请求到批量请求
            bulkRequest.add(singleIndexRequest);
            AgentTimeVo agentTimeVo = new AgentTimeVo();
            agentTimeVo.setAWSAccountId(jsonObject.get("AWSAccountId").toString());
            agentTimeVo.setReceptionDuration(duration);
            agentTimeVo.setAcwDuration(afterContactWorkDuration);
            agentTimeVo.setConnectAlias(connectAlias);
            agentTimeVo.setAgentName(userName);
            agentTimeList.add(agentTimeVo);
            QueueTimeVo queueTimeVo = new QueueTimeVo();
            queueTimeVo.setConnectAlias(connectAlias);
            queueTimeVo.setQueueId(queueId);
            queueTimeVo.setQueueName(queueName);
            queueTimeVo.setAcwDuration(afterContactWorkDuration);
            queueTimeVo.setReceptionDuration(duration);
            queueTimeVo.setWaitDuration(customerHoldDuration);
            queueTimeVo.setAgentName(userName);
            queueTimeList.add(queueTimeVo);
        }
        if (bulkRequest.numberOfActions() != 0) {
            // 进行批量保存ES
            batchSaveEs(bulkRequest);
        }
    }

    /**
     *  进行批量保存ES数据
     * @param bulkRequest 保存数据及索引
     */
    private void batchSaveEs(BulkRequest bulkRequest){
        try {
            // 执行批量索引操作 es版本和spring boot版本有冲突问题，添加成功，返回200，
            BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK")&&!e.getMessage().contains("201")) {
                log.error("es新增文档失败，异常信息：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        }
    }

    private void kinesisDataStorage(String kinesisJson, String companyId, int flowStatus, String contactId, String eventTimestamp, String awsAccountId) throws Exception{
        // 获取到索引名称
//        String indexName = embIndexName(companyId);
        String indexName = embIndexName(companyId);
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        if (!indexExists) {
            createIndex(indexName);
        }
        // 定义单条索引请求
        IndexRequest indexRequest = new IndexRequest(indexName);
        indexRequest.source(kinesisJson, XContentType.JSON);
        // 保存数据
        try {
            // 创建SearchRequest实例
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            // 如果是座席流，则可以直接保存，如果是联系流，则需要判断数据是否存在，存在则进行更新
            if(flowStatus == 1){
                searchSourceBuilder.query(QueryBuilders.termQuery("AWSAccountId", awsAccountId));
                searchSourceBuilder.query(QueryBuilders.termQuery("EventTimestamp", eventTimestamp));
                searchRequest.source(searchSourceBuilder);
            }else{
                searchSourceBuilder.query(QueryBuilders.termQuery("ContactId.keyword", contactId));
                searchRequest.source(searchSourceBuilder);
            }
            // 执行查询
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            if (searchResponse.getHits().getTotalHits().value > 0) {
                // 文档存在，执行更新操作
                String documentId = searchResponse.getHits().getHits()[0].getId();
                UpdateRequest updateRequest = new UpdateRequest(indexName, documentId);
                updateRequest.doc(kinesisJson, XContentType.JSON);
                restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
            } else {
                // 文档不存在，执行新增操作
                restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            }
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK")&&!e.getMessage().contains("201")) {
                log.error("es新增文档失败，异常信息：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 向量索引-拼接索引名称
     *
     * @return 索引名称
     */
    private String embIndexName(String companyId) {
        return KINESIS_DATA_INDEX_PREFIX + companyId;
    }
    private boolean headIndexExists(String index) throws IOException {
        GetIndexRequest req = new GetIndexRequest(index);
        boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
        log.info("当前索引{}, 是否存在: {}", index, exists);
        return exists;
    }

    private void createIndex(String indexName) throws IOException {
        CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
        CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
        boolean acknowledged = createIndexResponse.isAcknowledged();
        log.info("创建索引完成：{}", acknowledged);
    }



    @Override
    public void kinesisData(Object kinesisData) {
        JSONArray recordsArray = getObjects(kinesisData);
        if (recordsArray == null) return;

        // 遍历数组并提取data字段的值
        for (int i = 0; i < recordsArray.size(); i++) {
            JSONObject awsJson = getEntries(recordsArray, i);
//            JSONObject record = recordsArray.getJSONObject(i);
//            String data = record.get("data").toString();
//            byte[] decodedBytes = Base64.getDecoder().decode(data);
//            String kinesisJson = new String(decodedBytes);
//            // 取出awsAccountId 查询对应的公司ID
//            JSONObject awsJson = new JSONObject(kinesisJson);
//            log.info("kinesis,联系流数据：{}",awsJson);
            log.info("kinesis,联系流数据：{}",awsJson);
            String awsAccountId = awsJson.get("AWSAccountId").toString();
            String contactId = awsJson.get("ContactId") != null ? awsJson.get("ContactId").toString() : null;
            if(contactId == null || contactId.equals("null")){
                log.info("kinesis, 没有ContactId，可能为Agent事件，不进行处理！");
                return;
            }
//                log.info("kinesis, Agent: {}", awsJson.get("Agent"));
            CrmAwsAccount awsAccount = crmAwsAccountService.getById(awsAccountId);
            // 1. 时间
//            String lastUpdateTimestamp = awsJson.get("LastUpdateTimestamp").toString();
            String index = "kinesis_contact_details_" + awsAccount.getCompanyId();
//                log.info("kinesis, lastUpdateTimestamp :{}",lastUpdateTimestamp);
            KinesisContactDetailsVo detailsVo = dataHandle(awsJson, awsAccount);
                log.info("kinesis, detailsVo :{}", com.alibaba.fastjson.JSONObject.toJSONString(detailsVo));
            try {
                // 验证索引是否存在，不存就创建
                if (!headIndexExists(index)){
                    createDataIndex(index);
                }
                KinesisContactDetailsVo esContactDetails = getContactDetails(index,detailsVo.getContactId());
                if (esContactDetails == null) { // es中不存在，直接添加
                    log.info("第一次添加联络明细,channelTypeId不存在，需要设置默认值电话类型：7----getDisconnectTimestamp 时间则为 挂断时间。endTime");
                    detailsVo.setChannelTypeId("7");
                    detailsVo.setEndTime(detailsVo.getDisconnectTimestamp());
                    // 定义单条索引请求
                    IndexRequest indexRequest = new IndexRequest(index);
                    indexRequest.source(new JSONObject(detailsVo), XContentType.JSON);
                    // 文档不存在，执行新增操作
                    restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
                } else {
                    // 存在，验证数据，然后添加
                    Date lastUpdateTimestamp1 = esContactDetails.getLastUpdateTimestamp();
//                        log.info("更新时间对比：lastUpdateTimestamp1:{},time:{}",lastUpdateTimestamp1, detailsVo.getLastUpdateTimestamp());
                    // 修改数据
                    if (lastUpdateTimestamp1 == null) {
                        log.info("第一次更新联络明细,lastUpdateTimestamp1为null，直接更新");
                        UpdateRequest updateRequest = new UpdateRequest(index, esContactDetails.getDocumentId());
                        updateRequest.doc(new JSONObject(detailsVo) , XContentType.JSON);
                        restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
                    }
                    if (lastUpdateTimestamp1.compareTo(detailsVo.getLastUpdateTimestamp()) < 0) {
                        log.info("更新联络明细");
                        UpdateRequest updateRequest = new UpdateRequest(index, esContactDetails.getDocumentId());
                        updateRequest.doc(new JSONObject(detailsVo) , XContentType.JSON);
                        restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
                    }
                }
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK")&&!e.getMessage().contains("201")) {
                    log.error("es新增文档失败，异常信息：{}", e.getMessage());
                    throw new RuntimeException(e);
                }
            }

            // 电话计费  仅记录voice
            // 因为contactId会推送多次 这里计费就会按照最后一次为准
            ContactDetailVo contactDetailsVo = new ContactDetailVo()
                    .setCompanyId(detailsVo.getCompanyId())
                    .setContactId(contactId)
                    .setConnectAlias(detailsVo.getConnectAlias())
                    .setSystemPhone(detailsVo.getSystemPhone())
                    .setCustomerPhone(detailsVo.getCustomerPhone())
                    .setIncomingOutgoing(detailsVo.getIncomingOutgoing())
                    .setStartTime(detailsVo.getStartTime())
                    .setEndTime(detailsVo.getDisconnectTimestamp());
            log.info("========>>>>>> 电话计费参数: {}", contactDetailsVo);
            if ("VOICE".equals(detailsVo.getCallChannel())) {
                computePhoneBill(contactDetailsVo);
            }
        }
    }

    private static JSONArray getObjects(Object kinesisData) {
        if(kinesisData == null || kinesisData == ""){
            log.info("处理Kinesis数据获取的接口入参对象是空，数据打印为：{}", kinesisData);
            return null;
        }
        // 将数据转换为json
        String jsonString = JSONUtil.toJsonStr(kinesisData);
        // 转换为Json，取出对应的records
        JSONObject jsonObject = new JSONObject(jsonString);
//        String requestId = jsonObject.get("requestId").toString();
//        String timestamp = jsonObject.get("timestamp").toString();
//        log.info("kinesis, requestId:{}",requestId);
//        log.info("kinesis, timestamp:{}",timestamp);
        // 获取records数组
        JSONArray recordsArray = jsonObject.getJSONArray("records");
        return recordsArray;
    }

    @Override
    public void kinesisDataFlow(Object kinesisData) {
        JSONArray recordsArray = getObjects(kinesisData);
//        log.info("recordsArray:{}",recordsArray);
        for (int i = 0; i < recordsArray.size(); i++) {
            JSONObject awsJson = getEntries(recordsArray, i);
            String eventType = awsJson.getStr("EventType");
            if (eventType !=null && eventType.equals("HEART_BEAT")) {
                log.debug("kinesis,座席流,心跳");
                return;
            }
//            log.info("kinesis,座席流数据：{}",awsJson);
        }
    }

    @Override
    public boolean contactDetailPhoneProcessing(ContactDetailHandleVo contactDetailHandleVo) throws Exception {
        // 定义数据查询状态
        boolean messageRollbackStatus = false;

        KinesisContactDetailsVo kinesisContactDetailsVo = contactDetailHandleVo.getKinesisContactDetailsVo();
        Integer eventType = contactDetailHandleVo.getEventType();
        // 首先根据联络id查询各个信息
        CrmAwsConnect crmAwsConnect = crmAwsConnectService.getById(kinesisContactDetailsVo.getInstanceId());
//        CrmAwsAccount awsAccount = crmAwsAccountService.getById(crmAwsConnect.getAwsUserId());
        // 首先根据公司id查询索引是否存在
        String indexName = "kinesis_contact_details_"+kinesisContactDetailsVo.getCompanyId();
        // 查询索引是否存在，索引不存在则进行创建
        boolean indexExists = headIndexExists(indexName);
        if (!indexExists) {
            createIndex(indexName);
        }
        // 首先根据工单id查询出工单编号
        String ticketCode = null;
        String ticketId = null;
        if(StringUtil.isNotEmpty(kinesisContactDetailsVo.getWorkOrderId())){
            ticketId = kinesisContactDetailsVo.getWorkOrderId();
            // 查询工单是否存在
            List<QueryCondition> queryConditions = new ArrayList<>();
            queryConditions.add(createCondition("work_record_id", kinesisContactDetailsVo.getWorkOrderId()));
            TicketInfoIndex ticketInfoIndex = elasticsearchUtil.searchInfoQuery(queryConditions, ticketIndex+kinesisContactDetailsVo.getCompanyId(), TicketInfoIndex.class);
            if (ticketInfoIndex != null) {
                ticketCode = ticketInfoIndex.getWordRecordCode();
            }
        }
        // 如果是ACW，则暂停一秒才进行查询，因为现在acw和挂断是同时的
        if(eventType == 4){
            Thread.sleep(1000);
        }
        // TODO 呼入的开始时间，排队时间（进入队列时间），队列等待时间 这三个时间需要延迟获取。 或许可以在定时任务中进行获取
        // 根据实例id查询数据是否存在，不存在则进行新增，存在则计算其他数据
        KinesisContactDetailsVo esContactDetails = getContactDetails(indexName,kinesisContactDetailsVo.getContactId());
        if(esContactDetails==null){
            KinesisContactDetailsVo addDetails = new KinesisContactDetailsVo();
            switch (eventType) {
                case 1:
                    addDetails.setRingTime(kinesisContactDetailsVo.getRingTime());
                    addDetails.setAgentConnectionAttempts(1);
                    break;
                case 3:
                    addDetails.setStartTime(kinesisContactDetailsVo.getStartTime());
                    addDetails.setCustomerPhone(kinesisContactDetailsVo.getCustomerPhone());
                    addDetails.setSystemPhone(kinesisContactDetailsVo.getSystemPhone());
                    addDetails.setIncomingOutgoing("OUTBOUND");
                    break;
                default:
                    break;
            }
            addDetails.setContactId(kinesisContactDetailsVo.getContactId());
            addDetails.setCallChannel(kinesisContactDetailsVo.getCallChannel());
            addDetails.setAwsAccountId(crmAwsConnect.getAwsUserId());
            addDetails.setCompanyId(kinesisContactDetailsVo.getCompanyId());
            // 新增时不需要set BasicQueue  这个不是唯一的--通过第三方数据接口同步该值
//            addDetails.setQueueName("BasicQueue");
            addDetails.setConnectAlias(crmAwsConnect.getConnectAlias());
            addDetails.setQueueArn(crmAwsConnect.getConnectArn());
            addDetails.setChannelTypeId(kinesisContactDetailsVo.getChannelTypeId());
            // 根据渠道类型取到渠道名称
            addDetails.setChannelTypeName(CrmChannelEnum.getNameByCode(Integer.valueOf(kinesisContactDetailsVo.getChannelTypeId())));
            if(StringUtil.isNotEmpty(ticketCode)){
                addDetails.setWorkOrderNumber(ticketCode);
            }
            if(StringUtil.isNotEmpty(ticketId)){
                addDetails.setWorkOrderId(ticketId);
            }
            addDetails.setCreateTime(new Date());
            try {
                IndexRequest indexRequest = new IndexRequest(indexName);
                ObjectMapper objectMapper = new ObjectMapper();

                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());

                String value = objectMapper.writeValueAsString(addDetails);
                log.info("电话聊天联络明细:[{}]", com.alibaba.fastjson.JSONObject.toJSONString(addDetails));
                indexRequest.source(value, XContentType.JSON);
                // 进行添加
                IndexResponse response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                    log.error("电话聊天联络明细报错，异常信息：", e);
                }
            }
        }else{
            // 文档存在，根据事件类型赋值
            // 首先确定事件类型 1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
            switch (eventType) {
                case 1:
                    esContactDetails.setAgentConnectionAttempts(esContactDetails.getAgentConnectionAttempts()+1);
                    break;
                case 2:
                    esContactDetails.setCallTime(kinesisContactDetailsVo.getCallTime());
                    esContactDetails.setReceptionAgent(kinesisContactDetailsVo.getReceptionAgent());
                    esContactDetails.setAgentGroupId(kinesisContactDetailsVo.getAgentGroupId());
                    R deptName = deptClient.getDeptName(kinesisContactDetailsVo.getAgentGroupId());
                    if (deptName.getCode() == R.SUCCESS) {
                        if (deptName.getData() != null) {
                            esContactDetails.setAgentGroup(deptName.getData().toString());
                        }
                    }
                    // 根据响铃时间和接听时间计算出时间差
                    int phaseDifference = timeSubtraction(esContactDetails.getRingTime(), kinesisContactDetailsVo.getCallTime());
                    // 当接听时，计算出客户等待时间
                    esContactDetails.setCustomerHoldDuration(phaseDifference);
                    // TODO 需确定，接听和呼出时，是否能拿到这两个电话
                    esContactDetails.setCustomerPhone(kinesisContactDetailsVo.getCustomerPhone());
                    esContactDetails.setSystemPhone(kinesisContactDetailsVo.getSystemPhone());
                    esContactDetails.setIncomingOutgoing("INBOUND");
                    break;
                case 3:
                    esContactDetails.setReceptionAgent(kinesisContactDetailsVo.getReceptionAgent());
                    esContactDetails.setAgentGroupId(kinesisContactDetailsVo.getAgentGroupId());
                    R deptNameDetails = deptClient.getDeptName(kinesisContactDetailsVo.getAgentGroupId());
                    if (deptNameDetails.getCode() == R.SUCCESS) {
                        if (deptNameDetails.getData() != null) {
                            esContactDetails.setAgentGroup(deptNameDetails.getData().toString());
                        }
                    }
                    esContactDetails.setStartTime(kinesisContactDetailsVo.getStartTime());
                    esContactDetails.setCallTime(kinesisContactDetailsVo.getCallTime());
                    esContactDetails.setCustomerPhone(kinesisContactDetailsVo.getCustomerPhone());
                    esContactDetails.setSystemPhone(kinesisContactDetailsVo.getSystemPhone());
                    esContactDetails.setIncomingOutgoing("OUTBOUND");
                    break;
                case 4:
                    esContactDetails.setAcwTime(kinesisContactDetailsVo.getAcwTime());
                    if(esContactDetails.getStartTime() == null && esContactDetails.getIncomingOutgoing().equals("INBOUND")){
                        messageRollbackStatus = setQueueValue(kinesisContactDetailsVo.getInstanceId(), crmAwsConnect.getConnectId(), esContactDetails);
                    }
                    if(esContactDetails.getStartTime() != null){
                        // 根据ACW挂断时间和开始时间计算出通话时长
                        esContactDetails.setTotalTime(timeSubtraction(esContactDetails.getStartTime(), kinesisContactDetailsVo.getAcwTime()));
                    }
                    // acw时长  acw时间减去结束时间
                    esContactDetails.setAcwDuration(timeSubtraction(esContactDetails.getEndTime(), kinesisContactDetailsVo.getAcwTime()));
                    break;
                case 5:
                    if(esContactDetails.getEndTime() == null){
                        esContactDetails.setEndTime(kinesisContactDetailsVo.getEndTime());
                        // 互动时间 结束时间减去坐席应答时间
                        esContactDetails.setInteractionTime(timeSubtraction(esContactDetails.getCallTime(), esContactDetails.getEndTime()));
                        if ( "1".equals(kinesisContactDetailsVo.getHangingType())) {
                            esContactDetails.setHangingType("CUSTOMER_DISCONNECT");
                        }
                        if ( "2".equals(kinesisContactDetailsVo.getHangingType())) {
                            esContactDetails.setHangingType("AGENT_DISCONNECT");
                        }
                        // 只有呼入类型时，才需要查询 呼出没有进入队列时间，开始时间会直接存储
                        if(esContactDetails.getIncomingOutgoing().equals("INBOUND")){
                            messageRollbackStatus = setQueueValue(kinesisContactDetailsVo.getInstanceId(), crmAwsConnect.getConnectId(), esContactDetails);
                        }
                    }
                    break;
                case 6:
                    esContactDetails.setOnHoldTime(esContactDetails.getOnHoldTime()+kinesisContactDetailsVo.getOnHoldTime());
                    esContactDetails.setOnHoldNumber(esContactDetails.getOnHoldNumber()+1);
                    break;
                case 7:
                    esContactDetails.setIsSwitch(1);
                    esContactDetails.setInitialContactId(kinesisContactDetailsVo.getInitialContactId());
                    esContactDetails.setNextContactId(kinesisContactDetailsVo.getNextContactId());
                    esContactDetails.setPreviousContactId(kinesisContactDetailsVo.getPreviousContactId());
                    break;
                case 8:
                    esContactDetails.setCallTime(kinesisContactDetailsVo.getCallTime());
                    esContactDetails.setReceptionAgent(kinesisContactDetailsVo.getReceptionAgent());
                    esContactDetails.setAgentGroupId(kinesisContactDetailsVo.getAgentGroupId());
                    R deptNameDetail = deptClient.getDeptName(kinesisContactDetailsVo.getAgentGroupId());
                    if (deptNameDetail.getCode() == R.SUCCESS) {
                        if (deptNameDetail.getData() != null) {
                            esContactDetails.setAgentGroup(deptNameDetail.getData().toString());
                        }
                    }
                    break;
                default:
                    break;
            }
            if(StringUtil.isNotEmpty(ticketCode)){
                esContactDetails.setWorkOrderNumber(ticketCode);
            }
            if(StringUtil.isNotEmpty(ticketId)){
                esContactDetails.setWorkOrderId(ticketId);
            }
            UpdateRequest updateRequest = new UpdateRequest(indexName, esContactDetails.getDocumentId());
            try {
                String value = JSON.toJSONString(esContactDetails);
                updateRequest.doc(value, XContentType.JSON);
                restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                    log.error("电话聊天联络明细修改文档失败，异常信息：", e);
                    throw new RuntimeException(e);
                }
            }
        }
        return messageRollbackStatus;
    }

    /**
     * 获取队列值，设置到对象中
     * @param instanceId
     * @param connectId
     * @param esContactDetails
     */
    private boolean setQueueValue(String instanceId,String connectId, KinesisContactDetailsVo esContactDetails){
        try {
            R<ConnectInfoVo> connectInfoVoR = connectsServiceClient.queryConnectIdInstances(instanceId);
            if(connectInfoVoR.getCode() == AjaxResult.SUCCESS && Objects.nonNull(connectInfoVoR.getData())){
                ConnectInfoVo awsAccount = connectInfoVoR.getData();
                // 获取当前系统默认时区（UTC+8）的LocalDateTime
                LocalDateTime localDateTime = LocalDateTime.now(ZoneId.systemDefault());
                // 将LocalDateTime转换为Instant，使用系统默认时区进行转换
                Instant endTime = localDateTime.toInstant(ZoneOffset.UTC);
                // 结束时间为定时任务的当前时间
                Instant startTime = endTime.minus(Duration.ofHours(1));
                // 拼接条件 查询AWS 获取信息
                SearchContactsParam searchContactsParam = new SearchContactsParam()
                        .setInstanceId(connectId)
                        .setStartTime(startTime)
                        .setEndTime(endTime);
                searchContactsParam.setAccessKeyId(awsAccount.getAccessKey());
                searchContactsParam.setSecretAccessKey(awsAccount.getSecretAccessKey());
                searchContactsParam.setRegion(awsAccount.getRegionCode());
                searchContactsParam.setInstanceArn(awsAccount.getConnectArn());
                // 暂停两秒
                Thread.sleep(2000);
                R<String> stringR = connectClient.searchQueueContacts(searchContactsParam);
                log.info("打印一下获取aws的返回:{}",stringR);
                if(stringR.getCode() == AjaxResult.SUCCESS&&stringR.getData()!=null){
                    List<PhoneContactDetailUpdateVo> contactDetailUpdateVo = JSON.parseArray(stringR.getData(), PhoneContactDetailUpdateVo.class);
                    // 筛选 contactId 为 索引contactId 的元素
                    List<PhoneContactDetailUpdateVo> filteredList = contactDetailUpdateVo.stream()
                            .filter(vo -> esContactDetails.getContactId().equals(vo.getContactId()))
                            .collect(Collectors.toList());
                    if(!filteredList.isEmpty()){
                        PhoneContactDetailUpdateVo phoneContactDetailUpdateVo = filteredList.get(0);
                        esContactDetails.setStartTime(phoneContactDetailUpdateVo.getStartTime());
                        esContactDetails.setEnqueueTimestamp(phoneContactDetailUpdateVo.getEnqueueTimestamp());
                        // 计算队列等待时间 有坐席应答时间根据座席应答时间减，没有则根据结束时间减
                        Instant enqueueInstant = esContactDetails.getEnqueueTimestamp().toInstant();
                        long seconds = 0;
                        if(esContactDetails.getCallTime()!=null){
                            // 将 Date 转换为 Instant
                            Instant callInstant = esContactDetails.getCallTime().toInstant();
                            // 计算时间差（秒）
                            seconds = Duration.between(enqueueInstant, callInstant).getSeconds();
                        }else {
                            // 将 Date 转换为 Instant
                            Instant endInstant = esContactDetails.getEndTime().toInstant();
                            seconds = Duration.between(enqueueInstant, endInstant).getSeconds();
                        }
                        esContactDetails.setQueueWaitTime(Math.toIntExact(seconds));
                    }else {
                        log.info("电话联络明细根据数据匹配，未能匹配到对应的contactId。索引contactId为{},查询aws数据为:{}", esContactDetails.getContactId(), stringR.getData());
                        return true;
                    }

                }else{
                    log.warn("电话联络明细获取队列信息出现异常:{}", stringR);
                    return true;
                }
            }else{
                log.warn("电话联络明细获取AWS信息出现异常:{}",connectInfoVoR);
                return true;
            }
        }catch (Exception e){
            log.info("获取电话联络明细队列数据出现异常",e);
        }
        return false;
    }

    @Override
    public void contactDetailChannelProcessing(ContactDetailHandleVo contactDetailHandleVo) throws Exception {
        KinesisContactDetailsVo kinesisContactDetailsVo = contactDetailHandleVo.getKinesisContactDetailsVo();
        Integer eventType = contactDetailHandleVo.getEventType();
        String robotTicketId = contactDetailHandleVo.getRobotTicketId();
        // 首先根据公司id查询索引是否存在
        String indexName = "kinesis_contact_details_"+kinesisContactDetailsVo.getCompanyId();
        // 查询索引是否存在，索引不存在则进行创建
        boolean indexExists = headIndexExists(indexName);
        if (!indexExists) {
            createIndex(indexName);
        }
        // 首先根据工单id查询出工单编号
        String ticketCode = null;
        String ticketId = null;
        if(StringUtil.isNotEmpty(kinesisContactDetailsVo.getWorkOrderId())){
            ticketId = kinesisContactDetailsVo.getWorkOrderId();
            // 查询工单是否存在
            List<QueryCondition> queryConditions = new ArrayList<>();
            queryConditions.add(createCondition("work_record_id", kinesisContactDetailsVo.getWorkOrderId()));
            TicketInfoIndex ticketInfoIndex = elasticsearchUtil.searchInfoQuery(queryConditions, ticketIndex+kinesisContactDetailsVo.getCompanyId(), TicketInfoIndex.class);
            if (ticketInfoIndex != null) {
                ticketCode = ticketInfoIndex.getWordRecordCode();
            }
        }
        // 根据实例id查询数据是否存在，不存在则进行新增，存在则计算其他数据
        KinesisContactDetailsVo esContactDetails = getContactDetails(indexName,kinesisContactDetailsVo.getContactId());
        //事件类型 1、创建机器人工单 2、客户转人工 3、进入队列 4、退出队列 5、路由到座席 6、座席首次回复
        //     *      * 7、系统收到邮件 8、机器人工单挂断 9、转人工工单挂断 10、邮件结束工单 11、坐席联系客户
        if(esContactDetails==null){
            KinesisContactDetailsVo addDetails = new KinesisContactDetailsVo();
            switch (eventType) {
                case 1:
                    addDetails.setWorkOrderId(kinesisContactDetailsVo.getWorkOrderId());
                    addDetails.setStartTime(kinesisContactDetailsVo.getStartTime());
                    addDetails.setRobotFlag(1);
                    break;
                case 2:
                    // 根据机器人工单id查询将上一个联络明细的时间修改
                    updateRobotTicket(robotTicketId, kinesisContactDetailsVo.getStartTime(), indexName);
                    addDetails.setStartTime(kinesisContactDetailsVo.getStartTime());
                    break;
                case 7:
                    addDetails.setStartTime(kinesisContactDetailsVo.getStartTime());
                    addDetails.setIncomingOutgoing("IN");
                    addDetails.setWorkOrderId(kinesisContactDetailsVo.getWorkOrderId());
                    break;
                case 11:
                    addDetails.setStartTime(kinesisContactDetailsVo.getStartTime());
                    addDetails.setCallTime(kinesisContactDetailsVo.getStartTime());
                    addDetails.setFirstReplyTime(kinesisContactDetailsVo.getStartTime());
                    addDetails.setIncomingOutgoing("OUT");
                    break;
                default:
                    break;
            }
            addDetails.setChannelId(kinesisContactDetailsVo.getChannelId());
            addDetails.setContactId(kinesisContactDetailsVo.getContactId());
            addDetails.setCallChannel(kinesisContactDetailsVo.getCallChannel());
            addDetails.setChannelTypeId(kinesisContactDetailsVo.getChannelTypeId());
            addDetails.setChannelTypeName(CrmChannelEnum.getNameByCode(Integer.valueOf(kinesisContactDetailsVo.getChannelTypeId())));
            //  渠道id
            addDetails.setChannelId(kinesisContactDetailsVo.getChannelId());
            R<CrmChannelConfigVO> channelConfig = channelClient.queryChannelConfig(kinesisContactDetailsVo.getChannelId());
            if (channelConfig.getCode() == AjaxResult.SUCCESS) {
                addDetails.setChannelName(channelConfig.getData().getName());
            }
            if(StringUtil.isNotEmpty(ticketCode)){
                addDetails.setWorkOrderNumber(ticketCode);
            }
            if(StringUtil.isNotEmpty(ticketId)){
                addDetails.setWorkOrderId(ticketId);
            }
            addDetails.setCreateTime(new Date());
            try {
                IndexRequest indexRequest = new IndexRequest(indexName);
                ObjectMapper objectMapper = new ObjectMapper();

                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());

                String value = objectMapper.writeValueAsString(addDetails);
                indexRequest.source(value, XContentType.JSON);
                // 进行添加
                IndexResponse response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                    log.error("渠道联络明细报错，异常信息：", e);
                }
            }
        }else{
            //事件类型 1、创建机器人工单 2、客户转人工 3、进入队列 4、退出队列 5、路由到座席 6、座席首次回复
            //     *      * 7、系统收到邮件 8、机器人工单挂断 9、转人工工单挂断 10、邮件结束工单 11、坐席联系客户
            switch (eventType) {
                case 3:
                    // 排队时间
                    esContactDetails.setEnqueueTimestamp(kinesisContactDetailsVo.getEnqueueTimestamp());
                    break;
                case 4:
                    // 退队时间
                    esContactDetails.setDequeueTimestamp(kinesisContactDetailsVo.getDequeueTimestamp());
                    break;
                case 5:
                    esContactDetails.setDequeueTimestamp(kinesisContactDetailsVo.getCallTime());
                    // 路由到坐席时间
                    esContactDetails.setCallTime(kinesisContactDetailsVo.getCallTime());
                    esContactDetails.setReceptionAgent(kinesisContactDetailsVo.getReceptionAgent());
                    R<UserDetailsVO> userDetails = userClient.queryUserDetails(kinesisContactDetailsVo.getReceptionAgent());
                    if (userDetails.getCode() == AjaxResult.SUCCESS) {
                        esContactDetails.setAgentGroupId(userDetails.getData().getDeptId());
                        R deptName = deptClient.getDeptName(esContactDetails.getAgentGroupId());
                        if (deptName.getCode() == R.SUCCESS) {
                            if (deptName.getData() != null) {
                                esContactDetails.setAgentGroup(deptName.getData().toString());
                            }
                        }
                    }
                    break;
                case 6:
                    break;
                case 8:
                    // 机器人挂断工单
                    esContactDetails.setEndTime(kinesisContactDetailsVo.getEndTime());
                    break;
                case 9:
                    // 只有退队时间等于null时，才进行更新 （因为转人工挂断和机器人挂断是同一个按钮，如果为null时，代表机器人挂断）
                    if(esContactDetails.getDequeueTimestamp() == null){
                        esContactDetails.setDequeueTimestamp(kinesisContactDetailsVo.getEndTime());
                    }
                    // 转人工挂断工单
                    esContactDetails.setEndTime(kinesisContactDetailsVo.getEndTime());
                    if ( "1".equals(kinesisContactDetailsVo.getHangingType())) {
                        esContactDetails.setHangingType("CUSTOMER_DISCONNECT");
                    }
                    if ( "2".equals(kinesisContactDetailsVo.getHangingType())) {
                        esContactDetails.setHangingType("AGENT_DISCONNECT");
                    }                              // 设置坐席首次回复时间
                    try {
                        CacheTimeVO cacheTimeVO = crmAgentWorkRecordSlaDefService.calculationTime(kinesisContactDetailsVo.getCompanyId(), kinesisContactDetailsVo.getWorkOrderId());
                        // 坐席首次回复消息
                        esContactDetails.setFirstReplyTime(cacheTimeVO.getFirstReplyTime());
                        // 更新坐席回复时间
                        esContactDetails.setAverageReplyTime(String.valueOf(cacheTimeVO.getAverageReplyTime()));
                        // 清除该对话的redis
                        crmAgentWorkRecordSlaDefService.redisDateCacheDelete(kinesisContactDetailsVo.getCompanyId(), kinesisContactDetailsVo.getWorkOrderId());
                    }catch (Exception e){
                        log.error("获取坐席回复消息时间，平均时间报错：{}，工单id为：{},公司id为：{}",e,kinesisContactDetailsVo.getWorkOrderId(),kinesisContactDetailsVo.getCompanyId());
                    }
                    break;
                case 10:
                    // 邮件结束工单
                    esContactDetails.setEndTime(kinesisContactDetailsVo.getEndTime());
                    break;
                case 11:
                    // 邮件结束工单
                    esContactDetails.setEndTime(kinesisContactDetailsVo.getEndTime());
                    break;
                default:
                    break;
            }
            if(StringUtil.isNotEmpty(ticketCode)){
                esContactDetails.setWorkOrderNumber(ticketCode);
            }
            if(StringUtil.isNotEmpty(ticketId)){
                esContactDetails.setWorkOrderId(ticketId);
            }
            UpdateRequest updateRequest = new UpdateRequest(indexName, esContactDetails.getDocumentId());
            try {
                String value = JSON.toJSONString(esContactDetails);
                updateRequest.doc(value, XContentType.JSON);
                restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                    log.error("渠道联络明细修改文档失败，异常信息：", e);
                }
            }
        }
    }

    /**
     *  转人工时修改机器人工单联络明细
     * @param robotTicketId 机器人工单id
     * @param transferAgentTime 转人工时间
     * @param indexName 索引名称
     */
    private void updateRobotTicket(String robotTicketId, Date transferAgentTime, String indexName){
        if(StringUtil.isNotEmpty(robotTicketId)){
            UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(indexName);
            updateRequest.setQuery(QueryBuilders.termQuery("contactId", robotTicketId));
            // 构建脚本参数
            Map<String, Object> params = new HashMap<>();
            StringBuilder scriptBuilder = new StringBuilder();
            // 设置评估时间
            params.put("transferAgentTime", transferAgentTime);
            scriptBuilder.append("ctx._source.transferAgentTime = params.transferAgentTime; ");
            // 设置评估时间
            params.put("transferAgent", 1);
            scriptBuilder.append("ctx._source.transferAgent = params.transferAgent; ");
            // 设置脚本
            updateRequest.setScript(
                    new Script(ScriptType.INLINE, "painless",
                            scriptBuilder.toString(),
                            params)
            );
            // 执行更新
            try {
                BulkByScrollResponse bulkResponse = restHighLevelClient.updateByQuery(updateRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     *  计算两个时间时间差
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间差
     */
    private int timeSubtraction(Date startTime, Date endTime){
        // 将 Date 转换为 Instant
        Instant startInstant = startTime.toInstant();
        Instant endInstant = endTime.toInstant();

        // 计算时间差
        Duration duration = Duration.between(startInstant, endInstant);

        // 获取秒数
        return (int)duration.getSeconds();
    }


    private static JSONObject getEntries(JSONArray recordsArray, int i) {
        JSONObject record = recordsArray.getJSONObject(i);
        String data = record.get("data").toString();
        byte[] decodedBytes = Base64.getDecoder().decode(data);
        String kinesisJson = new String(decodedBytes);
        // 取出awsAccountId 查询对应的公司ID
        JSONObject awsJson = new JSONObject(kinesisJson);
        return awsJson;
    }

    /**
     * 查询es中的联络明细
     * @param index
     * @param contactId
     * @return
     */
    private KinesisContactDetailsVo getContactDetails(String index, String contactId) {
        // 构建搜索请求
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryOut = QueryBuilders.boolQuery();
        boolQueryOut.must(QueryBuilders.matchQuery("contactId", contactId));
        searchSourceBuilder.query(boolQueryOut);
        searchRequest.source(searchSourceBuilder);

        try {
            SearchResponse  searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = searchResponse.getHits();
            if (hits.getTotalHits().value > 0) {
                SearchHit firstHit = hits.getAt(0);
//                log.info("es中的数据：{}" , sourceAsString);
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> source = firstHit.getSourceAsMap();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                KinesisContactDetailsVo detailsVo = objectMapper.readValue(json, KinesisContactDetailsVo.class);
                detailsVo.setDocumentId(firstHit.getId());
                return detailsVo;
            } else {
                return null;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private KinesisContactDetailsVo dataHandle(JSONObject awsJson, CrmAwsAccount awsAccount) {
        JSONObject agent = awsJson.getJSONObject("Agent");
        JSONObject queue = awsJson.getJSONObject("Queue");
        KinesisContactDetailsVo detailsVo = new KinesisContactDetailsVo();
        detailsVo.setAwsAccountId(awsJson.get("AWSAccountId").toString());
        detailsVo.setCompanyId(awsAccount.getCompanyId());
        detailsVo.setContactId(awsJson.get("ContactId").toString());
        String instanceARN = awsJson.getStr("InstanceARN");
        // 拆分字符串并提取UUID
        String[] parts = instanceARN.split("/");
        // 获取到实例ID
        String instanceId = parts[1];
        CrmAwsConnect byId = crmAwsConnectService.getById(instanceId);
        detailsVo.setConnectAlias(byId.getConnectAlias());
        detailsVo.setInstanceId(instanceId);
        if(agent != null && !(agent.getStr("ARN")).equals("null")) {
            String connectedToAgentTimestamp = agent.get("ConnectedToAgentTimestamp").toString();
            String afterContactWorkStartTimestamp = agent.get("AfterContactWorkStartTimestamp").toString();
            detailsVo.setCallTime(agent.getDate("ConnectedToAgentTimestamp"));
            detailsVo.setEndTime(agent.getDate("AfterContactWorkStartTimestamp"));
            detailsVo.setTotalTime(dataTime(connectedToAgentTimestamp, afterContactWorkStartTimestamp));
            detailsVo.setInteractionTime(Integer.valueOf(agent.get("AgentInteractionDuration").toString()));
            detailsVo.setCustomerHoldDuration(Integer.valueOf(agent.get("CustomerHoldDuration").toString()));
            detailsVo.setReceptionAgent(agent.get("Username").toString());
            detailsVo.setAcwTime(agent.getDate("AfterContactWorkEndTimestamp"));
            detailsVo.setAcwDuration(Integer.valueOf(agent.get("AfterContactWorkDuration").toString()));
            detailsVo.setOnHoldTime(Integer.valueOf(agent.get("LongestHoldDuration").toString()));
            detailsVo.setOnHoldNumber(agent.getInt("NumberOfHolds"));
        }
        if(queue != null) {
            // 取出队列arn
            String queueArn = queue.get("ARN").toString();
            detailsVo.setQueueArn(queueArn);
            detailsVo.setQueueWaitTime(Integer.valueOf(queue.get("Duration").toString()));
            detailsVo.setQueueName(queue.get("Name").toString());
        }
        detailsVo.setStartTime(awsJson.getDate("ConnectedToSystemTimestamp"));
        detailsVo.setCallChannel(awsJson.get("Channel").toString());
        detailsVo.setIncomingOutgoing(awsJson.get("InitiationMethod").toString());
        detailsVo.setDisconnectTimestamp(awsJson.getDate("DisconnectTimestamp"));
        detailsVo.setConnectedToSystemTimestamp(awsJson.getDate("ConnectedToSystemTimestamp"));
        detailsVo.setAgentConnectionAttempts(awsJson.getInt("AgentConnectionAttempts"));

        if (awsJson.getJSONObject("CustomerEndpoint") != null) {
            detailsVo.setCustomerPhone(awsJson.getJSONObject("CustomerEndpoint").get("Address").toString());
        }

        TicketInfoIndex ticketInfoIndex = queryTicketInfoIndex(awsAccount.getCompanyId(), awsJson.get("ContactId").toString());
        // 查询到对应的工单编号
        if(ticketInfoIndex != null) {
            detailsVo.setWorkOrderNumber(ticketInfoIndex.getWordRecordCode());
            R deptName = deptClient.getDeptName(ticketInfoIndex.getDeptId());
            if (deptName.getCode() == R.SUCCESS) {
                if (deptName.getData() != null) {
                    detailsVo.setAgentGroup(deptName.getData().toString());
                }
            }
            detailsVo.setAgentGroupId(ticketInfoIndex.getDeptId());
            detailsVo.setChannelTypeId(ticketInfoIndex.getChannelTypeId());
            detailsVo.setChannelTypeName(ticketInfoIndex.getChannelTypeName());
            List<TicketSatisfaction> ticketSatisfaction = ticketInfoIndex.getTicketSatisfaction();
            if (CollectionUtils.isNotEmpty(ticketSatisfaction)) {
                TicketSatisfaction satisfaction = ticketSatisfaction.get(ticketSatisfaction.size() - 1);
                detailsVo.setSatisfactionRating(String.valueOf(satisfaction.getRating()));
            }
        }
        // 是否为转接
        String initiationMethod = awsJson.get("InitiationMethod").toString();
        // 如果为TRANSFER，则就是转接
        if(initiationMethod.equals("TRANSFER")){
            detailsVo.setIsSwitch(1);
        }else{
            detailsVo.setIsSwitch(0);
        }
        detailsVo.setHangingType(awsJson.get("DisconnectReason").toString());
        if (awsJson.getJSONObject("SystemEndpoint") != null){
            detailsVo.setSystemPhone(awsJson.getJSONObject("SystemEndpoint").get("Address").toString());
        }
        if (!awsJson.get("InitialContactId").toString().equals("null")) {
            detailsVo.setInitialContactId(awsJson.get("InitialContactId").toString());
        } else {
            detailsVo.setInitialContactId("");
        }
        if (!awsJson.get("NextContactId").toString().equals("null")) {
            detailsVo.setNextContactId(awsJson.get("NextContactId").toString());
        } else {
            detailsVo.setNextContactId("");
        }
        if (!awsJson.get("PreviousContactId").toString().equals("null")) {
            detailsVo.setPreviousContactId(awsJson.get("PreviousContactId").toString());
        } else {
            detailsVo.setPreviousContactId("");
        }
        detailsVo.setTags(awsJson.get("Tags").toString());
        detailsVo.setLastUpdateTimestamp(awsJson.getDate("LastUpdateTimestamp"));
        detailsVo.setCreateTime(new Date());
        // 保存数据
        return detailsVo;
    }

    /**
     *  根据contactId查询工单信息
     * @param companyId 公司id
     * @param contactId 联络id
     * @return 工单信息
     */
    private TicketInfoIndex queryTicketInfoIndex(String companyId, String contactId){
        // 根据ContactId查询到工单id
        // 定义索引名称
        String indexName = workContentIndex+companyId;
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("contact_id", contactId));
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(1);
        request.source(searchSourceBuilder);
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        try {
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            if(response.getHits().getHits().length==0){
                return null;
            }
            SearchHit hit = response.getHits().getHits()[0];
            Map<String, Object> source = hit.getSourceAsMap();
            String s = JSON.toJSONString(source);
            TicketContentIndex contentIndex = JSON.parseObject(s, TicketContentIndex.class);
            // 根据工单id查询工单信息
            SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex,contentIndex.getWork_record_id(),companyId);
            Map<String, Object> ticketSource = ticketHit.getSourceAsMap();
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            // 注册 JavaTimeModule
            objectMapper.registerModule(new JavaTimeModule());
            String json = objectMapper.writeValueAsString(ticketSource);
            // 将 JSON 字符串转换为 Java 对象
            ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
        }catch (Exception e){
            log.error("保存Kinesis查询工单聊天记录报错:{}",e.getMessage());
        }
        return ticketInfoIndex;
    }

    private static String getStringTime(String agentTimestamp) {
        // 格式化日期时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZoneId localZone = ZoneId.systemDefault();
        ZonedDateTime agentTimestampTime = ZonedDateTime.parse(agentTimestamp, DateTimeFormatter.ISO_ZONED_DATE_TIME);
        // 将 ZonedDateTime 对象转换为本地时区的 ZonedDateTime 对象
        ZonedDateTime localAgentTimestampTime = agentTimestampTime.withZoneSameInstant(localZone);
        return localAgentTimestampTime.format(formatter);
    }

    /**
     * 时间处理(区间)
     * @return
     */
    public Integer dataTime(String startTime,String endTime){
        ZonedDateTime startTimestampTime = ZonedDateTime.parse(startTime, DateTimeFormatter.ISO_ZONED_DATE_TIME);
        ZonedDateTime endTimestampTime = ZonedDateTime.parse(endTime, DateTimeFormatter.ISO_ZONED_DATE_TIME);
        Duration duration = Duration.between(startTimestampTime, endTimestampTime);
        return (int) duration.getSeconds();
    }




    /**
     * 通过 mappings 创建索引
     * @param index
     * @return
     */
    private boolean createDataIndex(String index) {
        CreateIndexRequest request = new CreateIndexRequest(index);
        // 设置索引的mappings
        request.mapping(embIndexTemplate(), XContentType.JSON);

        try {
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
            return createIndexResponse.isAcknowledged();
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    private String embIndexTemplate() {
        return "{\n" +
                "  \"properties\": {\n" +
                "    \"awsAccountId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"contactId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"instanceId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"companyId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"connectAlias\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"startTime\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"callTime\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"endTime\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"acwTime\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"totalTime\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"interactionTime\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"queueWaitTime\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"callChannel\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"receptionAgent\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"agentGroup\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"agentGroupId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"queueName\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"incomingOutgoing\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"customerPhone\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"acwDuration\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"workOrderNumber\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"customerHoldDuration\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"onHoldTime\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"onHoldNumber\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"isSwitch\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    },\n" +
                "    \"hangingType\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"systemPhone\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"satisfactionRating\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"initialContactId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"nextContactId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"previousContactId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"channelTypeId\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"channelTypeName\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"tags\": {\n" +
                "      \"type\": \"text\"\n" +
                "    },\n" +
                "    \"lastUpdateTimestamp\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"enqueueTimestamp\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"dequeueTimestamp\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"connectedToSystemTimestamp\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"disconnectTimestamp\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"createTime\": {\n" +
                "      \"type\": \"date\"\n" +
                "    },\n" +
                "    \"queueArn\": {\n" +
                "      \"type\": \"keyword\"\n" +
                "    },\n" +
                "    \"agentConnectionAttempts\": {\n" +
                "      \"type\": \"integer\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }

    /**
     * 查询队列历史指标
     */
    public void getQueueIndex() {
        // 1. 使用
        // ACW平均时间
        // 平均互动时间
        // 客户保持时间
        // 队列放弃时间，不包含座席数据
        // 队列应答时间 包含座席的数据
        // 放弃的联系人 不包含座席的数据
        // 排队的联系人 包含座席数据，队列等待时间大于0的
    }

    /**
     * 计算电话的sku费用并记录详细费用到elasticsearch
     * 月租费的sku项在定时任务中执行计算
     * @param contactDetailsVo contactDetailsVo
     */
    public void computePhoneBill(ContactDetailVo contactDetailsVo) {
        String companyId = contactDetailsVo.getCompanyId();
        String systemPhone = contactDetailsVo.getSystemPhone();
        long duration = DateUtils.calculateMinutesDifference(contactDetailsVo.getStartTime(), contactDetailsVo.getEndTime());

        // 查询充值账单 按照充值时间优先消耗费用
        // 如果剩余费用=0，取下一条数据消耗
        R<List<TelephoneBillComputeVo>> billsR = telephoneBillComputeClient.queryAllTelephoneBills(companyId);
        if (billsR != null && billsR.getCode() == AjaxResult.SUCCESS) {
            List<TelephoneBillComputeVo> billComputeVoList = billsR.getData();
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(billComputeVoList)) {
                // 查询system_phone对用的收费sku项
                List<TelephoneBillSkuDefinitionIndex> skuDefinitionIndexList = elasticsearchUtils.searchListQuery(
                        Lists.newArrayList(
                                QueryCondition.createCondition("company_id", companyId, QueryTypeEnum.TERM),
                                QueryCondition.createCondition("system_phone", systemPhone, QueryTypeEnum.TERM)),
                        TELEPHONE_BILL_SKU_INDEX,
                        TelephoneBillSkuDefinitionIndex.class);
                if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(skuDefinitionIndexList)) {
                    Map<String, List<TelephoneBillSkuDefinitionIndex>> skuMap = skuDefinitionIndexList.stream().collect(Collectors.groupingBy(TelephoneBillSkuDefinitionIndex::getSkuCode));
                    List<TelephoneBillDetailIndex> telephoneBillDetailIndexList = new ArrayList<>();
                    // 呼入
                    if (KINESIS_DATA_PHONE_INBOUND.equals(contactDetailsVo.getIncomingOutgoing())) {
                        skuMap.forEach((skuCode, list) -> {
                            // 去除 呼出/月租费的 sku
                            if (!KINESIS_DATA_PHONE_OUTBOUND.equals(skuCode) && !PHONE_SKU_MONTHLY_FEE.equals(skuCode)) {
                                list.forEach(sku -> {
                                    BigDecimal skuCost = sku.getUnitPrice().multiply(BigDecimal.valueOf(duration)).setScale(10, RoundingMode.HALF_UP);
                                    telephoneBillDetailIndexList.add(
                                            new TelephoneBillDetailIndex()
                                                    .setSkuCode(sku.getSkuCode())
                                                    .setSkuName(sku.getSkuName())
                                                    .setSystemPhone(systemPhone)
                                                    .setCustomerPhone(contactDetailsVo.getCustomerPhone())
                                                    .setContactId(contactDetailsVo.getContactId())
                                                    .setContactLine(contactDetailsVo.getConnectAlias())
                                                    .setUnitPrice(sku.getUnitPrice())
                                                    .setCurrency(sku.getCurrency())
                                                    .setCurrentTime(LocalDateTime.now())
                                                    .setCost(skuCost));
                                });
                            }
                        });
                    }
                    // 呼出
                    else if (KINESIS_DATA_PHONE_OUTBOUND.equals(contactDetailsVo.getIncomingOutgoing())) {
                        // 根据 customer 得到呼出区号
                        PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
                        Phonenumber.PhoneNumber phoneNumber = null;
                        try {
                            phoneNumber = phoneNumberUtil.parse(contactDetailsVo.getCustomerPhone(), null);
                        } catch (NumberParseException e) {
                            throw new RuntimeException(e);
                        }
                        String countryArea = "+" + phoneNumber.getCountryCode();

                        // 去除 呼入/月租费和非本区号的 的sku
                        skuMap.forEach((skuCode, list) -> {
                            if (!KINESIS_DATA_PHONE_INBOUND.equals(skuCode) && !PHONE_SKU_MONTHLY_FEE.equals(skuCode)) {
                                for (TelephoneBillSkuDefinitionIndex sku : list) {
                                    // 对于呼出来说不是呼出地区号的不计算
                                    if (KINESIS_DATA_PHONE_OUTBOUND.equals(sku.getSkuCode()) && !countryArea.equals(sku.getAreaCode())) {
                                        continue;
                                    }
                                    BigDecimal skuCost = sku.getUnitPrice().multiply(BigDecimal.valueOf(duration)).setScale(10, RoundingMode.HALF_UP);
                                    telephoneBillDetailIndexList.add(
                                            new TelephoneBillDetailIndex()
                                                    .setSkuCode(sku.getSkuCode())
                                                    .setSkuName(sku.getSkuName())
                                                    .setSystemPhone(systemPhone)
                                                    .setCustomerPhone(contactDetailsVo.getCustomerPhone())
                                                    .setContactId(contactDetailsVo.getContactId())
                                                    .setContactLine(contactDetailsVo.getConnectAlias())
                                                    .setAreaCode(countryArea)
                                                    .setUnitPrice(sku.getUnitPrice())
                                                    .setCurrency(sku.getCurrency())
                                                    .setCurrentTime(LocalDateTime.now())
                                                    .setCost(skuCost));
                                }
                            }
                        });
                    }

                    // 把sku详细花费存入elasticsearch
                    String indexName = TELEPHONE_BILL_DETAIL_INDEX + companyId;
                    elasticsearchUtils.existOrCreateIndex(indexName);
                    // 一次通话会有一个contactId
                    // 因为会推送多次contactId 仅选用第一次即可
                    List<TelephoneBillDetailIndex> billDetailIndexList = elasticsearchUtils.searchListQuery(
                            Lists.newArrayList(
                                    new QueryCondition("system_phone", systemPhone, QueryTypeEnum.TERM),
                                    new QueryCondition("contact_id", contactDetailsVo.getContactId(), QueryTypeEnum.TERM)
                            ),
                            indexName,
                            TelephoneBillDetailIndex.class);

                    // 如果已经存在 不再进行处理
                    if (CollectionUtils.isNotEmpty(billDetailIndexList)) {
                        return;
                    }
                    elasticsearchUtils.deleteDocumentByTerm(indexName, "contact_id", contactDetailsVo.getContactId());
                    elasticsearchUtils.insertListDocument(indexName, telephoneBillDetailIndexList);

                    // 更新MySQL剩余金额数据  加锁
                    RLock lock = redissonClient.getLock(Constants.TELEPHONE_BILL_UPDATE_LOCK.concat(companyId));
                    lock.lock();
                    try {
                        // 这里一定要再次查询MySQL费用 因为在前面的逻辑计算时可能MySQL的费用发生了变化
                        // 这里在分布式锁里再次查询MySQL费用 就是准确的
                        R<List<TelephoneBillComputeVo>> billR = telephoneBillComputeClient.queryAllTelephoneBills(companyId);
                        if (billR != null && billR.getCode() == AjaxResult.SUCCESS) {
                            List<TelephoneBillComputeVo> billComputeList = billR.getData();
                            BigDecimal totalCost = telephoneBillDetailIndexList.stream().map(TelephoneBillDetailIndex::getCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                            consumeByPayTimePriority(billComputeList, totalCost);
                            telephoneBillComputeClient.updateTelephoneBills(billComputeList);
                        }
                    } catch (Exception e) {
                        log.error("===========>>>>> 更新telephone bill出现问题", e);
                    } finally {
                        lock.unlock();
                    }
                }
            }
        }
    }

    /**
     * 按照 payTime 优先级消耗 remainPay
     *
     * @param billList        电话账单列表
     * @param amountToConsume 需要消耗的金额
     */
    public void consumeByPayTimePriority(List<TelephoneBillComputeVo> billList, BigDecimal amountToConsume) {
        // 按照 payTime 升序排序
        billList.sort(Comparator.comparing(TelephoneBillComputeVo::getPayTime));

        BigDecimal remainingAmount = amountToConsume;
        // 记录总的消耗金额
        BigDecimal totalConsumed = BigDecimal.ZERO;

        // 遍历排序后的列表
        for (TelephoneBillComputeVo bill : billList) {
            if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break; // 已经消耗完所需金额
            }

            if (bill.getRemainPay() == null || bill.getRemainPay().compareTo(BigDecimal.ZERO) <= 0) {
                continue; // 跳过余额为0的记录
            }

            // 计算当前记录可以消耗的金额
            BigDecimal canConsume = bill.getRemainPay().min(remainingAmount);

            // 更新余额和剩余需要消耗的金额
            bill.setRemainPay(bill.getRemainPay().subtract(canConsume).setScale(10, RoundingMode.HALF_UP));
            remainingAmount = remainingAmount.subtract(canConsume).setScale(10, RoundingMode.HALF_UP);
            totalConsumed = totalConsumed.add(canConsume).setScale(10, RoundingMode.HALF_UP);
        }
    }

}

