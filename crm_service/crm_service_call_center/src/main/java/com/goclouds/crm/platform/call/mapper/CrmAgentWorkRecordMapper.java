package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecord;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.statis.SatisfactionResult;
import com.goclouds.crm.platform.call.domain.vo.statis.WorkRecordResult;
import com.goclouds.crm.platform.call.domain.vo.statis.WorkRecordTopResult;
import com.goclouds.crm.platform.call.domain.vo.statis.report.InUseChannel;
import com.goclouds.crm.platform.call.domain.vo.statis.report.InUseWorkOrderType;
import com.goclouds.crm.platform.call.domain.vo.statis.report.WorkOrderCreateTypeSummary;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordResult;
import com.goclouds.crm.platform.openfeignClient.domain.call.AgentWorkRecordVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record(工作记录)】的数据库操作Mapper
* @createDate 2023-05-25 10:27:34
* @Entity workRecord.domain.CrmAgentWorkRecord
*/
public interface CrmAgentWorkRecordMapper extends BaseMapper<CrmAgentWorkRecord> {

    @Select("select cawrr.relation_work_record_id as workRecordId ,word_record_code,old_work_record_code, work_record_theme," +
            "        channel_type_id,channel_type_name,channel_config_name,customer_id," +
            "        customer_name,customer_telephone,agent_id," +
            "        agent_name,status,accept_type," +
            "        create_type,work_record_type_id,priority_level_id," +
            "        priority_level_name,initiation_time,resolve_time," +
            "        should_resolve_time,first_response_time,final_response_time," +
            "        should_response_time,terminate_time ,transfer_time," +
            "        time_of_duration,remark, reminder_status " +
            "       from crm_agent_work_record cawr" +
            "       left join crm_agent_work_record_relation cawrr on cawrr.relation_work_record_id = cawr.work_record_id " +
            "       left join crm_agent_work_record_relation cawrr2 on cawrr2.work_record_id = cawr.work_record_id " +
            "where ( cawrr.work_record_id = #{workRecordId}" +
            "   or cawrr2.relation_work_record_id = #{workRecordId} ) and cawr.data_status = 1")
    IPage<WorkOrderRecordResponseVO> associationWorkOrder(IPage<Object> page, @Param("workRecordId") String workRecordId);

    @Select("select work_record_id, word_record_code, channel_type_id,channel_type_name,create_time, content_summary from crm_agent_work_record ${ew.customSqlSegment}")
    List<CustomTicketRecordVO> customTicketRecord(@Param(Constants.WRAPPER) Wrapper<CrmAgentWorkRecord> orderByDesc);

    @Select("select count(*) " +
            "from crm_agent_work_record cawr " +
            "         left join sys_user su on cawr.agent_id = su.user_id " +
            "${ew.customSqlSegment}")
    Integer workTotalNumber(@Param(Constants.WRAPPER) QueryWrapper<CrmAgentWorkRecord> queryWrapper);

    @Select("select count(*) as workNumber, cawr.agent_id, su.user_name as agentName, su.dept_id, sd.dept_name " +
            "from crm_agent_work_record cawr " +
            "         left join sys_user su on cawr.agent_id = su.user_id " +
            "         inner join sys_dept sd on su.dept_id = sd.dept_id " +
            "${ew.customSqlSegment}")
    List<AgentQueryWorkNumberVo> agentWorkNumberTo3(@Param(Constants.WRAPPER) QueryWrapper<CrmAgentWorkRecord> queryWrapper);

    @Select("select TIMESTAMPDIFF(SECOND, cawr.create_time, cawr.resolve_time) AS time_difference_seconds, " +
            "       cawr.agent_id, " +
            "       su.user_name as agentName, " +
            "       su.dept_id, " +
            "       sd.dept_name " +
            "from crm_agent_work_record cawr " +
            "         left join sys_user su on cawr.agent_id = su.user_id " +
            "         inner join sys_dept sd on su.dept_id = sd.dept_id " +
            "${ew.customSqlSegment}")
    List<AgentQueryResolutionTimeVo> agentResolutionTime(@Param(Constants.WRAPPER) QueryWrapper<CrmAgentWorkRecord> queryWrapper);

    @Select("select COALESCE(ROUND(AVG(cawrs.rating), 2), 0) " +
            "from crm_agent_work_record_satisfaction cawrs " +
            "         left join crm_agent_work_record cawr on cawr.work_record_id = cawrs.work_record_id " +
            "         inner join sys_user su on cawr.agent_id = su.user_id " +
            "${ew.customSqlSegment}")
    BigDecimal avgSatisfactionLevel(@Param(Constants.WRAPPER) QueryWrapper<CrmAgentWorkRecord> queryWrapper);

    @Select("select count(1) " +
            "from crm_agent_work_record_satisfaction cawrs " +
            "         left join crm_agent_work_record cawr on cawr.work_record_id = cawrs.work_record_id " +
            "         inner join sys_user su on cawr.agent_id = su.user_id " +
            "${ew.customSqlSegment}")
    Integer satisfactionLevelNumber(@Param(Constants.WRAPPER) QueryWrapper<CrmAgentWorkRecord> queryWrapper);

    @Select("select cawrs.rating, " +
            "       cawr.agent_id, " +
            "       su.user_name as agentName, " +
            "       su.dept_id, " +
            "       sd.dept_name " +
            "from crm_agent_work_record_satisfaction cawrs " +
            "         left join crm_agent_work_record cawr on cawr.work_record_id = cawrs.work_record_id " +
            "         left join sys_user su on cawr.agent_id = su.user_id " +
            "         Inner join sys_dept sd on su.dept_id = sd.dept_id " +
            "${ew.customSqlSegment}")
    List<AgentQuerySatisfactionLevelVo> agentSatisfactionLevel(@Param(Constants.WRAPPER) QueryWrapper<CrmAgentWorkRecord> queryWrapper);

    /**
     * 根据id查询工单详细信息 包括扩展字段信息
     * @param wrapper wrapper
     * @return AgentWorkRecordVo
     */
    @Select({
        "select t1.*, json_arrayagg(json_object('code', t2.work_record_ext_code, 'value', t2.work_record_ext_value)) extraProperty",
        "from crm_agent_work_record t1 left join crm_agent_work_record_ext_ints t2 on t1.work_record_id = t2.work_record_id",
        "${ew.customSqlSegment}"
    })
    @Results(@Result(column = "extraProperty", property = "extraProperty", typeHandler = FastjsonTypeHandler.class))
    AgentWorkRecordVo innerQueryWorkRecordById(@Param(Constants.WRAPPER) Wrapper<Object> wrapper);
}




