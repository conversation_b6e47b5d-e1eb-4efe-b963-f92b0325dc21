package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordOperationLogDef;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordOperationLogDefMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordOperationLogDefService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.enums.TicketOperationTypeEnum;
import com.goclouds.crm.platform.common.utils.RedisCacheUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_operation_log_def(工单操作日志国际化定义表)】的数据库操作Service实现
* @createDate 2023-11-06 14:55:34
*/
@Service
@Slf4j
public class CrmAgentWorkRecordOperationLogDefServiceImpl extends ServiceImpl<CrmAgentWorkRecordOperationLogDefMapper, CrmAgentWorkRecordOperationLogDef>
    implements CrmAgentWorkRecordOperationLogDefService {

    @Override
    public String queryLogContent(String languageCode, Integer operationType) {
        String redisKey = Constants.LOGIN_TOKEN_KEY.concat(languageCode.concat(":" + TicketOperationTypeEnum.getTypeByName(operationType)));
        String redisContent = RedisCacheUtil.getCacheObject(redisKey);
        if (StringUtil.isNotBlank(redisContent)) {
            log.info("redis中取出logDef为{}, redisKey为{}" ,redisContent, redisKey);
            return redisContent;
        }
        // redis中不存在 查询数据库后重新放入Redis中
        CrmAgentWorkRecordOperationLogDef operationLogDef = this.baseMapper.selectOne(new QueryWrapper<CrmAgentWorkRecordOperationLogDef>().lambda()
                .eq(CrmAgentWorkRecordOperationLogDef::getLanguageCode, languageCode)
                .eq(CrmAgentWorkRecordOperationLogDef::getOperationType, operationType)
                .eq(CrmAgentWorkRecordOperationLogDef::getDataStatus, 1));

        if (Objects.nonNull(operationLogDef)) {
            // 把操作日志对应的国际化放到Redis中
            RedisCacheUtil.setCacheObject(redisKey, operationLogDef.getLogContent());
            log.info("数据库中取出logDef为{},并存放到redis中}, redisKey为{}" ,redisContent, redisKey);
            return operationLogDef.getLogContent();
        }
        return null;
    }
}




