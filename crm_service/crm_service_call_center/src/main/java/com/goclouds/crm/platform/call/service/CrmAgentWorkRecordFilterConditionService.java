package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilterCondition;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordFilterConditionResponseVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_filter_condition(工单筛选器条件;)】的数据库操作Service
* @createDate 2023-09-19 15:10:09
*/
public interface CrmAgentWorkRecordFilterConditionService extends IService<CrmAgentWorkRecordFilterCondition> {

    List<WorkRecordFilterConditionResponseVO> queryFilterCondition(String workRecordFilterId);
}
