package com.goclouds.crm.platform.call.service.impl;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goclouds.crm.platform.call.domain.TicketContentIndex;
import com.goclouds.crm.platform.call.strategy.AddTicketSmartFillFactory;
import com.goclouds.crm.platform.call.strategy.AigcTicketSmartFillEnum;
import com.goclouds.crm.platform.call.strategy.AigcTicketSmartFillStrategy;
import com.goclouds.crm.platform.common.domain.QueryCondition;
import com.goclouds.crm.platform.openfeignClient.client.aigc.AigcChatClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.call.smartFill.AILLMTicketSmartFill;
import com.goclouds.crm.platform.openfeignClient.domain.call.smartFill.QueryLLMTicketSmartFill;
import com.goclouds.crm.platform.openfeignClient.domain.call.smartFill.QueryLLMTicketSmartFillRequest;
import com.goclouds.crm.platform.utils.ElasticsearchUtil;
import com.google.common.collect.Lists;

import java.io.IOException;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.goclouds.crm.platform.call.domain.CrmCallTicketSmartFill;
import com.goclouds.crm.platform.call.domain.CrmCallTicketSmartFillDetail;
import com.goclouds.crm.platform.call.domain.smartFill.*;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordTypeVO;
import com.goclouds.crm.platform.call.mapper.CrmCallTicketSmartFillMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordTypeDefService;
import com.goclouds.crm.platform.call.service.CrmCallTicketSmartFillDetailService;
import com.goclouds.crm.platform.call.service.CrmCallTicketSmartFillService;
import com.goclouds.crm.platform.call.service.TicketSmartFillService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.utils.uuid.IdUtils;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import com.ibm.icu.text.SimpleDateFormat;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.goclouds.crm.platform.common.domain.QueryCondition.createCondition;
import static com.goclouds.crm.platform.utils.SecurityUtil.getUsername;

/**
 * @Author: sunlinan
 * @Description:
 * @Date: 2025-04-14 14:04
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class TicketSmartFillServiceImpl implements TicketSmartFillService {
    private static final String TICKET_INFO_INDEX = "ticket_info_index_";
    private final CrmCallTicketSmartFillService smartFillService;
    private final CrmCallTicketSmartFillDetailService smartFillDetailService;
    private final CrmAgentWorkRecordTypeDefService workRecordTypeDefService;
    private final CrmCallTicketSmartFillMapper callTicketSmartFillMapper;

    private final RestHighLevelClient restHighLevelClient;

    private final AigcChatClient aigcClient;

    private final AddTicketSmartFillFactory addTicketSmartFillFactory;

    private final ElasticsearchUtil elasticsearchUtil;

    @Override
    public IPage<TicketSmartFillVo> queryTicketSmartFillList(IPage<Object> pageParam) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String headerLanguage = ServletUtils.getHeaderLanguage();
        Page<TicketSmartFillVo> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        IPage<TicketSmartFillVo> ticketSmartFillVoIPage = callTicketSmartFillMapper.selectPageWithDetails(page, companyId);
        List<TicketSmartFillVo> records = ticketSmartFillVoIPage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<WorkRecordTypeVO> workRecordTypeVOList = workRecordTypeDefService.queryWorkRecordTypeByLanguage(headerLanguage);
            Map<String, String> workRecordTypeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(workRecordTypeVOList)) {
                //根据类型进行分组
                workRecordTypeMap = workRecordTypeVOList.stream().collect(Collectors.toMap(WorkRecordTypeVO::getWorkRecordTypeValue, WorkRecordTypeVO::getWorkRecordTypeName));
            }
            for (TicketSmartFillVo ticketSmartFillVo : records) {
                String ticketTypeCode = ticketSmartFillVo.getTicketTypeCode();
                if (StringUtils.isNotEmpty(ticketTypeCode)) {
                    //替换为国际化内容
                    String convertCodesToNames = convertCodesToNames(ticketTypeCode, workRecordTypeMap);
                    ticketSmartFillVo.setTicketTypeCode(convertCodesToNames);
                }

                List<TicketSmartFillDetailVo> ticketSmartFillDetailList = ticketSmartFillVo.getTicketSmartFillDetailList();
                if (CollectionUtils.isNotEmpty(ticketSmartFillDetailList)) {
                    String attrNames = ticketSmartFillDetailList.stream()
                            .map(TicketSmartFillDetailVo::getAttrName)
                            .filter(name -> name != null && !name.isEmpty()) // 过滤掉 null 和空字符串
                            .collect(Collectors.joining(","));
                    ticketSmartFillVo.setAttrName(attrNames);
                }

            }
        }
        return ticketSmartFillVoIPage;

    }

    @Override
    public AjaxResult queryTicketSmartFillDetail(String smartFillId) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        TicketSmartFillVo ticketSmartFillVo = callTicketSmartFillMapper.selectTicketSmartFillDetail(companyId, smartFillId);
        return AjaxResult.ok(ticketSmartFillVo);
    }

    @Override
    public AjaxResult createTicketSmartFill(AddTicketSmartFillParam addTicketSmartFillParam) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String userId = SecurityUtil.getLoginUser().getUserId();
        String languageCode = addTicketSmartFillParam.getLanguageCode();
        String ticketTypeCodes = addTicketSmartFillParam.getTicketTypeCode();
        List<TicketSmartFillDetailVo> ticketSmartFillDetailList = addTicketSmartFillParam.getTicketSmartFillDetailList();
        //校验数据合法性
        AjaxResult ajaxResult = checkCommonData(languageCode, ticketTypeCodes, ticketSmartFillDetailList, null,companyId);
        if (AjaxResult.SUCCESS != ajaxResult.getCode()) {
            return ajaxResult;
        }
        //存主表
        String smartFillId = IdUtils.simpleUUID();
        CrmCallTicketSmartFill crmCallTicketSmartFill = new CrmCallTicketSmartFill();
        crmCallTicketSmartFill.setSmartFillId(smartFillId);
        crmCallTicketSmartFill.setLanguageCode(addTicketSmartFillParam.getLanguageCode());
        crmCallTicketSmartFill.setTicketTypeCode(addTicketSmartFillParam.getTicketTypeCode());
        crmCallTicketSmartFill.setCompanyId(companyId);
        crmCallTicketSmartFill.setCreator(userId);
        crmCallTicketSmartFill.setCreateTime(new Date());
        crmCallTicketSmartFill.setModifier(userId);
        crmCallTicketSmartFill.setModifyTime(new Date());
        crmCallTicketSmartFill.setDataStatus(Constants.NORMAL);
        smartFillService.save(crmCallTicketSmartFill);

        //存详情表
        saveSmartFillDetail(ticketSmartFillDetailList, smartFillId, userId);
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult updateTicketSmartFill(UpdateTicketSmartFillParam updateTicketSmartFillParam) {
        String smartFillId = updateTicketSmartFillParam.getSmartFillId();
        String userId = SecurityUtil.getLoginUser().getUserId();
        String languageCode = updateTicketSmartFillParam.getLanguageCode();
        String ticketTypeCodes = updateTicketSmartFillParam.getTicketTypeCode();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        List<TicketSmartFillDetailVo> ticketSmartFillDetailList = updateTicketSmartFillParam.getTicketSmartFillDetailList();
        //校验数据合法性
        AjaxResult ajaxResult = checkCommonData(languageCode, ticketTypeCodes, ticketSmartFillDetailList, smartFillId, companyId);
        if (AjaxResult.SUCCESS != ajaxResult.getCode()) {
            return ajaxResult;
        }
        //更新主表信息
        smartFillService.update(new LambdaUpdateWrapper<CrmCallTicketSmartFill>()
                .set(CrmCallTicketSmartFill::getModifier, userId)
                .set(CrmCallTicketSmartFill::getModifyTime, new Date())
                .set(CrmCallTicketSmartFill::getLanguageCode, languageCode)
                .set(CrmCallTicketSmartFill::getTicketTypeCode, ticketTypeCodes)
                .eq(CrmCallTicketSmartFill::getSmartFillId, smartFillId));

        //删除从表信息
        deleteSmartFillDetail(smartFillId, userId);

        //重新向从表中添加本次修改后的配置属性内容
        saveSmartFillDetail(ticketSmartFillDetailList, smartFillId, userId);

        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult deleteTicketSmartFill(String smartFillId) {
        String userId = SecurityUtil.getLoginUser().getUserId();
        smartFillService.update(new LambdaUpdateWrapper<CrmCallTicketSmartFill>()
                .set(CrmCallTicketSmartFill::getDataStatus, Constants.DELETE)
                .set(CrmCallTicketSmartFill::getModifier, userId)
                .set(CrmCallTicketSmartFill::getModifyTime, new Date())
                .eq(CrmCallTicketSmartFill::getSmartFillId, smartFillId));

        deleteSmartFillDetail(smartFillId, userId);
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }


    /**
     * AIGC智能填单
     *
     * @param
     * @return
     */
    @Override
    public AjaxResult<AigcTicketSmartFillResponse> aigcTicketSmartFill(AigcTicketSmartFillRequest aigcTicketSmartFillRequest) {
        log.info("AIGC智能填单：【{}】", JSON.toJSONString(aigcTicketSmartFillRequest));
        AigcTicketSmartFillResponse aigcTicketSmartFillResponse = new AigcTicketSmartFillResponse();
        List<AigcTicketSmartFillDTO> aigcTicketSmartFillDTOS = new ArrayList<>();
        String indexName = embIndexName();
        SearchRequest request = new SearchRequest(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("work_record_id", aigcTicketSmartFillRequest.getWorkRecordId()));
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder).size(10000);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        // 进行查询
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("AIGC智能填单：根据工单ID查询工单信息报错:【{}】", e);
            throw new RuntimeException(e);
        }
        SearchHit[] hits = response.getHits().getHits();
        Map<String, Object> source = hits[0].getSourceAsMap();
        String workRecordTypeCode = null;
        if (source.get("work_record_type_code") == null) {
            workRecordTypeCode = "default";
        }
        if (source.get("work_record_type_code") != null) {
            workRecordTypeCode = source.get("work_record_type_code").toString();
        }
        CrmCallTicketSmartFill crmCallTicketSmartFill=new CrmCallTicketSmartFill();
        LambdaQueryWrapper<CrmCallTicketSmartFill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(CrmCallTicketSmartFill::getTicketTypeCode, workRecordTypeCode);
        queryWrapper.eq(CrmCallTicketSmartFill::getDataStatus, 1);
        queryWrapper.eq(CrmCallTicketSmartFill::getCompanyId, SecurityUtil.getLoginUser().getCompanyId());
        queryWrapper.eq(CrmCallTicketSmartFill::getLanguageCode, aigcTicketSmartFillRequest.getLanguage());
        crmCallTicketSmartFill = callTicketSmartFillMapper.selectOne(queryWrapper);
        // 当他第一次查询查出来位null，判断是否存在兜底行为，根据语言进行兜底
        if (Objects.isNull(crmCallTicketSmartFill)) {
            log.info("第一次查询工单填单规则，判断是否有兜底,兜底语言分割前为：[{}]", aigcTicketSmartFillRequest.getLanguage());
            String language = aigcTicketSmartFillRequest.getLanguage();
            String[] split = language.split("-");
            String preLanguage = split[0];
            log.info("第一次查询工单填单规则，判断是否有兜底,兜底语言为：[{}]", preLanguage);
            LambdaQueryWrapper<CrmCallTicketSmartFill> crmCallTicketSmartFillLambdaQueryWrapper = new LambdaQueryWrapper<>();
            crmCallTicketSmartFillLambdaQueryWrapper.like(CrmCallTicketSmartFill::getTicketTypeCode, workRecordTypeCode);
            crmCallTicketSmartFillLambdaQueryWrapper.eq(CrmCallTicketSmartFill::getDataStatus, 1);
            crmCallTicketSmartFillLambdaQueryWrapper.eq(CrmCallTicketSmartFill::getCompanyId, SecurityUtil.getLoginUser().getCompanyId());
            crmCallTicketSmartFillLambdaQueryWrapper.eq(CrmCallTicketSmartFill::getLanguageCode, preLanguage);
            crmCallTicketSmartFill = callTicketSmartFillMapper.selectOne(crmCallTicketSmartFillLambdaQueryWrapper);
        }
        // 假如最终数据位空，则进行return
        if (Objects.isNull(crmCallTicketSmartFill)) {
            log.info("第二次兜底查询工单填单规则，该工单不存在填单规则，工单信息如下>>>>>：");
            log.info("生成 AIGC 智能填单-》工单类型code没有工单关联：getTicketTypeCode【{}】,getCompanyId【{}】,getLanguageCode【{}】"
                    , workRecordTypeCode, SecurityUtil.getLoginUser().getCompanyId(), aigcTicketSmartFillRequest.getLanguage());
            return AjaxResult.failure(1001, MessageUtils.get("ticket.smart.fill"));
        }
        log.info("生成 AIGC 智能填单-》智能填单表查询信息：crmCallTicketSmartFill【{}】"
                , JSONObject.toJSONString(crmCallTicketSmartFill));
        // 拿到智能填单ID
        String smartFillId = crmCallTicketSmartFill.getSmartFillId();
        LambdaQueryWrapper<CrmCallTicketSmartFillDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CrmCallTicketSmartFillDetail::getSmartFillId, smartFillId);
        lambdaQueryWrapper.eq(CrmCallTicketSmartFillDetail::getDataStatus, 1);
        List<CrmCallTicketSmartFillDetail> callTicketSmartFillDetailList = smartFillDetailService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(callTicketSmartFillDetailList)) {
            return AjaxResult.failure(1001, MessageUtils.get("ticket.smart.fill"));
        }
        List<QueryLLMTicketSmartFill> llmTicketSmartFillRequestList = convertToQueryLLMTicketSmartFill(callTicketSmartFillDetailList);
        QueryLLMTicketSmartFillRequest queryLLMTicketSmartFillRequest = new QueryLLMTicketSmartFillRequest();
        queryLLMTicketSmartFillRequest.setQueryLLMTicketSmartFillList(llmTicketSmartFillRequestList);
        queryLLMTicketSmartFillRequest.setContent(aigcTicketSmartFillRequest.getContent());
        queryLLMTicketSmartFillRequest.setLanguage(aigcTicketSmartFillRequest.getLanguage());
        R<String> objectR = aigcClient.aigcTicketSmartFill(SecurityUtil.getLoginUser().getCompanyId(), SecurityUtil.getUserId(), queryLLMTicketSmartFillRequest);
        log.info("打印一下翻译后的内容:{}", objectR);
        if (objectR.getCode() == AjaxResult.SUCCESS && Objects.nonNull(objectR.getData())) {
            List<AILLMTicketSmartFill> queryAILLMTicketSmartFill = JSONObject.parseArray(objectR.getData(), AILLMTicketSmartFill.class);
            aigcTicketSmartFillDTOS = convertTOAigcTicketSmartFillDTOS(queryAILLMTicketSmartFill);
        }
        List<AigcTicketSmartFillDTO> aigcTicketSmartFillList = new ArrayList<>();
        // 全部返回给前端，不管有没有属性值
        callTicketSmartFillDetailList.forEach(llmTicketSmartFillDetail -> {
            AigcTicketSmartFillDTO aigcTicketSmartFillDTO = new AigcTicketSmartFillDTO();
            aigcTicketSmartFillDTO.setAttrName(llmTicketSmartFillDetail.getAttrName());
            aigcTicketSmartFillDTO.setStoreAttr(llmTicketSmartFillDetail.getStoreAttr());
            aigcTicketSmartFillDTO.setAttrValue(null);
            aigcTicketSmartFillDTO.setStoreAttrType(llmTicketSmartFillDetail.getStoreAttrType());
            aigcTicketSmartFillDTO.setStoreAttr(llmTicketSmartFillDetail.getStoreAttr());
            aigcTicketSmartFillList.add(aigcTicketSmartFillDTO);
        });
        List<AigcTicketSmartFillDTO> finalAigcTicketSmartFillDTOS = aigcTicketSmartFillDTOS;
        aigcTicketSmartFillList.forEach(aigcTicketSmartFillDTO -> {
            finalAigcTicketSmartFillDTOS.forEach(llmTicketSmartFillDTO -> {
                if (aigcTicketSmartFillDTO.getAttrName().equals(llmTicketSmartFillDTO.getAttrName())) {
                    aigcTicketSmartFillDTO.setAttrValue(llmTicketSmartFillDTO.getAttrValue());
                    aigcTicketSmartFillDTO.setStoreAttr(aigcTicketSmartFillDTO.getStoreAttr());
                    //todo 这次需求不涉及这个下拉框，如后续涉及后这块逻辑需要做更改
                    // 还需要更改提示词把getOriginalValue这个字段如果不存在枚举时为null，不能为原始值相同
//                    String attrValue = llmTicketSmartFillDTO.getAttrValue();
//                    if (StringUtils.isNotBlank(llmTicketSmartFillDTO.getOriginalValue())) {
//                        aigcTicketSmartFillDTO.setAttrValue(llmTicketSmartFillDTO.getOriginalValue());
//                    }
//                    aigcTicketSmartFillDTO.setOriginalValue(attrValue);
                }
            });
        });
//        if (CollectionUtils.isNotEmpty(llmTicketSmartFillRequestList)) {
//            aigcTicketSmartFillList.forEach(llmTicketSmartFillDTO -> {
//                if ("sex".equals(llmTicketSmartFillDTO.getStoreAttr())&&"1".equals(llmTicketSmartFillDTO.getAttrValue())){
//                    llmTicketSmartFillDTO.setAttrValue("男");
//                }
//                if ("sex".equals(llmTicketSmartFillDTO.getStoreAttr())&&"0".equals(llmTicketSmartFillDTO.getAttrValue())){
//                    llmTicketSmartFillDTO.setAttrValue("女");
//                }
//            });
//        }
        aigcTicketSmartFillResponse.setAigcTicketSmartFillList(aigcTicketSmartFillList);
        return AjaxResult.ok(aigcTicketSmartFillResponse);
    }

    private List<AigcTicketSmartFillDTO> convertTOAigcTicketSmartFillDTOS(List<AILLMTicketSmartFill> queryAILLMTicketSmartFill) {
        List<AigcTicketSmartFillDTO> aigcTicketSmartFillDTOlist=Lists.newArrayList();
        for (AILLMTicketSmartFill aillmTicketSmartFill :queryAILLMTicketSmartFill) {
        	aigcTicketSmartFillDTOlist.add(convertFromQueryAILLMTicketSmartFill(aillmTicketSmartFill));
        }
        return aigcTicketSmartFillDTOlist;
    }

    private AigcTicketSmartFillDTO convertFromQueryAILLMTicketSmartFill(AILLMTicketSmartFill aillmTicketSmartFill) {
        AigcTicketSmartFillDTO aigcTicketSmartFillDTO = new AigcTicketSmartFillDTO();
        aigcTicketSmartFillDTO.setStoreAttr(null);
        aigcTicketSmartFillDTO.setAttrValue(aillmTicketSmartFill.getAttributeValue());
        aigcTicketSmartFillDTO.setAttrName(aillmTicketSmartFill.getAttributeName());
        aigcTicketSmartFillDTO.setStoreAttrType(null);
        aigcTicketSmartFillDTO.setOriginalValue(aillmTicketSmartFill.getOriginalValue());
        return aigcTicketSmartFillDTO;
    }


    private List<QueryLLMTicketSmartFill> convertToQueryLLMTicketSmartFill(List<CrmCallTicketSmartFillDetail> callTicketSmartFillDetailList) {
        List<QueryLLMTicketSmartFill> queryLLMTicketSmartFillRequestlist=Lists.newArrayList();
        for (CrmCallTicketSmartFillDetail crmCallTicketSmartFillDetail :callTicketSmartFillDetailList) {
        	queryLLMTicketSmartFillRequestlist.add(convertFromCrmCallTicketSmartFillDetail(crmCallTicketSmartFillDetail));
        }
        return queryLLMTicketSmartFillRequestlist;
    }

    private QueryLLMTicketSmartFill convertFromCrmCallTicketSmartFillDetail(CrmCallTicketSmartFillDetail crmCallTicketSmartFillDetail) {
        QueryLLMTicketSmartFill queryLLMTicketSmartFillRequest = new QueryLLMTicketSmartFill();
        queryLLMTicketSmartFillRequest.setStoreAttr(crmCallTicketSmartFillDetail.getStoreAttr());
        queryLLMTicketSmartFillRequest.setAttrValueExample(crmCallTicketSmartFillDetail.getAttrValueExample());
        queryLLMTicketSmartFillRequest.setAttrDescribe(crmCallTicketSmartFillDetail.getAttrDescribe());
        queryLLMTicketSmartFillRequest.setAttrName(crmCallTicketSmartFillDetail.getAttrName());
        queryLLMTicketSmartFillRequest.setStoreAttrType(crmCallTicketSmartFillDetail.getStoreAttrType());
        return queryLLMTicketSmartFillRequest;
    }

    /**
     * 根据公司ID获取索引
     *
     * @return
     */
    private String embIndexName() {
        return TICKET_INFO_INDEX + SecurityUtil.getLoginUser().getCompanyId();
    }

    /**
     * AIGC智能填单保存
     *
     * @param
     * @return
     */
    @Override
    public AjaxResult addAigcTicketSmartFill(AddAigcTicketSmartFillRequest addAigcTicketSmartFillRequest) {
        log.info("AIGC智能填单保存:【{}】",JSONObject.toJSONString(addAigcTicketSmartFillRequest));
        if (Objects.isNull(addAigcTicketSmartFillRequest)) {
            return AjaxResult.ok();
        }
        List<AigcTicketSmartFillDTO> aigcTicketSmartFillList = addAigcTicketSmartFillRequest.getAigcTicketSmartFillList();
        log.info("AIGC智能填单保存过滤前属性列表:【{}】",JSONObject.toJSONString(addAigcTicketSmartFillRequest.getAigcTicketSmartFillList()));
        log.info("AIGC智能填单保存过滤前属性列表大小:【{}】",addAigcTicketSmartFillRequest.getAigcTicketSmartFillList().size());
        List<AigcTicketSmartFillDTO> smartFillDTOList = aigcTicketSmartFillList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getAttrValue()))
                .collect(Collectors.toList());
        log.info("AIGC智能填单保存过滤后属性列表:【{}】",JSONObject.toJSONString(smartFillDTOList));
        log.info("AIGC智能填单保存过滤后属性列表大小:【{}】",smartFillDTOList.size());
        addAigcTicketSmartFillRequest.setAigcTicketSmartFillList(smartFillDTOList);
        AddAigcTicketSmartFilESReplylRequest addAigcTicketSmartFilESReplylRequest = new AddAigcTicketSmartFilESReplylRequest();
        addAigcTicketSmartFilESReplylRequest.setAigcTicketSmartFillList(aigcTicketSmartFillList);
        checkInterfaceType(addAigcTicketSmartFillRequest,addAigcTicketSmartFilESReplylRequest);
        addAigcTicketSmartFillRequest.getAigcTicketSmartFillList().forEach(aigcTicketSmartFillDetail -> {
            if (StringUtils.isNotBlank(aigcTicketSmartFillDetail.getOriginalValue())){
                aigcTicketSmartFillDetail.setAttrValue(aigcTicketSmartFillDetail.getOriginalValue());
            }
            if ("1".equals(aigcTicketSmartFillDetail.getStoreAttrType())){
                log.info("保存智能填单策略实现-》工单-入参为：【{}】",JSONObject.toJSONString(aigcTicketSmartFillDetail));
                AigcTicketSmartFillStrategy aigcTicketSmartFillStrategy =
                        addTicketSmartFillFactory.getAigcTicketSmartFillStrategy(AigcTicketSmartFillEnum.WORK_ORDER);
                aigcTicketSmartFillStrategy.addAigcTicketSmartFillStrategy(aigcTicketSmartFillDetail,
                        addAigcTicketSmartFillRequest.getCustomerId(),addAigcTicketSmartFillRequest.getWorkRecordId());
            }
            if ("2".equals(aigcTicketSmartFillDetail.getStoreAttrType())){
//                setSexValue(aigcTicketSmartFillDetail);
                log.info("保存智能填单策略实现-》客户资料-入参为：【{}】",JSONObject.toJSONString(aigcTicketSmartFillDetail));
                AigcTicketSmartFillStrategy aigcTicketSmartFillStrategy =
                        addTicketSmartFillFactory.getAigcTicketSmartFillStrategy(AigcTicketSmartFillEnum.CUSTOMER);
                aigcTicketSmartFillStrategy.addAigcTicketSmartFillStrategy(aigcTicketSmartFillDetail,
                        addAigcTicketSmartFillRequest.getCustomerId(),addAigcTicketSmartFillRequest.getWorkRecordId());
            }
        });
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<TicketSmartFillLanguageResponse> getTicketSmartFillLanguage() {
        TicketSmartFillLanguageResponse ticketSmartFillLanguageResponse = new TicketSmartFillLanguageResponse();
        LambdaQueryWrapper<CrmCallTicketSmartFill> ticketSmartFillVoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        ticketSmartFillVoLambdaQueryWrapper.eq(CrmCallTicketSmartFill::getCompanyId,SecurityUtil.getLoginUser().getCompanyId());
        ticketSmartFillVoLambdaQueryWrapper.eq(CrmCallTicketSmartFill::getDataStatus,1);
        List<CrmCallTicketSmartFill> crmCallTicketSmartFills = callTicketSmartFillMapper.selectList(ticketSmartFillVoLambdaQueryWrapper);
        List<String>uniqueList=crmCallTicketSmartFills.stream().map(CrmCallTicketSmartFill::getLanguageCode)
                .collect(Collectors.toList());
        uniqueList=uniqueList.stream().distinct()
                .collect(Collectors.toList());
        ticketSmartFillLanguageResponse.setLanguagesCodeList(uniqueList);
        return AjaxResult.ok(ticketSmartFillLanguageResponse);
    }

    private static void setSexValue(AigcTicketSmartFillDTO aigcTicketSmartFillDetail) {
        if ("sex".equals(aigcTicketSmartFillDetail.getStoreAttr()) && "男".equals(aigcTicketSmartFillDetail.getAttrValue())){
            aigcTicketSmartFillDetail.setAttrValue("1");
        }
        if ("sex".equals(aigcTicketSmartFillDetail.getStoreAttr())  && "女".equals(aigcTicketSmartFillDetail.getAttrValue())){
            aigcTicketSmartFillDetail.setAttrValue("0");
        }
        if ("sex".equals(aigcTicketSmartFillDetail.getStoreAttr())  &&  "男生".equals(aigcTicketSmartFillDetail.getAttrValue())){
            aigcTicketSmartFillDetail.setAttrValue("1");
        }
        if ("sex".equals(aigcTicketSmartFillDetail.getStoreAttr())  &&  "女生".equals(aigcTicketSmartFillDetail.getAttrValue())){
            aigcTicketSmartFillDetail.setAttrValue("0");
        }
        // 说明是其他性别
        if ("sex".equals(aigcTicketSmartFillDetail.getStoreAttr()) &&StringUtils.isBlank(aigcTicketSmartFillDetail.getAttrValue())){
            aigcTicketSmartFillDetail.setAttrValue("2");
        }
    }

    private void checkInterfaceType(AddAigcTicketSmartFillRequest addAigcTicketSmartFillRequest,
                                    AddAigcTicketSmartFilESReplylRequest addAigcTicketSmartFilESReplylRequest) {
        // 说明是新增聊天记录
        if (addAigcTicketSmartFillRequest.getInterfaceType()==1){
            // 生成内容记录
            String contentId = UuidUtils.generateUuid();
            TicketContentIndex ticketContentIndex = new TicketContentIndex();
            ticketContentIndex.setWork_record_content_id(contentId);
            ticketContentIndex.setWork_record_id(addAigcTicketSmartFillRequest.getWorkRecordId());
            ticketContentIndex.setContent(JSONObject.toJSONString(addAigcTicketSmartFilESReplylRequest));
            ticketContentIndex.setContent_type(1);
            ticketContentIndex.setReply_type(9);
            ticketContentIndex.setReply_person(getUsername());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 智能填单时间
            ticketContentIndex.setReply_time(sdf.format(new Date()));
            log.info("智能填单时间保存聊天记录es入参：【{}】",JSONObject.toJSONString(ticketContentIndex));
            // 保存到ES中
            addAgentWorkRecordContent(ticketContentIndex, SecurityUtil.getLoginUser().getCompanyId());
        }
        // 说明是修改聊天记录
        if (addAigcTicketSmartFillRequest.getInterfaceType()==2){
            String index = "ticket_content_index_"+SecurityUtil.getLoginUser().getCompanyId();
            Map<String, Object> result = queryEsTicketInfo(addAigcTicketSmartFillRequest.getWorkRecordId());
            TicketContentIndex ticketContentIndex = (TicketContentIndex) result.get("data");
            String id = result.get("id").toString();
            ticketContentIndex.setContent(JSONObject.toJSONString(addAigcTicketSmartFilESReplylRequest));
            log.info("智能填单时间更新聊天记录es入参：【{}】",JSONObject.toJSONString(ticketContentIndex));
            elasticsearchUtil.updateDocument(index, id, ticketContentIndex);
        }
    }

    private void addAgentWorkRecordContent(TicketContentIndex ticketContentIndex, String companyId) {
        try {
            // 定义索引名称
            String indexName = "ticket_content_index_"+SecurityUtil.getLoginUser().getCompanyId();
            // 插入消息前判断该消息是否存在 根据contentId判断
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("work_record_content_id", ticketContentIndex.getWork_record_content_id())));
            SearchRequest searchRequest = new SearchRequest(indexName).source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            if (searchResponse.getHits().getTotalHits().value > 0) {
                return;
            }
            // 定义索引请求
            IndexRequest indexRequest = new IndexRequest(indexName);
            String value = new ObjectMapper().writeValueAsString(ticketContentIndex);
            indexRequest.source(value, XContentType.JSON);
            // 进行添加
            IndexResponse response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            restHighLevelClient.indices().refresh(new RefreshRequest(indexName), RequestOptions.DEFAULT);
            log.info(String.valueOf(response.getResult()));
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es保存工单聊天数据失败，异常信息：", e);
            }
        }
    }

    private Map<String,Object> queryEsTicketInfo(String workRecordId){
        String index = "ticket_content_index_"+SecurityUtil.getLoginUser().getCompanyId();
        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workRecordId));
        queryConditions.add(createCondition("reply_type", 9));
        queryConditions.add(createCondition(1));
        queryConditions.add(createCondition("reply_time", SortOrder.DESC));
        return elasticsearchUtil.searchInfoMapQuery(queryConditions, index, TicketContentIndex.class);
    }


    private String convertCodesToNames(String codeString, Map<String, String> codeToNameMap) {
        return Arrays.stream(codeString.split(","))
                .map(code -> codeToNameMap.getOrDefault(code.trim(), code.trim()))
                .collect(Collectors.joining(","));
    }

    //逻辑删除旧数据的时候，补充下data_status=1这个条件，这样可以排查是啥时候删除的旧数据
    private void deleteSmartFillDetail(String smartFillId, String userId) {
        smartFillDetailService.update(new LambdaUpdateWrapper<CrmCallTicketSmartFillDetail>()
                .set(CrmCallTicketSmartFillDetail::getDataStatus, Constants.DELETE)
                .set(CrmCallTicketSmartFillDetail::getModifier, userId)
                .set(CrmCallTicketSmartFillDetail::getModifyTime, new Date())
                .eq(CrmCallTicketSmartFillDetail::getDataStatus, Constants.NORMAL)
                .eq(CrmCallTicketSmartFillDetail::getSmartFillId, smartFillId));

    }

    private void saveSmartFillDetail(List<TicketSmartFillDetailVo> ticketSmartFillDetailList, String smartFillId, String userId) {
        List<CrmCallTicketSmartFillDetail> addDetailList = new ArrayList<>();
        int i = 1;
        for (TicketSmartFillDetailVo ticketSmartFillDetailVo : ticketSmartFillDetailList) {
            CrmCallTicketSmartFillDetail crmCallTicketSmartFillDetail = new CrmCallTicketSmartFillDetail();
            BeanUtils.copyProperties(ticketSmartFillDetailVo, crmCallTicketSmartFillDetail);
            Integer storeAttrOrNot = ticketSmartFillDetailVo.getStoreAttrOrNot();
            if (storeAttrOrNot != null && storeAttrOrNot.equals(1)) {
                //勾选了存储属性
                String storeAttrType = ticketSmartFillDetailVo.getStoreAttrType();
                if("1".equals(storeAttrType)){
                    crmCallTicketSmartFillDetail.setStoreAttr(ticketSmartFillDetailVo.getTicketAttribute());
                }else{
                    crmCallTicketSmartFillDetail.setStoreAttr(ticketSmartFillDetailVo.getCustomerAttribute());
                }
            }
            crmCallTicketSmartFillDetail.setDetailId(IdUtils.simpleUUID());
            crmCallTicketSmartFillDetail.setSmartFillId(smartFillId);
            crmCallTicketSmartFillDetail.setCreator(userId);
            crmCallTicketSmartFillDetail.setCreateTime(new Date());
            crmCallTicketSmartFillDetail.setModifier(userId);
            crmCallTicketSmartFillDetail.setModifyTime(new Date());
            crmCallTicketSmartFillDetail.setDataStatus(Constants.NORMAL);
            crmCallTicketSmartFillDetail.setAttrOrder(i++);
            addDetailList.add(crmCallTicketSmartFillDetail);
        }
        smartFillDetailService.saveBatch(addDetailList);
    }

    //添加或者更新操作，需要校验数据
    private AjaxResult checkCommonData(String languageCode, String ticketTypeCodes,
                                       List<TicketSmartFillDetailVo> ticketSmartFillDetailList, String smartFillId, String companyId) {
        //校验非空
        if (StringUtils.isEmpty(languageCode)) {
            //复用如下国际化
            return AjaxResult.failure(MessageUtils.get("answer.questions.not.null"));
        }

        if (StringUtils.isEmpty(ticketTypeCodes)) {
            return AjaxResult.failure(MessageUtils.get("ticket.smart.fill.ticket.type.can.not.be.empty"));
        }

        if (CollectionUtils.isEmpty(ticketSmartFillDetailList)) {
            return AjaxResult.failure(MessageUtils.get("ticket.smart.fill.configure.properties.can.not.be.empty"));
        }

        //用于判断入参中的标签内容是否重复
        Set<String> attrNameSet = new HashSet<>();
        for (TicketSmartFillDetailVo ticketSmartFillDetailVo : ticketSmartFillDetailList) {
            String attrName = ticketSmartFillDetailVo.getAttrName();
            String attrValueExample = ticketSmartFillDetailVo.getAttrValueExample();
            String attrDescribe = ticketSmartFillDetailVo.getAttrDescribe();
            if (StringUtils.isEmpty(attrName)) {
                return AjaxResult.failure(MessageUtils.get("ticket.smart.fill.attr.name.can.not.be.empty"));
            }
            if (StringUtils.isEmpty(attrValueExample)) {
                return AjaxResult.failure(MessageUtils.get("ticket.smart.fill.attr.value.example.can.not.be.empty"));
            }
            if (StringUtils.isEmpty(attrDescribe)) {
                return AjaxResult.failure(MessageUtils.get("ticket.smart.fill.attr.describe.can.not.be.empty"));
            }
            attrNameSet.add(attrName);
        }
        //多个属性名称之间的判重
        if (attrNameSet.size() != ticketSmartFillDetailList.size()) {
            //判断入参的属性名称内容是否有重复
            return AjaxResult.failure(MessageUtils.get("ticket.smart.fill.attr.name.repeat"));
        }
        //判重，语言+工单类型需要唯一（工单类型是包含式判断）
        //将本次入参的ticketTypeCode用逗号拆分
        // 将逗号分隔的字符串转换为列表并去重
        List<String> typeCodeList = Arrays.stream(ticketTypeCodes.split(","))
                .map(String::trim)
                .filter(code -> !code.isEmpty())
                .distinct()
                .collect(Collectors.toList());
        boolean exists;
        if (StringUtils.isEmpty(smartFillId)) {
            //新增操作
            exists = callTicketSmartFillMapper.existsByLanguageAndTicketTypes(languageCode, typeCodeList,companyId);
        } else {
            //更新操作
            exists = callTicketSmartFillMapper.existsByLanguageAndTicketTypesExcludeOwn(smartFillId, languageCode, typeCodeList,companyId);
        }
        if (exists) {
            return AjaxResult.failure(MessageUtils.get("ticket.smart.fill.language.and.type.union.exist"));
        }
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }
}
