package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAssessmentRulePoint;

import java.util.List;

/**
 * 评估规则扣分点Service接口
 */
public interface CrmAssessmentRulePointService extends IService<CrmAssessmentRulePoint> {
    
    /**
     * 分页查询扣分点
     *
     * @param pageParam 分页参数
     * @param rulePoint 查询条件
     * @return 分页结果
     */
    IPage<CrmAssessmentRulePoint> queryRulePointPages(IPage<Object> pageParam, CrmAssessmentRulePoint rulePoint);
    
    /**
     * 根据规则ID查询扣分点列表
     *
     * @param ruleId 规则ID
     * @return 扣分点列表
     */
    List<CrmAssessmentRulePoint> listRulePointsByRuleId(String ruleId);
    
    /**
     * 添加扣分点
     *
     * @param rulePoint 扣分点信息
     * @return 是否成功
     */
    boolean addRulePoint(CrmAssessmentRulePoint rulePoint);
    
    /**
     * 更新扣分点
     *
     * @param rulePoint 扣分点信息
     * @return 是否成功
     */
    boolean updateRulePoint(CrmAssessmentRulePoint rulePoint);
    
    /**
     * 删除扣分点
     *
     * @param ruleId 扣分点ID
     * @return 是否成功
     */
    int deleteRulePoint(String ruleId);
    
    /**
     * 获取扣分点详情
     *
     * @param pointId 扣分点ID
     * @return 扣分点详情
     */
    CrmAssessmentRulePoint getRulePointDetail(String pointId);

    void delPoiint(String ruleId);
}