package com.goclouds.crm.platform.call.service.impl;

import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.goclouds.crm.platform.call.domain.CrmCallHotlineAlarmRule;
import com.goclouds.crm.platform.call.domain.hotline.BusinessDataBean;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.TicketSatisfaction;
import com.goclouds.crm.platform.call.domain.hotline.*;
import com.goclouds.crm.platform.call.domain.vo.statis.*;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordEsResult;
import com.goclouds.crm.platform.call.service.CrmCallHotlineAlarmRuleService;
import com.goclouds.crm.platform.call.service.HotlineKeyIndicatorService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.utils.DateUtils;
import com.goclouds.crm.platform.common.utils.RedisCacheUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.system.UserClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysUserBaseInfoVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysUserVo;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.TimeZoneUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.Filter;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sunlinan
 * @Description: 热线指标
 * @Date: 2024-12-23 16:55
 **/

@Slf4j
@Service
@RequiredArgsConstructor
public class HotlineKeyIndicatorServiceImpl implements HotlineKeyIndicatorService {
    private final RestHighLevelClient restHighLevelClient;
    private final UserClient userClient;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private ConnectReportServiceImpl connectReportServiceImpl;

    @Autowired
    private CrmCallHotlineAlarmRuleService callHotlineAlarmRuleService;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    private static final String contactDetailsIndexPrefix = "kinesis_contact_details_";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS);
    //呼入
    private static final String INBOUND = "INBOUND";
    //呼出
    private static final String OUTBOUND = "OUTBOUND";

    @Override
    public AjaxResult saveHotlineRule(HotlineKeyIndicatorRule hotlineKeyIndicatorRule) {
        //将这部分内容转化为JSON保存到redis中
        SysUserVo loginUser = SecurityUtil.getLoginUser();
        String companyId = loginUser.getCompanyId();
        String userId = loginUser.getUserId();
        String jsonStr = JSONUtil.toJsonStr(hotlineKeyIndicatorRule);
//        log.info("入参的热线指标规则转化为JSON之后是：{}", jsonStr);
        RedisCacheUtil.setCacheObject(Constants.CONTACT_DETAILS_INDICATORS_OF_HOTLINE.concat(companyId), jsonStr);
        //除了存储到redis中一份，也要存储到mysql中一份
        //查询当前公司之前是否有数据存在，如果有，就更新，否则新增
        CrmCallHotlineAlarmRule crmCallHotlineAlarmRule = callHotlineAlarmRuleService.getOne(new LambdaQueryWrapper<CrmCallHotlineAlarmRule>()
                .eq(CrmCallHotlineAlarmRule::getCompanyId, companyId)
                .eq(CrmCallHotlineAlarmRule::getDataStatus, Constants.NORMAL));
        if (crmCallHotlineAlarmRule != null) {
            crmCallHotlineAlarmRule.setRuleContent(jsonStr);
            callHotlineAlarmRuleService.updateById(crmCallHotlineAlarmRule);
            return AjaxResult.ok();
        }
        //如果之前没有数据，就新增
        crmCallHotlineAlarmRule.setRuleId(UUID.randomUUID().toString().replace("-", ""));
        crmCallHotlineAlarmRule.setRuleContent(jsonStr);
        crmCallHotlineAlarmRule.setCompanyId(companyId);
        crmCallHotlineAlarmRule.setCreator(userId);
        crmCallHotlineAlarmRule.setCreateTime(new Date());
        crmCallHotlineAlarmRule.setModifier(userId);
        crmCallHotlineAlarmRule.setModifyTime(new Date());
        crmCallHotlineAlarmRule.setDataStatus(1);
        callHotlineAlarmRuleService.save(crmCallHotlineAlarmRule);
        return AjaxResult.ok();
    }

    @Override
    public HotlineKeyIndicatorRule queryHotlineRule() {
        HotlineKeyIndicatorRule hotlineKeyIndicatorRule = new HotlineKeyIndicatorRule();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String cacheObject = RedisCacheUtil.getCacheObject(Constants.CONTACT_DETAILS_INDICATORS_OF_HOTLINE.concat(companyId));
        if (StringUtil.isEmpty(cacheObject)) {
            //如果redis中数据为空，就到数据库中查询，如果数据库中的数据还是为空，就用默认的返回
            CrmCallHotlineAlarmRule crmCallHotlineAlarmRule = callHotlineAlarmRuleService.getOne(new LambdaQueryWrapper<CrmCallHotlineAlarmRule>()
                    .eq(CrmCallHotlineAlarmRule::getCompanyId, companyId)
                    .eq(CrmCallHotlineAlarmRule::getDataStatus, Constants.NORMAL));
            if (crmCallHotlineAlarmRule != null) {
                String ruleContent = crmCallHotlineAlarmRule.getRuleContent();
                if (StringUtil.isNotEmpty(ruleContent)) {
                    hotlineKeyIndicatorRule = JSONUtil.toBean(ruleContent, HotlineKeyIndicatorRule.class);
                } else {
                    //设置一个默认的配置(如果查询的时候为空，就用这个默认的)
                    String defaultHotlineKeyIndicatorRule = readJsonFileAsString();
                    if (StringUtil.isNotEmpty(defaultHotlineKeyIndicatorRule)) {
                        hotlineKeyIndicatorRule = JSON.parseObject(defaultHotlineKeyIndicatorRule, HotlineKeyIndicatorRule.class);
                    }
                }
            } else {
                String defaultHotlineKeyIndicatorRule = readJsonFileAsString();
                if (StringUtil.isNotEmpty(defaultHotlineKeyIndicatorRule)) {
                    hotlineKeyIndicatorRule = JSON.parseObject(defaultHotlineKeyIndicatorRule, HotlineKeyIndicatorRule.class);
                }
            }
        } else {
            hotlineKeyIndicatorRule = JSONUtil.toBean(cacheObject, HotlineKeyIndicatorRule.class);
        }
        return hotlineKeyIndicatorRule;
    }

    @Override
    public AjaxResult<InboundResult> queryInboundResult(HotlineRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板（呼入来电），入参信息是：{}", jsonStr);
        String connectAlias = hotlineRequest.getConnectAlias();
        List<ConnectQueueLatitudeAgentVo> filteredAgentList = new ArrayList<>();
        //在线座席数量，用队列实时指标中的坐席状态列表-在线，代码参考：queryQueueRealTimeIndex ，只受到联络明细筛选条件的影响
        List<ConnectQueueLatitudeAgentVo> connectQueueLatitudeAgentVoList = connectReportServiceImpl.publicQueryQueueRealTimeIndex(null);
        if (CollectionUtils.isNotEmpty(connectQueueLatitudeAgentVoList)) {
            //由于是实时指标数据，所以只考虑联络线路那个筛选条件，因为我要调用的其他人的接口，我们的共同筛选条件只有这个
            if (StringUtils.isNotBlank(connectAlias)) {
                filteredAgentList = connectQueueLatitudeAgentVoList.stream().filter(vo -> connectAlias.equals(vo.getConnectAlias())).collect(Collectors.toList());
            } else {
                //如果筛选条件为空，那么意味着查询所有
                filteredAgentList.addAll(connectQueueLatitudeAgentVoList);
            }
        }
        //在线座席数量
        Integer onlineAgentNum = 0;
        if (CollectionUtils.isNotEmpty(filteredAgentList)) {
            onlineAgentNum = filteredAgentList.stream().mapToInt(vo -> vo.getOnlineQuantity() == null ? 0 : vo.getOnlineQuantity()).sum();
        }

        //正在通话数量（参考实时队列指标-性能列表-已安排）
        //正在排队数量（参考实时队列指标-性能列表-已排队）
        List<ConnectQueueLatitudePropertyVo> filteredList = new ArrayList<>();
        List<ConnectQueueLatitudePropertyVo> propertyList = connectReportServiceImpl.publicQueryQueuePropertyIndex(null);
        if (CollectionUtils.isNotEmpty(propertyList)) {
            //由于是实时指标数据，所以只考虑联络线路那个筛选条件，因为我要调用的其他人的接口，我们的共同筛选条件只有这个
            if (StringUtils.isNotBlank(connectAlias)) {
                filteredList = propertyList.stream().filter(vo -> connectAlias.equals(vo.getConnectAlias())).collect(Collectors.toList());
            } else {
                //如果筛选条件为空，那么意味着查询所有
                filteredList.addAll(propertyList);
            }
        }

        //正在排队数量(只有这个需要返回颜色)
        Integer contactsQueued = 0;
        //正在通话数量（这个颜色返回null）
        Integer contactsScheduled = 0;

        if (CollectionUtils.isNotEmpty(filteredList)) {
            contactsQueued = filteredList.stream().mapToInt(vo -> vo.getContactsQueued() == null ? 0 : vo.getContactsQueued()).sum();

            contactsScheduled = filteredList.stream().mapToInt(vo -> vo.getContactsScheduled() == null ? 0 : vo.getContactsScheduled()).sum();
        }
        //查询设置的规则，便于后续计算控制颜色显示
        HotlineKeyIndicatorRule hotlineRule = queryHotlineRule();
        List<ColorRange> colorRangeList = hotlineRule.getIvr().getIvrQueue();
        List<DataResult> dataResultList = new ArrayList<>();
        //处理正在排队数量
        //如果被除数是0，那么用如下方法返回颜色进行处理
        if (onlineAgentNum == 0) {
            String colorCodeWhenDividendZero = getColorCodeWhenDividendZero(colorRangeList);
            dataResultList.add(new DataResult().setDataTypeName(MessageUtils.get("hotline.non.service.time.queue")).setNum(contactsQueued).setColorCode(colorCodeWhenDividendZero));
        } else {
            double ratio = (double) contactsQueued / onlineAgentNum;
            String findColorCode = findColorCodeForRatio(ratio, colorRangeList);
            dataResultList.add(new DataResult().setDataTypeName(MessageUtils.get("hotline.non.service.time.queue")).setNum(contactsQueued).setColorCode(findColorCode));
        }
        //添加正在通话数量
        dataResultList.add(new DataResult().setDataTypeName(MessageUtils.get("hotline.non.service.time.calling")).setNum(contactsScheduled).setColorCode(null));
        InboundResult inboundResult = new InboundResult().setTotalNum(onlineAgentNum).setDataResultList(dataResultList);
        return AjaxResult.ok(inboundResult);
    }

    @Override
    public AjaxResult<ConnectionRateResult> queryConnectionRateResult(ConnectionRateRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板（接通率），入参信息是：{}", jsonStr);
        //如何认定是接通：座席应答时间不为null
        //默认是查询所有（即不区分是呼入还是呼出）
        //0-所有 1-呼入接通率 2-服务时间呼入接通率 3-呼出接通率
        Integer showType = hotlineRequest.getShowType();
        //获取配置颜色
        ConnectionRate connectionRate = queryHotlineRule().getConnectionRate();
        List<ColorRange> colorRangeList;
        //根据入参的不同，返回不同的颜色
        if (1 == showType) {
            colorRangeList = connectionRate.getInboundConnectionRate();
        } else if (2 == showType) {
            colorRangeList = connectionRate.getServiceTimeInboundConnectionRate();
        } else if (3 == showType) {
            colorRangeList = connectionRate.getOutboundConnectionRate();
        } else {
            //如果不属于上述类型，则认为是所有
            colorRangeList = connectionRate.getTotalConnectionRate();
        }

        List<KinesisContactVo> handleDataList = new ArrayList<>();
        List<KinesisContactVo> kinesisContactVoList = queryContactDetails(hotlineRequest);
        //如果返回的数据为空，按照0来处理返回逻辑
        if (CollectionUtils.isEmpty(kinesisContactVoList)) {
            String colorCode = getColorCodeWhenDividendZero(colorRangeList);
            ConnectionRateResult connectionRateResult = new ConnectionRateResult().setTotalNum(0).setConnectionNum(0).setConnectionRate(BigDecimal.ZERO).setColorCode(colorCode);
            return AjaxResult.ok(connectionRateResult);
        }
        //如果查询到的数据不为空，走如下逻辑
        //默认是所有
        handleDataList.addAll(kinesisContactVoList);
        if (1 == showType) {
            handleDataList = kinesisContactVoList.stream().filter(vo -> INBOUND.equals(vo.getIncomingOutgoing())).filter(vo -> vo.getCallTime() != null).collect(Collectors.toList());
        } else if (2 == showType) {
            //todo 待提供上线数据

        } else if (3 == showType) {
            handleDataList = kinesisContactVoList.stream().filter(vo -> OUTBOUND.equals(vo.getIncomingOutgoing())).filter(vo -> vo.getCallTime() != null).collect(Collectors.toList());
        } else {
            //总接通率
            handleDataList = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() != null).collect(Collectors.toList());
        }

        int totalNum = kinesisContactVoList.size();
        int connectionNum = handleDataList.size();

        //如果根据条件筛选之后的结果为空
        if (CollectionUtils.isEmpty(handleDataList)) {
            String colorCode = getColorCodeWhenDividendZero(colorRangeList);
            ConnectionRateResult connectionRateResult = new ConnectionRateResult().setTotalNum(totalNum).setConnectionNum(connectionNum).setConnectionRate(BigDecimal.ZERO).setColorCode(colorCode);
            return AjaxResult.ok(connectionRateResult);
        }

        //如果根据条件筛选之后的结果不为空
        double ratio = (double) connectionNum / totalNum;
        String findColorCode = findColorCodeForRatio(ratio * 100, colorRangeList);

        ConnectionRateResult connectionRateResult = new ConnectionRateResult().setTotalNum(totalNum).setConnectionNum(connectionNum).setConnectionRate(handlePercentage(BigDecimal.valueOf(ratio))).setColorCode(findColorCode);

        return AjaxResult.ok(connectionRateResult);
    }

    @Override
    public AjaxResult<TotalCallsResult> queryTotalCallsResult(HotlineRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板（总通话量），入参信息是：{}", jsonStr);
        //是否展示呼入量激增 0-不展示 1-展示
        Integer showSurgeFlag = 0;
        List<KinesisContactVo> inBoundDataList = new ArrayList<>();
        List<KinesisContactVo> outBoundDataList = new ArrayList<>();
        List<DataResult> dataResultList = new ArrayList<>();
        List<KinesisContactVo> kinesisContactVoList = queryContactDetails(hotlineRequest);
        //如果返回的数据为空，按照0来处理返回逻辑
        if (CollectionUtils.isEmpty(kinesisContactVoList)) {
            TotalCallsResult totalCallsResult = new TotalCallsResult().setTotalNum(0).setDataResultList(dataResultList).setShowSurgeFlag(showSurgeFlag);
            return AjaxResult.ok(totalCallsResult);
        }

        //计算呼入量(不用考虑是否接通)
        inBoundDataList = kinesisContactVoList.stream().filter(vo -> INBOUND.equals(vo.getIncomingOutgoing())).collect(Collectors.toList());

        //计算呼出量(不用考虑是否接通)
        outBoundDataList = kinesisContactVoList.stream().filter(vo -> OUTBOUND.equals(vo.getIncomingOutgoing())).collect(Collectors.toList());

        dataResultList.add(new DataResult().setDataTypeName(MessageUtils.get("hotline.non.service.time.inbound.num")).setNum(inBoundDataList.size()));
        dataResultList.add(new DataResult().setDataTypeName(MessageUtils.get("hotline.non.service.time.outbound.num")).setNum(outBoundDataList.size()));

        //如下关于判断是否激增的查询统计逻辑，不受上述时间条件的控制，是独立的判断方式
        //用如下时间条件，判断近24小时呼入量 是否 比过去24小时呼入量激增 (关于激增的判断，不用考虑下拉筛选条件，只考虑如下计算的时间条件)
        String currentTime = getHoursAgo(0);
        String hour24Ago = getHoursAgo(24);
        String hour48Ago = getHoursAgo(48);

        //获取近24小时呼入量
        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        boolQueryBuilder1.must(QueryBuilders.termQuery("incomingOutgoing", INBOUND));
        boolQueryBuilder1.must(QueryBuilders.rangeQuery("createTime").gte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, hour24Ago)).lte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, currentTime)));
        List<KinesisContactDetailsVo> kinesisContactVo24List = querySpecialContactDetails(boolQueryBuilder1);

        //获取过去24小时呼入量
        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
        boolQueryBuilder2.must(QueryBuilders.termQuery("incomingOutgoing", INBOUND));
        boolQueryBuilder2.must(QueryBuilders.rangeQuery("createTime").gte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, hour48Ago)).lte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, hour24Ago)));
        List<KinesisContactDetailsVo> kinesisContactVo48List = querySpecialContactDetails(boolQueryBuilder2);

        HotlineKeyIndicatorRule hotlineKeyIndicatorRule = queryHotlineRule();
        Double surgeThreshold = hotlineKeyIndicatorRule.getInbound().getSurgeThreshold();
        Integer surge = isSurge(kinesisContactVo24List.size(), kinesisContactVo48List.size(), surgeThreshold);

        TotalCallsResult totalCallsResult = new TotalCallsResult().setTotalNum(kinesisContactVoList.size()).setDataResultList(dataResultList).setShowSurgeFlag(surge);
        return AjaxResult.ok(totalCallsResult);
    }

    @Override
    public AjaxResult<CallLossResult> queryCallLossResult(CallLossRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板（热线呼损情况），入参信息是：{}", jsonStr);
        CallLossRate callLossRate = queryHotlineRule().getCallLossRate();
        List<KinesisContactVo> kinesisContactVoList = queryContactDetails(hotlineRequest);
        //1-呼损量 2-呼损率
        Integer showType = hotlineRequest.getShowType();
        //如果返回的数据为空，直接返回如下处理结果
        if (CollectionUtils.isEmpty(kinesisContactVoList)) {
            List<ColorRange> totalCallLossRate = callLossRate.getTotalCallLossRate();
            String colorCodeForNum = findColorCodeForNum(BigDecimal.ZERO, totalCallLossRate);
            CallLossResult totalCallsResult = new CallLossResult().setCallLossList(Arrays.asList()).setTotalCallLoss(new TotalCallLoss().setTotalNum(0).setCallLossNum(0).setRate(BigDecimal.ZERO).setColorCode(colorCodeForNum));
            return AjaxResult.ok(totalCallsResult);
        }

        List<CallLoss> callLossList = new ArrayList<>();
        //呼叫尝试次数（即呼叫总数）
        int totalNum = kinesisContactVoList.size();
        //未接来电
        handleMissedCall(kinesisContactVoList, callLossRate, totalNum, callLossList, showType);
        //ivr放弃
        handleIvrAbandon(kinesisContactVoList, callLossRate, totalNum, callLossList, showType);
        //排队放弃
        handleQueueAbandon(kinesisContactVoList, callLossRate, totalNum, callLossList, showType);
        //todo 非服务时间呼入（待提供上线数据）
//        handleNonServiceTimeInbound(kinesisContactVoList, callLossRate, totalNum, callLossList, showType);
        //总呼损
        TotalCallLoss totalCallLoss = handleTotalCallLoss(kinesisContactVoList, callLossRate, totalNum, showType);
        //返回最终结果
        CallLossResult totalCallsResult = new CallLossResult().setCallLossList(callLossList).setTotalCallLoss(totalCallLoss);
        return AjaxResult.ok(totalCallsResult);
    }

    @Override
    public AjaxResult<AvgSatisfactionResult> queryAvgSatisfactionResult(AvgSatisfactionRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板（满意度平均分），入参信息是：{}", jsonStr);
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义索引名称
            String indexName = ticketIndex + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
                return AjaxResult.ok();
            }
        } catch (IOException e) {
            log.error("查询满意度评分，对工单索引进行检查操作时出现异常：", e);
        }
        AvgSatisfactionResult result = handleAvgSatisfactionResult(hotlineRequest);
        return AjaxResult.ok(result);
    }

    @Override
    public AjaxResult<AvgCallTimeResult> queryAvgCallTimeResult(AvgCallTimeRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板（平均通话时长），入参信息是：{}", jsonStr);
        //这个不用考虑看板规则颜色控制
        List<KinesisContactVo> kinesisContactVoList = queryContactDetails(hotlineRequest);
        //如果返回的数据为空，前端展示--
        if (CollectionUtils.isEmpty(kinesisContactVoList)) {
            return AjaxResult.ok();
        }
        AvgCallTimeResult result = handleAvgCallTimeResult(hotlineRequest);
        return AjaxResult.ok(result);
    }

    @Override
    public AjaxResult<TeamBusinessIndicatorsResult> queryTeamBusinessNoTrend(HotlineRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板-业务团队指标>>>>>>queryTeamBusinessNoTrend，入参信息是：{}", jsonStr);
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                //这个索引不能用Java创建，因为模板不再es中配置，是负责人用Java中写的
//                createIndex(indexName);
                return AjaxResult.ok();
            }
        } catch (IOException e) {
            log.error("热线关键指标看板-业务团队指标接口>>>>>>queryTeamBusinessNoTrend，对联络明细索引进行检查操作时出现异常：", e);
            return AjaxResult.ok();
        }
        //查询颜色配置规则
        HotlineKeyIndicatorRule hotlineKeyIndicatorRule = queryHotlineRule();

        //处理对比时间的数据参数
        Integer timeRangeCode = hotlineRequest.getTimeRangeCode();
        String startTime = hotlineRequest.getStartTime();
        String endTime = hotlineRequest.getEndTime();
        Map<String, String> timeMap = handleCompareTime(timeRangeCode, startTime, endTime);

        //呼入转接率： 呼入(incomingOutgoing) 转接（用isSwitch这个字段）  呼入数量是分母，转接数量是分子
        List<ColorRange> inboundSwitchRateColorList = hotlineKeyIndicatorRule.getOtherSetting().getInboundSwitchRate();
        BusinessDataBean inboundSwitchRate = calculateDiffRate(hotlineRequest, timeMap, INBOUND, "isSwitch", inboundSwitchRateColorList);
        //呼出转接率： 呼出数量是分母，转接数量是分子
        List<ColorRange> outboundSwitchRateColorList = hotlineKeyIndicatorRule.getOtherSetting().getOutboundSwitchRate();
        BusinessDataBean outboundSwitchRate = calculateDiffRate(hotlineRequest, timeMap, OUTBOUND, "isSwitch", outboundSwitchRateColorList);

        //呼入座席挂断率：hangingType : AGENT_DISCONNECT 呼入数量是分母，座席挂断数量是分子
        List<ColorRange> inboundAgentHangUpRateColorList = hotlineKeyIndicatorRule.getOtherSetting().getInboundAgentHangUpRate();
        BusinessDataBean inboundAgentHangUpRate = calculateDiffRate(hotlineRequest, timeMap, INBOUND, "hangingType", inboundAgentHangUpRateColorList);
        //呼出座席挂断率： 呼出数量是分母，座席挂断数量是分子
        List<ColorRange> outboundAgentHangUpRateColorList = hotlineKeyIndicatorRule.getOtherSetting().getOutboundAgentHangUpRate();
        BusinessDataBean outboundAgentHangUpRate = calculateDiffRate(hotlineRequest, timeMap, OUTBOUND, "hangingType", outboundAgentHangUpRateColorList);

        //重复进线率： 也是从字面意思，按照页面上用于时间切换范围来计算（客户电话  重复进线率 >=2）
        List<KinesisContactVo> kinesisContactVoList = queryContactDetails(hotlineRequest);
        BusinessDataBean repeatEntryRate = calRepeatEntryRate(kinesisContactVoList);

        TeamBusinessIndicatorsResult teamBusinessIndicatorsResult = new TeamBusinessIndicatorsResult();
        teamBusinessIndicatorsResult.setInboundSwitchRate(inboundSwitchRate);
        teamBusinessIndicatorsResult.setOutboundSwitchRate(outboundSwitchRate);
        teamBusinessIndicatorsResult.setInboundAgentHangUpRate(inboundAgentHangUpRate);
        teamBusinessIndicatorsResult.setOutboundAgentHangUpRate(outboundAgentHangUpRate);
        teamBusinessIndicatorsResult.setRepeatEntryRate(repeatEntryRate);

        return AjaxResult.ok(teamBusinessIndicatorsResult);
    }

    @Override
    public AjaxResult<TeamBusinessIndicatorsResult> queryTeamBusinessTrendPartOne(HotlineRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板-业务团队指标>>>>>>queryTeamBusinessTrendPartOne，入参信息是：{}", jsonStr);
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
//                createIndex(indexName);
                return AjaxResult.ok();
            }
        } catch (IOException e) {
            log.error("热线关键指标看板-业务团队指标接口>>>>>>queryTeamBusinessTrendPartOne，对联络明细索引进行检查操作时出现异常：", e);
            return AjaxResult.ok();
        }

        //处理对比时间的数据参数
        Integer timeRangeCode = hotlineRequest.getTimeRangeCode();
        String startTime = hotlineRequest.getStartTime();
        String endTime = hotlineRequest.getEndTime();
        Map<String, String> timeMap = handleCompareTime(timeRangeCode, startTime, endTime);

        //总呼入时长：用总时长totalTime
        BusinessDataBean totalInboundTime = calculateIncomingOutgoingTime(hotlineRequest, timeMap, INBOUND);
        //总呼出时长：
        BusinessDataBean totalOutboundTime = calculateIncomingOutgoingTime(hotlineRequest, timeMap, OUTBOUND);
        //10s坐席应答量
        BusinessDataBean agentResponseTenSecond = calAgentResponseTenSecond(hotlineRequest, timeMap);

        TeamBusinessIndicatorsResult teamBusinessIndicatorsResult = new TeamBusinessIndicatorsResult();
        teamBusinessIndicatorsResult.setTotalInboundTime(totalInboundTime);
        teamBusinessIndicatorsResult.setTotalOutboundTime(totalOutboundTime);
        teamBusinessIndicatorsResult.setAgentResponseTenSecond(agentResponseTenSecond);

        return AjaxResult.ok(teamBusinessIndicatorsResult);
    }

    @Override
    public AjaxResult<TeamBusinessIndicatorsResult> queryTeamBusinessTrendPartTwo(HotlineRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板-业务团队指标>>>>>>queryTeamBusinessTrendPartTwo，入参信息是：{}", jsonStr);
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
//                createIndex(indexName);
                return AjaxResult.ok();
            }
        } catch (IOException e) {
            log.error("热线关键指标看板-业务团队指标接口>>>>>>queryTeamBusinessTrendPartTwo，对联络明细索引进行检查操作时出现异常：", e);
            return AjaxResult.ok();
        }

        //处理对比时间的数据参数
        Integer timeRangeCode = hotlineRequest.getTimeRangeCode();
        String startTime = hotlineRequest.getStartTime();
        String endTime = hotlineRequest.getEndTime();
        Map<String, String> timeMap = handleCompareTime(timeRangeCode, startTime, endTime);


        //平均呼入时长：
        BusinessDataBean avgInboundTime = calculateDiffAvgTime(hotlineRequest, timeMap, INBOUND, "totalTime", Arrays.asList());
        //平均呼出时长：
        BusinessDataBean avgOutboundTime = calculateDiffAvgTime(hotlineRequest, timeMap, OUTBOUND, "totalTime", Arrays.asList());
        HotlineKeyIndicatorRule hotlineRule = queryHotlineRule();
        List<ColorRange> colorRangeList = hotlineRule.getIvr().getAvgInboundQueueTime();
        //平均呼入排队时长：
        BusinessDataBean avgInboundQueueTime = calculateDiffAvgTime(hotlineRequest, timeMap, INBOUND, "queueWaitTime", colorRangeList);

        TeamBusinessIndicatorsResult teamBusinessIndicatorsResult = new TeamBusinessIndicatorsResult();
        teamBusinessIndicatorsResult.setAvgInboundTime(avgInboundTime);
        teamBusinessIndicatorsResult.setAvgOutboundTime(avgOutboundTime);
        teamBusinessIndicatorsResult.setAvgInboundQueueTime(avgInboundQueueTime);

        return AjaxResult.ok(teamBusinessIndicatorsResult);
    }

    @Override
    public AjaxResult<TeamBusinessIndicatorsResult> queryTeamBusinessTrendPartThree(HotlineRequest hotlineRequest) {
        String jsonStr = JSONUtil.toJsonStr(hotlineRequest);
        log.info("查询热线关键指标看板-业务团队指标>>>>>>queryTeamBusinessTrendPartThree，入参信息是：{}", jsonStr);
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
//                createIndex(indexName);
                return AjaxResult.ok();
            }
        } catch (IOException e) {
            log.error("热线关键指标看板-业务团队指标接口>>>>>>queryTeamBusinessTrendPartThree，对联络明细索引进行检查操作时出现异常：", e);
            return AjaxResult.ok();
        }
        //查询颜色配置规则
        HotlineKeyIndicatorRule hotlineKeyIndicatorRule = queryHotlineRule();

        //处理对比时间的数据参数
        Integer timeRangeCode = hotlineRequest.getTimeRangeCode();
        String startTime = hotlineRequest.getStartTime();
        String endTime = hotlineRequest.getEndTime();
        Map<String, String> timeMap = handleCompareTime(timeRangeCode, startTime, endTime);

        //平均呼入互动时长：
        BusinessDataBean avgInboundChatTime = calculateDiffAvgTime(hotlineRequest, timeMap, INBOUND, "interactionTime", Arrays.asList());
        //平均呼出互动时长：
        BusinessDataBean avgOutboundChatTime = calculateDiffAvgTime(hotlineRequest, timeMap, OUTBOUND, "interactionTime", Arrays.asList());

        //平均呼入ACW时长：
        List<ColorRange> inboundAvgTime = hotlineKeyIndicatorRule.getAcw().getInboundAvgTime();
        BusinessDataBean avgInboundAcwTime = calculateDiffAvgTime(hotlineRequest, timeMap, INBOUND, "acwDuration", inboundAvgTime);
        //平均呼出ACW时长：
        List<ColorRange> outboundAvgTimeColorList = hotlineKeyIndicatorRule.getAcw().getOutboundAvgTime();
        BusinessDataBean avgOutboundAcwTime = calculateDiffAvgTime(hotlineRequest, timeMap, OUTBOUND, "acwDuration", outboundAvgTimeColorList);

        TeamBusinessIndicatorsResult teamBusinessIndicatorsResult = new TeamBusinessIndicatorsResult();
        teamBusinessIndicatorsResult.setAvgInboundChatTime(avgInboundChatTime);
        teamBusinessIndicatorsResult.setAvgOutboundChatTime(avgOutboundChatTime);
        teamBusinessIndicatorsResult.setAvgInboundAcwTime(avgInboundAcwTime);
        teamBusinessIndicatorsResult.setAvgOutboundAcwTime(avgOutboundAcwTime);

        return AjaxResult.ok(teamBusinessIndicatorsResult);
    }

    @Override
    public AjaxResult<List<SysUserBaseInfoVo>> queryUserInfoListByCompanyId() {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        R<List<SysUserBaseInfoVo>> listR = userClient.queryUserInfoListByCompanyId(companyId);
        if (AjaxResult.SUCCESS == listR.getCode()) {
            List<SysUserBaseInfoVo> list = listR.getData();
            return AjaxResult.ok(list);
        }
        return AjaxResult.ok(Arrays.asList());
    }

    @Override
    public AjaxResult saveHotlineAlarmEmailSetting(String userIds) {
        if (StringUtil.isNotEmpty(userIds)) {
            SysUserVo loginUser = SecurityUtil.getLoginUser();
            String companyId = loginUser.getCompanyId();
            String userId = loginUser.getUserId();
            //查询当前公司之前是否有数据存在，如果有，就更新，否则新增
            CrmCallHotlineAlarmRule crmCallHotlineAlarmRule = callHotlineAlarmRuleService.getOne(new LambdaQueryWrapper<CrmCallHotlineAlarmRule>()
                    .eq(CrmCallHotlineAlarmRule::getCompanyId, companyId)
                    .eq(CrmCallHotlineAlarmRule::getDataStatus, Constants.NORMAL));
            if (crmCallHotlineAlarmRule != null) {
                crmCallHotlineAlarmRule.setUserId(userIds);
                callHotlineAlarmRuleService.updateById(crmCallHotlineAlarmRule);
                return AjaxResult.ok();
            }
            //如果之前没有数据，就新增
            CrmCallHotlineAlarmRule hotlineAlarmRule = new CrmCallHotlineAlarmRule();
            hotlineAlarmRule.setRuleId(UUID.randomUUID().toString().replace("-", ""));
            hotlineAlarmRule.setUserId(userIds);
            hotlineAlarmRule.setCompanyId(companyId);
            hotlineAlarmRule.setCreator(userId);
            hotlineAlarmRule.setCreateTime(new Date());
            hotlineAlarmRule.setModifier(userId);
            hotlineAlarmRule.setModifyTime(new Date());
            hotlineAlarmRule.setDataStatus(1);
            callHotlineAlarmRuleService.save(hotlineAlarmRule);
        }
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<String> queryHotlineAlarmEmailSetting() {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        CrmCallHotlineAlarmRule crmCallHotlineAlarmRule = callHotlineAlarmRuleService.getOne(new LambdaQueryWrapper<CrmCallHotlineAlarmRule>()
                .eq(CrmCallHotlineAlarmRule::getCompanyId, companyId)
                .eq(CrmCallHotlineAlarmRule::getDataStatus, Constants.NORMAL));
        if (crmCallHotlineAlarmRule != null) {
            String userIds = crmCallHotlineAlarmRule.getUserId();
            return AjaxResult.ok(userIds);
        }
        return AjaxResult.ok();
    }

    //读取默认配置规则
    private String readJsonFileAsString() {
        try {
            Resource resource = resourceLoader.getResource("classpath:json/default-hotline-rule.json");
            StringBuilder jsonString = new StringBuilder();
            try (InputStream inputStream = resource.getInputStream(); InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8); BufferedReader bufferedReader = new BufferedReader(reader)) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    jsonString.append(line);
                }
            }
            return jsonString.toString();
        } catch (IOException e) {
            log.error("读取json文件中的默认热线指标配置规则出现异常：{}", e);
            return null;
        }
    }

    //处理时间查询条件
    public Map<String, String> handleTimeQueryCondition(Integer timeRangeCode, String customStartTime, String customEndTime) {
        Map<String, String> resultMap = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();

        LocalDateTime startTime = null;
        LocalDateTime endTime = null;

        //如果自定义时间没有传值，就默认近24小时
        if (StringUtil.isEmpty(customStartTime) || StringUtil.isEmpty(customEndTime)) {
            customStartTime = getHoursAgo(24);
            customEndTime = getHoursAgo(0);
        }

        switch (timeRangeCode) {
            case 0: // 当天
                startTime = now.truncatedTo(ChronoUnit.DAYS);
                endTime = now.truncatedTo(ChronoUnit.DAYS).plusDays(1).minusSeconds(1);
                break;
            case 1: // 近24小时
                startTime = now.minusHours(24);
                endTime = now;
                break;
            case 2: // 近2天
                startTime = now.minusDays(2);
                endTime = now;
                break;
            case 3: // 近3天
                startTime = now.minusDays(3);
                endTime = now;
                break;
            case 4: // 近7天
                startTime = now.minusDays(7);
                endTime = now;
                break;
            case 5: // 近1个月
                startTime = now.minusMonths(1);
                endTime = now;
                break;
            case 6: // 自定义时间
                startTime = LocalDateTime.parse(customStartTime, formatter);
                endTime = LocalDateTime.parse(customEndTime, formatter);
                break;
            default:
                break;
        }
        //将入参时间进行处理，转化为UTC+8，因为es中存储的数据是UTC+8的时间
        if (null != startTime && null != endTime) {
            String formatDateStart = TimeZoneUtils.requestTimeConversion(startTime.format(formatter));
            String formatDateEnd = TimeZoneUtils.requestTimeConversion(endTime.format(formatter));

            resultMap.put("startTime", formatDateStart);
            resultMap.put("endTime", formatDateEnd);
        }
        return resultMap;
    }

    //只用前端页面的入参条件查询，处理联络明细查询条件
    private BoolQueryBuilder packageContactSearchBuilder(HotlineRequest hotlineRequest) {
        Integer timeRangeCode = hotlineRequest.getTimeRangeCode();
        String startTime = hotlineRequest.getStartTime();
        String endTime = hotlineRequest.getEndTime();

        // 处理时间条件
        Map<String, String> timeResultMap = handleTimeQueryCondition(timeRangeCode, startTime, endTime);
        String formatDateStart = timeResultMap.get("startTime");
        String formatDateEnd = timeResultMap.get("endTime");
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 添加 callChannel 字段为 VOICE 的查询条件
        boolQueryBuilder.must(QueryBuilders.termQuery("callChannel", "VOICE"));

        // 时间范围查询
        if (StringUtils.isNotEmpty(formatDateStart) && StringUtils.isNotEmpty(formatDateEnd)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("createTime").gte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, formatDateStart)).lte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, formatDateEnd)));
        }

        //联络线路条件
        String connectId = hotlineRequest.getConnectAlias();
        if (StringUtil.isNotEmpty(connectId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("connectAlias", connectId));
        }

        //渠道类型条件
        String channelTypeId = hotlineRequest.getChannelTypeId();
        if (StringUtil.isNotEmpty(channelTypeId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("channelTypeId", channelTypeId));
        }

        //选择团队条件
        String deptId = hotlineRequest.getDeptId();
        if (StringUtil.isNotEmpty(deptId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("agentGroupId", deptId));
        }
        return boolQueryBuilder;
    }

    //查询联络明细数据
    private List<KinesisContactVo> queryContactDetails(HotlineRequest hotlineRequest) {
        List<KinesisContactVo> recordList = new ArrayList<>();
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
//                createIndex(indexName);
                return Arrays.asList();
            }
            SearchRequest searchRequest = new SearchRequest(indexName);
            BoolQueryBuilder boolQueryBuilder = packageContactSearchBuilder(hotlineRequest);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            sourceBuilder.size(10000);
            log.info("热线关键指标看板—ES查询语句，Java代码中的sourceBuilder条件是>>>>>>queryContactDetails>>>>>>{}", printSearchSourceBuilder(sourceBuilder));
            searchRequest.source(sourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            //获取总数量
            long totalCount = searchResponse.getHits().getTotalHits().value;
            if (totalCount > 0) {
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    // 创建 ObjectMapper 实例
                    ObjectMapper objectMapper = new ObjectMapper();
                    // 注册 JavaTimeModule
                    objectMapper.registerModule(new JavaTimeModule());
                    String json = objectMapper.writeValueAsString(source);
                    // 将 JSON 字符串转换为 Java 对象
                    KinesisContactDetailsVo detailsVo = objectMapper.readValue(json, KinesisContactDetailsVo.class);
                    KinesisContactVo contactVo = new KinesisContactVo(detailsVo);
                    recordList.add(contactVo);
                }
            }
        } catch (IOException e) {
            log.error("热线关键指标-查询联络明细数据出现异常");
        }
        return recordList;
    }

    //查询联络明细数据（入参条件不固定，用如下查询方式）
    private List<KinesisContactDetailsVo> querySpecialContactDetails(BoolQueryBuilder boolQueryBuilder) {
        List<KinesisContactDetailsVo> recordList = new ArrayList<>();
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
//                createIndex(indexName);
                return Arrays.asList();
            }
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            sourceBuilder.size(10000);
            searchRequest.source(sourceBuilder);
            log.info("热线关键指标看板(入参条件不固定)—ES查询语句，Java代码中的sourceBuilder条件是>>>>>>querySpecialContactDetails>>>>>>{}", printSearchSourceBuilder(sourceBuilder));
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            //获取总数量
            long totalCount = searchResponse.getHits().getTotalHits().value;
            if (totalCount > 0) {
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    // 创建 ObjectMapper 实例
                    ObjectMapper objectMapper = new ObjectMapper();
                    // 注册 JavaTimeModule
                    objectMapper.registerModule(new JavaTimeModule());
                    String json = objectMapper.writeValueAsString(source);
                    // 将 JSON 字符串转换为 Java 对象
                    KinesisContactDetailsVo detailsVo = objectMapper.readValue(json, KinesisContactDetailsVo.class);
                    recordList.add(detailsVo);
                }
            }
        } catch (IOException e) {
            log.error("热线关键指标-查询联络明细数据出现异常");
        }
        return recordList;
    }

    //联络明细index-部分查询涉及到对比，需要用部分特定的查询条件
    private void packageSpecialContactSearchBuilder(HotlineRequest hotlineRequest, BoolQueryBuilder boolQueryBuilder, String startTime, String endTime) {
        // 添加 callChannel 字段为 VOICE 的查询条件
        boolQueryBuilder.must(QueryBuilders.termQuery("callChannel", "VOICE"));

        // 时间范围查询
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("createTime").gte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime)).lte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime)));
        }

        //联络线路条件
        String connectId = hotlineRequest.getConnectAlias();
        if (StringUtil.isNotEmpty(connectId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("connectAlias", connectId));
        }

        //渠道类型条件
        String channelTypeId = hotlineRequest.getChannelTypeId();
        if (StringUtil.isNotEmpty(channelTypeId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("channelTypeId", channelTypeId));
        }

        //选择团队条件
        String deptId = hotlineRequest.getDeptId();
        if (StringUtil.isNotEmpty(deptId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("agentGroupId", deptId));
        }
    }

    //工单index-部分查询涉及到对比，需要用部分特定的查询条件
    private void packageSpecialTicketSearchBuilder(HotlineRequest hotlineRequest, BoolQueryBuilder boolQueryBuilder, String startTime, String endTime) {
        //只查询真人工单
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));

        //添加 callChannel 字段为 VOICE 的查询条件
        boolQueryBuilder.must(QueryBuilders.termQuery("callChannel", "VOICE"));

        // 时间范围查询
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startTime).lte(endTime));
        }

        //联络线路条件
        String connectId = hotlineRequest.getConnectId();
        if (StringUtil.isNotEmpty(connectId)) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("connect_id"));
            boolQueryBuilder.must(QueryBuilders.termQuery("connect_id", connectId));
        }

        //渠道类型条件
        String channelTypeId = hotlineRequest.getChannelTypeId();
        if (StringUtil.isNotEmpty(channelTypeId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("channel_type_id", channelTypeId));
        }

        //选择团队条件
        String deptId = hotlineRequest.getDeptId();
        if (StringUtil.isNotEmpty(deptId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
        }

        //除了共有的查询条件，还需要补充的是，满意度数据条件，不为null，且list集合数据size大于0
        //es中将满意度和工单表整到了一起，满意度字段是个集合属性，用如下方式
        //构建第一个 nested 查询：检查 ticket_satisfaction 是否存在
        QueryBuilder firstNestedQuery = QueryBuilders.nestedQuery("ticket_satisfaction", QueryBuilders.existsQuery("ticket_satisfaction"), ScoreMode.None);

        QueryBuilder secondNestedQuery = QueryBuilders.nestedQuery("ticket_satisfaction", QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("ticket_satisfaction.rating")), ScoreMode.None);

        // 组合两个 nested 查询到一个 bool 查询中
        boolQueryBuilder.filter(firstNestedQuery).filter(secondNestedQuery);
    }

    //查询工单数据
    private List<WorkOrderRecordEsResult> queryTickets(String indexName, SearchSourceBuilder searchSourceBuilder) throws Exception {
        SearchRequest searchRequest = new SearchRequest(indexName);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        log.info("热线关键指标看板(查询满意度平均分)—ES查询语句，Java代码中的sourceBuilder条件是>>>>>>queryTickets>>>>>>{}", printSearchSourceBuilder(searchSourceBuilder));
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        List<WorkOrderRecordEsResult> recordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult workOrderRecordEsResult = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, workOrderRecordEsResult);
                recordList.add(workOrderRecordEsResult);
            }
        }
        return recordList;
    }

    /**
     * 查询索引是否存在
     *
     * @param index 索引名称
     * @return
     * @throws IOException
     */
    private boolean headIndexExists(String index) throws IOException {
        GetIndexRequest req = new GetIndexRequest(index);
        boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
        log.info("当前索引{}, 是否存在: {}", index, exists);
        return exists;
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        } catch (Exception e) {
            log.error("创建索引报错", e);
        }
    }

    //如果被除数是0或者是小于0，就返回颜色区间中，范围最大的那个颜色代号（因为被除数如果为0，可以理解为算出来的数据是无穷大的）
    private String getColorCodeWhenDividendZero(List<ColorRange> colorRangeList) {
        // 找到范围最大的区间
        Optional<ColorRange> maxRange = colorRangeList.stream().max((r1, r2) -> {
            // 比较规则：null (无穷大) > 数值
            if (r1.getEndNum() == null && r2.getEndNum() == null) return 0; // 两个都是无穷大，相等
            if (r1.getEndNum() == null) return 1; // r1是无穷大，更大
            if (r2.getEndNum() == null) return -1; // r2是无穷大，更大
            return r1.getEndNum().compareTo(r2.getEndNum()); // 比较endNum数值
        });

        return maxRange.map(ColorRange::getColorCode).orElse("No ranges found"); // 如果没有找到最大区间，返回默认值
    }

    //为数量匹配颜色
    private String findColorCodeForNum(BigDecimal data, List<ColorRange> colorRangeList) {
        Optional<String> findColorCode = colorRangeList.stream().filter(range -> data.compareTo(BigDecimal.valueOf(range.getStartNum())) >= 0).filter(range -> range.getEndNum() == null || data.compareTo(BigDecimal.valueOf(range.getEndNum())) < 0).map(ColorRange::getColorCode).findFirst();
        return findColorCode.orElse("No matching range found");
    }

    //为比率匹配颜色 （如下入参有的ratio已经乘以100，因为配置的规则是百分比）
    private String findColorCodeForRatio(Double ratio, List<ColorRange> colorRangeList) {
        String findColorCode = colorRangeList.stream().filter(range -> ratio >= range.getStartNum()).filter(range -> range.getEndNum() == null || ratio < range.getEndNum()).map(ColorRange::getColorCode).findFirst().orElse("No matching range found");
        return findColorCode;
    }

    //获取当前时间开始，指定数量小时之前的时间(并转时区)
    public static String getHoursAgo(long amountToSubtract) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime hoursAgo = now.minus(amountToSubtract, ChronoUnit.HOURS);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String format = formatter.format(hoursAgo);
        //转时区
        String formatDateStart = TimeZoneUtils.requestTimeConversion(format);
        return formatDateStart;
    }

    //判断是否展示呼入量激增 0-不展示 1-展示
    //入参的surgePercentage是阈值，表示一个数字，比如120，代表的是120%
    public Integer isSurge(int currentCalls, int previousCalls, double surgePercentage) {
        if (previousCalls <= 0) {
            return 0;
        }
        //和设定的阈值比较
        boolean b = (double) (currentCalls - previousCalls) / previousCalls >= surgePercentage / 100;
        if (!b) {
            return 0;
        }
        return 1;
    }

    //处理不同的呼损情况-未接来电
    private void handleMissedCall(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                  int totalNum, List<CallLoss> callLossList, Integer showType) {
        //未接来电：座席应答时间为空，队列等待时间不为空，agentConnectionAttempts大于0
        List<KinesisContactVo> list = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() == null
                && vo.getQueueWaitTime() != null && (vo.getAgentConnectionAttempts() != null
                && vo.getAgentConnectionAttempts() > 0)).collect(Collectors.toList());
        int num = list.size();
        List<ColorRange> colorRangeList = callLossRate.getMissedCallRate();
        String xName = MessageUtils.get("hotline.missed.call");
        if (num == 0) {
            handleDiffCallLossSituationZero(colorRangeList, xName, callLossList);
        } else {
            double ratio = (double) num / totalNum;
            String colorCodeForRatio = findColorCodeForRatio(ratio * 100, colorRangeList);
            BigDecimal result;
            if (1 == showType) {
                result = new BigDecimal(num);
            } else {
                result = handlePercentage(BigDecimal.valueOf(ratio));
            }
            CallLoss callLoss = new CallLoss().setXName(xName).setNum(result).setColorCode(colorCodeForRatio);
            callLossList.add(callLoss);
        }
    }

    //处理不同的呼损情况-IVR放弃
    private void handleIvrAbandon(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                  int totalNum, List<CallLoss> callLossList, Integer showType) {
        //ivr放弃：座席应答时间，队列等待时间都是空
        List<KinesisContactVo> list = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() == null && vo.getQueueWaitTime() == null).collect(Collectors.toList());
        int num = list.size();
        List<ColorRange> colorRangeList = callLossRate.getIvrAbandonRate();
        String xName = MessageUtils.get("hotline.ivr.abandon");
        if (num == 0) {
            handleDiffCallLossSituationZero(colorRangeList, xName, callLossList);
        } else {
            double ratio = (double) num / totalNum;
            String colorCodeForRatio = findColorCodeForRatio(ratio * 100, colorRangeList);
            BigDecimal result;
            if (1 == showType) {
                result = new BigDecimal(num);
            } else {
                result = handlePercentage(BigDecimal.valueOf(ratio));
            }
            CallLoss callLoss = new CallLoss().setXName(xName).setNum(result).setColorCode(colorCodeForRatio);
            callLossList.add(callLoss);
        }
    }

    //处理不同的呼损情况-排队放弃
    private void handleQueueAbandon(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                    int totalNum, List<CallLoss> callLossList, Integer showType) {
        List<KinesisContactVo> list = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() == null
                && vo.getQueueWaitTime() != null).collect(Collectors.toList());
        int num = list.size();
        List<ColorRange> colorRangeList = callLossRate.getQueueAbandonRate();
        String xName = MessageUtils.get("hotline.queue.abandon");
        if (num == 0) {
            handleDiffCallLossSituationZero(colorRangeList, xName, callLossList);
        } else {
            double ratio = (double) num / totalNum;
            String colorCodeForRatio = findColorCodeForRatio(ratio * 100, colorRangeList);
            BigDecimal result;
            if (1 == showType) {
                result = new BigDecimal(num);
            } else {
                result = handlePercentage(BigDecimal.valueOf(ratio));
            }
            CallLoss callLoss = new CallLoss().setXName(xName).setNum(result).setColorCode(colorCodeForRatio);
            callLossList.add(callLoss);
        }
    }

    //处理不同的呼损情况-非服务时间呼入
    private void handleNonServiceTimeInbound(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                             int totalNum, List<CallLoss> callLossList, Integer showType) {
        //todo 待提供上线数据
        List<KinesisContactVo> list = new ArrayList<>();
        int num = list.size();
        List<ColorRange> colorRangeList = callLossRate.getNonServiceTimeInboundRate();
        String xName = MessageUtils.get("hotline.non.service.time.inbound");
        if (num == 0) {
            handleDiffCallLossSituationZero(colorRangeList, xName, callLossList);
        } else {
            double ratio = (double) num / totalNum;
            String colorCodeForRatio = findColorCodeForRatio(ratio * 100, colorRangeList);
            BigDecimal result;
            if (1 == showType) {
                result = new BigDecimal(num);
            } else {
                result = handlePercentage(BigDecimal.valueOf(ratio));
            }
            CallLoss callLoss = new CallLoss().setXName(xName).setNum(result).setColorCode(colorCodeForRatio);
            callLossList.add(callLoss);
        }
    }

    //如果筛选之后的数据为0，用如下公共方法处理(数据为0的时候就无所谓用数量还是比率来匹配颜色了)
    private void handleDiffCallLossSituationZero(List<ColorRange> colorRangeList, String xName, List<CallLoss> callLossList) {
        String colorCodeForNum = findColorCodeForNum(BigDecimal.ZERO, colorRangeList);
        CallLoss callLoss = new CallLoss().setXName(xName).setNum(BigDecimal.ZERO).setColorCode(colorCodeForNum);
        callLossList.add(callLoss);
    }

    //处理不同的呼损情况-总呼损
    private TotalCallLoss handleTotalCallLoss(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                              int totalNum, Integer showType) {
        //总呼损 =（未接通呼数（座席应答时间为null） / 呼叫尝试次数（即呼叫总数））×100%
        //未接通呼数：座席应答时间为null
        List<KinesisContactVo> disconnectedCallList = kinesisContactVoList.stream()
                .filter(vo -> vo.getCallTime() == null).collect(Collectors.toList());
        int num = disconnectedCallList.size();
        List<ColorRange> colorRangeList = callLossRate.getTotalCallLossRate();
        if (num == 0) {
            String colorCodeForNum = findColorCodeForNum(new BigDecimal(num), colorRangeList);
            return new TotalCallLoss().setCallLossNum(num).setTotalNum(totalNum).setRate(BigDecimal.ZERO).setColorCode(colorCodeForNum);
        } else {
            double ratio = (double) num / totalNum;
            String colorCodeForRatio = findColorCodeForRatio(ratio * 100, colorRangeList);
            return new TotalCallLoss().setCallLossNum(num).setTotalNum(totalNum).setRate(handlePercentage(BigDecimal.valueOf(ratio))).setColorCode(colorCodeForRatio);
        }
    }

    //根据入参时间，处理往前推，等间距的时间，作为对比时间范围
    //计算对比时间范围的原则：以始为终
    private Map<String, String> handleCompareTimeQueryCondition(Integer timeRangeCode, String startTimeParam, String endTimeParam) {
        Map<String, String> resultMap = new HashMap<>();
        // 处理时间条件
        Map<String, String> timeResultMap = handleTimeQueryCondition(timeRangeCode, startTimeParam, endTimeParam);
        //获取处理之后的页面时间，然后根据上述返回结果，获取对比时间范围（如下两个时间返回，是经过时区处理的）
        String formatDateStart = timeResultMap.get("startTime");
        String formatDateEnd = timeResultMap.get("endTime");

        LocalDateTime startTime = LocalDateTime.parse(formatDateStart, formatter);
        LocalDateTime endTime = LocalDateTime.parse(formatDateEnd, formatter);

        switch (timeRangeCode) {
            case 0: // 当天
                endTime = endTime.minusDays(1);
                startTime = endTime.toLocalDate().atStartOfDay();
                break;
            case 1: // 近24小时
                endTime = startTime;
                startTime = endTime.minusHours(24);
                break;
            case 2: // 近2天
                endTime = startTime;
                startTime = endTime.minusDays(2);
                break;
            case 3: // 近3天
                endTime = startTime;
                startTime = endTime.minusDays(3);
                break;
            case 4: // 近7天
                endTime = startTime;
                startTime = endTime.minusDays(7);
                break;
            case 5: // 近1个月
                endTime = startTime;
                startTime = endTime.minusMonths(1);
                break;
            case 6: // 自定义时间
                long durationSeconds = ChronoUnit.SECONDS.between(startTime, endTime); // 计算秒数差
                //  以下根据durationSeconds计算新的时间范围，你可以根据你的需求修改这个逻辑
                endTime = startTime;
                startTime = endTime.minusSeconds(durationSeconds);
            default:
                break;
        }
        if (null != startTime && null != endTime) {
            String formatCompareDateStart = startTime.format(formatter);
            String formatCompareDateEnd = endTime.format(formatter);
            resultMap.put("startTime", formatCompareDateStart);
            resultMap.put("endTime", formatCompareDateEnd);
        }
        return resultMap;
    }

    //从es中查询工单满意度
    private List<SatisfactionResult> querySatisfactionFromEs(BoolQueryBuilder boolQueryBuilder) {
        List<SatisfactionResult> recordList = new ArrayList<>();
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义索引名称
            String indexName = ticketIndex + companyId;

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            // 执行查询
            List<WorkOrderRecordEsResult> esResultList = queryTickets(indexName, sourceBuilder);
            //处理返回值数据中的满意度评分数据（按照满意度评分的总数据，进行返回值集合的组装，便于后续分组计算）
            if (CollectionUtils.isNotEmpty(esResultList)) {
                for (WorkOrderRecordEsResult workOrderRecordEsResult : esResultList) {
                    List<TicketSatisfaction> ticketSatisfactionList = workOrderRecordEsResult.getTicketSatisfaction();
                    for (TicketSatisfaction ticketSatisfaction : ticketSatisfactionList) {
                        SatisfactionResult satisfactionResult = new SatisfactionResult();
                        satisfactionResult.setDeptId(workOrderRecordEsResult.getDeptId());
                        satisfactionResult.setAgentId(workOrderRecordEsResult.getAgentId());
                        satisfactionResult.setAgentName(workOrderRecordEsResult.getAgentName());
                        satisfactionResult.setChannelTypeId(workOrderRecordEsResult.getChannelTypeId());
                        satisfactionResult.setChannelTypeName(workOrderRecordEsResult.getChannelTypeName());
                        satisfactionResult.setWorkRecordTypeCode(workOrderRecordEsResult.getWorkRecordTypeCode());
                        satisfactionResult.setWorkRecordTypeName(workOrderRecordEsResult.getWorkRecordTypeName());
                        satisfactionResult.setRating(new BigDecimal(ticketSatisfaction.getRating()));
                        satisfactionResult.setCreateTime(workOrderRecordEsResult.getCreateTime());
                        recordList.add(satisfactionResult);
                    }
                }
            }
        } catch (Exception e) {
            log.error("热线关键指标看板-满意度平均分-查询工单出现异常：", e);
        }
        return recordList;
    }

    //数据对比，得到变化趋势 1-下降 2-上升 3-相等  4-不显示（分母为0的时候）
    private static CompareResult compareBigDecimals(BigDecimal bigDecimal1, BigDecimal bigDecimal2) {
        log.info("用于对比的入参变量是 bigDecimal1: {} ,bigDecimal2: {}", bigDecimal1, bigDecimal2);
        if (bigDecimal1 == null || bigDecimal2 == null) {
            //数据为空，按相等处理吧
            return new CompareResult().setChangeIndicator(4);
        }
        //处理这两个入参可能为0的情况
        if (bigDecimal1.compareTo(BigDecimal.ZERO) == 0 && bigDecimal2.compareTo(BigDecimal.ZERO) == 0) {
            return new CompareResult().setChangeIndicator(4);
        }

        if (bigDecimal2.compareTo(BigDecimal.ZERO) == 0) {
            //如果分母为0，给比率设置为100% ？？？
            return new CompareResult().setChangeIndicator(4);
        }

        int comparisonResult = bigDecimal1.compareTo(bigDecimal2);

        if (comparisonResult == 0) {
            return new CompareResult().setPercentageChange(BigDecimal.ZERO).setChangeIndicator(3);
        } else if (comparisonResult < 0) {
            //bigDecimal1 小于 bigDecimal2
            return new CompareResult().setPercentageChange(bigDecimal2.subtract(bigDecimal1).divide(bigDecimal2, 4, RoundingMode.HALF_UP)).setChangeIndicator(1);
        } else {
            //bigDecimal1 大于 bigDecimal2
            return new CompareResult().setPercentageChange(bigDecimal1.subtract(bigDecimal2).divide(bigDecimal2, 4, RoundingMode.HALF_UP)).setChangeIndicator(2);
        }
    }

    //根据条件查询数据，返回部分参数，用返回值是否为null进行下一步的处理，在现有部分数据的基础上，补充剩余数据
 /*   private AvgCallTimeResult calculateAvgCallTime(BoolQueryBuilder queryBuilder) {
        List<KinesisContactDetailsVo> kinesisContactList = querySpecialContactDetails(queryBuilder);
        if (CollectionUtils.isEmpty(kinesisContactList)) {
            return new AvgCallTimeResult();
        }

        BigDecimal sum = kinesisContactList.stream().map(KinesisContactDetailsVo::getTotalTime).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);

        int count = kinesisContactList.size();
        BigDecimal average = count == 0 ? BigDecimal.ZERO : sum.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP);

        //将页面通话时长，转为指定格式
        return new AvgCallTimeResult().setAvgCallTotalTime(average).setAvgCallTime(DateUtils.dataTimeInt(convertToIntegerWithPrecision(average)));
    }*/

    //涉及到时间的处理，比如获取等间距的时间段范围
    private Map<String, String> handleCompareTime(Integer timeRangeCode, String startTime, String endTime) {
        Map<String, String> timeResultMap = handleTimeQueryCondition(timeRangeCode, startTime, endTime);
        String formatDateStart = timeResultMap.get("startTime");
        String formatDateEnd = timeResultMap.get("endTime");
        //获取用于对比的时间
        Map<String, String> stringStringMap = handleCompareTimeQueryCondition(timeRangeCode, formatDateStart, formatDateEnd);
        String formatCompareDateStart = stringStringMap.get("startTime");
        String formatCompareDateEnd = stringStringMap.get("endTime");
        Map<String, String> timeMap = new HashMap<>();
        timeMap.put("formatDateStart", formatDateStart);
        timeMap.put("formatDateEnd", formatDateEnd);
        timeMap.put("formatCompareDateStart", formatCompareDateStart);
        timeMap.put("formatCompareDateEnd", formatCompareDateEnd);
        return timeMap;
    }

    private AvgCallTimeResult handleAvgCallTimeResult(AvgCallTimeRequest hotlineRequest) {
        //涉及到时间的处理，比如获取等间距的时间段范围
        //获取页面入参时间处理后的结果
        Integer timeRangeCode = hotlineRequest.getTimeRangeCode();
        String startTime = hotlineRequest.getStartTime();
        String endTime = hotlineRequest.getEndTime();

        Map<String, String> timeMap = handleCompareTime(timeRangeCode, startTime, endTime);
        String formatDateStart = timeMap.get("formatDateStart");
        String formatDateEnd = timeMap.get("formatDateEnd");
        String formatCompareDateStart = timeMap.get("formatCompareDateStart");
        String formatCompareDateEnd = timeMap.get("formatCompareDateEnd");

        //0-所有 1-呼入时长 2-呼出时长
        //默认所有
        Integer showType = hotlineRequest.getShowType();
        //入参时间相关
        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        //对比时间相关
        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
        String incomingOutgoing = null;
        if (1 == showType) {
            incomingOutgoing = INBOUND;
        } else if (2 == showType) {
            incomingOutgoing = OUTBOUND;
        }
        //筛选出通话总时长，即totalTime不为null的参与计算，totalTime返回值的单位是秒
        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder1, formatDateStart, formatDateEnd);
        Double avgCallTotalTime1 = queryDiffAvgTime(boolQueryBuilder1, incomingOutgoing, "totalTime");
        log.info("查询热线关键指标看板-平均通话时长是{}秒，当前时间条件{}-{}", avgCallTotalTime1, formatDateStart, formatDateEnd);

        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder2, formatCompareDateStart, formatCompareDateEnd);
        Double avgCallTotalTime2 = queryDiffAvgTime(boolQueryBuilder2, incomingOutgoing, "totalTime");
        log.info("查询热线关键指标看板-平均通话时长是{}秒，对比时间条件{}-{}", avgCallTotalTime2, formatCompareDateStart, formatCompareDateEnd);

        AvgCallTimeResult result = new AvgCallTimeResult()
                .setAvgCallTotalTime(BigDecimal.valueOf(avgCallTotalTime1))
                .setAvgCallTime(DateUtils.dataTimeInt(convertToIntegerWithPrecision(BigDecimal.valueOf(avgCallTotalTime1))));

        //数据比较，获取变化趋势和比率
        CompareResult compareResult = compareBigDecimals(BigDecimal.valueOf(avgCallTotalTime1), BigDecimal.valueOf(avgCallTotalTime2));
        BigDecimal percentageChange = compareResult.getPercentageChange();
        if (null != percentageChange) {
            result.setRate(convertToDoubleWithPrecision(percentageChange, 2));
        }
        result.setRiseOrFall(compareResult.getChangeIndicator());
        return result;
    }

    private AvgSatisfactionResult handleAvgSatisfactionResult(AvgSatisfactionRequest hotlineRequest) {
        Integer timeRangeCode = hotlineRequest.getTimeRangeCode();
        String startTime = hotlineRequest.getStartTime();
        String endTime = hotlineRequest.getEndTime();

        Map<String, String> timeMap = handleCompareTime(timeRangeCode, startTime, endTime);
        String formatDateStart = timeMap.get("formatDateStart");
        String formatDateEnd = timeMap.get("formatDateEnd");
        String formatCompareDateStart = timeMap.get("formatCompareDateStart");
        String formatCompareDateEnd = timeMap.get("formatCompareDateEnd");

        //0-所有 1-呼入满意度 2-呼出满意度
        //默认所有
        Integer showType = hotlineRequest.getShowType();
        //入参时间相关
        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        //对比时间相关
        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
        if (1 == showType) {
            boolQueryBuilder1.must(QueryBuilders.termQuery("call_type", INBOUND));
            boolQueryBuilder2.must(QueryBuilders.termQuery("call_type", INBOUND));
        } else if (2 == showType) {
            boolQueryBuilder1.must(QueryBuilders.termQuery("call_type", OUTBOUND));
            boolQueryBuilder2.must(QueryBuilders.termQuery("call_type", OUTBOUND));
        }
        packageSpecialTicketSearchBuilder(hotlineRequest, boolQueryBuilder1, formatDateStart, formatDateEnd);
        AvgSatisfactionResult result1 = calculateAvgSatisfaction(boolQueryBuilder1, showType);

        packageSpecialTicketSearchBuilder(hotlineRequest, boolQueryBuilder2, formatCompareDateStart, formatCompareDateEnd);
        AvgSatisfactionResult result2 = calculateAvgSatisfaction(boolQueryBuilder2, showType);

        if (null == result2.getAvgScore()) {
            //如果没有对比项，那么上升或者是下降趋势代号，也设置为不显示
            result1.setRiseOrFall(4);
            return result1;
        }
        if (result1.getAvgScore() != null && result2.getAvgScore() != null) {
            BigDecimal avgScore1 = result1.getAvgScore();
            BigDecimal avgScore2 = result2.getAvgScore();
            log.info("查询热线关键指标看板-满意度平均分是{}分，当前时间条件下{}-{}", avgScore1, formatDateStart, formatDateEnd);
            log.info("查询热线关键指标看板-满意度平均分是{}分，对比时间条件下{}-{}", avgScore2, formatCompareDateStart, formatCompareDateEnd);
            CompareResult compareResult = compareBigDecimals(avgScore1, avgScore2);
            BigDecimal percentageChange = compareResult.getPercentageChange();
            if (null != percentageChange) {
                result1.setRate(handlePercentage(convertToDoubleWithPrecision(percentageChange, 4)));
            }
            result1.setRiseOrFall(compareResult.getChangeIndicator());
        }
        return result1;
    }

    //根据条件查询数据，返回部分参数，用返回值是否为null进行下一步的处理，在现有部分数据的基础上，补充剩余数据
    private AvgSatisfactionResult calculateAvgSatisfaction(BoolQueryBuilder queryBuilder, Integer showType) {
        List<SatisfactionResult> satisfactionResultList = querySatisfactionFromEs(queryBuilder);
        if (CollectionUtils.isEmpty(satisfactionResultList)) {
            return new AvgSatisfactionResult();
        }

        BigDecimal sum = satisfactionResultList.stream().map(SatisfactionResult::getRating).reduce(BigDecimal.ZERO, BigDecimal::add);
        int count = satisfactionResultList.size();
        BigDecimal average = count == 0 ? BigDecimal.ZERO : sum.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP);

        //处理返回值颜色
        Satisfaction satisfaction = queryHotlineRule().getSatisfaction();
        List<ColorRange> colorRangeList;
        if (1 == showType) {
            colorRangeList = satisfaction.getInboundSatisfactionAvg();
        } else if (2 == showType) {
            colorRangeList = satisfaction.getOutboundSatisfactionAvg();
        } else {
            colorRangeList = satisfaction.getSatisfactionAvg();
        }
        String colorCodeForNum = findColorCodeForNum(average, colorRangeList);
        return new AvgSatisfactionResult().setAvgScore(convertToDoubleWithPrecision(average, 2)).setColorCode(colorCodeForNum);
    }

    //处理比率类，返回值格式，转换为百分比并保留两位小数
    private static BigDecimal handlePercentage(BigDecimal ratioBD) {
        // Multiply by 100 to get percentage
        BigDecimal percentage = ratioBD.multiply(BigDecimal.valueOf(100));

        // Round to two decimal places using HALF_UP rounding
        return percentage.setScale(2, RoundingMode.HALF_UP);
    }

    private static BigDecimal convertToDoubleWithPrecision(BigDecimal bigDecimal, int scale) {
        if (bigDecimal == null) {
            return BigDecimal.ZERO; // 处理空值情况
        }
        //使用四舍五入模式
//        return bigDecimal.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        return bigDecimal.setScale(scale, RoundingMode.HALF_UP);
    }

    private Integer convertToIntegerWithPrecision(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return 0; // 处理空值情况
        }
        return bigDecimal.intValue();
    }

    //处理SearchSourceBuilder 中的查询条件，方便日志打印
    private String printSearchSourceBuilder(SearchSourceBuilder sourceBuilder) {
        StringWriter writer;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            writer = new StringWriter();
            objectMapper.writeValue(writer, sourceBuilder.toString());
            return writer.toString();
        } catch (IOException e) {
            log.error("处理打印 SearchSourceBuilder 中的查询条件出现异常：", e);
        }
        return "";
    }

    //计算呼入或者呼出总时长，然后对比
    private BusinessDataBean calculateIncomingOutgoingTime(HotlineRequest hotlineRequest, Map<String, String> timeMap, String incomingOutgoing) {
        String formatDateStart = timeMap.get("formatDateStart");
        String formatDateEnd = timeMap.get("formatDateEnd");
        String formatCompareDateStart = timeMap.get("formatCompareDateStart");
        String formatCompareDateEnd = timeMap.get("formatCompareDateEnd");

        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder1, formatDateStart, formatDateEnd);
        Double sum1 = queryIncomingOutgoingTime(boolQueryBuilder1, incomingOutgoing);
        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder2, formatCompareDateStart, formatCompareDateEnd);
        Double sum2 = queryIncomingOutgoingTime(boolQueryBuilder2, incomingOutgoing);

        String incomingOutgoingName;
        if (INBOUND.equals(incomingOutgoing)) {
            incomingOutgoingName = "呼入";
        } else {
            incomingOutgoingName = "呼出";
        }
        log.info("查询热线关键指标看板-团队业务指标-总{}时长是{}秒，当前时间条件下{}-{}", incomingOutgoingName, sum1, formatDateStart, formatDateEnd);
        log.info("查询热线关键指标看板-团队业务指标-总{}时长是{}秒，对比时间条件下{}-{}", incomingOutgoingName, sum2, formatCompareDateStart, formatCompareDateEnd);
        CompareResult compareResult = compareBigDecimals(new BigDecimal(sum1), new BigDecimal(sum2));

        //时长是秒，将秒转化为分钟，再进行千分制格式化
        String formatData = formatDouble(sum1 / 60);

        BusinessDataBean businessDataBean = new BusinessDataBean().setData(formatData)
                .setRiseOrFall(compareResult.getChangeIndicator());
        BigDecimal percentageChange = compareResult.getPercentageChange();
        if (null != percentageChange) {
            businessDataBean.setRate(handlePercentage(convertToDoubleWithPrecision(percentageChange, 4)));
        }
        return businessDataBean;
    }

    //计算总通话时长
    private Double queryIncomingOutgoingTime(BoolQueryBuilder boolQueryBuilder, String incomingOutgoing) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义索引名称
        String indexName = contactDetailsIndexPrefix + companyId;
        // 创建 SearchRequest 对象
        SearchRequest searchRequest = new SearchRequest(indexName);
        // 创建 SearchSourceBuilder 对象
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 添加 incomingOutgoing 字段为 INBOUND 的查询条件
        boolQueryBuilder.must(QueryBuilders.termQuery("incomingOutgoing", incomingOutgoing));
        // 添加 totalTime 字段存在(不为空)的查询条件
        boolQueryBuilder.must(QueryBuilders.existsQuery("totalTime"));
        // 将查询条件添加到 SearchSourceBuilder 对象
        searchSourceBuilder.query(boolQueryBuilder);
        // 创建 SumAggregationBuilder 对象，用于计算 totalTime 字段的总和
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum("totalTimeSum").field("totalTime");
        // 将聚合条件添加到 SearchSourceBuilder 对象
        searchSourceBuilder.aggregation(sumAggregationBuilder);
        // 将 SearchSourceBuilder 对象添加到 SearchRequest 对象
        searchRequest.source(searchSourceBuilder);
        // 执行查询
        SearchResponse searchResponse;
        Double sum = 0.0;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            // 获取聚合结果
            Sum totalTimeSum = searchResponse.getAggregations().get("totalTimeSum");

            if (totalTimeSum != null) {
                Double value = totalTimeSum.getValue();
                if (value != null && !Double.isNaN(value) && !Double.isInfinite(value)) {
                    sum = value;
                } else {
                    sum = 0.0; // Handle null, NaN, or Infinity
                }
            }
        } catch (IOException e) {
            log.error("热线关键指标-团队业务指标-总呼入/呼出时长-查询es出现异常：", e);
            //如果报错，认为算出来的数据是0
            return sum;
        }
        return sum;
    }

    //将数据进行千分制格式化
    private String formatDouble(Double num) {
        if (num == null) {
            return "0.00"; // Handle null input
        }
        return String.format(Locale.getDefault(), "%,.2f", num);
    }

    //将数据进行千分制格式化
    private String formatInteger(Integer num) {
        if (num == null) {
            return "0"; // Handle null input
        }
        return String.format(Locale.getDefault(), "%,d", num);
    }

    //计算不同维度的平均时长，然后对比
    private BusinessDataBean calculateDiffAvgTime(HotlineRequest hotlineRequest, Map<String, String> timeMap, String incomingOutgoing, String diffField, List<ColorRange> colorRangeList) {
        String formatDateStart = timeMap.get("formatDateStart");
        String formatDateEnd = timeMap.get("formatDateEnd");
        String formatCompareDateStart = timeMap.get("formatCompareDateStart");
        String formatCompareDateEnd = timeMap.get("formatCompareDateEnd");

        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder1, formatDateStart, formatDateEnd);
        Double num1 = queryDiffAvgTime(boolQueryBuilder1, incomingOutgoing, diffField);
        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder2, formatCompareDateStart, formatCompareDateEnd);
        Double num2 = queryDiffAvgTime(boolQueryBuilder2, incomingOutgoing, diffField);

        String incomingOutgoingName;
        if (INBOUND.equals(incomingOutgoing)) {
            incomingOutgoingName = "呼入";
        } else {
            incomingOutgoingName = "呼出";
        }
        String diffFieldName = "";
        if ("totalTime".equals(diffField)) {
            diffFieldName = "总";
        } else if ("interactionTime".equals(diffField)) {
            diffFieldName = "互动";
        } else if ("acwDuration".equals(diffField)) {
            diffFieldName = "ACW";
        } else if ("queueWaitTime".equals(diffField)) {
            diffFieldName = "排队";
        }
        log.info("查询热线关键指标看板-团队业务指标-平均{}{}时长是{}秒，当前时间条件{}-{}", incomingOutgoingName, diffFieldName, num1, formatDateStart, formatDateEnd);
        log.info("查询热线关键指标看板-团队业务指标-平均{}{}时长是{}秒，对比时间条件{}-{}", incomingOutgoingName, diffFieldName, num2, formatCompareDateStart, formatCompareDateEnd);
        CompareResult compareResult = compareBigDecimals(new BigDecimal(num1), new BigDecimal(num2));

        //将秒进行时间格式化
        String formatData = DateUtils.dataTimeInt(convertToIntegerWithPrecision(new BigDecimal(num1)));

        //个别涉及到颜色处理
        String colorCodeForNum = null;
        if (CollectionUtils.isNotEmpty(colorRangeList)) {
            colorCodeForNum = findColorCodeForNum(new BigDecimal(num1), colorRangeList);
        }

        BusinessDataBean businessDataBean = new BusinessDataBean()
                .setData(formatData)
                .setRiseOrFall(compareResult.getChangeIndicator())
                .setColorCode(colorCodeForNum);
        BigDecimal percentageChange = compareResult.getPercentageChange();
        if (null != percentageChange) {
            businessDataBean.setRate(handlePercentage(convertToDoubleWithPrecision(percentageChange, 2)));
        }
        return businessDataBean;

    }

    //计算不同维度的平均时长
    private Double queryDiffAvgTime(BoolQueryBuilder boolQueryBuilder, String incomingOutgoing, String diffField) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义索引名称
        String indexName = contactDetailsIndexPrefix + companyId;
        // 创建 SearchRequest 对象
        SearchRequest searchRequest = new SearchRequest(indexName);
        // 创建 SearchSourceBuilder 对象
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 添加 incomingOutgoing 字段为 INBOUND 的查询条件
        if (StringUtil.isNotEmpty(incomingOutgoing)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("incomingOutgoing", incomingOutgoing));
        }
        // 添加字段存在(不为空)的查询条件
        boolQueryBuilder.must(QueryBuilders.existsQuery(diffField));
        // 将查询条件添加到 SearchSourceBuilder 对象
        searchSourceBuilder.query(boolQueryBuilder);
        // 创建平均值聚合，计算 入参字段 的平均值
        AvgAggregationBuilder avgAggregation = AggregationBuilders.avg("avg").field(diffField);
        // 将聚合添加到查询中
        searchSourceBuilder.aggregation(avgAggregation);
        // 将 SearchSourceBuilder 对象添加到 SearchRequest 对象
        searchRequest.source(searchSourceBuilder);
        // 执行查询
        SearchResponse searchResponse;
        Double avg = 0.0;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            // 获取聚合结果
            Avg avgResult = searchResponse.getAggregations().get("avg");
            if (avgResult != null) {
                Double value = avgResult.getValue();
                if (value != null && !Double.isNaN(value) && !Double.isInfinite(value)) {
                    avg = value;
                } else {
                    avg = 0.0; // Handle null, NaN, or Infinity
                }
            }
        } catch (IOException e) {
            log.error("热线关键指标-团队业务指标-平均{},{},时长-查询es出现异常：", incomingOutgoing, diffField, e);
            //如果报错，认为算出来的数据是0
            return avg;
        }
        return avg;
    }

    //计算不同维度的比率
    private BusinessDataBean calculateDiffRate(HotlineRequest hotlineRequest, Map<String, String> timeMap, String incomingOutgoing, String diffField, List<ColorRange> colorRangeList) {
        String formatDateStart = timeMap.get("formatDateStart");
        String formatDateEnd = timeMap.get("formatDateEnd");
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder, formatDateStart, formatDateEnd);
        // 添加 incomingOutgoing 字段为 INBOUND 的查询条件
        boolQueryBuilder.must(QueryBuilders.termQuery("incomingOutgoing", incomingOutgoing));
        List<KinesisContactDetailsVo> kinesisContactDetailsVoList = querySpecialContactDetails(boolQueryBuilder);
        if (CollectionUtils.isEmpty(kinesisContactDetailsVoList)) {
            return new BusinessDataBean();
        }
        List<KinesisContactDetailsVo> filterList = new ArrayList<>();
        if ("isSwitch".equals(diffField)) {
            //是否为转接 0否， 1是
            //筛选转接了的
            filterList = kinesisContactDetailsVoList.stream().filter(vo -> "1".equals(vo.getIsSwitch())).collect(Collectors.toList());
        } else if ("hangingType".equals(diffField)) {
            //筛选座席挂断 （如果是客户挂断，是CUSTOMER_DISCONNECT）
            filterList = kinesisContactDetailsVoList.stream().filter(vo -> "AGENT_DISCONNECT".equals(vo.getHangingType())).collect(Collectors.toList());
        }
        //个别涉及到颜色处理
        int num = filterList.size();
        int totalNum = kinesisContactDetailsVoList.size();

        //个别涉及到颜色处理
        String findColorCode = null;
        double ratio = (double) num / totalNum;
        if (CollectionUtils.isNotEmpty(colorRangeList)) {
            findColorCode = findColorCodeForRatio(ratio * 100, colorRangeList);
        }
        //个别涉及到颜色处理
        return new BusinessDataBean()
                .setNum(formatInteger(num))
                .setTotalNum(formatInteger(totalNum))
//                .setData(handlePercentage(convertToDoubleWithPrecision(BigDecimal.valueOf(ratio), 4)) + "")
                .setRate(handlePercentage(convertToDoubleWithPrecision(BigDecimal.valueOf(ratio), 4)) + "")
                .setColorCode(findColorCode);
    }

    //计算重复进线率
    private BusinessDataBean calRepeatEntryRate(List<KinesisContactVo> kinesisContactVoList) {
        if (CollectionUtils.isEmpty(kinesisContactVoList)) {
            return new BusinessDataBean();
        }
        HotlineKeyIndicatorRule hotlineKeyIndicatorRule = queryHotlineRule();
        List<ColorRange> colorRangeList = hotlineKeyIndicatorRule.getOtherSetting().getRepeatEntryRate();
        //总数量是分母
        int totalNum = kinesisContactVoList.size();
        //统计重复进线个数
        List<KinesisContactVo> filterDuplicatePhoneList = filterDuplicatePhoneList(kinesisContactVoList);
        int num = filterDuplicatePhoneList.size();
        double ratio = (double) num / totalNum;
        String findColorCode = findColorCodeForRatio(ratio * 100, colorRangeList);
        return new BusinessDataBean()
                .setNum(formatInteger(num))
                .setTotalNum(formatInteger(totalNum))
//                .setData(handlePercentage(convertToDoubleWithPrecision(BigDecimal.valueOf(ratio), 4)))
                .setRate(handlePercentage(convertToDoubleWithPrecision(BigDecimal.valueOf(ratio), 4)))
                .setColorCode(findColorCode);
    }

    private List<KinesisContactVo> filterDuplicatePhoneList(List<KinesisContactVo> kinesisContactVoList) {
        // 使用 Collectors.groupingBy 将列表按 customerPhone 分组，并计算每个电话号码出现的次数，忽略null和空字符串
        Map<String, Long> phoneCounts = kinesisContactVoList.stream()
                .filter(vo -> vo.getCustomerPhone() != null && !vo.getCustomerPhone().isEmpty()) //过滤掉null和空字符串
                .collect(Collectors.groupingBy(KinesisContactVo::getCustomerPhone, Collectors.counting()));

        // 筛选出出现次数大于等于 2 的电话号码，并再次过滤掉null和空字符串，出现次数大于等于 2 的 KinesisContactVo 对象，结果收集到一个新的列表中
        return kinesisContactVoList.stream()
                .filter(vo -> vo.getCustomerPhone() != null && !vo.getCustomerPhone().isEmpty()) //过滤掉null和空字符串
                .filter(vo -> phoneCounts.get(vo.getCustomerPhone()) >= 2)
                .collect(Collectors.toList());
    }

    //10s坐席应答量：callTime存在且queueWaitTime小于等于10s
    private BusinessDataBean calAgentResponseTenSecond(HotlineRequest hotlineRequest, Map<String, String> timeMap) {
        String formatDateStart = timeMap.get("formatDateStart");
        String formatDateEnd = timeMap.get("formatDateEnd");
        String formatCompareDateStart = timeMap.get("formatCompareDateStart");
        String formatCompareDateEnd = timeMap.get("formatCompareDateEnd");

        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder1, formatDateStart, formatDateEnd);
        Double sum1 = queryAgentResponseTenSecond(boolQueryBuilder1);
        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
        packageSpecialContactSearchBuilder(hotlineRequest, boolQueryBuilder2, formatCompareDateStart, formatCompareDateEnd);
        Double sum2 = queryAgentResponseTenSecond(boolQueryBuilder2);

        log.info("查询热线关键指标看板-团队业务指标-10s坐席应答量是{}个，当前时间条件{}-{}", sum1, formatDateStart, formatDateEnd);
        log.info("查询热线关键指标看板-团队业务指标-10s坐席应答量是{}个，对比时间条件{}-{}", sum2, formatCompareDateStart, formatCompareDateEnd);
        CompareResult compareResult = compareBigDecimals(new BigDecimal(sum1), new BigDecimal(sum2));

        BusinessDataBean businessDataBean = new BusinessDataBean()
                .setData(formatDouble(sum1))
                .setRiseOrFall(compareResult.getChangeIndicator());
        BigDecimal percentageChange = compareResult.getPercentageChange();
        if (null != percentageChange) {
            businessDataBean.setRate(handlePercentage(convertToDoubleWithPrecision(percentageChange, 4)));
        }
        return businessDataBean;
    }

    private double queryAgentResponseTenSecond(BoolQueryBuilder boolQueryBuilder) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义索引名称
        String indexName = contactDetailsIndexPrefix + companyId;
        // 创建 SearchRequest 对象
        SearchRequest searchRequest = new SearchRequest(indexName);
        // 创建 SearchSourceBuilder 对象
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 构建查询条件：callTime存在且queueWaitTime小于等于10
        boolQueryBuilder.must(QueryBuilders.existsQuery("callTime"));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("queueWaitTime").lte(10));
        // 使用Filter聚合计算满足条件的文档数量
        FilterAggregationBuilder filterAggregation = AggregationBuilders.filter("myFilter", boolQueryBuilder);
        CardinalityAggregationBuilder cardinalityAggregation = AggregationBuilders.cardinality("uniqueDocs").field("companyId");
        filterAggregation.subAggregation(cardinalityAggregation);

        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.aggregation(filterAggregation);
        searchRequest.source(searchSourceBuilder);

        long count = 0;
        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 获取聚合结果
            Filter filter = searchResponse.getAggregations().get("myFilter");
            Cardinality cardinality = filter.getAggregations().get("uniqueDocs");
            count = cardinality.getValue();
        } catch (IOException e) {
            log.error("热线关键指标-团队业务指标-10s坐席应答量-查询es出现异常：", e);
        }
        return count;
    }

}
