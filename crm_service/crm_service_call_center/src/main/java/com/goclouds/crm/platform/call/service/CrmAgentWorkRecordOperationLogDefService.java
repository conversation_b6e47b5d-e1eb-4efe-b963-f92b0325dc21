package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordOperationLogDef;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_operation_log_def(工单操作日志国际化定义表)】的数据库操作Service
* @createDate 2023-11-06 14:55:34
*/
public interface CrmAgentWorkRecordOperationLogDefService extends IService<CrmAgentWorkRecordOperationLogDef> {

    /**
     *  查询国际化日志内容
     * @param languageCode 语言code
     * @param operationType 操作类型
     * @return 国际化日志内容
     */
    String queryLogContent(String languageCode, Integer operationType);
}
