package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAwsAccount;
import com.goclouds.crm.platform.call.mapper.CrmAwsAccountMapper;
import com.goclouds.crm.platform.call.service.CrmAwsAccountService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_aws_account(aws账号表)】的数据库操作Service实现
* @createDate 2023-05-23 13:50:29
*/
@Service
public class CrmAwsAccountServiceImpl extends ServiceImpl<CrmAwsAccountMapper, CrmAwsAccount>
    implements CrmAwsAccountService {

}




