package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExtInts;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordExtIntsMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordExtIntsService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_ext_ints(工单自定义属性实例化表;)】的数据库操作Service实现
* @createDate 2023-09-26 19:00:50
*/
@Service
public class CrmAgentWorkRecordExtIntsServiceImpl extends ServiceImpl<CrmAgentWorkRecordExtIntsMapper, CrmAgentWorkRecordExtInts>
    implements CrmAgentWorkRecordExtIntsService {

}




