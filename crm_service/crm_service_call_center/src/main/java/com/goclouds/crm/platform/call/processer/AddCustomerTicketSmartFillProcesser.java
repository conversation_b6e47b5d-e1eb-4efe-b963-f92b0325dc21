package com.goclouds.crm.platform.call.processer;

import com.alibaba.fastjson.JSONObject;
import com.goclouds.crm.platform.call.domain.smartFill.AddAigcTicketSmartFillRequest;
import com.goclouds.crm.platform.call.domain.smartFill.AigcTicketSmartFillDTO;
import com.goclouds.crm.platform.call.strategy.AigcTicketSmartFillEnum;
import com.goclouds.crm.platform.call.strategy.AigcTicketSmartFillStrategy;
import com.goclouds.crm.platform.openfeignClient.client.customer.CustomerClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.customer.UpdateSmartCustomer;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> pengliang.sun
 * @description : 策略实现
 */
@Slf4j
@Service
public class AddCustomerTicketSmartFillProcesser implements AigcTicketSmartFillStrategy {

    @Autowired
    private CustomerClient customerClient;

    @Override
    public AigcTicketSmartFillEnum aigcTicketSmartFillEnum() {
        return AigcTicketSmartFillEnum.CUSTOMER;
    }


    @Override
    public void addAigcTicketSmartFillStrategy(AigcTicketSmartFillDTO aigcTicketSmartFillDetail,
                                               String customerId, String workRecordId) {
        UpdateSmartCustomer updateSmartCustomer = new UpdateSmartCustomer();
        Map<String, Object> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put(aigcTicketSmartFillDetail.getStoreAttr(), aigcTicketSmartFillDetail.getAttrValue());
        updateSmartCustomer.setCustomerId(customerId);
        updateSmartCustomer.setUpdates(stringStringHashMap);
        updateSmartCustomer.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        updateSmartCustomer.setName(aigcTicketSmartFillDetail.getAttrName());
        log.info("保存智能填单策略实现-》客户资料RPC-入参为updateSmartCustomer：【{}】", JSONObject.toJSONString(updateSmartCustomer));
        customerClient.updateSmartCustomer(updateSmartCustomer);
    }


    // 将下划线命名转换为驼峰命名
    private static String toCamelCase(String underScore) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;
        for (char c : underScore.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    result.append(c);
                }
            }
        }
        return result.toString();
    }
}
