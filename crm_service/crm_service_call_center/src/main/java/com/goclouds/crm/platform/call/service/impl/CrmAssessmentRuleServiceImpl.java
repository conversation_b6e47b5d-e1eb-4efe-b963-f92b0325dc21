package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentRuleVo;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentCategoryMapper;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentFormVersionMapper;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentRuleMapper;
import com.goclouds.crm.platform.call.service.CrmAssessmentCategoryService;
import com.goclouds.crm.platform.call.service.CrmAssessmentRulePointService;
import com.goclouds.crm.platform.call.service.CrmAssessmentRuleService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评估规则Service实现
 */
@Service
@RequiredArgsConstructor
public class CrmAssessmentRuleServiceImpl extends ServiceImpl<CrmAssessmentRuleMapper, CrmAssessmentRule>
        implements CrmAssessmentRuleService {

    private final CrmAssessmentRulePointService rulePointService;
    private final CrmAssessmentCategoryMapper categoryMapper;
    private final CrmAssessmentFormVersionMapper versionMapper;

    @Override
    public IPage<CrmAssessmentRuleVo> queryRulePages(IPage<Object> pageParam, CrmAssessmentRule rule) {
        if (rule == null) {
            rule = new CrmAssessmentRule();
        }

        // 分页参数
        Page<CrmAssessmentRule> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());

        // 构建查询条件
        LambdaQueryWrapper<CrmAssessmentRule> queryWrapper = new LambdaQueryWrapper<>();

        // 按评估ID查询
        if (StringUtils.isNotBlank(rule.getAssessmentId())) {
            queryWrapper.eq(CrmAssessmentRule::getAssessmentId, rule.getAssessmentId());
        }

        // 按版本ID判断状态规则
        if (StringUtils.isNotBlank(rule.getVersionId())) {
            // 查询该评估下的所有版本（仅用来判断是否是最新版本）
            List<CrmAssessmentFormVersion> versionList = versionMapper.selectList(
                    new LambdaQueryWrapper<CrmAssessmentFormVersion>()
                            .eq(CrmAssessmentFormVersion::getAssessmentId, rule.getAssessmentId())
                            .orderByDesc(CrmAssessmentFormVersion::getVersionNo)
            );

            // 如果是最新版本，则查状态为 1（历史规则）
            if (!versionList.isEmpty() && Objects.equals(rule.getVersionId(), versionList.get(0).getVersionId())) {
                queryWrapper.eq(CrmAssessmentRule::getStatus, 1);
            } else {
                queryWrapper.eq(CrmAssessmentRule::getVersionId, rule.getVersionId());
                queryWrapper.eq(CrmAssessmentRule::getStatus, 0);
            }
        } else {
            // 没传 versionId 默认查最新规则
            queryWrapper.eq(CrmAssessmentRule::getStatus, 1);
        }

        if (StringUtils.isNotBlank(rule.getRuleName())) {
            String searchName = rule.getRuleName();
            queryWrapper.and(wrapper -> wrapper
                    .like(CrmAssessmentRule::getRuleName, searchName)
                    .or()
                    .like(CrmAssessmentRule::getDescription, searchName)
            );
        }
        // 分类ID包含子级分类
        if (StringUtils.isNotBlank(rule.getCategoryId())) {
            List<String> allCategoryIds = getAllChildCategoryIds(rule.getCategoryId());
            if (!allCategoryIds.isEmpty()) {
                queryWrapper.in(CrmAssessmentRule::getCategoryId, allCategoryIds);
            }
        }

        // 只查启用数据
        queryWrapper.eq(CrmAssessmentRule::getDataStatus, 1);
        queryWrapper.orderByDesc(CrmAssessmentRule::getCreateTime);

        // 执行分页查询
        IPage<CrmAssessmentRule> rulePage = this.baseMapper.selectPage(page, queryWrapper);

        // 转 VO，并填充分类名称
        List<CrmAssessmentRuleVo> voList = rulePage.getRecords().stream().map(ruleEntity -> {
            CrmAssessmentRuleVo vo = new CrmAssessmentRuleVo();
            BeanUtils.copyProperties(ruleEntity, vo);

            if (StringUtils.isNotBlank(ruleEntity.getCategoryId())) {
                CrmAssessmentCategory category = categoryMapper.selectById(ruleEntity.getCategoryId());
                if (category != null) {
                    vo.setCategoryName(category.getCategoryName());
                }
            }
            List<CrmAssessmentRulePoint> rulePointList= rulePointService.listRulePointsByRuleId(ruleEntity.getRuleId());
            vo.setRulePointList(rulePointList);
            if(vo.getScoreType()==2){
                vo.setScore(rulePointList.stream()
                        .map(CrmAssessmentRulePoint::getScore)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            return vo;
        }).collect(Collectors.toList());

        // 构建分页结果
        IPage<CrmAssessmentRuleVo> resultPage = new Page<>();
        resultPage.setCurrent(rulePage.getCurrent());
        resultPage.setSize(rulePage.getSize());
        resultPage.setTotal(rulePage.getTotal());
        resultPage.setRecords(voList);

        return resultPage;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRule(CrmAssessmentRule rule) {
        // 设置初始值
        rule.setCreateTime(LocalDateTime.now());
        return this.save(rule);
    }

//    @Override
//    public Boolean saveOrUpdateRuleInfo(CrmAssessmentRuleVo ruleInfo) {
//        CrmAssessmentRule rule = new CrmAssessmentRule();
//        BeanUtils.copyProperties(ruleInfo, rule);
//
//        boolean isUpdate = StringUtils.isNotBlank(rule.getRuleId());
//
//        //1.没有版本情况
//        //新增和删除不用新增规则数据
//        //2.有版本情况
//        //不动原来的数据新增和修改处理
//        if (isUpdate) {
//            // 更新操作
//            rule.setModifyTime(LocalDateTime.now());
//            this.updateById(rule);
//            return true;
//        } else {
//            // 新增操作
//            rule.setRuleId(UUID.randomUUID().toString().replace("-", ""));
//            rule.setCreateTime(LocalDateTime.now());
//
//            this.save(rule);
//            return true;
//        }
//    }

    @Override
    public Boolean saveOrUpdateRuleInfo(CrmAssessmentRuleVo ruleInfo) {
        String userId = SecurityUtil.getUserId();
        boolean isUpdate = StringUtils.isNotBlank(ruleInfo.getRuleId());
        boolean hasVersion = StringUtils.isNotBlank(ruleInfo.getVersionId());

        if (isUpdate) {
            // 更新
            CrmAssessmentRule rule = new CrmAssessmentRule();
            BeanUtils.copyProperties(ruleInfo, rule);
            rule.setModifyTime(LocalDateTime.now());
            rule.setModifier(userId);
            boolean updateResult = this.updateById(rule);
            saveOrUpdateRulePoints(rule.getRuleId(), ruleInfo.getRulePointList(), userId);
            return updateResult;
        } else {
            if (!hasVersion) {
                // 无版本：需要保存草稿 + 正式版本
                // 草稿
                String draftRuleId = UUID.randomUUID().toString().replace("-", "");
                CrmAssessmentRule draftRule = new CrmAssessmentRule();
                BeanUtils.copyProperties(ruleInfo, draftRule);
                draftRule.setRuleId(draftRuleId);
                draftRule.setStatus(1); // 草稿
                draftRule.setCreator(userId);
                draftRule.setCreateTime(LocalDateTime.now());
                this.save(draftRule);
                saveOrUpdateRulePoints(draftRuleId, ruleInfo.getRulePointList(), userId);

//                // 正式版本（重新生成 ID，赋值 versionId）
//                String formalRuleId = UUID.randomUUID().toString().replace("-", "");
//                CrmAssessmentRule formalRule = new CrmAssessmentRule();
//                BeanUtils.copyProperties(ruleInfo, formalRule);
//                formalRule.setRuleId(formalRuleId);
//                formalRule.setStatus(0); // 正式
//                formalRule.setCreator(userId);
//                formalRule.setCreateTime(LocalDateTime.now());
//                this.save(formalRule);
//                saveOrUpdateRulePoints(formalRuleId, ruleInfo.getRulePointList(), userId);

                return true;
            } else {
                //有版本：仅新增正式版本
                String newRuleId = UUID.randomUUID().toString().replace("-", "");
                CrmAssessmentRule rule = new CrmAssessmentRule();
                BeanUtils.copyProperties(ruleInfo, rule);
                rule.setRuleId(newRuleId);
                rule.setStatus(0); // 正式
                rule.setCreator(userId);
                rule.setCreateTime(LocalDateTime.now());
                rule.setVersionId(ruleInfo.getVersionId());
                boolean saveResult = this.save(rule);
                saveOrUpdateRulePoints(newRuleId, ruleInfo.getRulePointList(), userId);
                return saveResult;
            }
        }
    }

    /**
     * 保存或更新规则点
     */
    private void saveOrUpdateRulePoints(String ruleId, List<CrmAssessmentRulePoint> pointList, String userId) {
        if (pointList == null || pointList.isEmpty()) return;

        rulePointService.delPoiint(ruleId);
        for (CrmAssessmentRulePoint point : pointList) {
            point.setRuleId(ruleId);
            if (StringUtils.isBlank(point.getPointId())) {
                point.setPointId(UUID.randomUUID().toString().replace("-", ""));
                point.setCreator(userId);
                point.setCreateTime(LocalDateTime.now());
                rulePointService.addRulePoint(point);
            } else {
                rulePointService.updateRulePoint(point);
            }
        }
    }


    @Override
    public Boolean deleteRule(String ruleId) {
        if (StringUtils.isBlank(ruleId)) {
            return false;
        }

        // 检查是否有子分类
        //todo

        // 逻辑删除，将数据状态更新为已删除
        CrmAssessmentRule rule = new CrmAssessmentRule();
        rule.setRuleId(ruleId);
        rule.setDataStatus(0);
        rule.setModifyTime(LocalDateTime.now());

        return this.updateById(rule);
    }

    @Override
    public CrmAssessmentRuleVo getRuleDetail(String ruleId) {
        if (StringUtils.isBlank(ruleId)) {
            return null;
        }

        // 查询规则
        CrmAssessmentRule rule = this.getById(ruleId);
        if (rule == null) {
            return null;
        }

        CrmAssessmentRuleVo vo = new CrmAssessmentRuleVo();
        BeanUtils.copyProperties(rule, vo);

        // 查询分类名称
        if (StringUtils.isNotBlank(rule.getCategoryId())) {
            CrmAssessmentCategory category = categoryMapper.selectById(rule.getCategoryId());
            if (category != null) {
                vo.setCategoryName(category.getCategoryName());
            }
        }

        // 查询质检点列表
        vo.setRulePointList(rulePointService.listRulePointsByRuleId(ruleId));

        return vo;
    }

    @Override
    public void removeDraftRulesByAssessmentId(String assessmentId) {
        LambdaQueryWrapper<CrmAssessmentRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmAssessmentRule::getAssessmentId, assessmentId)
                .eq(CrmAssessmentRule::getStatus, 1); // 草稿
        this.remove(wrapper);
    }

    @Override
    public List<CrmAssessmentRuleVo> ruleListByassessmentId(String assessmentId) {
        LambdaQueryWrapper<CrmAssessmentRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmAssessmentRule::getAssessmentId, assessmentId)
                .eq(CrmAssessmentRule::getStatus, 1)
                .eq(CrmAssessmentRule::getDataStatus, 1);
        List<CrmAssessmentRule> ruleList = this.baseMapper.selectList(wrapper);
        List<CrmAssessmentRuleVo> voList = new ArrayList<>();
        if(!ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                CrmAssessmentRuleVo vo = new CrmAssessmentRuleVo();
                BeanUtils.copyProperties(rule, vo);

                vo.setRulePointList(rulePointService.listRulePointsByRuleId(rule.getRuleId()));

                voList.add(vo);
            });
        }
        return voList;

    }

    private List<String> getAllChildCategoryIds(String parentId) {
        List<String> categoryIds = new ArrayList<>();
        categoryIds.add(parentId);

        List<CrmAssessmentCategory> childCategories = categoryMapper.selectList(
                new LambdaQueryWrapper<CrmAssessmentCategory>()
                        .eq(CrmAssessmentCategory::getParentId, parentId)
                        .eq(CrmAssessmentCategory::getDataStatus, 1)
        );

        for (CrmAssessmentCategory child : childCategories) {
            categoryIds.addAll(getAllChildCategoryIds(child.getCategoryId()));
        }

        return categoryIds;
    }


} 