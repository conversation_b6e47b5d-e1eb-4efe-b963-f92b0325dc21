package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmWorkRecordChannel;
import com.goclouds.crm.platform.call.service.CrmWorkRecordChannelService;
import com.goclouds.crm.platform.call.mapper.CrmWorkRecordChannelMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_work_record_channel(操作-工单关联表)】的数据库操作Service实现
* @createDate 2025-04-08 09:35:51
*/
@Service
public class CrmWorkRecordChannelServiceImpl extends ServiceImpl<CrmWorkRecordChannelMapper, CrmWorkRecordChannel>
    implements CrmWorkRecordChannelService{

}




