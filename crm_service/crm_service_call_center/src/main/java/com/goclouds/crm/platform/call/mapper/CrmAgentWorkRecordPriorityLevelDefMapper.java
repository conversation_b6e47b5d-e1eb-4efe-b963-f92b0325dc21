package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordPriorityLevelDef;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordLevelVO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_priority_level_def(工单优先级定义表;)】的数据库操作Mapper
* @createDate 2023-09-21 16:09:26
* @Entity generator.domain.CrmAgentWorkRecordPriorityLevelDef
*/
public interface CrmAgentWorkRecordPriorityLevelDefMapper extends BaseMapper<CrmAgentWorkRecordPriorityLevelDef> {

    @Select("select priority_level_id, priority_level_code, priority_level_name, `order` " +
            " from crm_agent_work_record_priority_level_def " +
            " where data_status = 1 order by `priority_level_id` desc")
    List<WorkRecordLevelVO> queryWorkRecordLevel();
}




