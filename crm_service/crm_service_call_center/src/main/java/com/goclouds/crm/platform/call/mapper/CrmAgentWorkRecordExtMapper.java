package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExt;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_ext(工作记录扩展属性表)】的数据库操作Mapper
* @createDate 2023-05-25 10:30:27
* @Entity workRecord.domain.CrmAgentWorkRecordExt
*/
public interface CrmAgentWorkRecordExtMapper extends BaseMapper<CrmAgentWorkRecordExt> {

    @Select("")
    List<String> getQueryWorkId(QueryWrapper<String> queryWrapperExt);
}




