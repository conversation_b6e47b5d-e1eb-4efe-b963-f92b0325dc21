package com.goclouds.crm.platform.call;

import com.goclouds.crm.platform.properties.SecurityProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;

import javax.annotation.PostConstruct;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @description
 * @createTime 2023年09月12日 16:33
 */
@SpringBootApplication(scanBasePackages = {"com.goclouds.crm.platform"})
@MapperScan(basePackages = {"com.goclouds.crm.platform.**.mapper"})
@EnableFeignClients(basePackages = {"com.goclouds.crm.platform"})
@EnableConfigurationProperties({SecurityProperties.class})
public class CallApplication {


    public static void main(String[] args) {
        SpringApplication.run(CallApplication.class, args);
    }
}
