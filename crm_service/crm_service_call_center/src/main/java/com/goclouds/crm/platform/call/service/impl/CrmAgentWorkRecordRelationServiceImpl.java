package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordRelation;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.TicketOperationLog;
import com.goclouds.crm.platform.call.domain.es.TicketRelation;
import com.goclouds.crm.platform.call.domain.vo.UpdateWorkAssociationVO;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordRelationMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordOperationLogService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordRelationService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.TicketOperationTypeEnum;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysUserVo;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.core.Map;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.goclouds.crm.platform.utils.SecurityUtil.getUserId;

/**
 * <AUTHOR>
 * @description 针对表【crm_agent_work_record_relation(工单关联关系表;)】的数据库操作Service实现
 * @createDate 2023-09-25 17:41:07
 */
@Service
@RequiredArgsConstructor

public class CrmAgentWorkRecordRelationServiceImpl extends ServiceImpl<CrmAgentWorkRecordRelationMapper, CrmAgentWorkRecordRelation>
        implements CrmAgentWorkRecordRelationService {



}
