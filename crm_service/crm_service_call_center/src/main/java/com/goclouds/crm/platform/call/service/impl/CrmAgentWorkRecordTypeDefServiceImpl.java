package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecord;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordTypeDef;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordTypeVO;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordEsResult;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordTypeDefMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordTypeDefService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.platform.TranslateClient;
import com.goclouds.crm.platform.openfeignClient.client.system.UserPreferencesConfigClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysLanguageDefVo;
import com.goclouds.crm.platform.openfeignClient.domain.translate.TranslateParam;
import com.goclouds.crm.platform.openfeignClient.domain.translate.TranslateResult;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【crm_agent_work_record_type_def(工单类型定义表)】的数据库操作Service实现
 * @createDate 2023-09-21 16:09:26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CrmAgentWorkRecordTypeDefServiceImpl extends ServiceImpl<CrmAgentWorkRecordTypeDefMapper, CrmAgentWorkRecordTypeDef>
        implements CrmAgentWorkRecordTypeDefService {
    @Autowired
    @Lazy
    private CrmAgentWorkRecordService workRecordService;
    @Resource
    private UserPreferencesConfigClient preferencesConfigClient;

    private final RestHighLevelClient restHighLevelClient;

    private final TranslateClient translateClient;
    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    @Value("${translate.access-key}")
    private String accessKey;

    @Value("${translate.secret-key}")
    private String secretKey;

    @Value("${translate.region}")
    private String region;



    @Override
    public List<WorkRecordTypeVO> queryWorkRecordType() {

        return this.baseMapper.queryWorkRecordType(SecurityUtil.getLoginUser().getCompanyId(), ServletUtils.getHeaderLanguage());
    }

    @Override
    public List<WorkRecordTypeVO> queryWorkRecordTypeByLanguage(String languageCode) {

        return this.baseMapper.queryWorkRecordTypeByLanguage(SecurityUtil.getLoginUser().getCompanyId(), languageCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateWorkOrderType(List<WorkRecordTypeVO> workRecordTypeVOList, String languageCode) {
        if (CollectionUtils.isEmpty(workRecordTypeVOList)) {
            return AjaxResult.failure(MessageUtils.get("save.one.work.order.type.data.at.least"));
        }
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String userId = SecurityUtil.getLoginUser().getUserId();
        //查询要保存的数据是否有空值，是否有重复值
        Set<String> nameSet = new HashSet<>();
        Set<String> valueSet = new HashSet<>();
        List<CrmAgentWorkRecordTypeDef> defList = new ArrayList<>();
        List<CrmAgentWorkRecordTypeDef> addList = new ArrayList<>();
        for (int i = 0; i < workRecordTypeVOList.size(); i++) {
            WorkRecordTypeVO workRecordTypeVO = workRecordTypeVOList.get(i);
            String workRecordTypeName = workRecordTypeVO.getWorkRecordTypeName();
            String workRecordTypeValue = workRecordTypeVO.getWorkRecordTypeValue();
            String workRecordTypeId = workRecordTypeVO.getWorkRecordTypeId();
            if (StringUtil.isEmpty(workRecordTypeName) || StringUtil.isEmpty(workRecordTypeValue)) {
                //工单类型名称或工单类型值不能为空
                return AjaxResult.failure(MessageUtils.get("work.order.type.name.or.value.can.not.be.empty"));
            }
            nameSet.add(workRecordTypeName);
            valueSet.add(workRecordTypeValue);
            //提前组装要批量保存新数据
            CrmAgentWorkRecordTypeDef typeDef = new CrmAgentWorkRecordTypeDef();
            BeanUtils.copyProperties(workRecordTypeVO, typeDef);
            if (StringUtil.isEmpty(workRecordTypeId)) {
                workRecordTypeId = IdWorker.get32UUID();
                typeDef.setWorkRecordTypeId(workRecordTypeId);
                typeDef.setCompanyId(companyId);
                typeDef.setOrder(i + 1);
                typeDef.setCreator(userId);
                typeDef.setCreateTime(new Date());
                typeDef.setModifier(userId);
                typeDef.setModifyTime(new Date());
                typeDef.setDataStatus(Constants.NORMAL);
                typeDef.setLanguageCode(languageCode);
                //用于其他语言的新增数据（更新的话，只顾及当前所选语言，新增的话，还要兼顾其他语言）
                addList.add(typeDef);
                defList.add(typeDef);
            } else {
                typeDef.setCompanyId(companyId);
                typeDef.setOrder(i + 1);
                typeDef.setCreator(userId);
                typeDef.setModifier(userId);
                typeDef.setModifyTime(new Date());
                typeDef.setDataStatus(Constants.NORMAL);
                typeDef.setLanguageCode(languageCode);
                defList.add(typeDef);
            }

        }
        if (nameSet.size() != workRecordTypeVOList.size()) {
            //工单类型名称有重复
            return AjaxResult.failure(MessageUtils.get("work.order.type.name.repeat"));
        }
        if (valueSet.size() != workRecordTypeVOList.size()) {
            //工单类型值有重复
            return AjaxResult.failure(MessageUtils.get("work.order.type.value.repeat"));
        }
        //保存或者是更新当前指定语言下的数据
        this.saveOrUpdateBatch(defList);
        //保存除了当前语言之外，其他语言的新增数据
        if (CollectionUtils.isNotEmpty(addList)) {
            R<List<SysLanguageDefVo>> listR = preferencesConfigClient.queryLanguage();
            List<SysLanguageDefVo> list = new ArrayList<>();
            if (listR.getCode() == 200) {
                list = listR.getData();
                if (CollectionUtils.isNotEmpty(list)) {
                    //过滤掉不为入参语言的数据，目的是添加给其他语言添加配套数据
                    list = list.stream().filter((SysLanguageDefVo sysLanguageDefVo) -> !sysLanguageDefVo.getLanguageCode().equals(languageCode)).collect(Collectors.toList());
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                for (SysLanguageDefVo languageDefVo : list) {
                    String eachLanguageCode = languageDefVo.getLanguageCode();
                    String languageIsoCode = languageDefVo.getLanguageIsoCode();
                    //查询其他语言类型的旧数据，对本次新增数据进行order排序
                    LambdaQueryWrapper<CrmAgentWorkRecordTypeDef> queryWrapper = new LambdaQueryWrapper<CrmAgentWorkRecordTypeDef>()
                            .eq(CrmAgentWorkRecordTypeDef::getCompanyId, companyId)
                            .eq(CrmAgentWorkRecordTypeDef::getDataStatus, Constants.NORMAL)
                            .eq(CrmAgentWorkRecordTypeDef::getLanguageCode, eachLanguageCode)
                            .orderByDesc(CrmAgentWorkRecordTypeDef::getOrder)
                            .last("LIMIT 1");
                    CrmAgentWorkRecordTypeDef maxOrderOne = this.getOne(queryWrapper);
                    Integer currentOrder;
                    if (maxOrderOne != null) {
                        Integer customerExtDefOrder = maxOrderOne.getOrder();
                        currentOrder = customerExtDefOrder + 1;
                    } else {
                        currentOrder = 1;
                    }
                    for (int i = 0; i < addList.size(); i++) {
                        CrmAgentWorkRecordTypeDef typeDef = addList.get(i);
                        typeDef.setWorkRecordTypeId(IdWorker.get32UUID());
                        typeDef.setOrder(currentOrder + i);
                        typeDef.setLanguageCode(eachLanguageCode);
                        //翻译工单类型
                        String workRecordTypeName = typeDef.getWorkRecordTypeName();
                        //需求修改，不用谷歌翻译了
//                        String trans = TransUtil.trans(workRecordTypeName, languageIsoCode);
//                        typeDef.setWorkRecordTypeName(trans);
                        TranslateParam translateParam = new TranslateParam();
                        translateParam.setAccessKeyId(accessKey);
                        translateParam.setSecretAccessKey(secretKey);
                        translateParam.setRegion(region);
                        translateParam.setTextToTranslate(workRecordTypeName);
                        translateParam.setTargetLanguageCode(languageIsoCode);
                        try {
                            R<TranslateResult> translateResultR = translateClient.translateText(translateParam);
                            if (translateResultR.getCode() == AjaxResult.SUCCESS) {
                                TranslateResult translateResult = translateResultR.getData();
                                String translatedText = translateResult.getTranslatedText();
                                typeDef.setWorkRecordTypeName(translatedText);
                            }else{
                                log.error("调用call-api翻译文本返回值不为200:" + translateResultR.getMsg());
                                //如果翻译语言失败了，就用入参语言
                                typeDef.setWorkRecordTypeName(workRecordTypeName);
                            }
                        } catch (Exception e) {
                            log.error("调用call-api翻译文本出现异常:" ,e);
                            //如果翻译语言报错了，就用入参语言
                            typeDef.setWorkRecordTypeName(workRecordTypeName);
                        }
                    }
                    this.saveBatch(addList);
                }
            }
        }
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteWorkOrderType(String workRecordTypeId) {
        //如果要删除数据，需要判断该数据是否工单表占用，如果占用，不允许删除
        QueryWrapper<CrmAgentWorkRecordTypeDef> queryWrapper = new QueryWrapper<CrmAgentWorkRecordTypeDef>()
                .eq("work_record_type_id", workRecordTypeId)
                .eq("data_status", Constants.NORMAL);
        CrmAgentWorkRecordTypeDef workRecordTypeDefResult = this.getBaseMapper().selectOne(queryWrapper);
        if(null==workRecordTypeDefResult){
            //如果不存在，意味着也没必要删除了
            return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
        }
        String workRecordTypeCode = workRecordTypeDefResult.getWorkRecordTypeValue();
        //先根据这个工单类型id，查询到对应的工单类型code，因为es的工单index中并没有用到工单类型id，而是用的工单类型code
        Integer resultCode = queryWorkOrderTypeInUse(workRecordTypeCode);
        if (resultCode == 202) {
            //202这个值表示出现异常，201表示没查到被占用的情况
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
        if (resultCode == 200) {
            //200这个值表示有数据，被占用了
            return AjaxResult.failure(MessageUtils.get("work.record.type.id.occupied.by.work.order"));
        }
        //如果没被占用，就删除(删除所有语言对应的工单类型定义信息)
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        CrmAgentWorkRecordTypeDef typeDef = this.getOne(new QueryWrapper<CrmAgentWorkRecordTypeDef>()
                .eq("company_id", companyId)
                .eq("data_status", Constants.NORMAL)
                .eq("work_record_type_id", workRecordTypeId));
        if (typeDef != null) {
            String code = typeDef.getWorkRecordTypeValue();
            List<CrmAgentWorkRecordTypeDef> list = this.list(new QueryWrapper<CrmAgentWorkRecordTypeDef>()
                    .eq("company_id", companyId)
                    .eq("data_status", Constants.NORMAL)
                    .eq("work_record_type_value", code));
            for (CrmAgentWorkRecordTypeDef workRecordTypeDef : list) {
                String eachId = workRecordTypeDef.getWorkRecordTypeId();
                this.remove(new LambdaQueryWrapper<CrmAgentWorkRecordTypeDef>()
                        .eq(CrmAgentWorkRecordTypeDef::getWorkRecordTypeId, eachId));
            }
        }
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    private Integer queryWorkOrderTypeInUse(String workRecordTypeId) {
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义工单表索引名称
            String indexName = ticketIndex + companyId;

            AjaxResult ajaxResult = checkIndexExist(indexName);
            int code = ajaxResult.getCode();
            if (code == 201) {
                return code;
            }
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("work_record_type_code", workRecordTypeId));
            searchSourceBuilder.query(boolQueryBuilder);

            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            //获取总数量
            long totalCount = searchResponse.getHits().getTotalHits().value;
            if (totalCount > 0) {
                return 200;
            }
            return 201;
        } catch (IOException e) {
            log.error("查询工单类型是否被占用出现异常：", e);
            return 202;
        }
    }

    /**
     * 验证索引是否存在
     *
     * @param index 索引名称
     * @return 存在状态
     * @throws IOException
     */
    private boolean headIndexExists(String index) {
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        } catch (Exception e) {
            log.error("验证索引失败:", e);
        }
        return false;
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        } catch (Exception e) {
            log.error("创建索引报错", e);
        }
    }

    //公用方法，用于校验索引是否存在，如果不存在，返回空数据
    private AjaxResult checkIndexExist(String indexName) {
        try {
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
                //用201来表示没数据，这样直接返回对应的空集合
                return AjaxResult.ok().setCode(201);
            }
        } catch (Exception e) {
            //如果上述方法出现异常，那么也先返回201
            return AjaxResult.ok().setCode(201);
        }
        return AjaxResult.ok().setCode(200);
    }


    public CrmAgentWorkRecordTypeDef gatWorkRecordTypeInfo(String workRecordTypeId) {
            LambdaQueryWrapper<CrmAgentWorkRecordTypeDef> queryWrapper = new LambdaQueryWrapper<CrmAgentWorkRecordTypeDef>()
                    .eq(CrmAgentWorkRecordTypeDef::getLanguageCode,  ServletUtils.getHeaderLanguage())
                    .eq(CrmAgentWorkRecordTypeDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                    .eq(CrmAgentWorkRecordTypeDef::getDataStatus, 1)
                    .eq(CrmAgentWorkRecordTypeDef::getWorkRecordTypeValue, workRecordTypeId);

           return this.baseMapper.selectOne(queryWrapper);
        }


}




