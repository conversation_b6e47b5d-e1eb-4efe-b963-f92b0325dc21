package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordAutomaticallyManage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.vo.AddOrUpdateWorkOrderManageVO;
import com.goclouds.crm.platform.call.domain.vo.QueryWorkOrderManageVO;
import com.goclouds.crm.platform.call.domain.vo.QueryAllRecordAndCloseManageVO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_automatically_manage(工单自动管理表)】的数据库操作Service
* @createDate 2025-04-08 09:35:51
*/
public interface CrmAgentWorkRecordAutomaticallyManageService extends IService<CrmAgentWorkRecordAutomaticallyManage> {

    AjaxResult<IPage<QueryWorkOrderManageVO>> listWorkManage(IPage<CrmAgentWorkRecordAutomaticallyManage> pageParam,Long type);

    AjaxResult addOrUpdateWorkOrderManage(AddOrUpdateWorkOrderManageVO addOrUpdateWorkOrderManageVO);

    AjaxResult removeAll(String id);

    AjaxResult<QueryAllRecordAndCloseManageVO> queryAllRecordAndCloseManage(String companyId, String workRecordId);
}
