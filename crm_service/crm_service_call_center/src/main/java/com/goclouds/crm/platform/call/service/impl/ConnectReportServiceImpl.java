package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.goclouds.crm.platform.call.domain.vo.ConnectIndex;
import com.goclouds.crm.platform.call.domain.vo.statis.*;
import com.goclouds.crm.platform.call.service.ConnectReportService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.utils.RedisCacheUtil;
import com.goclouds.crm.platform.common.utils.ServletUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.channel.ConnectsServiceClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.TimeZoneUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ConnectReportServiceImpl implements ConnectReportService {

    private final ConnectsServiceClient connectsServiceClient;

    @Override
    public AjaxResult<IPage<ConnectQueueLatitudeAgentVo>> queryQueueRealTimeIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO,IPage<ConnectQueueLatitudeAgentVo> pageParam) {
        List<ConnectQueueLatitudeAgentVo> connectQueueLatitudeAgentVoList = publicQueryQueueRealTimeIndex(null);
        if(connectQueueLatitudeAgentVoList.isEmpty()){
            return AjaxResult.ok();
        }
        if(StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getConnectAlias())||StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getQueueName())){
            connectQueueLatitudeAgentVoList = connectQueueLatitudeAgentVoList.stream()
                    .filter(vo -> {
                        boolean matchesAlias = connectRealTimeIndicatorsVO.getConnectAlias() == null ||
                                (vo.getConnectAlias() != null && vo.getConnectAlias().contains(connectRealTimeIndicatorsVO.getConnectAlias()));
                        boolean matchesQueueName = connectRealTimeIndicatorsVO.getQueueName() == null ||
                                (vo.getQueueName() != null && vo.getQueueName().contains(connectRealTimeIndicatorsVO.getQueueName()));
                        return matchesAlias && matchesQueueName;
                    }).collect(Collectors.toList());
            if(connectQueueLatitudeAgentVoList.isEmpty()){
                return AjaxResult.ok();
            }
        }
        long pages = pageParam.getCurrent();
        long size = pageParam.getSize();
        // 计算起始索引
        int fromIndex = (int) ((pages - 1) * size);
        // 计算结束索引
        int toIndex = (int) (Math.min(fromIndex + size, connectQueueLatitudeAgentVoList.size()));

        // 判断起始索引是否超出范围
        if (fromIndex >= connectQueueLatitudeAgentVoList.size()) {
            return AjaxResult.ok(); // 返回一个空列表
        }
        // 获取分页后的子列表
        List<ConnectQueueLatitudeAgentVo> paginatedList = connectQueueLatitudeAgentVoList.subList(fromIndex, toIndex);
        pageParam.setRecords(paginatedList);
        pageParam.setTotal(connectQueueLatitudeAgentVoList.size());
        return AjaxResult.ok(pageParam);
    }

    /**
     * 通用查询队列纬度座席状态列表指标数据
     */
    public List<ConnectQueueLatitudeAgentVo> publicQueryQueueRealTimeIndex(String companyId){
        // 定义返回前端List
        List<ConnectQueueLatitudeAgentVo> connectQueueLatitudeAgentVoList = new ArrayList<>();
        //代码复用，部分功能的公司Id，是从数据库中取值的，而非从登录信息中获取，用如下代码进行判断
        //如果入参信息中的companyId为空，就从登录信息中获取
        if(StringUtil.isEmpty(companyId)){
            companyId = SecurityUtil.getLoginUser().getCompanyId();
        }

        // 获取到当前公司的实例信息
        R<List<String>> listR = connectsServiceClient.queryCompanyConnect(companyId);
        if (listR.getCode() == 200&&listR.getData() != null) {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        List<String> list = listR.getData();
            for (String connectId : list) {
                // 根据实例信息，拿队列的信息
                Object cacheObject = RedisCacheUtil.getCacheObject(Constants.CONNECT_INDEX_QUEUE_PREFIX.concat(connectId));
                if(cacheObject == null || cacheObject == ""){
                    continue;
                }
                Type type = new TypeToken<Map<String, List<ConnectIndex>>>() {}.getType();
                // 将JSON字符串转换回对象
                Map<String, List<ConnectIndex>> groupDate = gson.fromJson(cacheObject.toString(), type);
                // 因为数据是根据队列ID聚合的，所以每个key即为每行数据
                groupDate.forEach((key, value) -> {
                    ConnectQueueLatitudeAgentVo connectQueueLatitudeAgentVo = new ConnectQueueLatitudeAgentVo();
                    connectQueueLatitudeAgentVo.setQueueId(key);
                    connectQueueLatitudeAgentVo.setQueueName(value.get(0).getQueueName());
                    connectQueueLatitudeAgentVo.setConnectAlias(value.get(0).getConnectAlias());
                    for (ConnectIndex connectIndex : value) {
                        switch (connectIndex.getIndexName()) {
                            case "agentsOnline":
                                connectQueueLatitudeAgentVo.setOnlineQuantity(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                                break;
                            case "agentsOnContact":
                                connectQueueLatitudeAgentVo.setAgentReceptionNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                                break;
                            case "agentsNonProductive":
                                connectQueueLatitudeAgentVo.setNptNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                                break;
                            case "agentsAfterContactWork":
                                connectQueueLatitudeAgentVo.setAcwStatusNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                                break;
                            case "agentsError":
                                connectQueueLatitudeAgentVo.setErrorStatusNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                                break;
                            case "agentsAvailable":
                                connectQueueLatitudeAgentVo.setIdleNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                                break;
                            default:
                                break;
                        }
                    }
                    connectQueueLatitudeAgentVoList.add(connectQueueLatitudeAgentVo);
                });
            }
        }
        return connectQueueLatitudeAgentVoList;
    }

    @Override
    public AjaxResult<IPage<ConnectQueueLatitudeContactsVo>> queryQueueContactsIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectQueueLatitudeContactsVo> pageParam) {

        // 查询队列联系人数据
        List<ConnectQueueLatitudeContactsVo> contactsVos = publicQueryQueueContactsIndex();
        if(contactsVos.isEmpty()){
            return AjaxResult.ok();
        }
        if(StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getConnectAlias())||StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getQueueName())){
            contactsVos = contactsVos.stream()
                    .filter(vo -> {
                        boolean matchesAlias = connectRealTimeIndicatorsVO.getConnectAlias() == null ||
                                (vo.getConnectAlias() != null && vo.getConnectAlias().contains(connectRealTimeIndicatorsVO.getConnectAlias()));
                        boolean matchesQueueName = connectRealTimeIndicatorsVO.getQueueName() == null ||
                                (vo.getQueueName() != null && vo.getQueueName().contains(connectRealTimeIndicatorsVO.getQueueName()));
                        return matchesAlias && matchesQueueName;
                    }).collect(Collectors.toList());
            if(contactsVos.isEmpty()){
                return AjaxResult.ok();
            }
        }
        long pages = pageParam.getCurrent();
        long size = pageParam.getSize();
        // 计算起始索引
        int fromIndex = (int) ((pages - 1) * size);
        // 计算结束索引
        int toIndex = (int) (Math.min(fromIndex + size, contactsVos.size()));

        // 判断起始索引是否超出范围
        if (fromIndex >= contactsVos.size()) {
            return AjaxResult.ok(); // 返回一个空列表
        }
        // 获取分页后的子列表
        List<ConnectQueueLatitudeContactsVo> paginatedList = contactsVos.subList(fromIndex, toIndex);
        pageParam.setRecords(paginatedList);
        pageParam.setTotal(contactsVos.size());
        return AjaxResult.ok(pageParam);
    }

    private List<ConnectQueueLatitudeContactsVo> publicQueryQueueContactsIndex(){
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        List<ConnectQueueLatitudeContactsVo> contactsVos = new ArrayList<>();
        // 获取到当前公司的实例信息
        R<List<String>> listR = connectsServiceClient.queryCompanyConnect(companyId);
        if (listR.getCode() == 200&&listR.getData() != null) {
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            List<String> list = listR.getData();
            for (String connectId : list) {
                // 根据实例信息，拿队列的信息
                Object cacheObject = RedisCacheUtil.getCacheObject(Constants.CONNECT_INDEX_QUEUE_PREFIX.concat(connectId));
                if(cacheObject == null || cacheObject == ""){
                    continue;
                }
                Type type = new TypeToken<Map<String, List<ConnectIndex>>>() {}.getType();
                // 将JSON字符串转换回对象
                Map<String, List<ConnectIndex>> groupDate = gson.fromJson(cacheObject.toString(), type);
                // 因为数据是根据队列ID聚合的，所以每个key即为每行数据
                groupDate.forEach((key, value) -> {
                    ConnectQueueLatitudeContactsVo contactsVo = new ConnectQueueLatitudeContactsVo();
                    contactsVo.setQueueId(key);
                    contactsVo.setQueueName(value.get(0).getQueueName());
                    contactsVo.setConnectAlias(value.get(0).getConnectAlias());
                    for (ConnectIndex connectIndex : value) {
                        switch (connectIndex.getIndexName()) {
                            case "agentsOnline":
                                contactsVo.setAvailableNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                                break;
                            case "agentsOnContact":
                                contactsVo.setActiveNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                                break;
                            default:
                                break;
                        }
                    }
                    contactsVos.add(contactsVo);
                });
            }
        }
        return contactsVos;
    }

    /**
     * 查询队列纬度性能列表指标
     * @return 队列纬度性能列表指标
     */
    @Override
    public AjaxResult<IPage<ConnectQueueLatitudePropertyVo>> queryQueuePropertyIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectQueueLatitudePropertyVo> pageParam) {
        List<ConnectQueueLatitudePropertyVo> propertyList = publicQueryQueuePropertyIndex(null);
        if(propertyList.isEmpty()){
          return AjaxResult.ok();
        }
        if(StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getConnectAlias())||StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getQueueName())){
            propertyList = propertyList.stream()
                    .filter(vo -> {
                        boolean matchesAlias = connectRealTimeIndicatorsVO.getConnectAlias() == null ||
                                (vo.getConnectAlias() != null && vo.getConnectAlias().contains(connectRealTimeIndicatorsVO.getConnectAlias()));
                        boolean matchesQueueName = connectRealTimeIndicatorsVO.getQueueName() == null ||
                                (vo.getQueueName() != null && vo.getQueueName().contains(connectRealTimeIndicatorsVO.getQueueName()));
                        return matchesAlias && matchesQueueName;
                    }).collect(Collectors.toList());
            if(propertyList.isEmpty()){
                return AjaxResult.ok();
            }
        }
        pageParam.setTotal(propertyList.size());
        long pages = pageParam.getCurrent();
        long size = pageParam.getSize();
        // 计算起始索引
        int fromIndex = (int) ((pages - 1) * size);
        // 计算结束索引
        int toIndex = (int) (Math.min(fromIndex + size, propertyList.size()));

        // 判断起始索引是否超出范围
        if (fromIndex >= propertyList.size()) {
            return AjaxResult.ok(); // 返回一个空列表
        }
        // 获取分页后的子列表
        List<ConnectQueueLatitudePropertyVo> paginatedList = propertyList.subList(fromIndex, toIndex);
        pageParam.setRecords(paginatedList);
        pageParam.setTotal(propertyList.size());
        return AjaxResult.ok(pageParam);
    }

    //改为public，因为热线关键指标那边的接口要复用这个方法
    public List<ConnectQueueLatitudePropertyVo> publicQueryQueuePropertyIndex(String companyId){
        if(StringUtil.isEmpty(companyId)){
            companyId = SecurityUtil.getLoginUser().getCompanyId();
        }
        List<ConnectQueueLatitudePropertyVo> propertyList = new ArrayList<>();
        // 获取到当前公司的实例信息
        R<List<String>> listR = connectsServiceClient.queryCompanyConnect(companyId);
        if (listR.getCode() == 200&&listR.getData() != null) {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
            List<String> list = listR.getData();
            for (String connectId : list) {
                // 根据实例信息，拿队列的信息
                Object cacheObject = RedisCacheUtil.getCacheObject(Constants.CONNECT_INDEX_QUEUE_PREFIX.concat(connectId));
                if(cacheObject == null || cacheObject == ""){
                    continue;
                }
                Type type = new TypeToken<Map<String, List<ConnectIndex>>>() {}.getType();
                // 将JSON字符串转换回对象
                Map<String, List<ConnectIndex>> groupDate = gson.fromJson(cacheObject.toString(), type);
                // 因为数据是根据队列ID聚合的，所以每个key即为每行数据
                groupDate.forEach((key, value) -> {
                    ConnectQueueLatitudePropertyVo property = new ConnectQueueLatitudePropertyVo();
                    property.setQueueId(key);
                    property.setQueueName(value.get(0).getQueueName());
                    property.setConnectAlias(value.get(0).getConnectAlias());
                    for (ConnectIndex connectIndex : value) {
                        // 匹配存储值
                        queueIndicatorMatching(connectIndex, property);
                    }
                    propertyList.add(property);
                });
            }
        }
        return propertyList;
    }

    @Override
    public AjaxResult<IPage<ConnectAgentLatitudeAgentVo>> queryAgentIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectAgentLatitudeAgentVo> pageParam) {

        List<ConnectAgentLatitudeAgentVo> agentList = publicQueryAgentIndex();
        if(agentList.isEmpty()){
            return AjaxResult.ok();
        }
        if(StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getConnectAlias())||StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getAgentName())){
            agentList = agentList.stream()
                    .filter(vo -> {
                        boolean matchesAlias = connectRealTimeIndicatorsVO.getConnectAlias() == null ||
                                (vo.getConnectAlias() != null && vo.getConnectAlias().contains(connectRealTimeIndicatorsVO.getConnectAlias()));
                        boolean matchesAgentName = connectRealTimeIndicatorsVO.getAgentName() == null ||
                                (vo.getAgentName() != null && vo.getAgentName().contains(connectRealTimeIndicatorsVO.getAgentName()));
                        return matchesAlias && matchesAgentName;
                    }).collect(Collectors.toList());
            if(agentList.isEmpty()){
                return AjaxResult.ok();
            }
        }
        pageParam.setTotal(agentList.size());
        long pages = pageParam.getCurrent();
        long size = pageParam.getSize();
        // 计算起始索引
        int fromIndex = (int) ((pages - 1) * size);
        // 计算结束索引
        int toIndex = (int) (Math.min(fromIndex + size, agentList.size()));

        // 判断起始索引是否超出范围
        if (fromIndex >= agentList.size()) {
            return AjaxResult.ok(); // 返回一个空列表
        }
        // 获取分页后的子列表
        List<ConnectAgentLatitudeAgentVo> paginatedList = agentList.subList(fromIndex, toIndex);
        pageParam.setRecords(paginatedList);
        pageParam.setTotal(agentList.size());
        return AjaxResult.ok(pageParam);
    }

    private List<ConnectAgentLatitudeAgentVo> publicQueryAgentIndex(){
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        List<ConnectAgentLatitudeAgentVo> agentList = new ArrayList<>();
        // 获取到当前公司的实例信息
        R<List<String>> listR = connectsServiceClient.queryCompanyConnect(companyId);
        if (listR.getCode() == 200&&listR.getData() != null) {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
            List<String> list = listR.getData();
            for (String connectId : list) {
                // 根据实例信息，拿队列的信息
                Object cacheObject = RedisCacheUtil.getCacheObject(Constants.CONNECT_INDEX_AGENT_PREFIX.concat(connectId));
                if(cacheObject == null || cacheObject == ""){
                    continue;
                }
                Type type = new TypeToken<Map<String, List<ConnectIndex>>>() {}.getType();
                // 将JSON字符串转换回对象
                Map<String, List<ConnectIndex>> groupDate = gson.fromJson(cacheObject.toString(), type);
                groupDate.forEach((key, value) -> {
                    ConnectAgentLatitudeAgentVo connectAgent = new ConnectAgentLatitudeAgentVo();
                    connectAgent.setAgentName(value.get(0).getQueueName());
                    connectAgent.setLastName(value.get(0).getLastName());
                    connectAgent.setFirstName(value.get(0).getFirstName());
                    connectAgent.setConnectAlias(value.get(0).getConnectAlias());
                    connectAgent.setAgentId(key);
                    // TODO 座席列表所有字段都未找到，暂时不进行展示
        //            for (ConnectIndex connectIndex : value) {
        //                switch (connectIndex.getIndexName()) {
        //                    case "agentsOnline":
        //                        contactsVo.setAvailableNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
        //                        break;
        //                    case "agentsOnContact":
        //                        contactsVo.setActiveNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
        //                        break;
        //                    default:
        //                        break;
        //                }
        //            }
                    agentList.add(connectAgent);
                });
            }
        }
        return agentList;
    }

    @Override
    public AjaxResult<IPage<ConnectAgentLatitudeContactsVo>> queryAgentContactsIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectAgentLatitudeContactsVo> pageParam) {

        List<ConnectAgentLatitudeContactsVo> contactsList = publicQueryAgentContactsIndex();
        if(contactsList.isEmpty()){
            return AjaxResult.ok();
        }
        if(StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getConnectAlias())||StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getAgentName())){
            contactsList = contactsList.stream()
                    .filter(vo -> {
                        boolean matchesAlias = connectRealTimeIndicatorsVO.getConnectAlias() == null ||
                                (vo.getConnectAlias() != null && vo.getConnectAlias().contains(connectRealTimeIndicatorsVO.getConnectAlias()));
                        boolean matchesAgentName = connectRealTimeIndicatorsVO.getAgentName() == null ||
                                (vo.getAgentName() != null && vo.getAgentName().contains(connectRealTimeIndicatorsVO.getAgentName()));
                        return matchesAlias && matchesAgentName;
                    }).collect(Collectors.toList());
            if(contactsList.isEmpty()){
                return AjaxResult.ok();
            }
        }
        pageParam.setTotal(contactsList.size());
        long pages = pageParam.getCurrent();
        long size = pageParam.getSize();
        // 计算起始索引
        int fromIndex = (int) ((pages - 1) * size);
        // 计算结束索引
        int toIndex = (int) (Math.min(fromIndex + size, contactsList.size()));

        // 判断起始索引是否超出范围
        if (fromIndex >= contactsList.size()) {
            return AjaxResult.ok(); // 返回一个空列表
        }
        // 获取分页后的子列表
        List<ConnectAgentLatitudeContactsVo> paginatedList = contactsList.subList(fromIndex, toIndex);
        pageParam.setRecords(paginatedList);
        pageParam.setTotal(contactsList.size());
        return AjaxResult.ok(pageParam);
    }

    private List<ConnectAgentLatitudeContactsVo> publicQueryAgentContactsIndex(){
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        List<ConnectAgentLatitudeContactsVo> contactsList = new ArrayList<>();
        // 获取到当前公司的实例信息
        R<List<String>> listR = connectsServiceClient.queryCompanyConnect(companyId);
        if (listR.getCode() == 200&&listR.getData() != null) {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
            List<String> list = listR.getData();
            for (String connectId : list) {
                // 根据实例信息，拿队列的信息
                Object cacheObject = RedisCacheUtil.getCacheObject(Constants.CONNECT_INDEX_AGENT_PREFIX.concat(connectId));
                if(cacheObject == null || cacheObject == ""){
                    continue;
                }
                Type type = new TypeToken<Map<String, List<ConnectIndex>>>() {}.getType();
                // 将JSON字符串转换回对象
                Map<String, List<ConnectIndex>> groupDate = gson.fromJson(cacheObject.toString(), type);
                groupDate.forEach((key, value) -> {
                    ConnectAgentLatitudeContactsVo connect = new ConnectAgentLatitudeContactsVo();
                    connect.setAgentName(value.get(0).getQueueName());
                    connect.setLastName(value.get(0).getLastName());
                    connect.setFirstName(value.get(0).getFirstName());
                    connect.setConnectAlias(value.get(0).getConnectAlias());
                    connect.setAgentId(key);
                    // TODO 联系人列表所有字段都未找到，暂时不进行展示
        //            for (ConnectIndex connectIndex : value) {
        //                switch (connectIndex.getIndexName()) {
        //                    case "agentsOnline":
        //                        contactsVo.setAvailableNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
        //                        break;
        //                    case "agentsOnContact":
        //                        contactsVo.setActiveNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
        //                        break;
        //                    default:
        //                        break;
        //                }
        //            }
                    contactsList.add(connect);
                });
            }
        }
        return contactsList;
    }

    @Override
    public AjaxResult<IPage<ConnectAgentLatitudePropertyVo>> queryAgentPropertyIndex(ConnectRealTimeIndicatorsVO connectRealTimeIndicatorsVO, IPage<ConnectAgentLatitudePropertyVo> pageParam) {
        List<ConnectAgentLatitudePropertyVo> propertyList = publicQueryAgentPropertyIndex();
        if(propertyList.isEmpty()){
            return AjaxResult.ok();
        }
        if(StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getConnectAlias())||StringUtils.isNotBlank(connectRealTimeIndicatorsVO.getAgentName())){
            propertyList = propertyList.stream()
                    .filter(vo -> {
                        boolean matchesAlias = connectRealTimeIndicatorsVO.getConnectAlias() == null ||
                                (vo.getConnectAlias() != null && vo.getConnectAlias().contains(connectRealTimeIndicatorsVO.getConnectAlias()));
                        boolean matchesAgentName = connectRealTimeIndicatorsVO.getAgentName() == null ||
                                (vo.getAgentName() != null && vo.getAgentName().contains(connectRealTimeIndicatorsVO.getAgentName()));
                        return matchesAlias && matchesAgentName;
                    }).collect(Collectors.toList());
            if(propertyList.isEmpty()){
                return AjaxResult.ok();
            }
        }
        pageParam.setTotal(propertyList.size());
        long pages = pageParam.getCurrent();
        long size = pageParam.getSize();
        // 计算起始索引
        int fromIndex = (int) ((pages - 1) * size);
        // 计算结束索引
        int toIndex = (int) (Math.min(fromIndex + size, propertyList.size()));

        // 判断起始索引是否超出范围
        if (fromIndex >= propertyList.size()) {
            return AjaxResult.ok(); // 返回一个空列表
        }
        // 获取分页后的子列表
        List<ConnectAgentLatitudePropertyVo> paginatedList = propertyList.subList(fromIndex, toIndex);
        pageParam.setRecords(paginatedList);
        pageParam.setTotal(propertyList.size());
        return AjaxResult.ok(pageParam);
    }

    private List<ConnectAgentLatitudePropertyVo> publicQueryAgentPropertyIndex(){
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        List<ConnectAgentLatitudePropertyVo> propertyList = new ArrayList<>();
        // 获取到当前公司的实例信息
        R<List<String>> listR = connectsServiceClient.queryCompanyConnect(companyId);
        if (listR.getCode() == 200&&listR.getData() != null) {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
            List<String> list = listR.getData();
            for (String connectId : list) {
                // 根据实例信息，拿队列的信息
                Object cacheObject = RedisCacheUtil.getCacheObject(Constants.CONNECT_INDEX_AGENT_PREFIX.concat(connectId));
                if(cacheObject == null || cacheObject == ""){
                    continue;
                }
                Type type = new TypeToken<Map<String, List<ConnectIndex>>>() {}.getType();
                // 将JSON字符串转换回对象
                Map<String, List<ConnectIndex>> groupDate = gson.fromJson(cacheObject.toString(), type);
                groupDate.forEach((key, value) -> {
                    ConnectAgentLatitudePropertyVo property = new ConnectAgentLatitudePropertyVo();
                    property.setAgentName(value.get(0).getQueueName());
                    property.setAgentId(key);
                    property.setConnectAlias(value.get(0).getConnectAlias());
                    for (ConnectIndex connectIndex : value) {
                        agentIndicatorMatching(connectIndex, property);
                    }
                    propertyList.add(property);
                });
            }
        }
        return propertyList;
    }

    @Override
    public AjaxResult<ConnectAgentLatitudePropertyAttributeVo> queryAgentPropertyAttribute() {
        // 取值redis，判断redis中是否有数据
        String agentAttribute = RedisCacheUtil.getCacheObject(Constants.AGENT_ATTRIBUTE_KEY.concat(SecurityUtil.getUserId()));
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        // 如果Redis中没有数据，则返回默认列表
        if(agentAttribute==null){
            ConnectAgentLatitudePropertyAttributeVo propertyAttribute = new ConnectAgentLatitudePropertyAttributeVo();
            String json = gson.toJson(propertyAttribute);
            RedisCacheUtil.setCacheObject(Constants.AGENT_ATTRIBUTE_KEY.concat(SecurityUtil.getUserId()), json);
            return AjaxResult.ok(propertyAttribute);
        }
        // redis中有数据，则转换成对象格式进行返回
        ConnectAgentLatitudePropertyAttributeVo attributeVo = gson.fromJson(agentAttribute, ConnectAgentLatitudePropertyAttributeVo.class);
        return AjaxResult.ok(attributeVo);
    }

    @Override
    public AjaxResult<ConnectQueueLatitudePropertyAttributeVo> queryQueueAttribute() {
        // 取值redis，判断redis中是否有数据
        String queueAttribute = RedisCacheUtil.getCacheObject(Constants.QUEUE_ATTRIBUTE_KEY.concat(SecurityUtil.getUserId()));
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        // 如果Redis中没有数据，则返回默认列表
        if(queueAttribute==null){
            ConnectQueueLatitudePropertyAttributeVo propertyAttribute = new ConnectQueueLatitudePropertyAttributeVo();
            String json = gson.toJson(propertyAttribute);
            RedisCacheUtil.setCacheObject(Constants.QUEUE_ATTRIBUTE_KEY.concat(SecurityUtil.getUserId()), json);
            return AjaxResult.ok(propertyAttribute);
        }
        // redis中有数据，则转换成对象格式进行返回
        ConnectQueueLatitudePropertyAttributeVo attributeVo = gson.fromJson(queueAttribute, ConnectQueueLatitudePropertyAttributeVo.class);
        return AjaxResult.ok(attributeVo);
    }

    @Override
    public AjaxResult<ConnectAgentLatitudeAgentAttributeVo> queryAgentIndexAttribute() {
        // 取值redis，判断redis中是否有数据
        String queueAttribute = RedisCacheUtil.getCacheObject(Constants.AGENT_INDEX_KEY.concat(SecurityUtil.getUserId()));
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        // 如果Redis中没有数据，则返回默认列表
        if(queueAttribute==null){
            ConnectAgentLatitudeAgentAttributeVo propertyAttribute = new ConnectAgentLatitudeAgentAttributeVo();
            String json = gson.toJson(propertyAttribute);
            RedisCacheUtil.setCacheObject(Constants.AGENT_INDEX_KEY.concat(SecurityUtil.getUserId()), json);
            return AjaxResult.ok(propertyAttribute);
        }
        // redis中有数据，则转换成对象格式进行返回
        ConnectAgentLatitudeAgentAttributeVo attributeVo = gson.fromJson(queueAttribute, ConnectAgentLatitudeAgentAttributeVo.class);
        return AjaxResult.ok(attributeVo);
    }

    @Override
    public AjaxResult<Object> updateQueueAttribute(ConnectQueueLatitudePropertyAttributeVo propertyAttribute) {
        // 转换为json
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        String json = gson.toJson(propertyAttribute);
        RedisCacheUtil.setCacheObject(Constants.QUEUE_ATTRIBUTE_KEY.concat(SecurityUtil.getUserId()), json);
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<Object> updateAgentIndexAttribute(ConnectAgentLatitudeAgentAttributeVo agentAttribute) {
        // 转换为json
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        String json = gson.toJson(agentAttribute);
        RedisCacheUtil.setCacheObject(Constants.AGENT_INDEX_KEY.concat(SecurityUtil.getUserId()), json);
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<Object> updateAgentAttribute(ConnectAgentLatitudePropertyAttributeVo agentAttribute) {
        // 转换为json
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        String json = gson.toJson(agentAttribute);
        RedisCacheUtil.setCacheObject(Constants.AGENT_ATTRIBUTE_KEY.concat(SecurityUtil.getUserId()), json);
        return AjaxResult.ok();
    }

    @Override
    public void exportQueueReal(List<ConnectExportAttributeVO> connectExportAttribute) {
        // 查询出数据
        List<ConnectQueueLatitudeAgentVo> connectQueueLatitudeAgentVoList = publicQueryQueueRealTimeIndex(null);
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        publicExport(firstRow, rowList, connectQueueLatitudeAgentVoList, connectExportAttribute);
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, "座席状态列表");
        } catch (IOException e) {
            log.error("导出队列纬度座席状态列表excel出现异常", e);
        }
    }

    @Override
    public void exportQueueContacts(List<ConnectExportAttributeVO> connectExportAttribute) {
        // 查询队列联系人数据
        List<ConnectQueueLatitudeContactsVo> contactsVos = publicQueryQueueContactsIndex();
        if(contactsVos != null){
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();
            publicExport(firstRow, rowList, contactsVos, connectExportAttribute);
            try {
                // 导出excel逻辑
                exportWithResponse(firstRow, rowList, "座席状态列表");
            } catch (IOException e) {
                log.error("导出队列纬度联系人列表指标excel出现异常", e);
            }
        }
    }

    @Override
    public void exportQueueProperty(List<ConnectExportAttributeVO> connectExportAttribute) {
        List<ConnectQueueLatitudePropertyVo> propertyList = publicQueryQueuePropertyIndex(null);
        if(propertyList !=null){
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();
            publicExport(firstRow, rowList, propertyList, connectExportAttribute);
            try {
                // 导出excel逻辑
                exportWithResponse(firstRow, rowList, "座席状态列表");
            } catch (IOException e) {
                log.error("导出队列纬度性能列表指标excel出现异常", e);
            }
        }
    }

    @Override
    public void exportAgentIndex(List<ConnectExportAttributeVO> connectExportAttribute) {
        List<ConnectAgentLatitudeAgentVo> agentList = publicQueryAgentIndex();
        if(agentList != null){
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();
            publicExport(firstRow, rowList, agentList, connectExportAttribute);
            try {
                // 导出excel逻辑
                exportWithResponse(firstRow, rowList, "座席状态列表");
            } catch (IOException e) {
                log.error("导出座席纬度座席员列表指标excel出现异常", e);
            }
        }
    }

    @Override
    public void exportAgentContacts(List<ConnectExportAttributeVO> connectExportAttribute) {
        List<ConnectAgentLatitudeContactsVo> contactsList = publicQueryAgentContactsIndex();
        if(contactsList != null){
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();
            publicExport(firstRow, rowList, contactsList, connectExportAttribute);
            try {
                // 导出excel逻辑
                exportWithResponse(firstRow, rowList, "座席状态列表");
            } catch (IOException e) {
                log.error("导出座席纬度联系人列表指标excel出现异常", e);
            }
        }
    }

    @Override
    public void exportAgentProperty(List<ConnectExportAttributeVO> connectExportAttribute) {
        List<ConnectAgentLatitudePropertyVo> propertyList = publicQueryAgentPropertyIndex();
        if(propertyList != null){
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();
            publicExport(firstRow, rowList, propertyList, connectExportAttribute);
            try {
                // 导出excel逻辑
                exportWithResponse(firstRow, rowList, "座席状态列表");
            } catch (IOException e) {
                log.error("导出座席纬度性能列表指标excel出现异常", e);
            }
        }
    }

    private <T> void publicExport(List<String> firstRow, List<List<String>> rowList, List<T> list, List<ConnectExportAttributeVO> connectExportAttribute){
        // 导出到excel中
        // 存储表头的code
        List<String> firstCodeRow = connectExportAttribute.stream().map(ConnectExportAttributeVO::getValue).collect(Collectors.toList());

        // 循环表头数据，存储list中
        for(ConnectExportAttributeVO exportAttribute :connectExportAttribute){
            firstRow.add(exportAttribute.getLabel());
        }
        if(CollectionUtils.isNotEmpty(list)){
            // 存储行数据
            list.forEach(vo -> {
                List<String> row = new ArrayList<>();
                firstCodeRow.forEach(title -> {
                    try {
                        Field field = vo.getClass().getDeclaredField(title);
                        // 如果没有对应title的 field  会抛出异常 NoSuchFieldException
                        field.setAccessible(true);
                        try {
                            String value = null;
                            if (field.get(vo) != null) {
                                if("java.time.LocalDateTime".equals(field.get(vo).getClass().getName())){
                                    String format = ((LocalDateTime) field.get(vo)).format(DateTimeFormatter.ofPattern(Constants.DATE_TIME_FORMATTER));
                                    value = TimeZoneUtils.responseTimeConversion(format);
                                }else {
                                    value = field.get(vo).toString();
                                }
                                row.add(value);
                            }
                        } catch (IllegalAccessException e) {
                            row.add(null);
                            log.error("获取字段 {} 的值出现异常", field.getName(), e);
                        }

                    } catch (NoSuchFieldException e){
                        log.info("vo对象中没有这个字段属性，去到扩展属性中继续查找");
                    }

                });
                rowList.add(row);
            });
        }
    }
    /**
     * 导出excel
     * @param firstRow  表头字段信息
     * @param rowList   导出具体行数据
     * @param exportName   excel名
     * @throws IOException
     */
    private void exportWithResponse(List<String> firstRow, List<List<String>> rowList, String exportName) throws IOException {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(firstRow)) {
            return;
        }
        String[] firstRowArray = firstRow.toArray(new String[firstRow.size()]);
        Workbook wb = writeToExcelByList(firstRowArray, rowList);

        String fileName = URLEncoder.encode(exportName, "UTF-8");
        ServletUtil.getResponse().setContentType("application/vnd.ms-excel;charset=utf-8");
        ServletUtil.getResponse().setHeader("Content-Disposition","attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream outputStream= ServletUtil.getResponse().getOutputStream();
        wb.write(outputStream);
        outputStream.close();
    }
    /**
     *  @Description: array 表头数据 list 具体数据
     */
    private Workbook writeToExcelByList(String[] array, List<List<String>> list) {
        //创建工作薄
        Workbook wb = new XSSFWorkbook();

        //设置列名样式
        CellStyle headerStyle = wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.LEFT);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);//上下居中
        headerStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());//设置背景色
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);//必须设置 否则无效
        setCellBorderStyle(headerStyle);
        Font headerFont = wb.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setFontName("微软雅黑");
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        //创建sheet
        Sheet sheet = wb.createSheet(MessageUtils.get("work.record.file.name"));

        //在sheet中添加表头，由于不涉及到标题，所以表头行数从0开始
        Row row = sheet.createRow((int) 0);
        for (int i = 0; i < array.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(array[i]);
            cell.setCellStyle(headerStyle);
        }

        //数据样式
        CellStyle dataStyle = wb.createCellStyle();
        //设置居中样式，水平居中
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font dataFont = wb.createFont();
        dataFont.setFontHeightInPoints((short) 12);
        dataFont.setFontName("微软雅黑");
        dataStyle.setFont(dataFont);

        //数据填充
        try {
            int index = 1;
            for (List value : list) {
                row = sheet.createRow(index);
                index++;
                List data = value;
                for (int j = 0; j < data.size(); j++) {
                    Cell cell = row.createCell(j);
                    // 为当前列赋值
                    if(data.get(j)!=null){
                        cell.setCellValue(data.get(j).toString());
                    }else{
                        cell.setCellValue("");
                    }
                    //设置数据的样式
                    cell.setCellStyle(dataStyle);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 宽度自适应的问题，必须在单元格设值以后进行
        for (int k = 0; k < array.length; k++) {
            sheet.autoSizeColumn(k);
        }
        return wb;
    }
    private void setCellBorderStyle(CellStyle cellStyle) {
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN); //解决填充背景色没有边框问题
        cellStyle.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
    }

    private void queueIndicatorMatching(ConnectIndex connectIndex, ConnectQueueLatitudePropertyVo property){
        switch (connectIndex.getIndexName()) {
            case "oldestContactAge":
                property.setOldestContactAge(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsScheduled":
                property.setContactsScheduled(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsQueuedByEnqueue":
                property.setContactsQueued(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsHandled":
                property.setContactsHandled(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandoned":
                property.setSumContactsAbandoned(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX15":
                property.setSumContactsAbandonedInX15(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX20":
                property.setSumContactsAbandonedInX20(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX25":
                property.setSumContactsAbandonedInX25(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX30":
                property.setSumContactsAbandonedInX30(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX45":
                property.setSumContactsAbandonedInX45(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX60":
                property.setSumContactsAbandonedInX60(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX90":
                property.setSumContactsAbandonedInX90(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX120":
                property.setSumContactsAbandonedInX120(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX180":
                property.setSumContactsAbandonedInX180(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX240":
                property.setSumContactsAbandonedInX240(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX300":
                property.setSumContactsAbandonedInX300(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAbandonedInX600":
                property.setSumContactsAbandonedInX600(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX15":
                property.setSumContactsAnsweredInX15(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX20":
                property.setSumContactsAnsweredInX20(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX25":
                property.setSumContactsAnsweredInX25(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX30":
                property.setSumContactsAnsweredInX30(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX45":
                property.setSumContactsAnsweredInX45(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX60":
                property.setSumContactsAnsweredInX60(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX90":
                property.setSumContactsAnsweredInX90(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX120":
                property.setSumContactsAnsweredInX120(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX180":
                property.setSumContactsAnsweredInX180(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX240":
                property.setSumContactsAnsweredInX240(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX300":
                property.setSumContactsAnsweredInX300(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumContactsAnsweredInX600":
                property.setSumContactsAnsweredInX600(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "outboundNumber":
                property.setOutboundNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsHandledNumber":
                property.setContactsHandledNumber(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "retryCallbackAttempts":
                property.setRetryCallbackAttempts(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "queueTransferOut":
                property.setQueueTransferOut(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgQueueAnswerTime":
                property.setAvgQueueAnswerTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgAbandonTime":
                property.setAvgAbandonTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgActiveTime":
                property.setAvgActiveTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgInteractionAndHoldTime":
                property.setAvgInteractionAndHoldTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgInteractionTime":
                property.setAvgInteractionTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgHandleTime":
                property.setAvgHandleTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsInQueue":
                property.setContactsInQueue(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsTransferredOut":
                property.setContactsTransferredOut(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "maxQueuedTime":
                property.setMaxQueuedTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "agentNonResponse":
                property.setAgentNonResponse(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsTransferredOutAgent":
                property.setContactsTransferredOutAgent(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgAgentPauseTime":
                property.setAvgAgentPauseTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumConnectingTimeAgent":
                property.setSumConnectingTimeAgent(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "agentNonResponseWithoutCustomerAbandons":
                property.setAgentNonResponseWithoutCustomerAbandons(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            default:
                break;
        }
    }

    private void agentIndicatorMatching(ConnectIndex connectIndex, ConnectAgentLatitudePropertyVo property){
        switch (connectIndex.getIndexName()) {
            case "avgCaseRelatedContacts":
                property.setAvgCaseRelatedContacts(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsHandled":
                property.setContactsHandled(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumRetryCallbackAttempts":
                property.setSumRetryCallbackAttempts(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgInteractionAndHoldTime":
                property.setAvgInteractionAndHoldTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgActiveTime":
                property.setAvgActiveTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgInteractionTime":
                property.setAvgInteractionTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "contactsTransferredOutFromQueue":
                property.setContactsTransferredOutFromQueue(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "agentNonResponse":
                property.setAgentNonResponse(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgAgentPauseTime":
                property.setAvgAgentPauseTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "sumConnectingTimeAgent":
                property.setSumConnectingTimeAgent(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "retryCallbackAttempts":
                property.setRetryCallbackAttempts(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "queueTransferOut":
                property.setQueueTransferOut(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            case "avgQueueAnswerTime":
                property.setAvgQueueAnswerTime(Optional.ofNullable(connectIndex.getIndexValue()).map(Double::intValue).orElse(0));
                break;
            default:
                break;
        }
    }
}
