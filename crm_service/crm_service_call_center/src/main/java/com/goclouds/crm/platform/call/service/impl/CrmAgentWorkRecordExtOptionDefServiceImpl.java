package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExtOptionDef;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordExtOptionDefMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordExtOptionDefService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_ext_option_def(工单属性自定义选项定义表)】的数据库操作Service实现
* @createDate 2023-09-20 15:52:31
*/
@Service
public class CrmAgentWorkRecordExtOptionDefServiceImpl extends ServiceImpl<CrmAgentWorkRecordExtOptionDefMapper, CrmAgentWorkRecordExtOptionDef>
    implements CrmAgentWorkRecordExtOptionDefService {

}




