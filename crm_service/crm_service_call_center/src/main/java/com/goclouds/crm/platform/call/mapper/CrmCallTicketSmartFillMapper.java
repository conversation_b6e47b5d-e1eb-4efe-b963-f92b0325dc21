package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.goclouds.crm.platform.call.domain.CrmCallTicketSmartFill;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goclouds.crm.platform.call.domain.smartFill.TicketSmartFillVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_call_ticket_smart_fill(智能填单表)】的数据库操作Mapper
* @createDate 2025-04-14 13:57:40
* @Entity com.goclouds.crm.platform.call.domain.CrmCallTicketSmartFill
*/
public interface CrmCallTicketSmartFillMapper extends BaseMapper<CrmCallTicketSmartFill> {

    IPage<TicketSmartFillVo> selectPageWithDetails(Page<TicketSmartFillVo> page,
                                                   @Param("companyId") String companyId);

    TicketSmartFillVo selectTicketSmartFillDetail( @Param("companyId") String companyId,
                                                   @Param("smartFillId") String smartFillId);

    /**
     * 新增操作——检查是否存在相同的 languageCode 和包含的 ticketTypeCode
     * @return 存在返回 true，否则返回 false
     */
    @Select({
            "<script>",
            "SELECT COUNT(*) > 0 FROM crm_call_ticket_smart_fill",
            "WHERE data_status=1 and language_code = #{languageCode} and company_id=#{companyId}",
            "AND (",
            "<foreach item='code' collection='ticketTypeCodeList' separator=' OR '>",
            "FIND_IN_SET(#{code}, ticket_type_code) > 0",
            "</foreach>",
            ")",
            "</script>"
    })
    boolean existsByLanguageAndTicketTypes(
            @Param("languageCode") String languageCode,
            @Param("ticketTypeCodeList") List<String> ticketTypeCodeList,
            String companyId);

    /**
     * 更新操作——检查是否存在相同的 languageCode 和包含的 ticketTypeCode
     * @return 存在返回 true，否则返回 false
     */
    @Select({
            "<script>",
            "SELECT COUNT(*) > 0 FROM crm_call_ticket_smart_fill",
            "WHERE  data_status=1 and smart_fill_id != #{smartFillId} and company_id=#{companyId} ",
            "AND language_code = #{languageCode} ",
            "AND (",
            "<foreach item='code' collection='ticketTypeCodeList' separator=' OR '>",
            "FIND_IN_SET(#{code}, ticket_type_code) > 0",
            "</foreach>",
            ")",
            "</script>"
    })
    boolean existsByLanguageAndTicketTypesExcludeOwn(
            @Param("smartFillId") String smartFillId,
            @Param("languageCode") String languageCode,
            @Param("ticketTypeCodeList") List<String> ticketTypeCodeList,
            String companyId);
}




