package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExtDef;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderExtOptionDefVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_ext_def(工单属性定义表;)】的数据库操作Mapper
* @createDate 2023-09-20 15:52:31
* @Entity generator.domain.CrmAgentWorkRecordExtDef
*/
public interface CrmAgentWorkRecordExtDefMapper extends BaseMapper<CrmAgentWorkRecordExtDef> {

    @Select("select t1.work_record_ext_def_id, t1.work_record_ext_def_code, t1.work_record_ext_def_name," +
            "                   t1.property_type_id, t1.is_required, t1.work_record_ext_def_order, t1.prompt, t1.options_type, t1.interface_url,t1.is_system_default," +
            "                   t2.option_id, t2.option_name, t2.option_value, t2.option_order  " +
            "            from crm_agent_work_record_ext_def t1" +
            "            left join crm_agent_work_record_ext_option_def t2 on t1.work_record_ext_def_id = t2.work_record_ext_def_id" +
            "    ${ew.customSqlSegment}")
    List<WorkOrderExtOptionDefVo> queryWorkOrderExtList(@Param(Constants.WRAPPER) QueryWrapper<String> eq);

    void deleteBatchExtDefByIdList(String companyId, List<String> list,Integer isSystemDefault);
}




