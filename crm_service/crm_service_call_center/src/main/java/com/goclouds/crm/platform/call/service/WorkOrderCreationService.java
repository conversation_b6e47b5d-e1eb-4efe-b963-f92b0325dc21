package com.goclouds.crm.platform.call.service;

import com.goclouds.crm.platform.call.domain.vo.CreateWorkOrderRecordVO;
import com.goclouds.crm.platform.call.domain.vo.MessageRecallVO;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordCreateDTO;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordReplyDTO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 创建工单service
 * @createDate 2024-01-17 14:24:35
 */
public interface WorkOrderCreationService {
    /**
     * 创建工单
     * @param createWorkOrderRecordVO 创建工单参数
     * @return 操作状态
     */
    AjaxResult<Object> createWork(CreateWorkOrderRecordVO createWorkOrderRecordVO);

    /**
     *  工单内容消息
     * @param workRecordReplyDTO 创建内容参数
     * @return 操作状态
     */
    AjaxResult<Object> replyMessage(WorkRecordReplyDTO workRecordReplyDTO);




    /**
     * 自动回复参数
     * @param workRecordReplyDTO 创建内容参数
     * @param contactId contactId
     * @param connectId connectId
     * @return
     */
    AjaxResult<Object> autoReplyMessage(WorkRecordReplyDTO workRecordReplyDTO,String contactId, String connectId);

    /**
     *  自动创建工单
     * @param workRecordRemarkDTO 创建工单参数
     * @return 操作状态
     */
    AjaxResult<Object> autoCreateWork(WorkRecordCreateDTO workRecordRemarkDTO);

    /**
     *  返回客户上一次接待座席以及新创建的工单ID（粘性座席工单）
     * @param workRecordRemarkDTO 工单请求参数
     * @return 座席id 工单id
     */
    AjaxResult<Object> adhesiveAgentWork(WorkRecordCreateDTO workRecordRemarkDTO);


    /**
     *  必定创建工单
     * @param workRecordRemarkDTO 创建工单请求参数
     * @return 创建工单状态
     */
    AjaxResult<Object> mustCreateWork(WorkRecordCreateDTO workRecordRemarkDTO);

    /**
     *  根据工单类型以及部门id查询出工单最少的座席
     * @param workRecordRemarkDTO 查询条件
     * @return 座席id
     */
    AjaxResult<Object> ticketLeastAgent(WorkRecordCreateDTO workRecordRemarkDTO) throws IOException;

    AjaxResult updateReplyTicket(WorkRecordReplyDTO workRecordReplyDTO);

    /**
     * 消息撤回
     * @param recallVO 撤回消息
     * @return
     */
    AjaxResult messageRecall(MessageRecallVO recallVO);
}
