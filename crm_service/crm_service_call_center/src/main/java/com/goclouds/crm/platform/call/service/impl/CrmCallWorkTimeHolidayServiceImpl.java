package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmCallWorkTimeHoliday;
import com.goclouds.crm.platform.call.mapper.CrmCallWorkTimeHolidayMapper;
import com.goclouds.crm.platform.call.service.CrmCallWorkTimeHolidayService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_call_work_time_holiday(假期时间配置表)】的数据库操作Service实现
* @createDate 2025-02-07 16:58:25
*/
@Service
public class CrmCallWorkTimeHolidayServiceImpl extends ServiceImpl<CrmCallWorkTimeHolidayMapper, CrmCallWorkTimeHoliday>
    implements CrmCallWorkTimeHolidayService{

}




