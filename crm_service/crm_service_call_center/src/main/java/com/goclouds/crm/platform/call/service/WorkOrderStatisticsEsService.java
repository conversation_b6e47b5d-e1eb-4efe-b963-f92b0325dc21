package com.goclouds.crm.platform.call.service;

import com.goclouds.crm.platform.call.domain.vo.statis.SeatingWorkloadNewVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;

public interface WorkOrderStatisticsEsService {
    AjaxResult queryAvgHandleTime(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryWorkOrderIncrement(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult querySla(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryCustomerWorkRecordTop(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryAgentSatisfaction(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryWorkOrderTypeSatisfaction(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryChannelSatisfaction(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryHandleWorkOrderSum(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryWorkOrderGroupByChannel(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;;

    AjaxResult queryWorkOrderStatus(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryWorkOrderTypeDistribution(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryWorkOrderLevelDistribution(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryHandleTimeDistribution(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryRobotChannelWorkOrder(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;;

    AjaxResult queryWorkOrderIncrementPercent(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryHandleWorkOrderNumTrend(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryAgentSatisfactionTrend(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryHandleWorkOrderTimeTrend(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    AjaxResult queryWorkOrderFeedBack(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception;

    void exportWorkOrderStatus(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportHandleTimeDistribution(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportSla(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportWorkOrderIncrement(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportRobotChannelWorkOrder(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportRobotWorkOrderPercent(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportHandleWorkOrderNumTrend(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportAgentSatisfactionTrend(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportHandleWorkOrderTimeTrend(SeatingWorkloadNewVo seatingWorkloadVo);

    void exportWorkOrderFeedBack(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryHandleWorkOrderSumExport(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryWorkOrderGroupByChannelExport(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryWorkOrderTypeDistributionExport(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryWorkOrderLevelDistributionExport(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryAvgHandleTimeExport(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryCustomerWorkRecordTopExport(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryAgentSatisfactionExport(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryWorkOrderTypeSatisfactionExport(SeatingWorkloadNewVo seatingWorkloadVo);

    void queryChannelSatisfactionExport(SeatingWorkloadNewVo seatingWorkloadVo);

}
