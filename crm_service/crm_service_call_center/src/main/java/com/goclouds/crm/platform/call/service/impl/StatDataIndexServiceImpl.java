package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordTypeDef;
import com.goclouds.crm.platform.call.domain.es.TicketExt;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.statis.KinesisContactDetailsVo;
import com.goclouds.crm.platform.call.domain.vo.statis.KinesisContactVo;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordExtDefService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordTypeDefService;
import com.goclouds.crm.platform.call.service.StatDataIndexService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.constant.RabbitMqConstants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.utils.DateUtils;
import com.goclouds.crm.platform.common.utils.RedisCacheUtil;
import com.goclouds.crm.platform.common.utils.ServletUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.channel.ConnectsServiceClient;
import com.goclouds.crm.platform.openfeignClient.client.platform.ConnectClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.channel.ConnectInfoVo;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.SearchContactsParam;
import com.goclouds.crm.platform.utils.*;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.Filter;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.Avg;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.goclouds.crm.platform.common.constant.Constants.CONTACT_DETAILS_USER_TABLE_HEADER;
import static com.goclouds.crm.platform.utils.SecurityUtil.getUserId;


/**
 * @Description:
 * @Author: zhumengyang
 * @Date: 2024/07/03/11:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StatDataIndexServiceImpl implements StatDataIndexService {


    private final ConnectClient connectClient;
    private final ConnectsServiceClient connectsServiceClient;
    private final RestHighLevelClient restHighLevelClient;
    private final CrmAgentWorkRecordService crmAgentWorkRecordService;
    private final CrmAgentWorkRecordExtDefService crmAgentWorkRecordExtDefService;
    private final CrmAgentWorkRecordTypeDefService crmAgentWorkRecordTypeDefService;
    private final RabbitTemplate rabbitTemplate;


    /**
     * 数据明细索、座席指标、队列指标索引
     */
    @Value("${es.data-detail-index}")
    private String dataDetailsIndex;

    @Value("${es.agent-index}")
    private String agentIndex;

    @Value("${es.queue-index}")
    private String queueIndex;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    /**
     * 统计-数据明细查询
     * @return 数据明细页
     */
    @Override
    public AjaxResult<IPage<StatDataDetailsVO>> queryStatDataDetails(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean isPage) throws Exception {

        //筛选条件
        String startDate = statIndexDTO.getStartDate();
        String endDate = statIndexDTO.getEndDate();
        String multiFieldQuery = statIndexDTO.getMultiFieldQuery();
        String queryChannelType = statIndexDTO.getQueryChannelType();
        String connectAlias = statIndexDTO.getConnectAlias();
        // 索引不存在则直接返回空
        if (!headIndexExists(dataDetailsIndex)) {
            return AjaxResult.ok();
        }

        //创建查询
        SearchRequest request = new SearchRequest(dataDetailsIndex);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .sort(new FieldSortBuilder("call_time").order(SortOrder.DESC))
                .trackTotalHits(true);

        //多条件查询 and(or/or/or)and
        BoolQueryBuilder boolQueryOut = QueryBuilders.boolQuery();
        //boolQueryOut.must(QueryBuilders.matchQuery("company_id", "d6ff59a027861d7d4133e1d6b6872464"));
        boolQueryOut.must(QueryBuilders.matchQuery("company_id", SecurityUtil.getLoginUser().getCompanyId()));
        //日期上下限
        if (StringUtil.isNotNull(endDate) && StringUtil.isNotEmpty(endDate)
                && StringUtil.isNotNull(startDate) && StringUtil.isNotEmpty(startDate)){
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //日期查询
            boolQueryOut.must(QueryBuilders.rangeQuery("call_time")
                    .gte(dateFormat.parse(startDate))  // 开始时间，以毫秒数表示
                    .lte(dateFormat.parse(endDate)));   // 结束时间，以毫秒数表示
        }
        else{
            // 日期查询：默认本月
            // 获取当前时间
            Date currentDate = new Date();
            // 创建 Calendar 对象并设置为当前时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            // 设置为本月的第一天
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date firstDayOfMonth = calendar.getTime();
            // 获取当前时间的最后一天
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date lastDayOfMonth = calendar.getTime();

            boolQueryOut.must(QueryBuilders.rangeQuery("call_time")
                    .gte(firstDayOfMonth.getTime())  // 开始时间，以毫秒数表示
                    .lte(lastDayOfMonth.getTime()));   // 结束时间，以毫秒数表示
        }
        //四个字段模糊查询
        if (StringUtil.isNotNull(multiFieldQuery) && StringUtil.isNotEmpty(multiFieldQuery)){
            BoolQueryBuilder boolQueryIn = QueryBuilders.boolQuery();
            //忽略大小写，模糊查询
            boolQueryOut.must(boolQueryIn.should(QueryBuilders.wildcardQuery("contact_id", "*"+multiFieldQuery+"*").caseInsensitive(true))
                    .should(QueryBuilders.wildcardQuery("reception_agent", "*"+multiFieldQuery+"*").caseInsensitive(true))
                    .should(QueryBuilders.wildcardQuery("agent_group", "*"+multiFieldQuery+"*").caseInsensitive(true))
                    .should(QueryBuilders.wildcardQuery("queue_name", "*"+multiFieldQuery+"*").caseInsensitive(true)));
        }
        //呼入渠道查询
        if (StringUtil.isNotNull(queryChannelType) && StringUtil.isNotEmpty(queryChannelType)){
            boolQueryOut.must(QueryBuilders.matchQuery("call_channel", queryChannelType));
        }
        //联络线路查询
        if (StringUtil.isNotNull(connectAlias) && StringUtil.isNotEmpty(connectAlias)){
            boolQueryOut.must(QueryBuilders.matchQuery("connect_alias", connectAlias));
        }

        //返回值return AjaxResult.ok(iPage);
        IPage<StatDataDetailsVO> iPage = new Page<>();
        //数据容器
        List<StatDataDetailsVO> statDataDetailsVOs = new ArrayList<>();
        //@param isPage=true 分页
        if(isPage){
            //计算当前页的起始下标
            long start = (pageParam.getCurrent() - 1) * pageParam.getSize();
            searchSourceBuilder.from((int) start)
                    .size((int) pageParam.getSize());

            searchSourceBuilder.query(boolQueryOut);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            long total = response.getHits().getTotalHits().value;
            //装入数据
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSourceAsMap();
                StatDataDetailsVO statDataDetailsVO = mapToStatDataDetailsVO(source);
                statDataDetailsVOs.add(statDataDetailsVO);
            }
            //装入数据
            iPage.setRecords(statDataDetailsVOs);
            iPage.setTotal(total);

        }//@param isPage=false  滚动查询导出excel
        else{
            searchSourceBuilder.query(boolQueryOut);
            //滚动查询批次大小
            searchSourceBuilder.size(1000);
            //查询条件应用到请求中
            request.source(searchSourceBuilder);
            //滚动超时
            request.scroll(TimeValue.timeValueMinutes(1L));
            // 进行滚动查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            //标识当前滚动上下文的唯一标识符，滚动ID
            String scrollId = response.getScrollId();
            do {
                SearchHits hits = response.getHits();
                //装入数据
                for (SearchHit hit : hits) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    StatDataDetailsVO statDataDetailsVO = mapToStatDataDetailsVO(source);
                    statDataDetailsVOs.add(statDataDetailsVO);
                }
                //继续从上次停下的位置开始检索数据
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                //新的滚动超时时间
                scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
                //新的滚动 ID 和当前批次的结果
                response = restHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                //更新ID
                scrollId = response.getScrollId();
            } while (response.getHits().getHits().length > 0);

            //数据装入
            iPage.setRecords(statDataDetailsVOs);

            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            //滚动 ID 添加到清理请求
            clearScrollRequest.addScrollId(scrollId);
            //清理滚动上下文
            restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

        }
        return AjaxResult.ok(iPage);
    }

    /**
     * 统计-数据明细-excel导出
     * @return 数据明细导出
     */
    @Override
    public void exportDataDetails(StatIndexDTO statIndexDTO, boolean isPage){

        //获取数据
        IPage<Object> page = new Page<>(1, 0);
        List<KinesisContactVo> dataDetailsList = null;
        try {
            // 获取分页数据列表
            AjaxResult<IPage<KinesisContactVo>> dataDetails = queryStatDataDetailsNew(page, statIndexDTO, isPage, 2);
            dataDetailsList = dataDetails.getData().getRecords();
        } catch (Exception e) {
            log.error("导出联络(数据)明细excel错误statIndex/export,{0}",e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();

        // 表头国际化
        //联系ID
        firstRow.add(MessageUtils.get("contact.id"));
        //联络线路
        firstRow.add(MessageUtils.get("connect.alias"));
        //开始时间
        firstRow.add(MessageUtils.get("start.time"));
        //座席应答时间
        firstRow.add(MessageUtils.get("call.time"));
        //挂断时间
        firstRow.add(MessageUtils.get("end.time"));
        //ACW结束时间
        firstRow.add(MessageUtils.get("acw.time"));
        //通话总时长
        firstRow.add(MessageUtils.get("total.time"));
        //互动时间
        firstRow.add(MessageUtils.get("interaction.time"));
        //队列等待时间
        firstRow.add(MessageUtils.get("queue.wait.time"));
        //ACW时长
        firstRow.add(MessageUtils.get("acw.duration"));
        //渠道
        firstRow.add(MessageUtils.get("call.channel"));
        //接待座席
        firstRow.add(MessageUtils.get("reception.agent"));
        //座席组
        firstRow.add(MessageUtils.get("agent.group"));
        //队列
        firstRow.add(MessageUtils.get("queue.name"));
        //呼入/呼出
        firstRow.add(MessageUtils.get("in.coming.out.coming"));
        //客户电话
        firstRow.add(MessageUtils.get("customer.phone2"));
        //系统电话
        firstRow.add(MessageUtils.get("system.phone"));
        //对应工单编号
        firstRow.add(MessageUtils.get("work.order.number"));
        //座席OnHold时间
        firstRow.add(MessageUtils.get("on.hold.time"));
        //座席OnHold次数
        firstRow.add(MessageUtils.get("on.hold.number"));
        //是否转接
        firstRow.add(MessageUtils.get("is.switch"));
        //挂断类型
        firstRow.add(MessageUtils.get("hanging.type"));
        //初始联络ID
        firstRow.add(MessageUtils.get("initial.contact.id"));
        //上一个联络ID
        firstRow.add(MessageUtils.get("previous.contact.id"));
        //下一个联络ID
        firstRow.add(MessageUtils.get("next.contact.id"));
        //满意度评分
        firstRow.add(MessageUtils.get("satisfaction.rating"));

        // 表头国际化
        Map<String, String> extDefMap = crmAgentWorkRecordExtDefService.getExtDefMap();
        // 如果Redis中记录了用户指定的表头 那么导出指定表头
        List<WorkOrderExtVo> props = RedisCacheUtil.getCacheList(CONTACT_DETAILS_USER_TABLE_HEADER + getUserId());
        List<String> firstCodeRow = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(props)){
            firstRow.addAll(props.stream()
                    .map(vo -> {
                        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(extDefMap) && StringUtil.isNotBlank(extDefMap.get(vo.getWorkRecordExtDefCode()))) {
                            return extDefMap.get(vo.getWorkRecordExtDefCode());
                        }
                        return vo.getWorkRecordExtDefName();
                    })
                    .collect(Collectors.toList()));
            firstCodeRow = props.stream().map(WorkOrderExtVo::getWorkRecordExtDefCode).collect(Collectors.toList());
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(dataDetailsList)) {
            for (KinesisContactVo vo : dataDetailsList) {
                List<String> row = new ArrayList<>();
                //联系ID
                row.add(vo.getContactId());
                //联络线路
                row.add(vo.getConnectAlias());
                //开始时间
                row.add(vo.getStartTime() != null ? dateFormat.format(vo.getStartTime()) : null + "");
                //座席应答时间
                row.add(vo.getCallTime() != null ? dateFormat.format(vo.getCallTime()) : null + "");
                //挂断时间
                row.add(vo.getEndTime() != null ? dateFormat.format(vo.getEndTime()) : null + "");
                //ACW结束时间
                row.add(vo.getAcwTime() != null ? dateFormat.format(vo.getAcwTime()) : null + "");
                //通话总时长
                row.add(vo.getTotalTime() + "");
                //互动时间
                row.add(vo.getInteractionTime() + "");
                //队列等待时间
                row.add(vo.getQueueWaitTime() + "");
                //ACW时长
                row.add(vo.getAcwDuration() + "");
                //渠道
                row.add(vo.getCallChannel());
                //接待座席
                row.add(vo.getReceptionAgent());
                //座席组
                row.add(vo.getAgentGroup());
                //队列
                row.add(vo.getQueueName());
                //呼入/呼出
                row.add(vo.getIncomingOutgoing());
                //客户电话
                row.add(vo.getCustomerPhone());
                //系统电话
                row.add(vo.getSystemPhone());
                //对应工单编号
                row.add(vo.getWorkOrderNumber());
                //座席OnHold时间
                row.add(vo.getOnHoldTime() + "");
                //座席OnHold次数
                row.add(vo.getOnHoldNumber() + "");
                //是否转接
                row.add(vo.getIsSwitch() + "");
                //挂断类型
                row.add(vo.getHangingType());
                //初始联络ID
                row.add(vo.getInitialContactId());
                //上一个联络ID
                row.add(vo.getPreviousContactId());
                //下一个联络ID
                row.add(vo.getNextContactId());
                //满意度评分
                row.add(vo.getSatisfactionRating());
                if(CollectionUtils.isNotEmpty(firstCodeRow)){
                    firstCodeRow.forEach(title -> {
                        try {
                            Field field = vo.getClass().getDeclaredField(title);
                            // 如果没有对应title的 field  会抛出异常 NoSuchFieldException
                            field.setAccessible(true);
                            try {
                                String value = null;
                                if(field.get(vo) != null){
                                    if("java.time.LocalDateTime".equals(field.get(vo).getClass().getName())){
                                        String format = ((LocalDateTime) field.get(vo)).format(DateTimeFormatter.ofPattern(Constants.DATE_TIME_FORMATTER));
                                        value = TimeZoneUtils.responseTimeConversion(format);
                                    }
                                    if("java.util.Date".equals(field.get(vo).getClass().getName())){
                                        value = dateFormat.format(field.get(vo));
                                    }else if("reminderStatus".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 0) {
                                                value = MessageUtils.get("work.reminder.no");
                                            } else if (intValue == 1) {
                                                value = MessageUtils.get("work.reminder.yes");
                                            }
                                        }
                                    }else if("status".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 0) {
                                                value = MessageUtils.get("work.status.undistributed");
                                            } else if (intValue == 1) {
                                                value = MessageUtils.get("work.status.agent");
                                            }else if (intValue == 2) {
                                                value = MessageUtils.get("work.status.custom");
                                            }else if (intValue == 3) {
                                                value = MessageUtils.get("work.status.solve");
                                            }else if (intValue == 4) {
                                                value = MessageUtils.get("work.status.termination");
                                            }else if (intValue == 5) {
                                                value = MessageUtils.get("work.status.transfer");
                                            }
                                        }
                                    } else if("acceptType".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 0) {
                                                value = MessageUtils.get("work.accept.type.artificial");
                                            } else if (intValue == 1) {
                                                value = MessageUtils.get("work.accept.type.claim");
                                            } else if (intValue == 2) {
                                                value = MessageUtils.get("work.accept.type.automatic");
                                            }
                                        }
                                    }else if("createType".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 0) {
                                                value = MessageUtils.get("work.create.automatic");
                                            } else if (intValue == 1) {
                                                value = MessageUtils.get("work.create.artificial");
                                            }
                                        }
                                    }else if("serviceStatus".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 1) {
                                                value = MessageUtils.get("work.beyond.unresolved");
                                            } else if (intValue == 2) {
                                                value = MessageUtils.get("work.status.unresolved");
                                            }else if (intValue == 3) {
                                                value = MessageUtils.get("work.timeout.resolution");
                                            }else if (intValue == 4) {
                                                value = MessageUtils.get("work.time.solve");
                                            }else if (intValue == 5) {
                                                value = MessageUtils.get("work.status.termination");
                                            }else if (intValue == 6) {
                                                value = MessageUtils.get("work.status.transfer");
                                            }
                                        }
                                    }else if("channelTypeName".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            String strValue = field.get(vo).toString();
                                            value = TransUtil.trans(strValue);
                                        }
                                    }else{

                                        value = field.get(vo).toString();
                                    }
                                }
                                row.add(value);

                            } catch (IllegalAccessException e) {
                                row.add(null);
                                log.error("获取字段 {} 的值出现异常", field.getName(), e);
                            }
                        } catch (NoSuchFieldException e) {
                            log.info("vo对象中没有这个字段属性，去到扩展属性中继续查找");

                            // 继续在对象的扩展属性中继续查找该title对应的属性值
                            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(vo.getWorkRecordResVoIPage())) {
                                boolean flag = false;
                                if(vo.getWorkRecordResVoIPage().get(title)!=null){
                                    row.add(vo.getWorkRecordResVoIPage().get(title).toString());
                                    flag = true;
                                }
                                // 在扩展字段中没有查询到 填充null
                                if (!flag) {
                                    row.add(null);
                                }
                            } else {
                                // 如果以上都没有  那么就填充null
                                row.add(null);
                            }
                        }
                    });
                }
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statIndex.export.work.record.filename1")+"_"+ DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出联络(数据)明细excel出现异常statIndex/export,", e);
        }
    }

    /**
     * 统计-座席工作指标-分页查询
     * @return 座席工作指标页
     */
    @Override
    public AjaxResult<IPage<StatAgentIndexVO>> queryStatAgentIndex(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean isPage) throws Exception {

        String startDate = statIndexDTO.getStartDate();
        String endDate = statIndexDTO.getEndDate();
        String multiFieldQuery = statIndexDTO.getMultiFieldQuery();
        String connectAlias = statIndexDTO.getConnectAlias();
        // 索引不存在则直接返回空
        if (!headIndexExists(agentIndex)) {
            return AjaxResult.ok();
        }

        //创建查询
        SearchRequest request = new SearchRequest(agentIndex);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .sort(new FieldSortBuilder("modify_time").order(SortOrder.DESC))
                .trackTotalHits(true);

        //@param isPage 是否分页，分页：查询用：不分页：导出excel用
        if(isPage){
            //计算当前页的起始下标
            long start = (pageParam.getCurrent() - 1) * pageParam.getSize();
            searchSourceBuilder.from((int) start)
                    .size((int) pageParam.getSize());
        }
        else{
            searchSourceBuilder.from(0).size(9999);  //返回所有文档
        }

        //多条件查询and
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        //boolQuery.must(QueryBuilders.matchQuery("company_id", "d6ff59a027861d7d4133e1d6b6872464"));
        boolQuery.must(QueryBuilders.matchQuery("company_id", SecurityUtil.getLoginUser().getCompanyId()));
        //日期上下限
        if (StringUtil.isNotNull(endDate) && StringUtil.isNotEmpty(endDate)
                && StringUtil.isNotNull(startDate) && StringUtil.isNotEmpty(startDate)){
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //日期查询
            boolQuery.must(QueryBuilders.rangeQuery("create_time")
                    .gte(dateFormat.parse(startDate).getTime()/ 1000)  // 开始时间，以秒数表示
                    .lte(dateFormat.parse(endDate).getTime()/ 1000));   // 结束时间，以秒数表示
        }
        else{
            // 日期查询：默认本月
            // 获取当前时间
            Date currentDate = new Date();
            // 创建 Calendar 对象并设置为当前时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            // 设置为本月的第一天
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date firstDayOfMonth = calendar.getTime();
            // 获取当前时间的最后一天
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date lastDayOfMonth = calendar.getTime();

            boolQuery.must(QueryBuilders.rangeQuery("create_time")
                    .gte(firstDayOfMonth.getTime()/ 1000)  // 开始时间，以秒数表示
                    .lte(lastDayOfMonth.getTime()/ 1000));   // 结束时间，以秒数表示
        }
        //座席名称模糊检索
        if (StringUtil.isNotNull(multiFieldQuery) && StringUtil.isNotEmpty(multiFieldQuery)){
            //boolQuery.must(QueryBuilders.wildcardQuery("agent_name", "*"+agentName+"*"));
            //忽略大小写，模糊查询 .caseInsensitive(true)
            boolQuery.must(QueryBuilders.wildcardQuery("agent_name", "*"+multiFieldQuery+"*").caseInsensitive(true));
        }
        //联络线路查询
        if (StringUtil.isNotNull(connectAlias) && StringUtil.isNotEmpty(connectAlias)){
            boolQuery.must(QueryBuilders.matchQuery("connect_alias", connectAlias));
        }
        searchSourceBuilder.query(boolQuery);

        //嵌套聚合查询
        // 分桶字段"agent_name"  分桶分组名称"group_by_agent_name"
        // 按座席名称类别分桶,查询各个累计时间、数量，以及各个平均时间
        searchSourceBuilder.aggregation(
                AggregationBuilders.terms("group_by_connect_alias").field("connect_alias")
                        .subAggregation(
                                AggregationBuilders.terms("group_by_agent_name").field("agent_name")
                                        .subAggregation(AggregationBuilders.sum("sum_accumulated_online_duration").field("accumulated_online_duration"))
                                        .subAggregation(AggregationBuilders.sum("sum_accumulated_idle_duration").field("accumulated_idle_duration"))
                                        .subAggregation(AggregationBuilders.sum("sum_accumulated_reception_duration").field("accumulated_reception_duration"))
                                        .subAggregation(AggregationBuilders.sum("sum_unresponsive_quantity").field("unresponsive_quantity"))
                                        .subAggregation(AggregationBuilders.sum("sum_reception_contacts_quantity").field("reception_contacts_quantity"))
                                        .subAggregation(AggregationBuilders.avg("average_working_hours_after_contact").field("avg_working_hours_after_contact"))
                                        .subAggregation(AggregationBuilders.avg("average_customer_retention_time").field("avg_customer_retention_time"))));

        //进行查询
        request.source(searchSourceBuilder);
        SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        //数据容器
        List<StatAgentIndexVO> statAgentIndexVOs = new ArrayList<>();
        IPage<StatAgentIndexVO> iPage = new Page<>();
        // 初始化总文档条数 计数器
        long total = 0;
        //第一层聚合
        Terms termsConnectAlias = response.getAggregations().get("group_by_connect_alias");
        for (Terms.Bucket bucket1 : termsConnectAlias.getBuckets()) {
            //联络线路
            String connectAliasString = bucket1.getKeyAsString();  //"connect_alias"字段
            //第二层聚合
            Terms termsAgentName = bucket1.getAggregations().get("group_by_agent_name");
            // 获取当前桶的文档数量
            long connectAliasDocCount = termsAgentName.getBuckets().size();
            // 累加到总文档计数器
            total += connectAliasDocCount;
            //解析嵌套聚合结果，并装入数据
            for (Terms.Bucket bucket2 : termsAgentName.getBuckets()) {
                Map<String, Aggregation> agentMap = bucket2.getAggregations().asMap();

                StatAgentIndexVO statAgentIndexVO = new StatAgentIndexVO();
                //statAgentIndexVO.setCompanyId("d6ff59a027861d7d4133e1d6b6872464");//SecurityUtil.getLoginUser().getCompanyId()
                //座席名(聚合字段)  getKey
                statAgentIndexVO.setAgentName(bucket2.getKeyAsString());//"agent_name"字段
                //实例别名 (新加字段)
                statAgentIndexVO.setConnectAlias(connectAliasString);
                //累计在线时长(求和字段)
                Sum sumOnline = (Sum) agentMap.get("sum_accumulated_online_duration");
                statAgentIndexVO.setAccumulatedOnlineDuration(secFormat((long) sumOnline.getValue()));  //时分秒
                //累计空闲时长(求和字段)
                statAgentIndexVO.setAccumulatedIdleDuration(secFormat((long) ((Sum) agentMap.get("sum_accumulated_idle_duration")).getValue()));//时分秒
                //累计接待时长(求和字段)
                Sum sumReception = (Sum) agentMap.get("sum_accumulated_reception_duration");
                statAgentIndexVO.setAccumulatedReceptionDuration(secFormat((long) (sumReception.getValue())));//时分秒
                //工时利用率 = 累计接待时长/累计在线时长 (计算字段)
                double fenMu1 = sumOnline.getValue();
                double fenZi = sumReception.getValue();
                statAgentIndexVO.setTaskTimeUtilizationRate(String.format("%.2f", (fenZi / fenMu1) * 100) + "%");
                //未响应数量(求和字段)
                Sum sumUnresponse = (Sum) agentMap.get("sum_unresponsive_quantity");
                statAgentIndexVO.setUnresponsiveQuantity((long) sumUnresponse.getValue());
                //接待联系人数量(求和字段)
                Sum sumContacts = (Sum) agentMap.get("sum_reception_contacts_quantity");
                statAgentIndexVO.setReceptionContactsQuantity((long) sumContacts.getValue());
                //应答率 = 接待联系人数量/（接待联系人数量+未响应数量）  (计算字段)
                double fenMu2 = sumContacts.getValue();
                double yinZi = sumUnresponse.getValue();
                statAgentIndexVO.setResponseRate(String.format("%.2f", (fenMu2 / (fenMu2 + yinZi)) * 100) + "%");
                //平均联系后续工作时间 (求平均字段)
                statAgentIndexVO.setAvgWorkingHoursAfterContact(secFormat((long) ((Avg) agentMap.get("average_working_hours_after_contact")).getValue()));//时分秒
                //平均客户保持时间 (求平均字段)
                statAgentIndexVO.setAvgCustomerRetentionTime(secFormat((long) ((Avg) agentMap.get("average_customer_retention_time")).getValue()));//时分秒
                statAgentIndexVOs.add(statAgentIndexVO);
            }
        }
        //返回页面
        iPage.setRecords(statAgentIndexVOs);
        //long total = response.getHits().getTotalHits().value;
        //嵌套聚合的总条数total
        iPage.setTotal(total);
        return AjaxResult.ok(iPage);
    }

    /**
     * 统计-座席指标-excel导出
     * @return 座席指标导出
     */
    @Override
    public void exportAgentIndex(StatIndexDTO statIndexDTO, boolean isPage) {

        //获取数据
        IPage<Object> page = new Page<>(1, 0);
        List<StatAgentIndexVO> agentIndexList = null;
        try {
            // 获取分页数据列表
            AjaxResult<IPage<StatAgentIndexVO>> agentIndex = queryStatAgentIndex(page, statIndexDTO, isPage);
            agentIndexList = agentIndex.getData().getRecords();
        } catch (Exception e) {
            log.error("导出座席历史工作指标excel错误statIndex/export,{0}",e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 表头国际化
        firstRow.add(MessageUtils.get("agent.name"));
        firstRow.add(MessageUtils.get("connect.alias"));
        firstRow.add(MessageUtils.get("accumulated.online.duration"));
        firstRow.add(MessageUtils.get("accumulated.idle.duration"));
        firstRow.add(MessageUtils.get("accumulated.reception.duration"));
        firstRow.add(MessageUtils.get("task.time.utilization.rate"));
        firstRow.add(MessageUtils.get("unresponsive.quantity"));
        firstRow.add(MessageUtils.get("reception.contacts.quantity"));
        firstRow.add(MessageUtils.get("response.rate"));
        firstRow.add(MessageUtils.get("avg.working.hours.after.contact"));
        firstRow.add(MessageUtils.get("avg.customer.retention.time"));

        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(agentIndexList)) {
            for (StatAgentIndexVO vo : agentIndexList) {
                List<String> row = new ArrayList<>();
                row.add(vo.getAgentName());
                row.add(vo.getConnectAlias());
                row.add(vo.getAccumulatedOnlineDuration());
                row.add(vo.getAccumulatedIdleDuration());
                row.add(vo.getAccumulatedReceptionDuration());
                row.add(vo.getTaskTimeUtilizationRate());
                row.add(String.valueOf(vo.getUnresponsiveQuantity()));
                row.add(String.valueOf(vo.getReceptionContactsQuantity()));
                row.add(vo.getResponseRate());
                row.add(vo.getAvgWorkingHoursAfterContact());
                row.add(vo.getAvgCustomerRetentionTime());
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statIndex.export.work.record.filename2")+"_"+ DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出座席历史工作指标excel出现异常statIndex/export,", e);
        }
    }

    /**
     * 统计-队列指标-分页查询
     * @return 队列指标页
     */
    @Override
    public AjaxResult<IPage<StatQueueIndexVO>> queryStatQueueIndex(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean isPage) throws Exception {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String index = "kinesis_contact_details_"+companyId;
        if (!headIndexExists(index)){
            return AjaxResult.ok();
        }
        List<StatQueueIndexVO> statQueueIndexVOs = new ArrayList<>();
        IPage<StatQueueIndexVO> iPage = new Page<>();
        SearchRequest request = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtil.isNotBlank(statIndexDTO.getMultiFieldQuery())){
            boolQuery.must(QueryBuilders.wildcardQuery("queueName", "*"+statIndexDTO.getMultiFieldQuery()+"*").caseInsensitive(true));
        }
        if (StringUtil.isNotBlank(statIndexDTO.getConnectAlias())){
            boolQuery.must(QueryBuilders.matchQuery("connectAlias", statIndexDTO.getConnectAlias()));
        }

        boolQuery.must(QueryBuilders.rangeQuery("disconnectTimestamp")
                    .gte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,statIndexDTO.getStartDate()))  // 开始时间，以秒数表示
                    .lte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,statIndexDTO.getEndDate())));

        searchSourceBuilder.size((int) pageParam.getSize());
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.aggregation(AggregationBuilders.terms("group_by_field1")
                        .field("queueArn") // 确保字段名与Elasticsearch中的字段名匹配，如果是text类型字段，则使用.keyword
                        .size((int) pageParam.getSize())
                        .subAggregation(AggregationBuilders.filter("value_field_exists", QueryBuilders.existsQuery("receptionAgent"))
                                .subAggregation(AggregationBuilders.avg("average_of_fieldA").field("acwDuration"))
                                .subAggregation(AggregationBuilders.avg("average_of_fieldB").field("onHoldTime"))
                                .subAggregation(AggregationBuilders.avg("average_of_fieldC").field("interactionTime"))
                                .subAggregation(AggregationBuilders.avg("average_of_fieldD").field("queueWaitTime"))
                        )
                        .subAggregation(AggregationBuilders.filter("value_field_not_exists", QueryBuilders.boolQuery()
                                        .mustNot(QueryBuilders.existsQuery("receptionAgent")))
                                .subAggregation(AggregationBuilders.avg("average_when_exists").field("queueWaitTime"))
                        )
        );
        request.source(searchSourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        SearchHit[] hits = searchResponse.getHits().getHits();
        System.out.println(searchResponse);
        Terms terms = searchResponse.getAggregations().get("group_by_field1");
        for (Terms.Bucket bucket : terms.getBuckets()) {
            System.out.println("Queue Name: " + bucket.getKeyAsString());
            StatQueueIndexVO vo = new StatQueueIndexVO();
            vo.setCompanyId(companyId);
            vo.setQueueName(getQueueName(hits,bucket.getKeyAsString()));
            vo.setConnectAlias(getConnectAlias(hits,bucket.getKeyAsString()));
            bucket.getDocCount();
            Filter exists = bucket.getAggregations().get("value_field_exists");
            vo.setQueuedContactsQuantity(exists.getDocCount());
            Avg averageOfFieldA = exists.getAggregations().get("average_of_fieldA");
            if (averageOfFieldA != null) {
                vo.setAvgWorkingHoursAfterContact(DateUtils.dataTimeInt((int) averageOfFieldA.getValue()));
            } else {
                vo.setAvgWorkingHoursAfterContact("00:00:00");
            }

            Avg averageOfFieldB = exists.getAggregations().get("average_of_fieldB");
            if (averageOfFieldB != null) {
                vo.setAvgCustomerRetentionTime(DateUtils.dataTimeInt((int) averageOfFieldB.getValue()));
            } else {
                vo.setAvgCustomerRetentionTime("00:00:00");
            }
            Avg averageOfFieldC = exists.getAggregations().get("average_of_fieldC");
            if (averageOfFieldC != null) {
                vo.setAvgAgentInteractionTime(DateUtils.dataTimeInt((int) averageOfFieldC.getValue()));
            } else {
                vo.setAvgAgentInteractionTime("00:00:00");
            }
            Avg averageOfFieldD = exists.getAggregations().get("average_of_fieldD");
            if (averageOfFieldD != null) {
                vo.setAvgQueueWaitingTime(DateUtils.dataTimeInt((int) averageOfFieldD.getValue()));
            } else {
                vo.setAvgQueueWaitingTime("00:00:00");
            }
            Filter notExists = bucket.getAggregations().get("value_field_not_exists");
            vo.setAbandonContactQuantity(notExists.getDocCount());
            Avg averageWhenExists = notExists.getAggregations().get("average_when_exists");
            if(notExists.getDocCount() > 0) {
                vo.setAvgQueueAbandonmentTime(DateUtils.dataTimeInt((int) averageWhenExists.getValue()));
            } else {
                vo.setAvgQueueAbandonmentTime("00:00:00");
            }
            statQueueIndexVOs.add(vo);
        }
        iPage.setRecords(statQueueIndexVOs);
        iPage.setTotal(terms.getBuckets().size());
        return AjaxResult.ok(iPage);
    }

    private String getConnectAlias(SearchHit[] hits, String keyAsString) {
        for (SearchHit searchHit: hits) {
            if (searchHit.getSourceAsMap().get("queueArn").toString().equals(keyAsString)){
                return searchHit.getSourceAsMap().get("connectAlias").toString();
            }
        }
        return "";
    }

    private String getQueueName(SearchHit[] hits, String keyAsString) {
        for (SearchHit searchHit: hits) {
            if (searchHit.getSourceAsMap().get("queueArn").toString().equals(keyAsString)){
                return searchHit.getSourceAsMap().get("queueName").toString();
            }
        }
        return "";
    }
//    @Override
//    public AjaxResult<IPage<StatQueueIndexVO>> queryStatQueueIndex(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean isPage) throws Exception {
//
//        String startDate = statIndexDTO.getStartDate();
//        String endDate = statIndexDTO.getEndDate();
//        String multiFieldQuery = statIndexDTO.getMultiFieldQuery();
//        String connectAlias = statIndexDTO.getConnectAlias();
//        if (!headIndexExists(queueIndex)){
//            return AjaxResult.ok();
//        }
//
//        //创建查询
//        SearchRequest request = new SearchRequest(queueIndex);
//        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
//                .sort(new FieldSortBuilder("modify_time").order(SortOrder.DESC))
//                .trackTotalHits(true);
//
//        //@param isPage 是否分页，分页：查询用：不分页：导出excel用
//        if(isPage){
//            //计算当前页的起始下标
//            long start = (pageParam.getCurrent() - 1) * pageParam.getSize();
//            searchSourceBuilder.from((int) start)
//                    .size((int) pageParam.getSize());
//        }
//        else{
//            searchSourceBuilder.from(0).size(9999);  //返回所有文档
//        }
//
//        //多条件查询and
//        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//        //boolQuery.must(QueryBuilders.matchQuery("company_id", "d6ff59a027861d7d4133e1d6b6872464"));
//        boolQuery.must(QueryBuilders.matchQuery("company_id", SecurityUtil.getLoginUser().getCompanyId()));
//        //日期上下限
//        if (StringUtil.isNotNull(endDate) && StringUtil.isNotEmpty(endDate)
//                && StringUtil.isNotNull(startDate) && StringUtil.isNotEmpty(startDate)){
//            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            //日期查询
//            boolQuery.must(QueryBuilders.rangeQuery("create_time")
//                    .gte(dateFormat.parse(startDate).getTime()/ 1000)  // 开始时间，以秒数表示
//                    .lte(dateFormat.parse(endDate).getTime()/ 1000));   // 结束时间，以秒数表示
//        }
//        else{
//            // 日期查询：默认本月
//            // 获取当前时间
//            Date currentDate = new Date();
//            // 创建 Calendar 对象并设置为当前时间
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(currentDate);
//            // 设置为本月的第一天
//            calendar.set(Calendar.DAY_OF_MONTH, 1);
//            Date firstDayOfMonth = calendar.getTime();
//            // 获取当前时间的最后一天
//            calendar.add(Calendar.MONTH, 1);
//            calendar.add(Calendar.DAY_OF_MONTH, -1);
//            Date lastDayOfMonth = calendar.getTime();
//
//            boolQuery.must(QueryBuilders.rangeQuery("create_time")
//                    .gte(firstDayOfMonth.getTime()/ 1000)  // 开始时间，以秒数表示
//                    .lte(lastDayOfMonth.getTime()/ 1000));   // 结束时间，以秒数表示
//        }
//        //队列名称模糊检索，忽略大小写
//        if (StringUtil.isNotNull(multiFieldQuery) && StringUtil.isNotEmpty(multiFieldQuery)){
//            boolQuery.must(QueryBuilders.wildcardQuery("queue_name", "*"+multiFieldQuery+"*").caseInsensitive(true));
//        }
//        //联络线路查询
//        if (StringUtil.isNotNull(connectAlias) && StringUtil.isNotEmpty(connectAlias)){
//            boolQuery.must(QueryBuilders.matchQuery("connect_alias", connectAlias));
//        }
//        searchSourceBuilder.query(boolQuery);
//
//        //聚合查询
//        // 分桶字段"queue_name"  分桶分组名称"group_by_queue_name"
//        // 按座席名称类别分桶,查询各个累计时间、数量，以及各个平均时间
//        searchSourceBuilder.aggregation(
//                AggregationBuilders.terms("group_by_connect_alias").field("connect_alias")
//                        .subAggregation(
//                                AggregationBuilders.terms("group_by_queue_name").field("queue_name")
//                                        .subAggregation(AggregationBuilders.avg("average_working_hours_after_contact").field("avg_working_hours_after_contact"))
//                                        .subAggregation(AggregationBuilders.avg("average_agent_interaction_time").field("avg_agent_interaction_time"))
//                                        .subAggregation(AggregationBuilders.avg("average_customer_retention_time").field("avg_customer_retention_time"))
//                                        .subAggregation(AggregationBuilders.avg("average_queue_abandonment_time").field("avg_queue_abandonment_time"))
//                                        .subAggregation(AggregationBuilders.avg("average_queue_waiting_time").field("avg_queue_waiting_time"))
//                                        .subAggregation(AggregationBuilders.sum("sum_abandon_contact_quantity").field("abandon_contact_quantity"))
//                                        .subAggregation(AggregationBuilders.sum("sum_queued_contacts_quantity").field("queued_contacts_quantity"))));
//
//        //进行查询
//        request.source(searchSourceBuilder);
//        //根据查询结果聚合
//        SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
//        //数据容器
//        List<StatQueueIndexVO> statQueueIndexVOs = new ArrayList<>();
//        IPage<StatQueueIndexVO> iPage = new Page<>();
//        // 初始化总文档条数 计数器
//        long total = 0;
//        //第一层聚合
//        Terms termsConnectAlias = response.getAggregations().get("group_by_connect_alias");
//        for (Terms.Bucket bucket1 : termsConnectAlias.getBuckets()) {
//            //联络线路
//            String connectAliasString = bucket1.getKeyAsString();  //"connect_alias"字段
//            //第二层聚合
//            Terms termsQueueName = bucket1.getAggregations().get("group_by_queue_name");
//            // 获取当前桶的文档数量
//            long connectAliasDocCount = termsQueueName.getBuckets().size();
//            // 累加到总文档计数器
//            total += connectAliasDocCount;
//            //解析聚合结果，并装入数据
//            for (Terms.Bucket bucket2 : termsQueueName.getBuckets()) {
//                Map<String, Aggregation> queueMap = bucket2.getAggregations().asMap();
//                //装入数据
//                StatQueueIndexVO statQueueIndexVO = new StatQueueIndexVO();
//                //statQueueIndexVO.setCompanyId("d6ff59a027861d7d4133e1d6b6872464"); //SecurityUtil.getLoginUser().getCompanyId()
//                statQueueIndexVO.setQueueName(bucket2.getKeyAsString());//"queue_name"AsString
//                statQueueIndexVO.setConnectAlias(connectAliasString);
//                statQueueIndexVO.setAvgWorkingHoursAfterContact(secFormat((long) (((Avg) queueMap.get("average_working_hours_after_contact")).getValue())));//时分秒
//                statQueueIndexVO.setAvgAgentInteractionTime(secFormat((long) (((Avg) queueMap.get("average_agent_interaction_time")).getValue())));//时分秒
//                statQueueIndexVO.setAvgCustomerRetentionTime(secFormat((long) (((Avg) queueMap.get("average_customer_retention_time")).getValue())));//时分秒
//                statQueueIndexVO.setAvgQueueAbandonmentTime(secFormat((long) (((Avg) queueMap.get("average_queue_abandonment_time")).getValue())));//时分秒
//                statQueueIndexVO.setAvgQueueWaitingTime(secFormat((long) (((Avg) queueMap.get("average_queue_waiting_time")).getValue())));//时分秒
//                statQueueIndexVO.setAbandonContactQuantity((long) ((Sum) queueMap.get("sum_abandon_contact_quantity")).getValue());
//                statQueueIndexVO.setQueuedContactsQuantity((long) ((Sum) queueMap.get("sum_queued_contacts_quantity")).getValue());
//                statQueueIndexVOs.add(statQueueIndexVO);
//            }
//        }
//        //返回页面
//        iPage.setRecords(statQueueIndexVOs);
//        //long total = response.getHits().getTotalHits().value;
//        //双层聚合下的文档总条数
//        iPage.setTotal(total);
//        return AjaxResult.ok(iPage);
//    }

    /**
     * 统计-队列指标-excel导出
     * @return 队列指标导出
     */
    @Override
    public void exportQueueIndex(StatIndexDTO statIndexDTO, boolean isPage) {

        //获取数据
        IPage<Object> page = new Page<>(1, 0);
        List<StatQueueIndexVO> queueIndexList = null;
        try {
            // 获取分页数据列表
            AjaxResult<IPage<StatQueueIndexVO>> queueIndex = queryStatQueueIndex(page, statIndexDTO, isPage);
            queueIndexList = queueIndex.getData().getRecords();
        } catch (Exception e) {
            log.error("导出历史队列指标excel错误statIndex/export,{0}",e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 表头国际化
        firstRow.add(MessageUtils.get("queue.name"));
        firstRow.add(MessageUtils.get("connect.alias"));
        firstRow.add(MessageUtils.get("avg.working.hours.after.contact"));
        firstRow.add(MessageUtils.get("avg.agent.interaction.time"));
        firstRow.add(MessageUtils.get("avg.customer.retention.time"));
        firstRow.add(MessageUtils.get("avg.queue.abandonment.time"));
        firstRow.add(MessageUtils.get("avg.queue.waiting.time"));
        firstRow.add(MessageUtils.get("abandon.contact.quantity"));
        firstRow.add(MessageUtils.get("queued.contacts.quantity"));

        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(queueIndexList)) {
            for (StatQueueIndexVO vo : queueIndexList) {
                List<String> row = new ArrayList<>();
                row.add(vo.getQueueName());
                row.add(vo.getConnectAlias());
                row.add(vo.getAvgWorkingHoursAfterContact());
                row.add(vo.getAvgAgentInteractionTime());
                row.add(vo.getAvgCustomerRetentionTime());
                row.add(String.valueOf(vo.getAvgQueueAbandonmentTime()));
                row.add(String.valueOf(vo.getAvgQueueWaitingTime()));
                row.add(String.valueOf(vo.getAbandonContactQuantity()));
                row.add(String.valueOf(vo.getQueuedContactsQuantity()));
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statIndex.export.work.record.filename3")+"_"+ DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出历史队列指标excel错误出现异常statIndex/export,", e);
        }
    }

    @Override
    public AjaxResult<IPage<KinesisContactVo>> queryStatDataDetailsNew(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean isPage, Integer queryType) throws IOException, ParseException {
        // 查询出来满足条件的工单数据
        List<WorkOrderRecordResponseVO> ticketDataList = new ArrayList<>();

        //返回值return AjaxResult.ok(iPage);
        IPage<KinesisContactVo> iPage = new Page<>();

        // 当工单查询条件不为null时进行查询
        if(CollectionUtils.isNotEmpty(statIndexDTO.getWorkOrderExtVos())){
            // 查询出来满足条件的工单数据
            ticketDataList = ticketData(statIndexDTO, queryType);
            if(CollectionUtils.isEmpty(ticketDataList)){
                iPage.setTotal(0);
                iPage.setRecords(null);
                return AjaxResult.ok(iPage);
            }
        }
        //筛选条件
        String startDate = statIndexDTO.getStartDate();
        String endDate = statIndexDTO.getEndDate();
        String multiFieldQuery = statIndexDTO.getMultiFieldQuery();
        String queryChannelType = statIndexDTO.getQueryChannelType();
        String connectAlias = statIndexDTO.getConnectAlias();
        String index = "kinesis_contact_details_" + SecurityUtil.getLoginUser().getCompanyId();
        // 索引不存在则直接返回空
        if (!headIndexExists(index)) {
            iPage.setTotal(0);
            iPage.setRecords(null);
            return AjaxResult.ok(iPage);
        }
        //创建查询
        SearchRequest request = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .sort(new FieldSortBuilder("createTime").order(SortOrder.DESC))
                .trackTotalHits(true);

        //多条件查询 and(or/or/or)and
        BoolQueryBuilder boolQueryOut = QueryBuilders.boolQuery();
        //boolQueryOut.must(QueryBuilders.matchQuery("company_id", "d6ff59a027861d7d4133e1d6b6872464"));
//        boolQueryOut.must(QueryBuilders.matchQuery("company_id", SecurityUtil.getLoginUser().getCompanyId()));
        //日期上下限
        if (StringUtil.isNotNull(endDate) && StringUtil.isNotEmpty(endDate)
                && StringUtil.isNotNull(startDate) && StringUtil.isNotEmpty(startDate)){
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //日期查询
            boolQueryOut.must(QueryBuilders.rangeQuery("createTime")
                    .gte(dateFormat.parse(startDate))  // 开始时间，以毫秒数表示
                    .lte(dateFormat.parse(endDate)));   // 结束时间，以毫秒数表示
        } else {
            // 日期查询：默认本月
            // 获取当前时间
            Date currentDate = new Date();
            // 创建 Calendar 对象并设置为当前时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            // 设置为本月的第一天
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date firstDayOfMonth = calendar.getTime();
            // 获取当前时间的最后一天
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date lastDayOfMonth = calendar.getTime();

            boolQueryOut.must(QueryBuilders.rangeQuery("createTime")
                    .gte(firstDayOfMonth.getTime())  // 开始时间，以毫秒数表示
                    .lte(lastDayOfMonth.getTime()));   // 结束时间，以毫秒数表示
        }
        //四个字段模糊查询
        if (StringUtil.isNotNull(multiFieldQuery) && StringUtil.isNotEmpty(multiFieldQuery)){
            BoolQueryBuilder boolQueryIn = QueryBuilders.boolQuery();
            //忽略大小写，模糊查询
            boolQueryOut.must(boolQueryIn.should(QueryBuilders.wildcardQuery("contactId", "*"+multiFieldQuery+"*").caseInsensitive(true))
                    .should(QueryBuilders.wildcardQuery("receptionAgent", "*"+multiFieldQuery+"*").caseInsensitive(true))
                    .should(QueryBuilders.wildcardQuery("agentGroup", "*"+multiFieldQuery+"*").caseInsensitive(true))
                    .should(QueryBuilders.wildcardQuery("queueName", "*"+multiFieldQuery+"*").caseInsensitive(true)));
        }
        //呼入渠道查询
        if (StringUtil.isNotNull(queryChannelType) && StringUtil.isNotEmpty(queryChannelType)){
            boolQueryOut.must(QueryBuilders.matchQuery("channelTypeId", queryChannelType));
        }
        //联络线路查询
        if (StringUtil.isNotNull(connectAlias) && StringUtil.isNotEmpty(connectAlias)){
            boolQueryOut.must(QueryBuilders.matchQuery("connectAlias", connectAlias));
        }
        // 如果工单数据不为null，则根据工单编号批量查询
        if(CollectionUtils.isNotEmpty(ticketDataList)){
            List<String> ticketCodeList = ticketDataList.stream().map(WorkOrderRecordResponseVO::getWordRecordCode).collect(Collectors.toList());
            boolQueryOut.must(QueryBuilders.termsQuery("workOrderNumber", ticketCodeList));
        }


        //数据容器
        List<KinesisContactVo> statDataDetailsVOs = new ArrayList<>();
        //@param isPage=true 分页
        if(isPage){
            //计算当前页的起始下标
            long start = (pageParam.getCurrent() - 1) * pageParam.getSize();
            searchSourceBuilder.from((int) start)
                    .size((int) pageParam.getSize());

            searchSourceBuilder.query(boolQueryOut);
            request.source(searchSourceBuilder);
            // 进行查询
            log.info(request.toString());
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            long total = response.getHits().getTotalHits().value;
            log.info("es数据数量：{}",total);
            //装入数据
            for (SearchHit hit : hits) {
                String source = hit.getSourceAsString();
                ObjectMapper objectMapper = new ObjectMapper();
                KinesisContactDetailsVo detailsVo = objectMapper.readValue(source, KinesisContactDetailsVo.class);
                KinesisContactVo contactVo = new KinesisContactVo(detailsVo);
                statDataDetailsVOs.add(contactVo);
            }

            iPage.setTotal(total);

        }//@param isPage=false  滚动查询导出excel
        else{
            searchSourceBuilder.query(boolQueryOut);
            //滚动查询批次大小
            searchSourceBuilder.size(1000);
            //查询条件应用到请求中
            request.source(searchSourceBuilder);
            //滚动超时
            request.scroll(TimeValue.timeValueMinutes(1L));
            // 进行滚动查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            //标识当前滚动上下文的唯一标识符，滚动ID
            String scrollId = response.getScrollId();
            do {
                SearchHits hits = response.getHits();
                //装入数据
                for (SearchHit hit : hits) {
                    String source = hit.getSourceAsString();
                    ObjectMapper objectMapper = new ObjectMapper();
                    KinesisContactDetailsVo detailsVo = objectMapper.readValue(source, KinesisContactDetailsVo.class);
                    KinesisContactVo contactVo = new KinesisContactVo(detailsVo);
                    statDataDetailsVOs.add(contactVo);
                }
                //继续从上次停下的位置开始检索数据
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                //新的滚动超时时间
                scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
                //新的滚动 ID 和当前批次的结果
                response = restHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                //更新ID
                scrollId = response.getScrollId();
            } while (response.getHits().getHits().length > 0);


            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            //滚动 ID 添加到清理请求
            clearScrollRequest.addScrollId(scrollId);
            //清理滚动上下文
            restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

        }

        // 当工单查询条件为null并且查询的联络明细数据存在，才进行工单的查询
        if(CollectionUtils.isEmpty(statIndexDTO.getWorkOrderExtVos())&&CollectionUtils.isNotEmpty(statDataDetailsVOs)){
            ticketDataList = queryTicket(statDataDetailsVOs, queryType);
        }
        if (CollectionUtils.isNotEmpty(ticketDataList)) {
//                for (KinesisContactVo kinesisContact: statDataDetailsVOs){
//                    Optional<WorkOrderRecordResponseVO> result = ticketDataList.stream()
//                            .filter(ticket -> ticket.getWordRecordCode().equals(kinesisContact.getWorkOrderNumber()))
//                            .findFirst();
//                    if(result.isPresent()){
//                        WorkOrderRecordResponseVO ticket = result.get();
//                        BeanUtils.copyProperties(ticket, kinesisContact);
//                    }
//                }

            for (KinesisContactVo kinesisContact: statDataDetailsVOs){
                for (WorkOrderRecordResponseVO ticket :ticketDataList){
                    if(kinesisContact.getWorkOrderNumber()!=null&&kinesisContact.getWorkOrderNumber().equals(ticket.getWordRecordCode())){
                        BeanUtils.copyProperties(ticket, kinesisContact);
                    }
                }
            }
        }
        //装入数据
        iPage.setRecords(statDataDetailsVOs);

        return AjaxResult.ok(iPage);
    }

    @Override
    public AjaxResult<Object> addPhoneContactDetail(ContactDetailPhoneAddVo detailAddVo) {
        log.info("电话渠道请求参数:{}",detailAddVo);
        KinesisContactDetailsVo contactDetailsVo = new KinesisContactDetailsVo();
        BeanUtils.copyProperties(detailAddVo, contactDetailsVo);
        contactDetailsVo.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        // 首先确定事件类型
        switch (detailAddVo.getEventType()) {
            case 2:
                contactDetailsVo.setReceptionAgent(SecurityUtil.getLoginUser().getUserId());
                contactDetailsVo.setAgentGroupId(SecurityUtil.getLoginUser().getDeptId());
                break;
            case 3:
                contactDetailsVo.setReceptionAgent(SecurityUtil.getLoginUser().getUserId());
                contactDetailsVo.setAgentGroupId(SecurityUtil.getLoginUser().getDeptId());
                break;
            case 6:
                contactDetailsVo.setOnHoldTime(detailAddVo.getOnHoldTime());
                contactDetailsVo.setOnHoldNumber(1);
                break;
            case 7:
                contactDetailsVo.setInitialContactId(detailAddVo.getInitialContactId());
                contactDetailsVo.setNextContactId(detailAddVo.getNextContactId());
                contactDetailsVo.setPreviousContactId(detailAddVo.getPreviousContactId());
                break;
            default:
                break;
        }
        ContactDetailHandleVo contactDetailHandleVo = new ContactDetailHandleVo();
        contactDetailHandleVo.setKinesisContactDetailsVo(contactDetailsVo);
        contactDetailHandleVo.setEventType(detailAddVo.getEventType());
        // 基础处理完以后，发送MQ，开始数据存储
        String jsonString = JSON.toJSONString(contactDetailHandleVo);
        rabbitTemplate.convertAndSend(RabbitMqConstants.CONTACT_DETAIL_EXCHANGE, RabbitMqConstants.CONTACT_DETAIL_PHONE_ROUTING_KEY, jsonString);
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<Object> addChannelContactDetail(ContactDetailChannelAddVo detailAddVo) {
        log.info("联络明细渠道请求参数:{}",detailAddVo);
        if(detailAddVo.getContactId() == null){
            log.info("ContactId为null，不执行后续请求");
            return AjaxResult.ok();
        }
        KinesisContactDetailsVo contactDetailsVo = new KinesisContactDetailsVo();
        BeanUtils.copyProperties(detailAddVo, contactDetailsVo);
        contactDetailsVo.setCompanyId(detailAddVo.getCompanyId());
        ContactDetailHandleVo contactDetailHandleVo = new ContactDetailHandleVo();
        if(detailAddVo.getAverageReplyTime()!=null){
            contactDetailsVo.setAverageReplyTime(String.valueOf(detailAddVo.getAverageReplyTime()));
        }
        contactDetailHandleVo.setKinesisContactDetailsVo(contactDetailsVo);
        contactDetailHandleVo.setEventType(detailAddVo.getEventType());
        contactDetailHandleVo.setRobotTicketId(detailAddVo.getRobotTicketId());
        // 基础处理完以后，发送MQ，开始数据存储
        String jsonString = JSON.toJSONString(contactDetailHandleVo);
        rabbitTemplate.convertAndSend(RabbitMqConstants.CONTACT_DETAIL_EXCHANGE, RabbitMqConstants.CONTACT_DETAIL_CHANNEL_ROUTING_KEY, jsonString);
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<Object> contactDetailsUpdate() {
        // 获取当前系统默认时区（UTC+8）的LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.now(ZoneOffset.UTC);
        // 将LocalDateTime转换为Instant，使用系统默认时区进行转换
        Instant endTime = localDateTime.toInstant(ZoneOffset.UTC);
        // 结束时间为定时任务的当前时间
        Instant startTime = endTime.minus(Duration.ofMinutes(60));
        log.info("联络明细更新请求参数:{},{}",startTime,endTime);
//        Instant startTime =Instant.parse("2025-03-24T12:00:00Z");
//        Instant endTime = Instant.parse("2025-03-24T12:50:00Z");
        // 查询出所有公司的全部队列
        R<List<ConnectInfoVo>> connectInstances = connectsServiceClient.queryConnectInstances();
        log.info("联络明细更新请求参数,查询出所有公司的全部队列:{}",connectInstances.getCode());
        if(connectInstances.getCode() == AjaxResult.SUCCESS&& connectInstances.getData()!=null){
            log.info("联络明细更新请求参数,查询所有公司的全部队列===============已经进入查询的if判断了");
            // 循环调用aws服务，查询出所有的刚刚时间内所有信息
            for(ConnectInfoVo connectInfoVo :connectInstances.getData()){
                log.info("联络明细更新请求参数,查询所有公司的全部队列===============已经进入for循环了");
                SearchContactsParam searchContactsParam = new SearchContactsParam()
                        .setInstanceId(connectInfoVo.getConnectId())
                        .setStartTime(startTime)
                        .setEndTime(endTime);
                searchContactsParam.setAccessKeyId(connectInfoVo.getAccessKey());
                searchContactsParam.setSecretAccessKey(connectInfoVo.getSecretAccessKey());
                searchContactsParam.setRegion(connectInfoVo.getRegionCode());
                searchContactsParam.setInstanceArn(connectInfoVo.getConnectArn());
                R<String> stringR = connectClient.searchQueueContacts(searchContactsParam);
                log.info("联络明细更新请求参数,查询cloudsapi拿到的返回值是：{}",stringR.getCode());
                if(stringR.getCode() == AjaxResult.SUCCESS&&stringR.getData()!=null){
                    log.info("联络明细更新请求参数,查询cloudsapi拿到的返回值进入调用成功判断了====================");
                    // 定义批量请求
                    BulkRequest bulkRequest = new BulkRequest("kinesis_contact_details_"+connectInfoVo.getCompanyId());

                    List<PhoneContactDetailUpdateVo> contactDetailUpdateVo = JSON.parseArray(stringR.getData(), PhoneContactDetailUpdateVo.class);
                    List<String> contactIdList = contactDetailUpdateVo.stream()
                            .map(PhoneContactDetailUpdateVo::getContactId)
                            .collect(Collectors.toList());
                    // 构建搜索请求
                    // 1. 先查询ES中存在的contactId
                    SearchRequest existsRequest = new SearchRequest("kinesis_contact_details_"+connectInfoVo.getCompanyId());
                    SearchSourceBuilder existsSource = new SearchSourceBuilder();
                    existsSource.query(QueryBuilders.termsQuery("contactId", contactIdList));
                    existsSource.fetchSource(new String[]{"contactId"}, null);
                    existsSource.size(contactIdList.size()); // 确保返回所有匹配项

                    existsRequest.source(existsSource);
                    SearchResponse existsResponse = null;
                    try {
                        existsResponse = restHighLevelClient.search(existsRequest, RequestOptions.DEFAULT);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }

                    // 2. 收集ES中存在的contactId
                    Set<String> existingIds = new HashSet<>();
                    existsResponse.getHits().forEach(hit -> {
                        String existingId = (String) hit.getSourceAsMap().get("contactId");
                        if (existingId != null) {
                            existingIds.add(existingId);
                        }
                    });
                    // 不存在es的索引，准备进行添加
                    List<String> stringList = contactIdList.stream()
                            .filter(id -> !existingIds.contains(id))
                            .collect(Collectors.toList());
                    for(String str : stringList){
                        for(PhoneContactDetailUpdateVo detailUpdateVo :contactDetailUpdateVo){
                            if(str.equals(detailUpdateVo.getContactId())){
                                KinesisContactDetailsVo addDetails = new KinesisContactDetailsVo();
                                addDetails.setContactId(detailUpdateVo.getContactId());
                                addDetails.setCallChannel("voice");
//                                addDetails.setAwsAccountId(connectInfoVo.getAwsUserId());
                                addDetails.setCompanyId(connectInfoVo.getCompanyId());
                                addDetails.setQueueName(connectInfoVo.getConnectAlias());
                                addDetails.setConnectAlias(connectInfoVo.getConnectAlias());
                                addDetails.setQueueArn(connectInfoVo.getConnectArn());
                                addDetails.setIncomingOutgoing(detailUpdateVo.getInitiationMethod());
                                addDetails.setChannelTypeId("7");
                                addDetails.setCreateTime(detailUpdateVo.getStartTime());
                                addDetails.setStartTime(detailUpdateVo.getStartTime());
                                addDetails.setEnqueueTimestamp(detailUpdateVo.getEnqueueTimestamp());
                                addDetails.setEndTime(detailUpdateVo.getDisconnectTimestamp());
                                // 定义单条索引请求
                                IndexRequest singleIndexRequest = new IndexRequest("kinesis_contact_details_"+connectInfoVo.getCompanyId());
                                // 将单个AddAnswerEsVo对象转为json
                                String value = null;
                                try {
                                    value = new ObjectMapper().writeValueAsString(addDetails);
                                } catch (JsonProcessingException e) {
                                    throw new RuntimeException(e);
                                }
                                singleIndexRequest.source(value, XContentType.JSON);
                                // 添加单条索引请求到批量请求
                                bulkRequest.add(singleIndexRequest);
                            }
                        }
                    }
                    try {
                        // 执行批量索引操作 es版本和spring boot版本有冲突问题，添加成功，返回200，
                        BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                        log.info("联络明细更新请求参数,存储ES结束====================");
                    } catch (IOException e) {
                        if (!e.getMessage().contains("200 OK")) {
                            log.error("es新增文档失败，异常信息：", e);
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        }
        return null;
    }


    private List<WorkOrderRecordResponseVO> ticketData(StatIndexDTO statIndexDTO, Integer queryType) throws IOException{
        // 定义索引名称
        String indexName = ticketIndex+SecurityUtil.getLoginUser().getCompanyId();
        // 首先进行ES索引判断
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在则返回null
        if (!indexExists) {
            return null;
        }
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);

        // 首先查询拼接工单的查询语句
        WorkOrderRecordRequestVO workOrderRecordRequest = new WorkOrderRecordRequestVO();
        workOrderRecordRequest.setWorkOrderExtVos(statIndexDTO.getWorkOrderExtVos());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder =crmAgentWorkRecordService.queryConditionMontage(workOrderRecordRequest);
        searchSourceBuilder.sort("create_time", SortOrder.DESC);
        // 分页查询
        searchSourceBuilder.query(boolQueryBuilder).size(10000);
        request.source(searchSourceBuilder);
        // 查询出对应的工单数据
        SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);

        return dataProcessingTicket(response,queryType);
    }

    /**
     * 根据工单编号，查询出对应工单
     * @param statDataDetailsVOs 联络明细数据
     * @return 工单数据
     */
    private List<WorkOrderRecordResponseVO> queryTicket(List<KinesisContactVo> statDataDetailsVOs, Integer queryType) throws IOException{
        List<String> ticketCodeList = statDataDetailsVOs.stream()
                .map(KinesisContactVo::getWorkOrderNumber)
                .filter(Objects::nonNull) // 排除为 null 的数据
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ticketCodeList)){
            return null;
        }
        // 定义索引名称
        String indexName = ticketIndex+SecurityUtil.getLoginUser().getCompanyId();
        // 首先进行ES索引判断
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在则返回null
        if (!indexExists) {
            return null;
        }
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        //构建请求
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("word_record_code", ticketCodeList));
        searchSourceBuilder.sort("create_time", SortOrder.DESC);
        // 分页查询
        searchSourceBuilder.query(boolQueryBuilder).size(10000);
        request.source(searchSourceBuilder);
        // 查询出对应的工单数据
        SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);

        return dataProcessingTicket(response, queryType);
    }

    /**
     *  根据查询的工单数据，将特殊字段数据处理
     * @param response 查询的ES工单数据
     * @return 处理后的工单数据
     * @throws IOException IO异常
     */
    private List<WorkOrderRecordResponseVO> dataProcessingTicket(SearchResponse response, Integer queryType) throws IOException{

        List<WorkOrderRecordResponseVO> workListVos = new ArrayList<>();
        // 查询公司的拓展字段
        List<WorkOrderExtVo> defList = crmAgentWorkRecordExtDefService.queryList(8).getData();
        SearchHit[] hits = response.getHits().getHits();
        for (SearchHit hit : hits) {
            WorkOrderRecordResponseVO workOrderRecordResponseVO = new WorkOrderRecordResponseVO();
            Map<String, Object> source = hit.getSourceAsMap();
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            // 注册 JavaTimeModule
            objectMapper.registerModule(new JavaTimeModule());
            String json = objectMapper.writeValueAsString(source);
            // 将 JSON 字符串转换为 Java 对象
            TicketInfoIndex contentIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            BeanUtils.copyProperties(contentIndex, workOrderRecordResponseVO);
            workOrderRecordResponseVO.setCustomerTelephone(DesensitizationUtils.desensitizeCustomerInfo(contentIndex.getCustomerTelephone()));
            if(CollectionUtils.isNotEmpty(contentIndex.getTicketExt())){
                List<TicketExt> ticketExt = contentIndex.getTicketExt();
                // 目标 map
                Map<String, Object> result = ticketExt.stream()
                        .collect(Collectors.toMap(
                                TicketExt::getExtCode, // key: extCode
                                ext -> defList.stream()
                                        .filter(def -> def.getWorkRecordExtDefCode().equals(ext.getExtCode()))
                                        .flatMap(def -> {
                                            // 根据 propertyTypeId 处理逻辑
                                            switch (def.getPropertyTypeId()) {
                                                case "1003":
                                                case "1005":
                                                    // 单选框逻辑
                                                    return def.getWorkOrderExtOptionDefList().stream()
                                                            .filter(option -> option.getOptionValue().equals(ext.getExtValue()))
                                                            .map(WorkOrderExtOptionDefVo::getOptionName);

                                                case "1004":
                                                case "1006":
                                                    // 多选框逻辑
                                                    return Arrays.stream(ext.getExtValue().split(","))
                                                            .flatMap(value -> def.getWorkOrderExtOptionDefList().stream()
                                                                    .filter(option -> option.getOptionValue().equals(value))
                                                                    .map(WorkOrderExtOptionDefVo::getOptionName));

                                                default:
                                                    // 不处理
                                                    return Stream.empty();
                                            }
                                        })
                                        .collect(Collectors.joining(",")), // 将多个 optionName 拼接成字符串
                                (v1, v2) -> v1 // 处理 key 冲突
                        ));

                workOrderRecordResponseVO.setWorkRecordResVoIPage(result);
            }
            workListVos.add(workOrderRecordResponseVO);
        }
        // 查询工单类型
        List<CrmAgentWorkRecordTypeDef> list = crmAgentWorkRecordTypeDefService.list(new QueryWrapper<CrmAgentWorkRecordTypeDef>().lambda()
                .eq(CrmAgentWorkRecordTypeDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                .eq(CrmAgentWorkRecordTypeDef::getLanguageCode, ServletUtils.getHeaderLanguage())
                .eq(CrmAgentWorkRecordTypeDef::getDataStatus, 1));

        if(CollectionUtils.isNotEmpty(list)&&CollectionUtils.isNotEmpty(workListVos)){
            // 根据工单类型code进行匹配，相匹的进行存入
            list.forEach(crmAgent ->
                    workListVos.stream()
                            .filter(workOrder -> StringUtil.isNotEmpty(workOrder.getWorkRecordTypeCode())&&
                                    StringUtil.isNotEmpty(crmAgent.getWorkRecordTypeValue())&&
                                    workOrder.getWorkRecordTypeCode().equals(crmAgent.getWorkRecordTypeValue()))
                            .forEach(workOrder -> workOrder.setWorkRecordTypeName(crmAgent.getWorkRecordTypeName()))
            );
        }
        if(CollectionUtils.isNotEmpty(workListVos)){
            // 计算工单时间 根据状态来判断]
            crmAgentWorkRecordService.ticketDataHandle(workListVos, queryType);
        }
        return workListVos;
    }

    /**
     * 查询索引是否存在
     * @param index 索引名称
     * @return
     * @throws IOException
     */
    private boolean headIndexExists(String index) throws IOException {
        GetIndexRequest req = new GetIndexRequest(index);
        boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
        log.info("当前索引{}, 是否存在: {}", index, exists);
        return exists;
    }

    /**
     * dataDetails装入VO
     * @param source
     * @return
     */
    private StatDataDetailsVO mapToStatDataDetailsVO(Map<String, Object> source) throws Exception{

        StatDataDetailsVO statDataDetailsVO = new StatDataDetailsVO();

        //id
        //statDataDetailsVO.setCompanyId((String) source.get("company_id"));
        statDataDetailsVO.setContactId((String) source.get("contact_id"));

        //Date类型
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        statDataDetailsVO.setCallTime(dateFormat.parse(source.get("call_time").toString()));
        statDataDetailsVO.setEndTime(dateFormat.parse(source.get("end_time").toString()));
        statDataDetailsVO.setAcwTime(dateFormat.parse(source.get("acw_time").toString()));

        //keyword
        statDataDetailsVO.setTotalTime((String) source.get("total_time"));
        statDataDetailsVO.setInteractionTime((String) source.get("interaction_time"));
        statDataDetailsVO.setQueueWaitTime((String) source.get("queue_wait_time"));
        //statDataDetailsVO.setCallChannel((String) source.get("call_channel"));
        //statDataDetailsVO.setCallChannel(MessageUtils.get("channel.number" + source.get("call_channel")));
        statDataDetailsVO.setCallChannel(source.get("call_channel").toString());
        statDataDetailsVO.setReceptionAgent((String) source.get("reception_agent"));
        statDataDetailsVO.setAgentGroup((String) source.get("agent_group"));
        statDataDetailsVO.setQueueName((String) source.get("queue_name"));
        statDataDetailsVO.setAcwDuration((String) source.get("acw_duration"));
        statDataDetailsVO.setWorkOrderNumber((String) source.get("work_order_number"));

        //新增六个字段
        statDataDetailsVO.setOnHoldTime((String) source.get("on_hold_time"));
        statDataDetailsVO.setOnHoldNumber((String) source.get("on_hold_number"));
        statDataDetailsVO.setIsSwitch((String) source.get("is_switch"));
        statDataDetailsVO.setHangingType((String) source.get("hanging_type"));
        statDataDetailsVO.setSystemPhone((String) source.get("system_phone"));
        statDataDetailsVO.setSatisfactionRating((String) source.get("satisfaction_rating"));

        //新增字段
        statDataDetailsVO.setConnectAlias((String) source.get("connect_alias"));

        //创建、修改时间
        //statDataDetailsVO.setCreator((String) source.get("creator"));
        //statDataDetailsVO.setCreateTime(new Date(Long.parseLong(source.get("create_time").toString())));
        //statDataDetailsVO.setModifier((String) source.get("modifier"));
        //statDataDetailsVO.setModifyTime(new Date(Long.parseLong(source.get("modify_time").toString())));

        return statDataDetailsVO;
    }
//
//    // 静态方法，CrmChannelEnum根据Integer code获取对应的String name
//    public static String getNameByCode(Integer code) {
//        for (CrmChannelEnum channel : CrmChannelEnum.values()) {
//            if (channel.getCode().equals(code)) {
//                return channel.getName();
//            }
//        }
//        return null; // 如果找不到对应的code，则返回null或者抛出异常，视情况而定
//    }

    /**
     * 导出excel
     * @param firstRow  表头字段信息
     * @param rowList   导出具体行数据
     * @param exportName   excel名
     * @throws IOException
     */
    private void exportWithResponse(List<String> firstRow, List<List<String>> rowList, String exportName) throws IOException {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(firstRow)) {
            return;
        }
        String[] firstRowArray = firstRow.toArray(new String[firstRow.size()]);
        Workbook wb = writeToExcelByList(firstRowArray, rowList);

        String fileName = URLEncoder.encode(exportName, "UTF-8");
        ServletUtil.getResponse().setContentType("application/vnd.ms-excel;charset=utf-8");
        ServletUtil.getResponse().setHeader("Content-Disposition","attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream outputStream= ServletUtil.getResponse().getOutputStream();
        wb.write(outputStream);
        outputStream.close();
    }
    /**
     *  @Description: array 表头数据 list 具体数据
     */
    private Workbook writeToExcelByList(String[] array, List<List<String>> list) {
        //创建工作薄
        Workbook wb = new XSSFWorkbook();

        //设置列名样式
        CellStyle headerStyle = wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.LEFT);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);//上下居中
        headerStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());//设置背景色
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);//必须设置 否则无效
        setCellBorderStyle(headerStyle);
        Font headerFont = wb.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setFontName("微软雅黑");
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        //创建sheet
        Sheet sheet = wb.createSheet(MessageUtils.get("work.record.file.name"));

        //在sheet中添加表头，由于不涉及到标题，所以表头行数从0开始
        Row row = sheet.createRow((int) 0);
        for (int i = 0; i < array.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(array[i]);
            cell.setCellStyle(headerStyle);
        }

        //数据样式
        CellStyle dataStyle = wb.createCellStyle();
        //设置居中样式，水平居中
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font dataFont = wb.createFont();
        dataFont.setFontHeightInPoints((short) 12);
        dataFont.setFontName("微软雅黑");
        dataStyle.setFont(dataFont);

        //数据填充
        try {
            int index = 1;
            for (List value : list) {
                row = sheet.createRow(index);
                index++;
                List data = value;
                for (int j = 0; j < data.size(); j++) {
                    Cell cell = row.createCell(j);
                    // 为当前列赋值
                    if(data.get(j)!=null){
                        cell.setCellValue(data.get(j).toString());
                    }else{
                        cell.setCellValue("");
                    }
                    //设置数据的样式
                    cell.setCellStyle(dataStyle);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 宽度自适应的问题，必须在单元格设值以后进行
        for (int k = 0; k < array.length; k++) {
            sheet.autoSizeColumn(k);
        }
        return wb;
    }
    private void setCellBorderStyle(CellStyle cellStyle) {
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN); //解决填充背景色没有边框问题
        cellStyle.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
    }

    /**
     * 秒转时分秒
     * @param seconds
     * @return
     */
    public static String secFormat(Long seconds) {
        // 格式化成 "hh:mm:ss"
        return String.format("%02d:%02d:%02d", seconds / 3600, (seconds % 3600) / 60, seconds % 60);
    }
}