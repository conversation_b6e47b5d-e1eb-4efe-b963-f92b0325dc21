package com.goclouds.crm.platform.call.service.impl;

import com.goclouds.crm.platform.call.service.VoiceTranscriptionService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.client.rescmgnt.RescmgntClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @description
 * @createTime 2025年04月25日 10:11
 */
@Service
public class VoiceTranscriptionServiceImpl implements VoiceTranscriptionService {

    @Autowired
    private RescmgntClient rescmgntClient;

    @Override
    public AjaxResult transcription(MultipartFile file, String companyId, String language, int isOpenChannel) {
        R r = rescmgntClient.transcription(file, companyId, language, isOpenChannel);
        return AjaxResult.ok(r.getData());
    }


    @Override
    public AjaxResult queryTranscription(String transcriptionId, String self) {
        R r = rescmgntClient.query(transcriptionId, self);
        return AjaxResult.ok(r.getData());
    }

}
