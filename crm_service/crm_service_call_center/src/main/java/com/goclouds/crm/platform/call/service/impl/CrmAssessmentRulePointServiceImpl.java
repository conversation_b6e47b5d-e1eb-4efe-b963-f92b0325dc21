package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAssessmentRulePoint;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentRulePointMapper;
import com.goclouds.crm.platform.call.service.CrmAssessmentRulePointService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 评估规则扣分点Service实现
 */
@Service
public class CrmAssessmentRulePointServiceImpl extends ServiceImpl<CrmAssessmentRulePointMapper, CrmAssessmentRulePoint>
        implements CrmAssessmentRulePointService {

    @Override
    public IPage<CrmAssessmentRulePoint> queryRulePointPages(IPage<Object> pageParam, CrmAssessmentRulePoint rulePoint) {
        // 构建分页条件
        Page<CrmAssessmentRulePoint> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        
        // 构建查询条件
        LambdaQueryWrapper<CrmAssessmentRulePoint> queryWrapper = new LambdaQueryWrapper<>();
        
        // 按规则ID查询
        if (StringUtils.isNotBlank(rulePoint.getRuleId())) {
            queryWrapper.eq(CrmAssessmentRulePoint::getRuleId, rulePoint.getRuleId());
        }
        
//        // 按描述模糊查询
//        if (StringUtils.isNotBlank(rulePoint.getPointDescription())) {
//            queryWrapper.like(CrmAssessmentRulePoint::getPointDescription, rulePoint.getPointDescription());
//        }
//
        // 只查询未删除的数据
        queryWrapper.eq(CrmAssessmentRulePoint::getDataStatus, 0);
        
        // 排序
        queryWrapper.orderByAsc(CrmAssessmentRulePoint::getPointSort);
        
        // 执行查询
        return this.baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<CrmAssessmentRulePoint> listRulePointsByRuleId(String ruleId) {
        // 根据规则ID查询扣分点列表
        LambdaQueryWrapper<CrmAssessmentRulePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentRulePoint::getRuleId, ruleId);
        queryWrapper.eq(CrmAssessmentRulePoint::getDataStatus, 1);
        queryWrapper.orderByAsc(CrmAssessmentRulePoint::getPointSort);
        
        return this.list(queryWrapper);
    }

    public void delPoiint(String ruleId){  //删除ruleId下其他的
        CrmAssessmentRulePoint rulePoint = new CrmAssessmentRulePoint();
        rulePoint.setRuleId(ruleId);
        this.baseMapper.delete(new LambdaQueryWrapper<>(rulePoint));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRulePoint(CrmAssessmentRulePoint rulePoint) {
        // 设置初始值
        rulePoint.setPointId(UUID.randomUUID().toString().replace("-", ""));
        rulePoint.setCreateTime(LocalDateTime.now());
        rulePoint.setDataStatus(1);

        // 获取当前该 ruleId 下 point_sort 最大值
        Integer maxPointSort = this.baseMapper.selectObjs(
                        new QueryWrapper<CrmAssessmentRulePoint>()
                                .eq("rule_id", rulePoint.getRuleId())
                                .orderByDesc("point_sort")
                                .last("LIMIT 1")
                                .select("point_sort")
                ).stream()
                .filter(Objects::nonNull)
                .map(obj -> (Integer) obj)
                .findFirst()
                .orElse(-1); // 没有记录时默认从 0 开始

        // 设置当前规则点的排序号
        rulePoint.setPointSort(maxPointSort + 1);

        // 插入记录
        this.baseMapper.insert(rulePoint);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRulePoint(CrmAssessmentRulePoint rulePoint) {
        return this.updateById(rulePoint);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRulePoint(String pointId) {
        LambdaQueryWrapper<CrmAssessmentRulePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentRulePoint::getRuleId, pointId);
        
        return this.baseMapper.delete(queryWrapper);
    }

    @Override
    public CrmAssessmentRulePoint getRulePointDetail(String pointId) {
        // 查询单个扣分点详情
        return this.getById(pointId);
    }
} 