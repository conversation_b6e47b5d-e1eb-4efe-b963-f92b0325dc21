package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecordRuleItem;
import com.goclouds.crm.platform.call.domain.vo.AssessmentRuleVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 智能质检评估记录规则项表 - 业务逻辑类
 */
public interface CrmTicketAssessmentRecordRuleItemService extends IService<CrmTicketAssessmentRecordRuleItem> {

    /**
     * 更新评估记录规则状态。
     *
     * @param assessmentRecordRuleItemId 记录规则ID
     * @param assessmentStatus           记录规则项状态
     * @param aiScore                    记录规则项AI得分
     */
    void updateInfoByAssessmentRecordRuleItemId(String assessmentRecordRuleItemId, Integer assessmentStatus, BigDecimal aiScore);


    /**
     * 获取评估记录所有的记录规则列表
     *
     * @param recordId 记录ID
     * @return 记录规则列表
     */
    List<CrmTicketAssessmentRecordRuleItem> getAllRecordRule(String recordId);
//
//    /**
//     * 获取评估记录所有的AI打分的规则
//     *
//     * @param recordId 评估记录ID
//     * @return 记录规则列表
//     */
//    List<CrmTicketAssessmentRecordRuleItem> getAllAIRule(String recordId);

    /**
     * 获取评估规则数据
     *
     * @param recordRuleItemId 评估记录规则ID
     * @return 评估规则数据
     */
    AssessmentRuleVO selectAssessmentRuleWithCheckpoints(String recordRuleItemId);

}
