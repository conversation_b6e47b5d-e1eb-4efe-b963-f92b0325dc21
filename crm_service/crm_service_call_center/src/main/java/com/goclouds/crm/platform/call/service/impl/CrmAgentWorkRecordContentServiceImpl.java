package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordContent;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordContentMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordContentService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_content(工作记录内容)】的数据库操作Service实现
* @createDate 2023-05-25 10:30:03
*/
@Service
public class CrmAgentWorkRecordContentServiceImpl extends ServiceImpl<CrmAgentWorkRecordContentMapper, CrmAgentWorkRecordContent>
    implements CrmAgentWorkRecordContentService {

}




