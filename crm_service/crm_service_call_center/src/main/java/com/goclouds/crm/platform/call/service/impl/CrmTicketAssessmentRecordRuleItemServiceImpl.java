package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecordRuleItem;
import com.goclouds.crm.platform.call.domain.vo.AssessmentRuleVO;
import com.goclouds.crm.platform.call.mapper.CrmTicketAssessmentRecordRuleItemMapper;
import com.goclouds.crm.platform.call.service.CrmTicketAssessmentRecordRuleItemService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 智能质检评估记录表 - 业务逻辑实现类
 */
@Service
public class CrmTicketAssessmentRecordRuleItemServiceImpl extends ServiceImpl<CrmTicketAssessmentRecordRuleItemMapper, CrmTicketAssessmentRecordRuleItem> implements CrmTicketAssessmentRecordRuleItemService {


    /**
     * 更新评估记录规则状态。
     *
     * @param assessmentRecordRuleItemId 记录规则ID
     * @param assessmentStatus           记录规则项状态
     * @param aiScore                    记录规则项AI得分
     */
    @Override
    public void updateInfoByAssessmentRecordRuleItemId(String assessmentRecordRuleItemId, Integer assessmentStatus, BigDecimal aiScore) {
        this.baseMapper.updateInfoByAssessmentRecordRuleItemId(assessmentRecordRuleItemId, assessmentStatus, aiScore);
    }

    /**
     * 获取评估记录所有的记录规则列表
     *
     * @param recordId 记录ID
     * @return 记录规则列表
     */
    @Override
    public List<CrmTicketAssessmentRecordRuleItem> getAllRecordRule(String recordId) {
        return this.baseMapper.getAllRecordRule(recordId);
    }

//    /**
//     * 获取评估记录所有的AI打分的规则
//     *
//     * @param recordId 评估记录ID
//     * @return 记录规则列表
//     */
//    @Override
//    public List<CrmTicketAssessmentRecordRuleItem> getAllAIRule(String recordId) {
//        return this.baseMapper.selectAllAIRule(recordId);
//    }

    /**
     * 获取评估规则数据
     *
     * @param recordRuleItemId 评估记录规则ID
     * @return 评估规则数据
     */
    @Override
    public AssessmentRuleVO selectAssessmentRuleWithCheckpoints(String recordRuleItemId) {
        return this.baseMapper.selectAssessmentRuleWithCheckpoints(recordRuleItemId);
    }


}
