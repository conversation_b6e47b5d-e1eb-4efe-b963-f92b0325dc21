package com.goclouds.crm.platform.call.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @description
 * @createTime 2025年01月07日 14:48
 */
@Component
@Slf4j
public class RagUtils {

    @Value("${rag.llmPath}")
    private String llmPath;

    /**
     * 调用 llm 接口 - 用于智能质检评估
     *
     * @param companyId 公司ID
     * @param prompt    提示词
     * @param chatList  工单聊天记录
     * @return llm结果
     * @throws IOException exception
     */
    public String assessmentQualityWithLLM(String companyId, String prompt, List<JSONObject> chatList) throws IOException {
        JSONObject jsonObject = new JSONObject();
        JSONObject conf = new JSONObject();
        conf.put("temperature", 0.99);
        conf.put("max_tokens", 2048);
        conf.put("stop", "</response>");
//        conf.put("top_k", 200);
        conf.put("top_p", 0.2);
        jsonObject.put("prompt_conf", conf);
        jsonObject.put("use_stream", false);
        jsonObject.put("company_id", companyId);

        conf.put("system_role", "");
        jsonObject.put("prompt", prompt);
        jsonObject.put("origin_prompt", prompt);

        jsonObject.put("func_name", "All-Know-Content-Review");

        // 聊天记录 上下文
        jsonObject.put("chat_history", chatList);

        jsonObject.put("extend_attr", new JSONObject());
        OkHttpClient client = new OkHttpClient().newBuilder()
                .readTimeout(100000, TimeUnit.MILLISECONDS)
                .writeTimeout(100000, TimeUnit.MILLISECONDS)
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonObject.toJSONString());
        log.info("请求参数：{}", jsonObject.toJSONString());
        log.info("请求路径：{}", llmPath);
        Request request = new Request.Builder()
                .url(llmPath + "/llm")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        return response.body().string();
    }

}
