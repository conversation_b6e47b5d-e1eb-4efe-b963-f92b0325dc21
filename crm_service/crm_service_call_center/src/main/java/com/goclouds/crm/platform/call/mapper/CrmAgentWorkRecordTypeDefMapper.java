package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordTypeDef;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordLevelVO;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordTypeVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_type_def(工单类型定义表)】的数据库操作Mapper
* @createDate 2023-09-21 16:09:26
* @Entity generator.domain.CrmAgentWorkRecordTypeDef
*/
public interface CrmAgentWorkRecordTypeDefMapper extends BaseMapper<CrmAgentWorkRecordTypeDef> {

    @Select("select work_record_type_value as workRecordTypeId, work_record_type_name, `order` " +
            "from crm_agent_work_record_type_def t " +
            "where t.company_id = #{companyId} and t.data_status = 1 and t.language_code = #{languageCode} ")
    List<WorkRecordTypeVO> queryWorkRecordType(@Param("companyId") String companyId,@Param("languageCode") String languageCode);

    @Select("select work_record_type_id, work_record_type_value, work_record_type_name, `order` " +
            "from crm_agent_work_record_type_def " +
            "where company_id = #{companyId} and language_code = #{languageCode} and data_status = 1 ")
    List<WorkRecordTypeVO> queryWorkRecordTypeByLanguage(@Param("companyId") String companyId,@Param("languageCode") String languageCode);
}




