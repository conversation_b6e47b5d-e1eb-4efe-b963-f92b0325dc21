package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordRelation;
import com.goclouds.crm.platform.call.domain.vo.UpdateWorkAssociationVO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_relation(工单关联关系表;)】的数据库操作Service
* @createDate 2023-09-25 17:41:07
*/
public interface CrmAgentWorkRecordRelationService extends IService<CrmAgentWorkRecordRelation> {

}
