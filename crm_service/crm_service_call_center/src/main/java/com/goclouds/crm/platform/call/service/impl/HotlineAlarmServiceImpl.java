package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.CrmCallHotlineAlarmRule;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.TicketSatisfaction;
import com.goclouds.crm.platform.call.domain.hotline.*;
import com.goclouds.crm.platform.call.domain.vo.statis.*;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordEsResult;
import com.goclouds.crm.platform.call.service.CrmCallHotlineAlarmRuleService;
import com.goclouds.crm.platform.call.service.HotlineAlarmService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.constant.RabbitMqConstants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.domain.message.SendEmailParams;
import com.goclouds.crm.platform.common.enums.EmailTemplateUrl;
import com.goclouds.crm.platform.common.utils.DateUtils;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.system.CompanyClient;
import com.goclouds.crm.platform.openfeignClient.client.system.UserClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysCompanyVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.UserDetailsVO;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.TimeZoneUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sunlinan
 * @Description: 告警，时间条件选择：当天，所有要告警的内容，都不考虑其他下拉筛选条件
 * @Date: 2025-02-06 19:08
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class HotlineAlarmServiceImpl implements HotlineAlarmService {
    @Autowired
    private ResourceLoader resourceLoader;
    @Autowired
    private ConnectReportServiceImpl connectReportServiceImpl;
    @Autowired
    private CrmCallHotlineAlarmRuleService callHotlineAlarmRuleService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    private final RestHighLevelClient restHighLevelClient;
    private final UserClient userClient;
    private final CompanyClient companyClient;

    private static final String contactDetailsIndexPrefix = "kinesis_contact_details_";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS);
    //呼入
    private static final String INBOUND = "INBOUND";
    //呼出
    private static final String OUTBOUND = "OUTBOUND";

    @Override
    public void hotlineAlarm() {
        //查询数据库热线指标告警规则表中所有的公司
        List<CrmCallHotlineAlarmRule> callHotlineAlarmRuleList = queryCallHotlineAlarmRule();
        if (CollectionUtils.isNotEmpty(callHotlineAlarmRuleList)) {
            for (CrmCallHotlineAlarmRule callHotlineAlarmRule : callHotlineAlarmRuleList) {
                String companyId = callHotlineAlarmRule.getCompanyId();
                String ruleContent = callHotlineAlarmRule.getRuleContent();
                String userIds = callHotlineAlarmRule.getUserId();
                //ruleContent和userId都不为空，才走告警
                if (StringUtil.isEmpty(ruleContent) || StringUtil.isEmpty(userIds)) {
                    continue;
                }
                //颜色配置规则
                HotlineKeyIndicatorRule hotlineKeyIndicatorRule = queryHotlineRule(ruleContent);
                if (null == hotlineKeyIndicatorRule) {
                    continue;
                }
                JSONArray jsonArray = new JSONArray();
                try {
                    jsonArray = queryAllAlarmData(companyId, hotlineKeyIndicatorRule);
                } catch (Exception e) {
                    log.error("查询公司Id为{}的热线指标告警数据出现异常：", companyId, e);
                }
                if (CollectionUtils.isEmpty(jsonArray)) {
                    continue;
                }
                //获取公司名称
                String companyName = "";
                R<SysCompanyVo> sysCompanyVoR = companyClient.queryCompanyById(companyId);
                if (AjaxResult.SUCCESS == sysCompanyVoR.getCode()) {
                    SysCompanyVo sysCompanyVo = sysCompanyVoR.getData();
                    companyName = sysCompanyVo.getCompanyName();
                }
                try {
                    handleAlarmEmailData(jsonArray, userIds, companyName);
                } catch (Exception e) {
                    log.error("发送公司Id为{}的热线指标告警邮件出现异常：", companyId, e);
                }
            }
        }
    }

    //查询告警数据
    //告警数据中拼接的内容是按照邮件模板来的，会涉及到一些html标签的拼接
    private JSONArray queryAllAlarmData(String companyId, HotlineKeyIndicatorRule hotlineRule) {
        //这个JSONArray用于存储告警信息
        JSONArray alarmJsonArray = new JSONArray();
        //由于只查询「当天」的数据进行告警，所以在此处，提前处理好时间条件
        Map<String, String> timeResultMap = handleTimeQueryCondition(0, null, null);
        String startTime = timeResultMap.get("startTime");
        String endTime = timeResultMap.get("endTime");
        //IVR排队量（当前排队人数 / 当前在线座席人数）
        try {
            queryInboundResult(alarmJsonArray, companyId, hotlineRule);
        } catch (Exception e) {
            log.error("查询公司Id为{}的热线指标告警数据(IVR排队量)出现异常：", companyId, e);

        }
        //平均呼入排队时长
        try {
            queryAvgInboundQueueTime(alarmJsonArray, startTime, endTime, companyId, hotlineRule);
        } catch (Exception e) {
            log.error("查询公司Id为{}的热线指标告警数据(平均呼入排队时长)出现异常：", companyId, e);

        }
        //呼入量（判断是否激增）
        try {
            queryTotalCallsResult(alarmJsonArray, companyId, hotlineRule);
        } catch (Exception e) {
            log.error("查询公司Id为{}的热线指标告警数据(呼入量激增情况)出现异常：", companyId, e);
        }
        //接通率
        try {
            queryConnectionRateResult(alarmJsonArray, startTime, endTime, companyId, hotlineRule);
        } catch (Exception e) {
            log.error("查询公司Id为{}的热线指标告警数据(接通率)出现异常：", companyId, e);
        }
        //ACW时间
        try {
            queryAcwTime(alarmJsonArray, startTime, endTime, companyId, hotlineRule);
        } catch (Exception e) {
            log.error("查询公司Id为{}的热线指标告警数据(ACW时间)出现异常：", companyId, e);
        }
        //呼损率
        try {
            queryCallLossResult(alarmJsonArray, startTime, endTime, companyId, hotlineRule);
        } catch (Exception e) {
            log.error("查询公司Id为{}的热线指标告警数据(呼损率)出现异常：", companyId, e);
        }
        //满意度评分
        try {
            queryAvgSatisfactionResult(alarmJsonArray, startTime, endTime, companyId, hotlineRule);
        } catch (Exception e) {
            log.error("查询公司Id为{}的热线指标告警数据(满意度评分)出现异常：", companyId, e);
        }
        //其他配置
        try {
            queryOtherSetting(alarmJsonArray, startTime, endTime, companyId, hotlineRule);
        } catch (Exception e) {
            log.error("查询公司Id为{}的热线指标告警数据(其他配置)出现异常：", companyId, e);
        }
        return alarmJsonArray;
    }

    private void handleAlarmEmailData(JSONArray jsonArray, String userIds, String companyName) {
        //拼接邮件告警信息
        StringBuilder alarmContent = new StringBuilder();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String tipsContent = jsonObject.get("tipsContent").toString();
            alarmContent.append("<li><p><b>").append(tipsContent).append("</p></li>");
        }

        //根据userIds查询邮箱
        R<List<UserDetailsVO>> listR = userClient.queryUserEmailByUserIds(userIds);
        if (AjaxResult.SUCCESS == listR.getCode()) {
            List<UserDetailsVO> emailList = listR.getData();
            if (CollectionUtils.isNotEmpty(emailList)) {
                sendHotlineAlarmEmail(alarmContent, emailList, companyName);
            }
        }
    }

    private void sendHotlineAlarmEmail(StringBuilder alarmContent, List<UserDetailsVO> userDetailsVOList, String companyName) {
        for (UserDetailsVO userDetailsVO : userDetailsVOList) {
            String email = userDetailsVO.getEmail();
            SendEmailParams sendEmailParams = new SendEmailParams()
                    .setDesAddress(email)
                    .setMsgSubject("ConnectNow热线指标告警通知")
                    .setType(1);
            sendEmailParams.setTemplateUrl(EmailTemplateUrl.HOTLINE_ALARM.getTemplateUrl());

            Map<String, Object> dataMap = new HashMap<>();
            //如下设置的userEmail内容可能不会用到，但是不能删除
            dataMap.put("userEmail", "");
            dataMap.put("alarmContent", alarmContent);
            dataMap.put("customerCompanyName", companyName);
            sendEmailParams.setDataMap(dataMap);
            log.info("准备给用户{}发送热线指标告警邮件，告警拼接内容是：{}", email, alarmContent);
            // 发送邮箱
            rabbitTemplate.convertAndSend(RabbitMqConstants.AUTH_EXCHANGE,
                    RabbitMqConstants.REGISTER_SEND_EMAIL_CAPTCHA_ROUTING_KEY, JSON.toJSONString(sendEmailParams));
        }
    }

    //告警规则
    private HotlineKeyIndicatorRule queryHotlineRule(String ruleContent) {
        HotlineKeyIndicatorRule hotlineKeyIndicatorRule = new HotlineKeyIndicatorRule();
        //如果数据库中的数据还是为空，就用默认的返回
        try {
            if (StringUtil.isNotEmpty(ruleContent)) {
                hotlineKeyIndicatorRule = JSONUtil.toBean(ruleContent, HotlineKeyIndicatorRule.class);
            } else {
                //设置一个默认的配置(如果查询的时候为空，就用这个默认的)
                String defaultHotlineKeyIndicatorRule = readJsonFileAsString();
                if (StringUtil.isNotEmpty(defaultHotlineKeyIndicatorRule)) {
                    hotlineKeyIndicatorRule = JSON.parseObject(defaultHotlineKeyIndicatorRule, HotlineKeyIndicatorRule.class);
                }
            }
        } catch (Exception e) {
            log.error("将热线指标看板规则json转化为实体的过程中出现异常：", e);
            return null;
        }
        return hotlineKeyIndicatorRule;
    }

    //IVR排队量-IVR排队
    private void queryInboundResult(JSONArray alarmJsonArray, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        //在线座席数量，用队列实时指标中的坐席状态列表-在线，代码参考：queryQueueRealTimeIndex
        List<ConnectQueueLatitudeAgentVo> filteredAgentList = connectReportServiceImpl.publicQueryQueueRealTimeIndex(companyId);
        //在线座席数量
        Integer onlineAgentNum = 0;
        if (CollectionUtils.isNotEmpty(filteredAgentList)) {
            onlineAgentNum = filteredAgentList.stream().mapToInt(vo -> vo.getOnlineQuantity() == null ? 0 : vo.getOnlineQuantity()).sum();
        }
        //正在排队数量（参考实时队列指标-性能列表-已排队）
        List<ConnectQueueLatitudePropertyVo> filteredList = connectReportServiceImpl.publicQueryQueuePropertyIndex(companyId);

        //正在排队数量(只有这个需要返回颜色)
        Integer contactsQueued = 0;
        if (CollectionUtils.isNotEmpty(filteredList)) {
            contactsQueued = filteredList.stream().mapToInt(vo -> vo.getContactsQueued() == null ? 0 : vo.getContactsQueued()).sum();
        }
        List<ColorRange> colorRangeList = hotlineRule.getIvr().getIvrQueue();
        //处理正在排队数量
        //如果被除数不为0，才进行颜色的处理
        if (onlineAgentNum != 0) {
            double ratio = (double) contactsQueued / onlineAgentNum;
            Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio);
            if (threshold != null) {
                //如果是告警色，进行处理
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("alarmTypeName", "IVR排队");
                jsonObject.put("totalNum", onlineAgentNum);
                jsonObject.put("num", contactsQueued);
                jsonObject.put("threshold", threshold);
                jsonObject.put("currentResult", formatDouble(ratio));
//                jsonObject.put("tipsContent", "当前IVR为：" + formatDouble(ratio) + "倍，告警阈值为" + formatDouble(threshold) + "倍");
                jsonObject.put("tipsContent", "当前<b>IVR</b>为：<span>" + formatDouble(ratio) + "倍</span>，告警阈值为" + formatDouble(threshold) + "倍");
                alarmJsonArray.add(jsonObject);
            }
        }
    }

    //平均呼入排队时长
    private void queryAvgInboundQueueTime(JSONArray alarmJsonArray, String startTime, String endTime, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        //平均呼入排队时长：
        List<ColorRange> avgInboundQueueTimeColorList = hotlineRule.getIvr().getAvgInboundQueueTime();
        calculateDiffAvgTime(INBOUND, "queueWaitTime", avgInboundQueueTimeColorList, startTime, endTime, alarmJsonArray, companyId);
    }

    //呼入量（判断是否激增）
    private void queryTotalCallsResult(JSONArray alarmJsonArray, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        //如下关于判断是否激增的查询统计逻辑，不受上述时间条件的控制，是独立的判断方式
        //用如下时间条件，判断近24小时呼入量 是否 比过去24小时呼入量激增 (关于激增的判断，不用考虑下拉筛选条件，只考虑如下计算的时间条件)
        String currentTime = getHoursAgo(0);
        String hour24Ago = getHoursAgo(24);
        String hour48Ago = getHoursAgo(48);

        //获取近24小时呼入量
        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        boolQueryBuilder1.must(QueryBuilders.termQuery("incomingOutgoing", INBOUND));
        boolQueryBuilder1.must(QueryBuilders.rangeQuery("createTime").gte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, hour24Ago)).lte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, currentTime)));
        List<KinesisContactDetailsVo> kinesisContactVo24List = querySpecialContactDetails(boolQueryBuilder1, companyId);

        //获取过去24小时呼入量
        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
        boolQueryBuilder2.must(QueryBuilders.termQuery("incomingOutgoing", INBOUND));
        boolQueryBuilder2.must(QueryBuilders.rangeQuery("createTime").gte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, hour48Ago)).lte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, hour24Ago)));
        List<KinesisContactDetailsVo> kinesisContactVo48List = querySpecialContactDetails(boolQueryBuilder2, companyId);

        Double surgeThreshold = hotlineRule.getInbound().getSurgeThreshold();
        //0-不激增 1-激增
        Integer currentCalls = kinesisContactVo24List.size();
        Integer previousCalls = kinesisContactVo48List.size();
        double ratio = (double) (currentCalls - previousCalls) / previousCalls;
        Integer surge = isSurge(currentCalls, previousCalls, surgeThreshold);
        if (surge == 1) {
            //如果是告警色，进行处理
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("alarmTypeName", "呼入量");
            jsonObject.put("threshold", surgeThreshold);
            jsonObject.put("currentResult", handlePercentage(BigDecimal.valueOf(ratio)));
//            jsonObject.put("tipsContent", "近24小时呼入量比过去24小时呼入量激增" + handlePercentage(BigDecimal.valueOf(ratio)) + "%，告警阈值为" + surgeThreshold + "%");
            jsonObject.put("tipsContent", "<b>近24小时呼入量比过去24小时呼入量激增</b>：<span>" + handlePercentage(BigDecimal.valueOf(ratio)) + "%</span>，告警阈值为" + surgeThreshold + "%");

            alarmJsonArray.add(jsonObject);
        }
    }

    //接通率
    private void queryConnectionRateResult(JSONArray alarmJsonArray, String startTime, String endTime, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        //如何认定是接通：座席应答时间不为null
        //默认是查询所有（即不区分是呼入还是呼出）
        //0-所有 1-呼入接通率 2-服务时间呼入接通率(待提供上线数据) 3-呼出接通率
        //获取配置颜色
        ConnectionRate connectionRate = hotlineRule.getConnectionRate();
        List<ColorRange> colorRangeList0 = connectionRate.getTotalConnectionRate();
        List<ColorRange> colorRangeList1 = connectionRate.getInboundConnectionRate();
        List<ColorRange> colorRangeList2 = connectionRate.getServiceTimeInboundConnectionRate();
        List<ColorRange> colorRangeList3 = connectionRate.getOutboundConnectionRate();

        List<KinesisContactVo> kinesisContactVoList = queryContactDetails(startTime, endTime, companyId);
        if (CollectionUtils.isNotEmpty(kinesisContactVoList)) {
            List<KinesisContactVo> handleDataList0 = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() != null).collect(Collectors.toList());
            List<KinesisContactVo> handleDataList1 = kinesisContactVoList.stream().filter(vo -> INBOUND.equals(vo.getIncomingOutgoing())).filter(vo -> vo.getCallTime() != null).collect(Collectors.toList());
            List<KinesisContactVo> handleDataList3 = kinesisContactVoList.stream().filter(vo -> OUTBOUND.equals(vo.getIncomingOutgoing())).filter(vo -> vo.getCallTime() != null).collect(Collectors.toList());
            int totalNum = kinesisContactVoList.size();
            handleDiffConnectionRate(alarmJsonArray, totalNum, handleDataList0, colorRangeList0, "总接通率");
            handleDiffConnectionRate(alarmJsonArray, totalNum, handleDataList1, colorRangeList1, "呼入接通率");
            handleDiffConnectionRate(alarmJsonArray, totalNum, handleDataList3, colorRangeList3, "呼出接通率");
        }
    }

    //ACW时间
    private void queryAcwTime(JSONArray alarmJsonArray, String startTime, String endTime, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        //平均呼入ACW时长：
        List<ColorRange> inboundAvgTimeColorList = hotlineRule.getAcw().getInboundAvgTime();
        calculateDiffAvgTime(INBOUND, "acwDuration", inboundAvgTimeColorList, startTime, endTime, alarmJsonArray, companyId);
        //平均呼出ACW时长：
        List<ColorRange> outboundAvgTimeColorList = hotlineRule.getAcw().getOutboundAvgTime();
        calculateDiffAvgTime(OUTBOUND, "acwDuration", outboundAvgTimeColorList, startTime, endTime, alarmJsonArray, companyId);
    }

    //呼损率
    private void queryCallLossResult(JSONArray alarmJsonArray, String startTime, String endTime, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        CallLossRate callLossRate = hotlineRule.getCallLossRate();
        List<KinesisContactVo> kinesisContactVoList = queryContactDetails(startTime, endTime, companyId);
        //如果返回的数据为空，直接返回如下处理结果
        if (CollectionUtils.isNotEmpty(kinesisContactVoList)) {
            //呼叫尝试次数（即呼叫总数）
            int totalNum = kinesisContactVoList.size();
            //未接来电
            handleMissedCall(kinesisContactVoList, callLossRate, totalNum, alarmJsonArray);
            //ivr放弃
            handleIvrAbandon(kinesisContactVoList, callLossRate, totalNum, alarmJsonArray);
            //排队放弃
            handleQueueAbandon(kinesisContactVoList, callLossRate, totalNum, alarmJsonArray);
            //todo 非服务时间呼入（待提供上线数据）
            handleNonServiceTimeInbound(kinesisContactVoList, callLossRate, totalNum, alarmJsonArray);
            //总呼损
            handleTotalCallLoss(kinesisContactVoList, callLossRate, totalNum, alarmJsonArray);
        }
    }

    //满意度评分
    private void queryAvgSatisfactionResult(JSONArray alarmJsonArray, String startTime, String endTime, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        try {
            // 定义工单表索引名称
            String indexName = ticketIndex + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
                return;
            }
        } catch (IOException e) {
            log.error("查询满意度评分，对工单索引进行检查操作时出现异常：", e);
            return;
        }
        handleAvgSatisfactionResult(alarmJsonArray, startTime, endTime, companyId, hotlineRule);
    }

    //其他配置
    private void queryOtherSetting(JSONArray alarmJsonArray, String startTime, String endTime, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        try {
            // 定义工单表索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
                return;
            }
        } catch (IOException e) {
            log.error("热线关键指标看板-业务团队指标接口>>>>>>queryOtherSetting，对联络明细索引进行检查操作时出现异常：", e);
            return;
        }

        //呼入转接率： 呼入(incomingOutgoing) 转接（用isSwitch这个字段）  呼入数量是分母，转接数量是分子
        List<ColorRange> inboundSwitchRateColorList = hotlineRule.getOtherSetting().getInboundSwitchRate();
        calculateDiffRate(INBOUND, "isSwitch", inboundSwitchRateColorList, startTime, endTime, alarmJsonArray, companyId);
        //呼出转接率： 呼出数量是分母，转接数量是分子
        List<ColorRange> outboundSwitchRateColorList = hotlineRule.getOtherSetting().getOutboundSwitchRate();
        calculateDiffRate(OUTBOUND, "isSwitch", outboundSwitchRateColorList, startTime, endTime, alarmJsonArray, companyId);

        //呼入座席挂断率：hangingType : AGENT_DISCONNECT 呼入数量是分母，座席挂断数量是分子
        List<ColorRange> inboundAgentHangUpRateColorList = hotlineRule.getOtherSetting().getInboundAgentHangUpRate();
        calculateDiffRate(INBOUND, "hangingType", inboundAgentHangUpRateColorList, startTime, endTime, alarmJsonArray, companyId);
        //呼出座席挂断率： 呼出数量是分母，座席挂断数量是分子
        List<ColorRange> outboundAgentHangUpRateColorList = hotlineRule.getOtherSetting().getOutboundAgentHangUpRate();
        calculateDiffRate(OUTBOUND, "hangingType", outboundAgentHangUpRateColorList, startTime, endTime, alarmJsonArray, companyId);

        //重复进线率： 也是从字面意思，按照页面上用于时间切换范围来计算（客户电话  重复进线率 >=2）
        List<ColorRange> repeatEntryRateColorList = hotlineRule.getOtherSetting().getRepeatEntryRate();
        List<KinesisContactVo> kinesisContactVoList = queryContactDetails(startTime, endTime, companyId);
        calRepeatEntryRate(kinesisContactVoList, repeatEntryRateColorList, alarmJsonArray);

    }

    //判断数据在哪个区间，返回在告警状态下的阈值
    private Double checkColorRangeAndReturnThreshold(List<ColorRange> colorRangeList, double abc) {
        if (CollectionUtils.isEmpty(colorRangeList)) {
            return null;
        }
        for (ColorRange colorRange : colorRangeList) {
            // 获取范围
            Double startNum = colorRange.getStartNum();
            Double endNum = colorRange.getEndNum();

            // 判断abc是否在当前区间内
            boolean isInRange = abc >= startNum && (endNum == null || abc < endNum);

            if (isInRange && colorRange.getIsAlarmColor() == 1) {
                // 如果isAlarmColor为1，返回相应的值
                if (endNum != null) {
                    return endNum; // 如果endNum不为null，返回endNum
                } else {
                    return startNum; // 如果endNum为null，返回startNum
                }
            }
        }
        // 如果没有找到符合条件的区间，返回null，即未达到告警条件
        return null;
    }

    private void handleDiffConnectionRate(JSONArray alarmJsonArray, int totalNum, List<KinesisContactVo> handleDataList,
                                          List<ColorRange> colorRangeList, String connectionRateName) {
        //如果根据条件筛选之后的结果为空
        if (CollectionUtils.isNotEmpty(handleDataList)) {
            int connectionNum = handleDataList.size();
            //如果根据条件筛选之后的结果不为空
            double ratio = (double) connectionNum / totalNum;
            Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio * 100);
            if (threshold != null) {
                //如果是告警色，进行处理
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("alarmTypeName", "接通率");
                jsonObject.put("totalNum", totalNum);
                jsonObject.put("num", connectionNum);
                jsonObject.put("threshold", threshold);
                jsonObject.put("currentResult", handlePercentage(BigDecimal.valueOf(ratio)));
//                jsonObject.put("tipsContent", "当前" + connectionRateName + "为：" + handlePercentage(BigDecimal.valueOf(ratio)) + "%，告警阈值为" + threshold + "%");
                jsonObject.put("tipsContent", "当前<b>"+connectionRateName+"</b>为：<span>" + handlePercentage(BigDecimal.valueOf(ratio)) + "%</span>，告警阈值为" + formatDouble(threshold) + "%");

                alarmJsonArray.add(jsonObject);
            }
        }
    }

    //读取默认配置规则
    private String readJsonFileAsString() {
        try {
            Resource resource = resourceLoader.getResource("classpath:json/default-hotline-rule.json");
            StringBuilder jsonString = new StringBuilder();
            try (InputStream inputStream = resource.getInputStream(); InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8); BufferedReader bufferedReader = new BufferedReader(reader)) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    jsonString.append(line);
                }
            }
            return jsonString.toString();
        } catch (IOException e) {
            log.error("读取json文件中的默认热线指标配置规则出现异常：{}", e);
            return null;
        }
    }

    //处理时间查询条件
    private Map<String, String> handleTimeQueryCondition(Integer timeRangeCode, String customStartTime, String customEndTime) {
        Map<String, String> resultMap = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();

        LocalDateTime startTime = null;
        LocalDateTime endTime = null;

        //如果自定义时间没有传值，就默认近24小时
        if (StringUtil.isEmpty(customStartTime) || StringUtil.isEmpty(customEndTime)) {
            customStartTime = getHoursAgo(24);
            customEndTime = getHoursAgo(0);
        }

        switch (timeRangeCode) {
            case 0: // 当天
                startTime = now.truncatedTo(ChronoUnit.DAYS);
                endTime = now.truncatedTo(ChronoUnit.DAYS).plusDays(1).minusSeconds(1);
                break;
            case 1: // 近24小时
                startTime = now.minusHours(24);
                endTime = now;
                break;
            case 2: // 近2天
                startTime = now.minusDays(2);
                endTime = now;
                break;
            case 3: // 近3天
                startTime = now.minusDays(3);
                endTime = now;
                break;
            case 4: // 近7天
                startTime = now.minusDays(7);
                endTime = now;
                break;
            case 5: // 近1个月
                startTime = now.minusMonths(1);
                endTime = now;
                break;
            case 6: // 自定义时间
                startTime = LocalDateTime.parse(customStartTime, formatter);
                endTime = LocalDateTime.parse(customEndTime, formatter);
                break;
            default:
                break;
        }
        //将入参时间进行处理，转化为UTC+8，因为es中存储的数据是UTC+8的时间
        if (null != startTime && null != endTime) {
            String formatDateStart = TimeZoneUtils.requestTimeConversion(startTime.format(formatter));
            String formatDateEnd = TimeZoneUtils.requestTimeConversion(endTime.format(formatter));

            resultMap.put("startTime", formatDateStart);
            resultMap.put("endTime", formatDateEnd);
        }
        return resultMap;
    }

    //处理联络明细查询条件
    private BoolQueryBuilder packageContactSearchBuilder(String startTime, String endTime) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 时间范围查询
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("createTime").gte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime)).lte(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime)));
        }
        return boolQueryBuilder;
    }

    //查询联络明细数据
    private List<KinesisContactVo> queryContactDetails(String startTime, String endTime, String companyId) {
        List<KinesisContactVo> recordList = new ArrayList<>();
        try {
            // 定义工单表索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
                return Arrays.asList();
            }
            SearchRequest searchRequest = new SearchRequest(indexName);
            BoolQueryBuilder boolQueryBuilder = packageContactSearchBuilder(startTime, endTime);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            sourceBuilder.size(10000);
//            log.info("热线关键指标看板—ES查询语句，Java代码中的sourceBuilder条件是>>>>>>queryContactDetails>>>>>>{}", printSearchSourceBuilder(sourceBuilder));
            searchRequest.source(sourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            //获取总数量
            long totalCount = searchResponse.getHits().getTotalHits().value;
            if (totalCount > 0) {
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    // 创建 ObjectMapper 实例
                    ObjectMapper objectMapper = new ObjectMapper();
                    // 注册 JavaTimeModule
                    objectMapper.registerModule(new JavaTimeModule());
                    String json = objectMapper.writeValueAsString(source);
                    // 将 JSON 字符串转换为 Java 对象
                    KinesisContactDetailsVo detailsVo = objectMapper.readValue(json, KinesisContactDetailsVo.class);
                    KinesisContactVo contactVo = new KinesisContactVo(detailsVo);
                    recordList.add(contactVo);
                }
            }
        } catch (IOException e) {
            log.error("热线关键指标-查询联络明细数据出现异常");
        }
        return recordList;
    }

    //查询联络明细数据（入参条件不固定，用如下查询方式）
    private List<KinesisContactDetailsVo> querySpecialContactDetails(BoolQueryBuilder boolQueryBuilder, String companyId) {
        List<KinesisContactDetailsVo> recordList = new ArrayList<>();
        try {
            // 定义工单表索引名称
            String indexName = contactDetailsIndexPrefix + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
                return Arrays.asList();
            }
            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            sourceBuilder.size(10000);
            searchRequest.source(sourceBuilder);
//            log.info("热线关键指标看板(入参条件不固定)—ES查询语句，Java代码中的sourceBuilder条件是>>>>>>querySpecialContactDetails>>>>>>{}", printSearchSourceBuilder(sourceBuilder));
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            //获取总数量
            long totalCount = searchResponse.getHits().getTotalHits().value;
            if (totalCount > 0) {
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    // 创建 ObjectMapper 实例
                    ObjectMapper objectMapper = new ObjectMapper();
                    // 注册 JavaTimeModule
                    objectMapper.registerModule(new JavaTimeModule());
                    String json = objectMapper.writeValueAsString(source);
                    // 将 JSON 字符串转换为 Java 对象
                    KinesisContactDetailsVo detailsVo = objectMapper.readValue(json, KinesisContactDetailsVo.class);
                    recordList.add(detailsVo);
                }
            }
        } catch (IOException e) {
            log.error("热线关键指标-查询联络明细数据出现异常");
        }
        return recordList;
    }

    //工单index-部分查询涉及到对比，需要用部分特定的查询条件
    private void packageSpecialTicketSearchBuilder(BoolQueryBuilder boolQueryBuilder, String startTime, String endTime) {
        //只查询真人工单
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));

        // 时间范围查询
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startTime).lte(endTime));
        }

        //除了共有的查询条件，还需要补充的是，满意度数据条件，不为null，且list集合数据size大于0
        //es中将满意度和工单表整到了一起，满意度字段是个集合属性，用如下方式
        //构建第一个 nested 查询：检查 ticket_satisfaction 是否存在
        QueryBuilder firstNestedQuery = QueryBuilders.nestedQuery("ticket_satisfaction", QueryBuilders.existsQuery("ticket_satisfaction"), ScoreMode.None);

        QueryBuilder secondNestedQuery = QueryBuilders.nestedQuery("ticket_satisfaction", QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("ticket_satisfaction.rating")), ScoreMode.None);

        // 组合两个 nested 查询到一个 bool 查询中
        boolQueryBuilder.filter(firstNestedQuery).filter(secondNestedQuery);
    }

    //查询工单数据
    private List<WorkOrderRecordEsResult> queryTickets(String indexName, SearchSourceBuilder searchSourceBuilder) throws Exception {
        SearchRequest searchRequest = new SearchRequest(indexName);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
//        log.info("热线关键指标看板(查询满意度平均分)—ES查询语句，Java代码中的sourceBuilder条件是>>>>>>queryTickets>>>>>>{}", printSearchSourceBuilder(searchSourceBuilder));
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        List<WorkOrderRecordEsResult> recordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult workOrderRecordEsResult = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, workOrderRecordEsResult);
                recordList.add(workOrderRecordEsResult);
            }
        }
        return recordList;
    }

    /**
     * 查询索引是否存在
     *
     * @param index 索引名称
     * @return
     * @throws IOException
     */
    private boolean headIndexExists(String index) throws IOException {
        GetIndexRequest req = new GetIndexRequest(index);
        boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
        log.info("当前索引{}, 是否存在: {}", index, exists);
        return exists;
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        } catch (Exception e) {
            log.error("创建索引报错", e);
        }
    }

    //获取当前时间开始，指定数量小时之前的时间(并转时区)
    private static String getHoursAgo(long amountToSubtract) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime hoursAgo = now.minus(amountToSubtract, ChronoUnit.HOURS);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String format = formatter.format(hoursAgo);
        //转时区
        String formatDateStart = TimeZoneUtils.requestTimeConversion(format);
        return formatDateStart;
    }

    //判断是否展示呼入量激增 0-不展示 1-展示
//入参的surgePercentage是阈值，表示一个数字，比如120，代表的是120%
    private Integer isSurge(int currentCalls, int previousCalls, double surgePercentage) {
        if (previousCalls <= 0) {
            return 0;
        }
        //和设定的阈值比较
        boolean b = (double) (currentCalls - previousCalls) / previousCalls >= surgePercentage / 100;
        if (!b) {
            return 0;
        }
        return 1;
    }

    //处理不同的呼损情况-未接来电
    private void handleMissedCall(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                  int totalNum, JSONArray alarmJsonArray) {
        //未接来电：座席应答时间为空，队列等待时间不为空，agentConnectionAttempts大于0
        List<KinesisContactVo> list = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() == null && vo.getQueueWaitTime() != null
                && (vo.getAgentConnectionAttempts() != null && vo.getAgentConnectionAttempts() > 0)).collect(Collectors.toList());
        int num = list.size();
        List<ColorRange> colorRangeList = callLossRate.getMissedCallRate();
        if (num != 0) {
            double ratio = (double) num / totalNum;
            Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio * 100);
            if (threshold != null) {
                //如果是告警色，进行处理
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("alarmTypeName", "呼损率");
                jsonObject.put("totalNum", totalNum);
                jsonObject.put("num", num);
                jsonObject.put("threshold", threshold);
                jsonObject.put("currentResult", handlePercentage(BigDecimal.valueOf(ratio)));
//                jsonObject.put("tipsContent", "当前未接来电率为：" + handlePercentage(BigDecimal.valueOf(ratio)) + "%，告警阈值为" + threshold + "%");
                jsonObject.put("tipsContent", "当前<b>未接来电率</b>为：<span>" + handlePercentage(BigDecimal.valueOf(ratio))+ "%</span>，告警阈值为" + threshold + "%");
                alarmJsonArray.add(jsonObject);
            }
        }
    }

    //处理不同的呼损情况-IVR放弃
    private void handleIvrAbandon(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                  int totalNum, JSONArray alarmJsonArray) {
        //ivr放弃：座席应答时间，队列等待时间都是空
        List<KinesisContactVo> list = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() == null && vo.getQueueWaitTime() == null).collect(Collectors.toList());
        int num = list.size();
        List<ColorRange> colorRangeList = callLossRate.getIvrAbandonRate();
        if (num != 0) {
            double ratio = (double) num / totalNum;
            Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio * 100);
            if (threshold != null) {
                //如果是告警色，进行处理
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("alarmTypeName", "呼损率");
                jsonObject.put("totalNum", totalNum);
                jsonObject.put("num", num);
                jsonObject.put("threshold", threshold);
                jsonObject.put("currentResult", handlePercentage(BigDecimal.valueOf(ratio)));
//                jsonObject.put("tipsContent", "当前IVR放弃率为：" + handlePercentage(BigDecimal.valueOf(ratio)) + "%，告警阈值为" + threshold + "%");
                jsonObject.put("tipsContent", "当前<b>IVR放弃率</b>为：<span>" + handlePercentage(BigDecimal.valueOf(ratio))+ "%</span>，告警阈值为" + threshold + "%");

                alarmJsonArray.add(jsonObject);
            }
        }
    }

    //处理不同的呼损情况-排队放弃
    private void handleQueueAbandon(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                    int totalNum, JSONArray alarmJsonArray) {
        List<KinesisContactVo> list = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() == null && vo.getQueueWaitTime() != null).collect(Collectors.toList());
        int num = list.size();
        List<ColorRange> colorRangeList = callLossRate.getQueueAbandonRate();
        if (num != 0) {
            double ratio = (double) num / totalNum;
            Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio * 100);
            if (threshold != null) {
                //如果是告警色，进行处理
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("alarmTypeName", "呼损率");
                jsonObject.put("totalNum", totalNum);
                jsonObject.put("num", num);
                jsonObject.put("threshold", threshold);
                jsonObject.put("currentResult", handlePercentage(BigDecimal.valueOf(ratio)));
//                jsonObject.put("tipsContent", "当前排队放弃率为：" + handlePercentage(BigDecimal.valueOf(ratio)) + "%，告警阈值为" + threshold + "%");
                jsonObject.put("tipsContent", "当前<b>排队放弃率</b>为：<span>" + handlePercentage(BigDecimal.valueOf(ratio))+ "%</span>，告警阈值为" + threshold + "%");

                alarmJsonArray.add(jsonObject);
            }
        }
    }

    //处理不同的呼损情况-非服务时间呼入
    private void handleNonServiceTimeInbound(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                             int totalNum, JSONArray alarmJsonArray) {
        //todo 待提供上线数据
        List<KinesisContactVo> list = new ArrayList<>();
        int num = list.size();
        List<ColorRange> colorRangeList = callLossRate.getNonServiceTimeInboundRate();
        String xName = MessageUtils.get("hotline.non.service.time.inbound");
        if (num != 0) {
            double ratio = (double) num / totalNum;
            Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio * 100);
            if (threshold != null) {
                //如果是告警色，进行处理
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("alarmTypeName", "呼损率");
                jsonObject.put("totalNum", totalNum);
                jsonObject.put("num", num);
                jsonObject.put("threshold", threshold);
                jsonObject.put("currentResult", handlePercentage(BigDecimal.valueOf(ratio)));
//                jsonObject.put("tipsContent", "当前非服务时间呼入率为：" + handlePercentage(BigDecimal.valueOf(ratio)) + "%，告警阈值为" + threshold + "%");
                jsonObject.put("tipsContent", "当前<b>非服务时间呼入率</b>为：<span>" + handlePercentage(BigDecimal.valueOf(ratio))+ "%</span>，告警阈值为" + threshold + "%");
                alarmJsonArray.add(jsonObject);
            }
        }
    }

    //处理不同的呼损情况-总呼损
    private void handleTotalCallLoss(List<KinesisContactVo> kinesisContactVoList, CallLossRate callLossRate,
                                     int totalNum, JSONArray alarmJsonArray) {
        //总呼损 =（未接通呼数（座席应答时间为null） / 呼叫尝试次数（即呼叫总数））×100%
        //未接通呼数：座席应答时间为null
        List<KinesisContactVo> disconnectedCallList = kinesisContactVoList.stream().filter(vo -> vo.getCallTime() == null).collect(Collectors.toList());
        int num = disconnectedCallList.size();
        List<ColorRange> colorRangeList = callLossRate.getTotalCallLossRate();
        if (num != 0) {
            double ratio = (double) num / totalNum;
            Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio * 100);
            if (threshold != null) {
                //如果是告警色，进行处理
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("alarmTypeName", "呼损率");
                jsonObject.put("totalNum", totalNum);
                jsonObject.put("num", num);
                jsonObject.put("threshold", threshold);
                jsonObject.put("currentResult", handlePercentage(BigDecimal.valueOf(ratio)));
//                jsonObject.put("tipsContent", "当前总呼损率为：" + handlePercentage(BigDecimal.valueOf(ratio)) + "%，告警阈值为" + threshold + "%");
                jsonObject.put("tipsContent", "当前<b>总呼损率</b>为：<span>" + handlePercentage(BigDecimal.valueOf(ratio))+ "%</span>，告警阈值为" + threshold + "%");

                alarmJsonArray.add(jsonObject);
            }
        }
    }

    //从es中查询工单满意度
    private List<SatisfactionResult> querySatisfactionFromEs(BoolQueryBuilder boolQueryBuilder, String companyId) {
        List<SatisfactionResult> recordList = new ArrayList<>();
        try {
            // 定义工单表索引名称
            String indexName = ticketIndex + companyId;

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            // 执行查询
            List<WorkOrderRecordEsResult> esResultList = queryTickets(indexName, sourceBuilder);
            //处理返回值数据中的满意度评分数据（按照满意度评分的总数据，进行返回值集合的组装，便于后续分组计算）
            if (CollectionUtils.isNotEmpty(esResultList)) {
                for (WorkOrderRecordEsResult workOrderRecordEsResult : esResultList) {
                    List<TicketSatisfaction> ticketSatisfactionList = workOrderRecordEsResult.getTicketSatisfaction();
                    for (TicketSatisfaction ticketSatisfaction : ticketSatisfactionList) {
                        SatisfactionResult satisfactionResult = new SatisfactionResult();
                        satisfactionResult.setDeptId(workOrderRecordEsResult.getDeptId());
                        satisfactionResult.setAgentId(workOrderRecordEsResult.getAgentId());
                        satisfactionResult.setAgentName(workOrderRecordEsResult.getAgentName());
                        satisfactionResult.setChannelTypeId(workOrderRecordEsResult.getChannelTypeId());
                        satisfactionResult.setChannelTypeName(workOrderRecordEsResult.getChannelTypeName());
                        satisfactionResult.setWorkRecordTypeCode(workOrderRecordEsResult.getWorkRecordTypeCode());
                        satisfactionResult.setWorkRecordTypeName(workOrderRecordEsResult.getWorkRecordTypeName());
                        satisfactionResult.setRating(new BigDecimal(ticketSatisfaction.getRating()));
                        satisfactionResult.setCreateTime(workOrderRecordEsResult.getCreateTime());
                        recordList.add(satisfactionResult);
                    }
                }
            }
        } catch (Exception e) {
            log.error("热线关键指标看板-满意度平均分-查询工单出现异常：", e);
        }
        return recordList;
    }

    private void handleAvgSatisfactionResult(JSONArray alarmJsonArray, String startTime, String endTime, String companyId, HotlineKeyIndicatorRule hotlineRule) {
        //0-所有 1-呼入满意度 2-呼出满意度
        calDiffTypeSatisfaction(alarmJsonArray, startTime, endTime, companyId, 0, hotlineRule);
        calDiffTypeSatisfaction(alarmJsonArray, startTime, endTime, companyId, 1, hotlineRule);
        calDiffTypeSatisfaction(alarmJsonArray, startTime, endTime, companyId, 2, hotlineRule);
    }

    private void calDiffTypeSatisfaction(JSONArray alarmJsonArray, String startTime, String endTime,
                                         String companyId, Integer showType, HotlineKeyIndicatorRule hotlineRule) {
        //处理返回值颜色
        Satisfaction satisfaction = hotlineRule.getSatisfaction();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        String incomingOutgoing;
        List<ColorRange> colorRangeList;
        if (1 == showType) {
            boolQueryBuilder.must(QueryBuilders.termQuery("call_type", INBOUND));
            incomingOutgoing = "呼入满意度平均分";
            colorRangeList = satisfaction.getInboundSatisfactionAvg();
        } else if (2 == showType) {
            boolQueryBuilder.must(QueryBuilders.termQuery("call_type", OUTBOUND));
            incomingOutgoing = "呼出满意度平均分";
            colorRangeList = satisfaction.getOutboundSatisfactionAvg();
        } else {
            incomingOutgoing = "满意度平均分";
            colorRangeList = satisfaction.getSatisfactionAvg();
        }
        packageSpecialTicketSearchBuilder(boolQueryBuilder, startTime, endTime);
        //没有工单评分数据的时候，计算出来的满意度平均分也会是0，这种情况，不参与告警
        BigDecimal bigDecimal = calculateAvgSatisfaction(boolQueryBuilder, companyId);
        Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, bigDecimal.doubleValue());
        if (threshold != null && !bigDecimal.equals(BigDecimal.ZERO)) {
            //如果是告警色，进行处理
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("alarmTypeName", "满意度平均分");
            jsonObject.put("threshold", threshold);
            jsonObject.put("num", bigDecimal);
            jsonObject.put("currentResult", formatDouble(bigDecimal.doubleValue()));
//            jsonObject.put("tipsContent", "当前" + incomingOutgoing + "为：" + formatDouble(bigDecimal.doubleValue()) + "分，告警阈值为" + threshold + "分");
            jsonObject.put("tipsContent", "当前<b>"+incomingOutgoing+"</b>为：<span>" + formatDouble(bigDecimal.doubleValue()) + "分</span>，告警阈值为" + threshold + "分");

            alarmJsonArray.add(jsonObject);
        }
    }

    //根据条件查询数据，返回部分参数，用返回值是否为null进行下一步的处理，在现有部分数据的基础上，补充剩余数据
    private BigDecimal calculateAvgSatisfaction(BoolQueryBuilder queryBuilder, String companyId) {
        List<SatisfactionResult> satisfactionResultList = querySatisfactionFromEs(queryBuilder, companyId);
        if (CollectionUtils.isEmpty(satisfactionResultList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = satisfactionResultList.stream().map(SatisfactionResult::getRating).reduce(BigDecimal.ZERO, BigDecimal::add);
        int count = satisfactionResultList.size();
        BigDecimal average = count == 0 ? BigDecimal.ZERO : sum.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP);
        return average;
    }

    //处理比率类，返回值格式，转换为百分比并保留两位小数
    private static BigDecimal handlePercentage(BigDecimal ratioBD) {
        // Multiply by 100 to get percentage
        BigDecimal percentage = ratioBD.multiply(BigDecimal.valueOf(100));

        // Round to two decimal places using HALF_UP rounding
        return percentage.setScale(2, RoundingMode.HALF_UP);
    }

    private static BigDecimal convertToDoubleWithPrecision(BigDecimal bigDecimal, int scale) {
        if (bigDecimal == null) {
            return BigDecimal.ZERO; // 处理空值情况
        }
        //使用四舍五入模式
        return bigDecimal.setScale(scale, RoundingMode.HALF_UP);
    }

    //处理SearchSourceBuilder 中的查询条件，方便日志打印
    private String printSearchSourceBuilder(SearchSourceBuilder sourceBuilder) {
        StringWriter writer;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            writer = new StringWriter();
            objectMapper.writeValue(writer, sourceBuilder.toString());
            return writer.toString();
        } catch (IOException e) {
            log.error("处理打印 SearchSourceBuilder 中的查询条件出现异常：", e);
        }
        return "";
    }

    //将数据进行千分制格式化
    private String formatDouble(Double num) {
        if (num == null) {
            return "0.00"; // Handle null input
        }
        return String.format(Locale.getDefault(), "%,.2f", num);
    }

    //将数据进行千分制格式化
    private String formatInteger(Integer num) {
        if (num == null) {
            return "0"; // Handle null input
        }
        return String.format(Locale.getDefault(), "%,d", num);
    }

    //计算不同维度的平均时长，然后对比
    private void calculateDiffAvgTime(String incomingOutgoing, String diffField, List<ColorRange> colorRangeList, String startTime,
                                      String endTime, JSONArray alarmJsonArray, String companyId) {
        BoolQueryBuilder boolQueryBuilder = packageContactSearchBuilder(startTime, endTime);
        //时间，单位是秒
        Double num = queryDiffAvgTime(boolQueryBuilder, incomingOutgoing, diffField, companyId);

        String incomingOutgoingName;
        if (INBOUND.equals(incomingOutgoing)) {
            incomingOutgoingName = "呼入";
        } else {
            incomingOutgoingName = "呼出";
        }
        String diffFieldName = "";
        if ("totalTime".equals(diffField)) {
            diffFieldName = "总";
        } else if ("interactionTime".equals(diffField)) {
            diffFieldName = "互动";
        } else if ("acwDuration".equals(diffField)) {
            diffFieldName = "ACW";
        } else if ("queueWaitTime".equals(diffField)) {
            diffFieldName = "排队";
        }
        Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, num);
        if (threshold != null) {
            //如果是告警色，进行处理
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("alarmTypeName", "ACW时长");
            jsonObject.put("threshold", threshold);
            jsonObject.put("currentResult", formatDouble(num));
//            jsonObject.put("tipsContent", "当前平均" + incomingOutgoingName + diffFieldName + "时长为：" + formatDouble(num) + "秒，告警阈值为" + formatDouble(threshold) + "秒");
            jsonObject.put("tipsContent", "当前<b>平均" + incomingOutgoingName + diffFieldName + "时长</b>为：<span>" + formatDouble(num) + "秒</span>，告警阈值为" + formatDouble(threshold) + "秒");

            alarmJsonArray.add(jsonObject);
        }
    }

    //计算不同维度的平均时长
    private Double queryDiffAvgTime(BoolQueryBuilder boolQueryBuilder, String incomingOutgoing, String diffField, String companyId) {
        // 定义工单表索引名称
        String indexName = contactDetailsIndexPrefix + companyId;
        // 创建 SearchRequest 对象
        SearchRequest searchRequest = new SearchRequest(indexName);
        // 创建 SearchSourceBuilder 对象
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 添加 incomingOutgoing 字段为 INBOUND 的查询条件
        if (StringUtil.isNotEmpty(incomingOutgoing)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("incomingOutgoing", incomingOutgoing));
        }
        // 添加字段存在(不为空)的查询条件
        boolQueryBuilder.must(QueryBuilders.existsQuery(diffField));
        // 将查询条件添加到 SearchSourceBuilder 对象
        searchSourceBuilder.query(boolQueryBuilder);
        // 创建平均值聚合，计算 入参字段 的平均值
        AvgAggregationBuilder avgAggregation = AggregationBuilders.avg("avg").field(diffField);
        // 将聚合添加到查询中
        searchSourceBuilder.aggregation(avgAggregation);
        // 将 SearchSourceBuilder 对象添加到 SearchRequest 对象
        searchRequest.source(searchSourceBuilder);
        // 执行查询
        SearchResponse searchResponse;
        Double avg = 0.0;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            // 获取聚合结果
            Avg avgResult = searchResponse.getAggregations().get("avg");
            if (avgResult != null) {
                Double value = avgResult.getValue();
                if (value != null && !Double.isNaN(value) && !Double.isInfinite(value)) {
                    avg = value;
                } else {
                    avg = 0.0; // Handle null, NaN, or Infinity
                }
            }
        } catch (IOException e) {
            log.error("热线关键指标-团队业务指标-平均{},{},时长-查询es出现异常：", incomingOutgoing, diffField, e);
            //如果报错，认为算出来的数据是0
            return avg;
        }
        return avg;
    }

    //计算不同维度的比率
    private void calculateDiffRate(String incomingOutgoing, String diffField, List<ColorRange> colorRangeList,
                                   String startTime, String endTime, JSONArray alarmJsonArray, String companyId) {

        BoolQueryBuilder boolQueryBuilder = packageContactSearchBuilder(startTime, endTime);
        // 添加 incomingOutgoing 字段为 INBOUND 的查询条件
        boolQueryBuilder.must(QueryBuilders.matchQuery("incomingOutgoing", incomingOutgoing));
        List<KinesisContactDetailsVo> kinesisContactDetailsVoList = querySpecialContactDetails(boolQueryBuilder, companyId);
        if (CollectionUtils.isEmpty(kinesisContactDetailsVoList)) {
            return;
        }
        List<KinesisContactDetailsVo> filterList = new ArrayList<>();
        if ("isSwitch".equals(diffField)) {
            //是否为转接 0否， 1是
            //筛选转接了的
            filterList = kinesisContactDetailsVoList.stream().filter(vo -> "1".equals(vo.getIsSwitch())).collect(Collectors.toList());
        } else if ("hangingType".equals(diffField)) {
            //筛选座席挂断 （如果是客户挂断，是CUSTOMER_DISCONNECT）
            filterList = kinesisContactDetailsVoList.stream().filter(vo -> "AGENT_DISCONNECT".equals(vo.getHangingType())).collect(Collectors.toList());
        }
        int num = filterList.size();
        int totalNum = kinesisContactDetailsVoList.size();
        double ratio = (double) num / totalNum;

        String incomingOutgoingName;
        if (INBOUND.equals(incomingOutgoing)) {
            incomingOutgoingName = "呼入";
        } else {
            incomingOutgoingName = "呼出";
        }
        String diffFieldName = "";
        if ("isSwitch".equals(diffField)) {
            diffFieldName = "转接";
        } else if ("hangingType".equals(diffField)) {
            diffFieldName = "座席挂断";
        }
        Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio * 100);
        if (threshold != null) {
            BigDecimal currentResult = handlePercentage(convertToDoubleWithPrecision(BigDecimal.valueOf(ratio), 4));
            //如果是告警色，进行处理
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("alarmTypeName", "其他配置");
            jsonObject.put("totalNum", totalNum);
            jsonObject.put("num", num);
            jsonObject.put("threshold", threshold);
            jsonObject.put("currentResult", currentResult);
//            jsonObject.put("tipsContent", "当前" + incomingOutgoingName + diffFieldName + "率为：" + currentResult + "%，告警阈值为" + threshold + "%");
            jsonObject.put("tipsContent", "当前<b>"+ incomingOutgoingName + diffFieldName +"率</b>为：<span>" + currentResult + "%</span>，告警阈值为" + threshold + "%");

            alarmJsonArray.add(jsonObject);
        }
    }

    //计算重复进线率
    private void calRepeatEntryRate(List<KinesisContactVo> kinesisContactVoList, List<ColorRange> colorRangeList, JSONArray alarmJsonArray) {
        if (CollectionUtils.isEmpty(kinesisContactVoList)) {
            return;
        }
        //总数量是分母
        int totalNum = kinesisContactVoList.size();
        //统计重复进线个数
        List<KinesisContactVo> filterDuplicatePhoneList = filterDuplicatePhoneList(kinesisContactVoList);
        int num = filterDuplicatePhoneList.size();
        double ratio = (double) num / totalNum;
        Double threshold = checkColorRangeAndReturnThreshold(colorRangeList, ratio * 100);
        if (threshold != null) {
            BigDecimal currentResult = handlePercentage(convertToDoubleWithPrecision(BigDecimal.valueOf(ratio), 4));
            //如果是告警色，进行处理
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("alarmTypeName", "其他配置");
            jsonObject.put("totalNum", totalNum);
            jsonObject.put("num", num);
            jsonObject.put("threshold", threshold);
            jsonObject.put("currentResult", currentResult);
//            jsonObject.put("tipsContent", "当前重复进线率为：" + currentResult + "%，告警阈值为" + threshold + "%");
            jsonObject.put("tipsContent", "当前<b>重复进线率</b>为：<span>" + currentResult + "%</span>，告警阈值为" + threshold + "%");

            alarmJsonArray.add(jsonObject);
        }
    }

    private List<KinesisContactVo> filterDuplicatePhoneList(List<KinesisContactVo> kinesisContactVoList) {
        // 使用 Collectors.groupingBy 将列表按 customerPhone 分组，并计算每个电话号码出现的次数，忽略null和空字符串
        Map<String, Long> phoneCounts = kinesisContactVoList.stream()
                .filter(vo -> vo.getCustomerPhone() != null && !vo.getCustomerPhone().isEmpty()) //过滤掉null和空字符串
                .collect(Collectors.groupingBy(KinesisContactVo::getCustomerPhone, Collectors.counting()));

        // 筛选出出现次数大于等于 2 的电话号码，并再次过滤掉null和空字符串，出现次数大于等于 2 的 KinesisContactVo 对象，结果收集到一个新的列表中
        return kinesisContactVoList.stream()
                .filter(vo -> vo.getCustomerPhone() != null && !vo.getCustomerPhone().isEmpty()) //过滤掉null和空字符串
                .filter(vo -> phoneCounts.get(vo.getCustomerPhone()) >= 2)
                .collect(Collectors.toList());
    }

    //查询所有告警规则
    private List<CrmCallHotlineAlarmRule> queryCallHotlineAlarmRule() {
        List<CrmCallHotlineAlarmRule> list = callHotlineAlarmRuleService.list(new QueryWrapper<CrmCallHotlineAlarmRule>().lambda()
                .eq(CrmCallHotlineAlarmRule::getDataStatus, Constants.NORMAL)
        );
        return list;
    }
}
