package com.goclouds.crm.platform.call.mapper;

import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordAutoMerge;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordAutoMergeVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_auto_merge(是否开启合并工单表)】的数据库操作Mapper
* @createDate 2024-02-20 14:17:14
* @Entity com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordAutoMerge
*/
public interface CrmAgentWorkRecordAutoMergeMapper extends BaseMapper<CrmAgentWorkRecordAutoMerge> {

    List<WorkRecordAutoMergeVo> queryAutoMergeSetting(String companyId);
}




