package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmCallAgentStatusDef;
import com.goclouds.crm.platform.call.domain.vo.AgentStatusVO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
 * <AUTHOR>
 * @description 针对表【crm_call_agent_status_def(座席状态管理表)】的数据库操作Service
 * @createDate 2025-03-10 15:08:34
 */
public interface CrmCallAgentStatusDefService extends IService<CrmCallAgentStatusDef> {

    AjaxResult addOrUpdateAgentStatus(AgentStatusVO agentStatusVO);

    AjaxResult agentStatusDel(String agentStatusId);

    IPage<CrmCallAgentStatusDef> queryAgentStatus(IPage<CrmCallAgentStatusDef> pageParam);

    void init();
}
