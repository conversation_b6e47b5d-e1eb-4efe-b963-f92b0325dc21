package com.goclouds.crm.platform.call.processer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExtDef;
import com.goclouds.crm.platform.call.domain.smartFill.AigcTicketSmartFillDTO;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordExtDefService;
import com.goclouds.crm.platform.call.strategy.AigcTicketSmartFillEnum;
import com.goclouds.crm.platform.call.strategy.AigcTicketSmartFillStrategy;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> pengliang.sun
 * @description : 策略实现
 */
@Slf4j
@Service
public class AddWorkOrderTicketSmartFillProcesser implements AigcTicketSmartFillStrategy {

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private CrmAgentWorkRecordExtDefService crmAgentWorkRecordExtDefService;

    @Override
    public AigcTicketSmartFillEnum aigcTicketSmartFillEnum() {
        return AigcTicketSmartFillEnum.WORK_ORDER;
    }


    @Override
    public void addAigcTicketSmartFillStrategy(AigcTicketSmartFillDTO aigcTicketSmartFillDetail,
                                               String customerId, String workRecordId) {
        LambdaQueryWrapper<CrmAgentWorkRecordExtDef> crmAgentWorkRecordExtDefLambdaQueryWrapper = new LambdaQueryWrapper<>();
        crmAgentWorkRecordExtDefLambdaQueryWrapper.eq(CrmAgentWorkRecordExtDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId());
        crmAgentWorkRecordExtDefLambdaQueryWrapper.eq(CrmAgentWorkRecordExtDef::getWorkRecordExtDefCode, aigcTicketSmartFillDetail.getStoreAttr());
        List<CrmAgentWorkRecordExtDef> list = crmAgentWorkRecordExtDefService.list(crmAgentWorkRecordExtDefLambdaQueryWrapper);
        log.info("保存智能填单策略实现-》查询工单配置信息为：【{}】", JSONObject.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 定义索引名称
        String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("work_record_id", workRecordId));
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.trackTotalHits(true).size(1000);
        request.source(searchSourceBuilder);
        // 进行查询
        String docId;
        try {
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            docId = hits[0].getId();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Integer isSystemDefault = list.get(0).getIsSystemDefault();
        // 说明是默认字段
        if (isSystemDefault == 1) {
            Map<String, Object> jsonMap = new HashMap<>();
            String storeAttrEs = toSnakeCase(aigcTicketSmartFillDetail.getStoreAttr());
            jsonMap.put(storeAttrEs, aigcTicketSmartFillDetail.getAttrValue());
            try {
                UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(indexName);
                updateRequest.setQuery(QueryBuilders.termQuery("_id", docId));
                updateRequest.setRefresh(true);
                StringBuilder scriptBuilder = new StringBuilder();
                Map<String, Object> params = new HashMap<>();
                int paramIndex = 1;
                for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
                    String paramName = "val" + paramIndex++;
                    scriptBuilder.append("ctx._source.")
                            .append(entry.getKey())
                            .append("=params.")
                            .append(paramName)
                            .append(";");
                    params.put(paramName, entry.getValue());
                }
                updateRequest.setScript(new Script(
                        ScriptType.INLINE,
                        "painless",
                        scriptBuilder.toString(),
                        params
                ));
                restHighLevelClient.updateByQuery(updateRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                log.error("更新异常：【{}】", e);
                throw new RuntimeException(e);
            }
            return;
        }
        String storeAttrEs = aigcTicketSmartFillDetail.getStoreAttr();
        updateOrAddTicketExtWithScript(indexName,docId,storeAttrEs,aigcTicketSmartFillDetail.getAttrValue());
    }

    private String toSnakeCase(String camelCase) {
        StringBuilder result = new StringBuilder();
        for (char c : camelCase.toCharArray()) {
            if (Character.isUpperCase(c)) {
                result.append('_');
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    // 将下划线命名转换为驼峰命名
    private static String toCamelCase(String underScore) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;
        for (char c : underScore.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    result.append(c);
                }
            }
        }
        return result.toString();
    }

    public void updateOrAddTicketExtWithScript(String indexName, String docId, String extCode, String extValue) {
        try {
            // 构建脚本
            String scriptSource =
                    "if (ctx._source.ticket_ext == null) { " +
                            "   ctx._source.ticket_ext = []; " +
                            "} " +
                            "def found = false; " +
                            "for (item in ctx._source.ticket_ext) { " +
                            "   if (item.ext_code == params.extCode) { " +
                            "       item.ext_value = params.extValue; " +
                            "       found = true; " +
                            "       break; " +
                            "   } " +
                            "} " +
                            "if (!found) { " +
                            "   ctx._source.ticket_ext.add(['ext_code': params.extCode, 'ext_value': params.extValue]); " +
                            "}";

            Map<String, Object> params = new HashMap<>();
            params.put("extCode", extCode);
            params.put("extValue", extValue);

            UpdateRequest updateRequest = new UpdateRequest(indexName, docId)
                    .script(new Script(ScriptType.INLINE, "painless", scriptSource, params))
                    .setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
            UpdateResponse updateResponse = restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
            if (updateResponse.getResult() != DocWriteResponse.Result.UPDATED) {
                throw new RuntimeException("更新失败: " + updateResponse.getResult());
            }
        } catch (IOException e) {

        }
    }
}
