package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.config.MyWebSocketHandler;
import com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecord;
import com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecordRuleItem;
import com.goclouds.crm.platform.call.domain.dto.*;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.mapper.CrmTicketAssessmentRecordMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordService;
import com.goclouds.crm.platform.call.service.CrmTicketAssessmentRecordRuleItemService;
import com.goclouds.crm.platform.call.service.CrmTicketAssessmentRecordService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.constant.RabbitMqConstants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.domain.message.WebSocketMessageDTO;
import com.goclouds.crm.platform.common.enums.AIGCModule;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.enums.ResultCodeEnum;
import com.goclouds.crm.platform.common.publisher.WebSocketMessagePublisher;
import com.goclouds.crm.platform.common.utils.MessageI18NUtils;
import com.goclouds.crm.platform.openfeignClient.client.aigc.AigcChatClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.call.GetContentDetailsVO;
import com.goclouds.crm.platform.utils.AIGCRequestCheckUtil;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能质检评估记录规则项表 - 业务逻辑实现类
 */
@Service
@Slf4j
public class CrmTicketAssessmentRecordServiceImpl extends ServiceImpl<CrmTicketAssessmentRecordMapper, CrmTicketAssessmentRecord> implements CrmTicketAssessmentRecordService {

    @Resource
    private CrmTicketAssessmentRecordRuleItemService crmTicketAssessmentRecordRuleItemService;

//    @Resource
//    @Lazy
//    private MyWebSocketHandler myWebSocketHandler;

    @Resource
    private RedisTemplate redisTemplateForString;

    @Resource
    private AigcChatClient aigcChatClient;

    @Resource
    private CrmAgentWorkRecordService crmAgentWorkRecordService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private AIGCRequestCheckUtil aigcRequestCheckUtil;

    @Resource
    private WebSocketMessagePublisher webSocketMessagePublisher;


    /**
     * 获取工单最新评估记录。
     *
     * @param ticketId           工单ID
     * @param assessorId         评估人ID
     * @param assessmentRecordId 评估记录ID
     * @return 评估记录信息
     */
    @Override
    public AssessmentBriefForRecordVO latestAssessment(String ticketId, String assessorId, String assessmentRecordId) {
        return this.baseMapper.getLatestAssessment(ticketId, assessorId, assessmentRecordId);
    }

    /**
     * 获取可用的评估表列表
     *
     * @param ticketType      工单类型
     * @param channelConfigId 渠道ID
     * @return 评估表列表
     */
    @Override
    public List<AssessmentBriefVO> availableAssessments(String ticketType, String channelConfigId) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String userId = SecurityUtil.getLoginUser().getUserId();
        return this.baseMapper.getAvailableAssessments(ticketType, channelConfigId, userId, companyId);
    }

    /**
     * 获取评估记录详细信息
     *
     * @param recordId 评估记录ID
     * @return 评估详情数据
     */
    @Override
    public AssessmentDetailVO assessmentDetailRecord(String recordId) {
        return buildAssessmentDetailVO(recordId);
    }

    /**
     * 获取评估记录规则详细信息
     *
     * @param recordId 评估记录ID
     * @return 评估信息和规则列表
     */
    @Override
    public AssessmentRecordRuleDetailVO assessmentDetailRule(String recordId) {

        AssessmentRecordRuleDetailVO assessmentRecordRuleDetailVO = new AssessmentRecordRuleDetailVO();
        AssessmentBriefForRecordVO record = baseMapper.getAssessmentRecordByRecordId(recordId);
        if (record == null) {
            log.info("评估记录不存在，记录ID：{}", recordId);
            return null;
        }

        List<AssessmentCategoryVO> ruleTree = buildAssessmentCategoriesWithCheckpoints(recordId);
        List<AssessmentRecordRuleItemsVO> assessmentRecordRuleItemsVOS = extractAiAssessmentRules(ruleTree);

        assessmentRecordRuleDetailVO.setAssessmentRecordId(recordId);
        assessmentRecordRuleDetailVO.setScoringRule(record.getScoringRule());
        assessmentRecordRuleDetailVO.setTotalScore(record.getTotalScore());
        assessmentRecordRuleDetailVO.setScoreColor(record.getScoreColor());
        assessmentRecordRuleDetailVO.setFullScore(record.getFullScore());
        assessmentRecordRuleDetailVO.setRules(assessmentRecordRuleItemsVOS);
        return assessmentRecordRuleDetailVO;
    }

    /**
     * 评估记录分页列表
     *
     * @param pageParam                   分页参数
     * @param assessmentRecordPageFormDto 查询条件入参
     * @return 评估记录列表
     */
    @Override
    public IPage<CrmTicketAssessmentRecordPageVO> assessmentRecordPages(IPage<Object> pageParam, CrmTicketAssessmentRecordPageFormDTO assessmentRecordPageFormDto) {
        Page<CrmTicketAssessmentRecordPageVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        assessmentRecordPageFormDto.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        return this.baseMapper.selectAssessmentRecordPages(page, assessmentRecordPageFormDto);
    }

    /**
     * 评估完成
     *
     * @param recordId 评估记录ID
     * @return True / False
     */
    @Override
    public Boolean assessmentCompleted(String recordId) {
        CrmTicketAssessmentRecord byId = this.getById(recordId);
        if (null != byId) {
            byId.setAssessmentStatus(2);
            byId.setModifyTime(new Date());
            boolean flag = saveOrUpdate(byId);
            redisTemplateForString.delete(Constants.ASSESSMENT_SCORE + recordId);
            return flag;
        }
        return false;
    }


    /**
     * 生成评估记录数据并返回评估规则数据
     *
     * @return 评估记录数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssessmentDetailVO generateAssessmentRecord(AssessmentBuildDataDTO assessmentBuildDataDTO) {

        // 获取工单聊天记录。
        List<AssessmentTicketChatListDTO> ticketChatList = getChatHistory("生成评估记录前检查工单聊天记录", assessmentBuildDataDTO.getTicketId(), assessmentBuildDataDTO.getCompanyId(), assessmentBuildDataDTO.getChannelTypeId());
        if (ticketChatList.size() == 0) {
            log.info("[评估表ID: {}] 工单ID：{}，未查询到符合的工单聊天记录", assessmentBuildDataDTO.getAssessmentId(), assessmentBuildDataDTO.getTicketId());
            return null;
        }

        String targetRecordId = StringUtils.isBlank(assessmentBuildDataDTO.getRecordId())
                ? createAssessmentRecordIfAbsent(assessmentBuildDataDTO)
                : assessmentBuildDataDTO.getRecordId();

        return buildAssessmentDetailVO(targetRecordId);
    }


    // ====================================================================== 评估记录生成相关方法 start

    /**
     * 创建评估记录数据
     *
     * @return 评估记录ID
     */
    private String createAssessmentRecordIfAbsent(AssessmentBuildDataDTO assessmentBuildData) {
        // 1. 查询当前评估人当前工单最新的评估记录。
        String lastRecordId = baseMapper.getLastAssessmentRecordByTicketAndUserId(assessmentBuildData.getTicketId(), assessmentBuildData.getUserId());
        if (StringUtils.isNotBlank(lastRecordId)) {
            return lastRecordId;
        }

        // 2. 查询评估表数据是否存在
        boolean assessmentForm = baseMapper.getAssessmentFormByIdAndVersionId(assessmentBuildData.getAssessmentId(), assessmentBuildData.getVersionId());
        if (!assessmentForm) {
            log.warn("【智能质检】- 评估表不存在，ID：{}", assessmentBuildData.getAssessmentId());
            throw new RuntimeException("评估表不存在或已被删除");
        }

        // 3. 查询评估分类、规则数据，用于创建记录数据使用
        List<CrmTicketAssessmentRecordRuleItem> ruleBaseList = baseMapper.getRuleBaseListByVersionId(assessmentBuildData.getAssessmentId(), assessmentBuildData.getVersionId());
        Date now = new Date();
        String newRecordId = UUID.randomUUID().toString();

        // 获取工单详情数据
        AjaxResult<WorkRecordDetailsVO> details = crmAgentWorkRecordService.getDetailsForAIAssessment(assessmentBuildData.getTicketId(), assessmentBuildData.getCompanyId());
        if (details.getCode() != R.SUCCESS) {
            log.warn("【智能质检】- 工单信息不存在，评估ID：{}，工单ID：{}", assessmentBuildData.getAssessmentId(), assessmentBuildData.getTicketId());
            throw new RuntimeException("工单信息不存在或已被删除");
        }
        WorkRecordDetailVo workRecordDetailVo = details.getData().getWorkRecordDetailVo();


        // 4. 创建评估记录数据并存储数据库
        CrmTicketAssessmentRecord record = new CrmTicketAssessmentRecord();
        record.setAssessmentRecordId(newRecordId);
        record.setCompanyId(assessmentBuildData.getCompanyId());
        record.setAssessmentId(assessmentBuildData.getAssessmentId());
        record.setAssessmentVersionId(assessmentBuildData.getVersionId());
        record.setAssessorId(assessmentBuildData.getUserId());
        record.setAssessmentStatus(0);
        record.setCreator(assessmentBuildData.getUserId());
        record.setCreateTime(now);
        record.setDataStatus(1);

        // 工单信息
        record.setChannelTypeId(workRecordDetailVo.getChannelTypeId());
        record.setChannelConfigId(workRecordDetailVo.getChannelConfigId());
        record.setTicketId(workRecordDetailVo.getWorkRecordId());
        record.setTicketCode(workRecordDetailVo.getWordRecordCode());
        record.setTicketType(workRecordDetailVo.getWorkRecordTypeId());
        record.setTicketTypeName(workRecordDetailVo.getWorkRecordTypeName());
        record.setAssessedAgentId(workRecordDetailVo.getAgentId()); // 被评估人ID
        if (!this.save(record)) {
            log.error("【智能质检】- 插入评估记录失败，recordId: {}", newRecordId);
            throw new RuntimeException("插入评估记录失败");
        }

        // 5. 创建评估记录规则项数据并存储数据库
        for (CrmTicketAssessmentRecordRuleItem item : ruleBaseList) {
            item.setAssessmentRecordRuleItemId(UUID.randomUUID().toString());
            item.setAssessmentRecordId(newRecordId);
            item.setAssessmentStatus(0);
            item.setCreateTime(now);
            item.setCreator(assessmentBuildData.getUserId());
            item.setDataStatus(1);
        }

        if (!ruleBaseList.isEmpty()) {
            boolean saveRecordRuleItemResult = crmTicketAssessmentRecordRuleItemService.saveBatch(ruleBaseList);
            if (!saveRecordRuleItemResult) {
                log.error("【智能质检】- 插入评估记录规则失败，recordId: {}", newRecordId);
                throw new RuntimeException("插入评估记录失败");
            }
        }

        // 发送开始评估任务MQ
        JSONObject rabbitMessageData = new JSONObject();
        rabbitMessageData.put("recordId", newRecordId);
        rabbitTemplate.convertAndSend(RabbitMqConstants.ASSESSMENT_TASK_START_EXCHANGE, RabbitMqConstants.ASSESSMENT_TASK_START_KEY, rabbitMessageData.toJSONString(), correlationData -> {
            correlationData.getMessageProperties().setDelay(2000);
            return correlationData;
        });

        // 6. 返回最新的评估记录ID
        return newRecordId;
    }

    /**
     * 构建评估记录数据返回前端使用。
     *
     * @param recordId 评估记录ID
     * @return 评估记录数据
     */
    private AssessmentDetailVO buildAssessmentDetailVO(String recordId) {
        // 1. 查询评估记录
        AssessmentBriefForRecordVO record = baseMapper.getAssessmentRecordByRecordId(recordId);
        if (record == null) {
            log.info("评估记录不存在，记录ID：{}, 结果：{}", recordId, record);
            return null;
        }

        // 2. 构建分类规则树列表
        List<AssessmentCategoryVO> categoryTree = buildAssessmentCategoriesWithCheckpoints(recordId);

        // 3. 构建评估记录详情数据
        AssessmentDetailVO vo = new AssessmentDetailVO();
        vo.setAssessmentRecordId(recordId);
        vo.setAssessmentId(record.getAssessmentId());
        vo.setAssessmentName(record.getAssessmentName());
        vo.setVersionId(record.getAssessmentVersionId());
        vo.setVersionNo(record.getAssessmentVersionNo());
        vo.setCreateTime(record.getCreateTime());
        vo.setAssessorId(record.getAssessorId());
        vo.setAssessorName(record.getAssessorName());
        vo.setTotalScore(record.getTotalScore());
        vo.setScoreColor(record.getScoreColor());
        vo.setScoringRule(record.getScoringRule());
        vo.setFullScore(record.getFullScore());
        vo.setAssessmentStatus(record.getAssessmentStatus());
        vo.setAssessedAgentId(record.getAssessedAgentId());
        vo.setAssessedAgentName(record.getAssessedAgentName());
        vo.setCategories(categoryTree);

        return vo;
    }

    /**
     * 构建分类规则树列表，根据评估记录ID
     *
     * @param recordId 评估记录ID
     * @return 分类规则树列表
     */
    private List<AssessmentCategoryVO> buildAssessmentCategoriesWithCheckpoints(String recordId) {
        // 查询分类与规则
        List<AssessmentCategoryVO> allCategoryRule = baseMapper.getAllCategoryRuleByRecordId(recordId);

        // 查询质检点
        List<AssessmentRuleQualityCheckpointVO> allQualityCheckpoint = baseMapper.getAllQualityCheckpointByRecordId(recordId);

        // 构建 ruleId -> 质检点列表映射，并对质检点排序
        Map<String, List<AssessmentRuleQualityCheckpointVO>> checkpointMap =
                allQualityCheckpoint.stream()
                        .collect(Collectors.groupingBy(AssessmentRuleQualityCheckpointVO::getRuleId));

        for (List<AssessmentRuleQualityCheckpointVO> checkpoints : checkpointMap.values()) {
            checkpoints.sort(Comparator.comparing(AssessmentRuleQualityCheckpointVO::getPointSort,
                    Comparator.nullsLast(Comparator.naturalOrder())));
        }

        try {
            // 设置质检点进规则，并对规则排序
            for (AssessmentCategoryVO category : allCategoryRule) {
                if (category.getRules() != null) {
                    for (AssessmentRuleVO rule : category.getRules()) {
                        List<AssessmentRuleQualityCheckpointVO> checkpoints = checkpointMap.get(rule.getRuleId());
                        rule.setQualityCheckpoints(checkpoints != null ? checkpoints : new ArrayList<>());
                    }
                    category.getRules().sort(
                            Comparator.comparing(
                                    AssessmentRuleVO::getRuleSort,
                                    Comparator.nullsLast(Comparator.naturalOrder())
                            )
                    );
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("[评估记录ID: {}] 设置质检点进规则，并对规则排序出现异常：{}", recordId, e.getMessage(), e);
        }

        // 构建分类树并排序
        return buildCategoryTree(allCategoryRule);
    }

    /**
     * 将分类列表构建为树状结构
     *
     * @param flatList 所有的分类列表（MyBatis 查询结果）
     * @return 树状结构的分类列表（仅返回顶级节点）
     */
    private List<AssessmentCategoryVO> buildCategoryTree(List<AssessmentCategoryVO> flatList) {
        if (flatList == null || flatList.isEmpty()) return Collections.emptyList();

        // id -> category 映射
        Map<String, AssessmentCategoryVO> idToCategoryMap = new HashMap<>();
        for (AssessmentCategoryVO category : flatList) {
            idToCategoryMap.put(category.getCategoryId(), category);
        }

        List<AssessmentCategoryVO> roots = new ArrayList<>();
        for (AssessmentCategoryVO category : flatList) {
            String parentId = category.getParentId();
            if (parentId == null || !idToCategoryMap.containsKey(parentId)) {
                roots.add(category);
            } else {
                AssessmentCategoryVO parent = idToCategoryMap.get(parentId);
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(category);
            }
        }

        // 对分类树和子节点排序
        sortCategoriesRecursively(roots);

        return roots;
    }

    /**
     * 分类、规则、质检点排序
     *
     * @param categories 分类数据
     */
    private void sortCategoriesRecursively(List<AssessmentCategoryVO> categories) {
        if (categories == null) return;
        categories.sort(Comparator.comparingInt(AssessmentCategoryVO::getCategorySort));
        for (AssessmentCategoryVO category : categories) {
            sortCategoriesRecursively(category.getChildren());
        }
    }

    // ====================================================================== 评估记录生成相关方法 end

    /**
     * 开始执行质检任务
     *
     * @param recordId 评估记录ID
     * @return True / False
     */
    @Override
    public Boolean startAssessmentTask(String recordId) {

        log.info("【AI质检-评估记录ID: {}】AI质检启动，开始执行质检任务", recordId);
        // 获取评估记录信息。
        AssessmentBriefForRecordVO record = baseMapper.getAssessmentRecordByRecordId(recordId);
        if (null == record) {
            log.warn("【AI质检-评估记录ID: {}】评估记录不存在，终止评估任务", recordId);
            return false;
        }

        String companyId = record.getCompanyId();
        String channelTypeId = record.getChannelTypeId();
        String ticketId = record.getTicketId();

        String sessionId = buildSessionId(record);

        // 获取工单聊天记录。
        List<AssessmentTicketChatListDTO> ticketChatList = getChatHistory(recordId, ticketId, companyId, channelTypeId);
        if (ticketChatList.size() == 0) {
            log.warn("【AI质检-评估记录ID: {}】工单ID: {} 无工单聊天记录，终止评估任务", recordId, ticketId);
            JSONObject msg = new JSONObject();
            msg.put("assessmentRecordId", recordId);
            // todo 修改国际化内容
            msg.put("msg", MessageI18NUtils.get(ResultCodeEnum.OPERATE_FAILURE.getMessage()));
            pushWebSocket(sessionId, "ASSESSMENT_TASK_FAILED", msg);
            return false;
        }

        log.info("【AI质检-评估记录ID: {}】工单聊天记录准备完成：{}", recordId, JSONObject.toJSONString(ticketChatList));

        // 获取评估记录数据
        List<AssessmentCategoryVO> ruleTree = buildAssessmentCategoriesWithCheckpoints(recordId);
        // 获取评估记录中需要AI打分的规则列表。
        List<String> ruleItemIds = extractAiAssessmentRuleIds(ruleTree);

        if (ruleItemIds.size() == 0) {
            log.info("【AI质检-评估记录ID: {}】评估记录规则项数据不存在，终止评估任务", recordId);
            return false;
        }

        // 初始化评估记录的分数值。
        String redisKey = Constants.ASSESSMENT_SCORE + recordId;
        initRedisScore(redisKey, record);

        // 更新评估记录状态为进行中
        CrmTicketAssessmentRecord byId = this.getById(recordId);
        if (null != byId) {
            byId.setAssessmentStatus(1);
            byId.setModifyTime(new Date());
            saveOrUpdate(byId);
        }

        JSONObject msg = new JSONObject();
        msg.put("assessmentRecordId", recordId);
        msg.put("status", 1);
        pushWebSocket(sessionId, "UPDATE_ASSESSMENT_STATUS", msg);

        // 评估记录规则项列表
        for (String ruleItemId : ruleItemIds) {
            // 组装MQ携带的数据
            JSONObject rabbitMessageData = new JSONObject();
            rabbitMessageData.put("assessmentRecordRuleItemId", ruleItemId);
            rabbitMessageData.put("ticketChatList", JSONObject.toJSONString(ticketChatList)); // 工单聊天记录

            // 发送执行某项规则打分逻辑
//            rabbitTemplate.convertAndSend(RabbitMqConstants.ASSESSMENT_TASK_RULE_START_EXCHANGE, RabbitMqConstants.ASSESSMENT_TASK_RULE_START_KEY, rabbitMessageData.toJSONString());
            rabbitTemplate.convertAndSend(RabbitMqConstants.ASSESSMENT_TASK_RULE_START_EXCHANGE, RabbitMqConstants.ASSESSMENT_TASK_RULE_START_KEY, rabbitMessageData.toJSONString(), correlationData -> {
//                correlationData.getMessageProperties().setDelay(800);
                return correlationData;
            });

            // 模拟测试打分
//            startAssessmentRuleTask(ruleItemId, ticketChatList);
        }

        return true;
    }

    /**
     * 开始执行某条记录中的规则打分任务
     *
     * @param recordRuleItemId 评估记录规则项ID
     * @param ticketChatList   工单聊天记录
     * @return Tue / False
     */
    @Override
    public Boolean startAssessmentRuleTask(String recordRuleItemId, List<AssessmentTicketChatListDTO> ticketChatList) {

        // 查询记录规则信息。
        AssessmentRuleVO rule = crmTicketAssessmentRecordRuleItemService.selectAssessmentRuleWithCheckpoints(recordRuleItemId);

        String recordId = rule.getAssessmentRecordId();

        // 查询记录信息。
        AssessmentBriefForRecordVO record = baseMapper.getAssessmentRecordByRecordId(recordId);

        String companyId = record.getCompanyId();

        log.info("【AI质检-评估记录ID: {} - 规则记录ID：{}】开始质检规则：{}", recordId, recordRuleItemId, rule.getRuleName());

        String redisKey = Constants.ASSESSMENT_SCORE + recordId;
        String sessionId = buildSessionId(record);

        try {
            // 1. 更新规则状态为“进行中”
            updateRuleItemStatus(recordRuleItemId, 1, sessionId, null, null, null);
            log.info("【AI质检-评估记录ID: {} - 规则记录ID：{}】更新状态为：进行中", recordId, recordRuleItemId);

            // 2. 构建上下文内容。
            JSONObject llmRequest = buildLLMContext(rule, ticketChatList);
            log.info("【AI质检-评估记录ID: {} - 记录规则ID：{}】构建聊天上下文成功", recordId, recordRuleItemId);

            // 3. 调用LLM API
            AssessmentLLMResDTO assessmentLLMResDTO = callLLMAssessmentAPI(recordId, companyId, llmRequest, rule.getAssessmentRecordRuleItemId());
            if (null == assessmentLLMResDTO) {
                log.info("【AI质检-评估记录ID: {} - 记录规则ID：{}】规则名称 {}，LLM 结果不正确为空", recordId, recordRuleItemId, rule.getRuleName());
                updateRuleItemStatus(rule.getAssessmentRecordRuleItemId(), 3, sessionId, BigDecimal.valueOf(0), null, null);
                // 检查是否规则是否全部已完成
                checkAndSendCompletionEvent(recordId, sessionId);
                return false;
            }
            log.info("【AI质检-评估记录ID: {} - 记录规则ID：{}】LLM 最终响应结果: {}", recordId, recordRuleItemId, assessmentLLMResDTO);

            // 4. 计算规则分数。
            BigDecimal score = calculateRuleScore(rule.getAiAssessmentRule(), assessmentLLMResDTO);
            log.info("【AI质检-评估记录ID: {} - 记录规则ID：{}】规则: {}，得分：{}", recordId, recordRuleItemId, rule.getRuleName(), score);

            // 5. 获取 Redis 中记录的总分数，并进行计算返回信息的总分值
            BigDecimal lastScore = getAssessmentRecordScore(redisKey, record.getScoringRule(), score, null);

            // 6. 更新数据库中记录规则项的分数
            updateRuleItemStatus(rule.getAssessmentRecordRuleItemId(), 2, sessionId, score, lastScore, assessmentLLMResDTO.getQualityCheckPoints());

            // 7. 更新评估记录总分数。
            Boolean aBoolean = updateAssessmentRecordTotalScore(recordId, lastScore);
            if (aBoolean) {
                log.info("【AI质检-评估记录ID: {} - 记录规则ID：{}】规则: {}，规则评估完成，记录总分数: {}", recordId, recordRuleItemId, rule.getRuleName(), lastScore);

                // 检查是否规则是否全部已完成
                checkAndSendCompletionEvent(recordId, sessionId);

                return true;
            } else {
                log.info("【AI质检-评估记录ID: {} - 记录规则ID：{}】规则: {}，规则评估完成，更新记录总分数异常，现计算的总分数为: {}，不做更新", recordId, recordRuleItemId, rule.getRuleName(), lastScore);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.info("【AI质检-评估记录ID: {} - 记录规则ID：{}】规则评估异常，异常信息: {}, ", recordId, recordRuleItemId, e.getMessage());
            updateRuleItemStatus(rule.getAssessmentRecordRuleItemId(), 3, sessionId, BigDecimal.valueOf(0), null, null);

            // 检查是否规则是否全部已完成
            checkAndSendCompletionEvent(recordId, sessionId);
        }
        return false;
    }


    /**
     * 从分类树中提取所有AIgc评分规则
     *
     * @param categories 分类树列表
     * @return 所有assessmentType为1的AssessmentRuleVO列表
     */
    private List<String> extractAiAssessmentRuleIds(List<AssessmentCategoryVO> categories) {
        List<String> result = new ArrayList<>();
        if (categories == null || categories.isEmpty()) {
            return result;
        }

        for (AssessmentCategoryVO category : categories) {
            // 处理当前分类的规则
            if (category.getRules() != null) {
                for (AssessmentRuleVO rule : category.getRules()) {
                    if (rule.getAssessmentType() != null && rule.getAssessmentType() == 1) {
                        result.add(rule.getAssessmentRecordRuleItemId());
                    }
                }
            }

            // 递归处理子分类
            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                result.addAll(extractAiAssessmentRuleIds(category.getChildren()));
            }
        }

        return result;
    }

    private List<AssessmentRecordRuleItemsVO> extractAiAssessmentRules(List<AssessmentCategoryVO> categories) {
        List<AssessmentRecordRuleItemsVO> result = new ArrayList<>();
        if (categories == null || categories.isEmpty()) {
            return result;
        }

        for (AssessmentCategoryVO category : categories) {
            // 处理当前分类的规则
            if (category.getRules() != null) {
                for (AssessmentRuleVO rule : category.getRules()) {
                    AssessmentRecordRuleItemsVO assessmentRecordRuleItemsVO = new AssessmentRecordRuleItemsVO();
                    assessmentRecordRuleItemsVO.setAssessmentRuleId(rule.getAssessmentRecordRuleItemId());
                    assessmentRecordRuleItemsVO.setRuleName(rule.getRuleName());
                    assessmentRecordRuleItemsVO.setManualScore(rule.getManualScore());
                    assessmentRecordRuleItemsVO.setAiScore(rule.getAiScore());
                    assessmentRecordRuleItemsVO.setAssessmentRuleId(rule.getRuleId());
                    result.add(assessmentRecordRuleItemsVO);
                }
            }

            // 递归处理子分类
            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                result.addAll(extractAiAssessmentRules(category.getChildren()));
            }
        }

        return result;
    }


    // ====================================================================== AI质检评估 - 获取工单聊天记录 start

//    /**
//     * 获取工单聊天记录
//     *
//     * @param recordId      评估记录ID
//     * @param ticketId      工单ID
//     * @param companyId     公司ID
//     * @param channelTypeId 渠道类型ID
//     * @return 工单的聊天记录列表
//     */
//    private List<AssessmentTicketChatListDTO> getChatHistory(String recordId, String ticketId, String companyId, String channelTypeId) {
//        List<AssessmentTicketChatListDTO> chatList = new ArrayList<>();
//
//        try {
//            GetContentDetailsVO params = new GetContentDetailsVO();
//            params.setWorkRecordId(ticketId);
//            params.setCompanyId(companyId);
//
//            AjaxResult result = crmAgentWorkRecordService.getContentDetails(params);
//            if (result.getCode() != R.SUCCESS) {
//                log.warn("【AI质检-评估记录ID: {}】获取聊天失败，工单ID: {}", recordId, ticketId);
//                return chatList;
//            }
//
//            Map<String, Object> data = (Map<String, Object>) result.getData();
//            if (data == null) {
//                log.warn("【AI质检-评估记录ID: {}】聊天数据为空，工单ID: {}", recordId, ticketId);
//                return chatList;
//            }
//
//            boolean isVoiceChannel = CrmChannelEnum.PHONE.getCode().equals(channelTypeId)
//                    || CrmChannelEnum.WEB_ONLINE_VOICE.getCode().equals(channelTypeId)
//                    || CrmChannelEnum.APP_ONLINE_VOICE.getCode().equals(channelTypeId);
//
//            if (isVoiceChannel) {
//                List<TranslateVO> records = (List<TranslateVO>) data.get("translateVOS");
//                for (TranslateVO vo : Optional.ofNullable(records).orElse(Collections.emptyList())) {
//                    chatList.add(buildFromTranslateVO(vo));
//                }
//            } else {
//                // 添加类型安全检查和更明确的空值处理
//                List<?> rawList = (List<?>) data.get("dataList");
//                List<TicketContentIndexVO> records = rawList.stream()
//                        .filter(TicketContentIndexVO.class::isInstance)
//                        .map(TicketContentIndexVO.class::cast)
//                        .collect(Collectors.toList());
//
//                if (!records.isEmpty()) {
//                    // 收集所有机器人消息的索引
//                    List<Integer> robotIndices = IntStream.range(0, records.size())
//                            .filter(i -> records.get(i).getReply_type() != null)
//                            .filter(i -> records.get(i).getReply_type() == 3)
//                            .boxed()
//                            .collect(Collectors.toList());
//
//                    // 如果存在机器人回复，则删除最后一个机器人消息及之前的所有记录
//                    if (!robotIndices.isEmpty()) {
//                        int lastRobotIndex = robotIndices.get(robotIndices.size() - 1);
//                        records.subList(0, lastRobotIndex + 1).clear();
//                    }
//
//                    // 遍历剩余记录，添加客服回复
//                    records.stream()
//                            .filter(vo -> vo.getReply_type() != null)
//                            .filter(vo -> vo.getReply_type() == 1 || vo.getReply_type() == 2)
//                            .forEach(vo -> chatList.add(buildFromTicketContentVO(vo)));
//                }
//            }
//        } catch (Exception e) {
//            e.getStackTrace();
//            log.error("【AI质检-评估记录ID: {}】工单ID：{}，获取聊天记录异常: {}", recordId, ticketId, e.getMessage(), e);
//        }
//
//        return chatList;
//    }

    /**
     * 获取工单聊天记录
     *
     * @param recordId      评估记录ID
     * @param ticketId      工单ID
     * @param companyId     公司ID
     * @param channelTypeId 渠道类型ID
     * @return 工单的聊天记录列表
     */
    private List<AssessmentTicketChatListDTO> getChatHistory(String recordId, String ticketId, String companyId, String channelTypeId) {
        List<AssessmentTicketChatListDTO> chatList = new ArrayList<>();

        try {
            GetContentDetailsVO params = new GetContentDetailsVO();
            params.setWorkRecordId(ticketId);
            params.setCompanyId(companyId);

            AjaxResult result = crmAgentWorkRecordService.getContentDetails(params);
            if (result.getCode() != R.SUCCESS) {
                log.warn("【AI质检-评估记录ID: {}】获取聊天失败，工单ID: {}, 错误码: {}, 错误信息: {}",
                        recordId, ticketId, result.getCode(), result.getMsg());
                return chatList;
            }

            if (!(result.getData() instanceof Map)) {
                log.warn("【AI质检-评估记录ID: {}】聊天数据格式不正确，工单ID: {}", recordId, ticketId);
                return chatList;
            }

            Map<String, Object> data = (Map<String, Object>) result.getData();
            if (data == null || data.isEmpty()) {
                log.warn("【AI质检-评估记录ID: {}】聊天数据为空，工单ID: {}", recordId, ticketId);
                return chatList;
            }

            boolean isVoiceChannel = CrmChannelEnum.PHONE.getCode() == Integer.parseInt(channelTypeId)
                    || CrmChannelEnum.WEB_ONLINE_VOICE.getCode() == Integer.parseInt(channelTypeId)
                    || CrmChannelEnum.APP_ONLINE_VOICE.getCode() == Integer.parseInt(channelTypeId);

            // 如果是语音工单
            if (isVoiceChannel) {
                Object translateVOs = data.get("translateVOS");
                if (translateVOs instanceof List) {
                    List<TranslateVO> records = ((List<?>) translateVOs).stream()
                            .filter(TranslateVO.class::isInstance)
                            .map(TranslateVO.class::cast)
                            .collect(Collectors.toList());

                    records.forEach(vo -> chatList.add(buildFromTranslateVO(vo)));
                }
            } else {
                Object dataList = data.get("dataList");
                if (dataList instanceof List) {
                    List<TicketContentIndexVO> records = ((List<?>) dataList).stream()
                            .filter(TicketContentIndexVO.class::isInstance)
                            .map(TicketContentIndexVO.class::cast)
                            .collect(Collectors.toList());

                    if (!records.isEmpty()) {
                        /*
                         * 目标需求：
                         * 从每段 agent 开始 的位置，开始记录聊天内容；
                         * 一直记录到 下一个 bot 出现之前；
                         * 每段聊天仅包括：agent（客服）customer（客户）忽略 bot 开头之前的内容、system 类型、以及非 agent 开头的段落。
                         */
                        boolean inSegment = false;
                        for (TicketContentIndexVO vo : records) {
                            Integer replyType = vo.getReply_type();
                            Integer contentType = vo.getContent_type();

                            if (replyType == null || contentType == null) continue;

                            if (replyType == 3) {
                                // 遇到 bot，关闭当前段落收集
                                inSegment = false;
                                continue;
                            }

                            if (replyType == 1 && !inSegment) {
                                // 遇到 agent 且未开始新段落，开始收集
                                inSegment = true;
                            }

                            if (inSegment && (replyType == 1 || replyType == 2) && contentType == 1) {
                                // 收集 agent 和 customer 的文本消息
                                chatList.add(buildFromTicketContentVO(vo));
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("【AI质检-评估记录ID: {}】工单ID：{}，获取聊天记录异常: {}", recordId, ticketId, e.getMessage(), e);
            e.printStackTrace();
        }

        return chatList;
    }

    /**
     * 构建语音工单聊天记录
     *
     * @param vo 语音工单数据
     * @return 工单聊天记录
     */
    private AssessmentTicketChatListDTO buildFromTranslateVO(TranslateVO vo) {
        AssessmentTicketChatListDTO dto = new AssessmentTicketChatListDTO();
        dto.setRole("AGENT".equalsIgnoreCase(vo.getType()) ? "Agent" : "User");
        dto.setContent(Optional.ofNullable(vo.getContent()).orElse(""));
        dto.setOriginalContent(Optional.ofNullable(vo.getTranslatePhoneContent()).orElse(""));
        dto.setContentType("1");
        return dto;
    }

    /**
     * 构建非语音工单聊天记录
     *
     * @param vo 非语音工单数据
     * @return 工单聊天记录
     */
    private AssessmentTicketChatListDTO buildFromTicketContentVO(TicketContentIndexVO vo) {
        AssessmentTicketChatListDTO dto = new AssessmentTicketChatListDTO();
        dto.setRole(vo.getReply_type() == 1 ? "Agent" : "User");
        dto.setContent(Optional.ofNullable(vo.getContent()).orElse(""));
        dto.setOriginalContent(Optional.ofNullable(vo.getTranslate_content()).orElse(""));
        dto.setContentType(Optional.ofNullable(vo.getContent_type()).map(String::valueOf).orElse("1"));
        return dto;
    }

    // ====================================================================== AI质检评估 - 获取工单聊天记录 end

    // ====================================================================== AI质检评估 - 操作 Redis 相关方法 start

    /**
     * 设置评估记录的初始分数在 Redis 中
     *
     * @param redisKey redis key
     * @param record   评估记录信息
     */
    private void initRedisScore(String redisKey, AssessmentBriefForRecordVO record) {
        BigDecimal initialScore = (record.getScoringRule() == 1) ? BigDecimal.ZERO : record.getFullScore();
        redisTemplateForString.opsForValue().set(redisKey, initialScore.toPlainString());
    }

    /**
     * 从 Redis 读取分数
     *
     * @param key Redis Key
     * @return BigDecimal 分数值（可能为 null）
     */
    public BigDecimal getRedisScore(String key) {
        String valueStr = (String) redisTemplateForString.opsForValue().get(key);
        if (StringUtils.isBlank(valueStr)) {
            return null;
        }
        // 防御性处理：去除首尾空格和引号
        valueStr = valueStr.trim().replaceAll("^\"|\"$", "");
        try {
            return new BigDecimal(valueStr);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            throw new IllegalStateException("Redis 中的值不是有效数字: " + valueStr + "，无法计算", e);
        }
    }

    // ====================================================================== AI质检评估 - 操作 Redis 相关方法 end

    // ====================================================================== AI质检评估 - 调用LLM API相关方法 start

    /**
     * 格式化工单聊天记录信息
     *
     * @param chatList 工单聊天记录
     * @return 字符串格式的聊天记录
     */
    private String formatChatListToText(List<AssessmentTicketChatListDTO> chatList) {
        StringBuilder sb = new StringBuilder();
        for (AssessmentTicketChatListDTO chat : chatList) {
            if (chat.getRole() != null && chat.getContent() != null && !chat.getContent().trim().isEmpty()) {
                sb.append(chat.getRole()).append(": ").append(chat.getContent().trim()).append("\n");
            }
        }
        return sb.toString().trim();
    }

    /**
     * 构建调用LLM API的提示词
     *
     * @param rule           评估记录规则信息
     * @param ticketChatList 工单聊天记录
     * @return 提示词内容和上下文内容
     */
//    private JSONObject buildLLMContext(AssessmentRuleVO rule, List<AssessmentTicketChatListDTO> ticketChatList) {
//
//        String prompt =
//                "Rule information:\n" +
//                        "- Rule Name: %s\n" +
//                        "- Checkpoints:\n" +
//                        "%s\n" +
//                        "\n" +
//                        "Your task:\n" +
//                        "1. Go through all checkpoints;\n" +
//                        "2. Examine the chat transcript line by line;\n" +
//                        "3. Count a hit whenever a checkpoint is triggered (even repeatedly);\n" +
//                        "4. Return the total number of hits (hitCount);\n" +
//                        "5. Output the result in the following JSON format:\n" +
//                        "{\n" +
//                        "  \"ruleName\": \"Rule Name\",\n" +
//                        "  \"hitCount\": Number of hits\n" +
//                        "}\n" +
//                        "\n" +
//                        "Important:\n" +
//                        "- Output only the JSON result;\n" +
//                        "- Do not include any explanations;\n" +
//                        "- ruleName must exactly match the one provided above;\n" +
//                        "- hitCount must be an integer;";
//
//        // 质检点列表
//        StringBuilder checkPoints = new StringBuilder();
//        for (AssessmentRuleQualityCheckpointVO point : rule.getQualityCheckpoints()) {
//            checkPoints.append("- ").append(point.getPointDesc()).append("\n");
//        }
//
//        // LLM上下文聊天内容
//        List<JSONObject> chatHistory = new ArrayList<>();
//
//        // 第一条 user 消息
//        JSONObject userMessage1 = new JSONObject();
//        userMessage1.put("role", "user");
//        userMessage1.put("prompt", "You are a customer service quality inspection assistant. I will provide a chat transcript and a quality inspection guideline. Please identify any violations in the conversation based on the \"inspection points\" in the guideline.");
//        chatHistory.add(userMessage1);
//
//        // 第一条 bot 回复
//        JSONObject botMessage1 = new JSONObject();
//        botMessage1.put("role", "bot");
//        botMessage1.put("prompt", "Okay, please provide the chat transcript.");
//        chatHistory.add(botMessage1);
//
//        /*// 第二条 user 消息 —— 聊天记录部分
//        JSONObject userMessage2 = new JSONObject();
//        userMessage2.put("role", "user");
//        userMessage2.put("prompt", "[Chat Transcript Below]:\n" + formatChatListToText(ticketChatList));
//        chatHistory.add(userMessage2);
//
//        // 第二条 bot 回复
//        JSONObject botMessage2 = new JSONObject();
//        botMessage2.put("role", "bot");
//        botMessage2.put("prompt", "Got it. Please provide the quality inspection guideline.");
//        chatHistory.add(botMessage2);*/
//
//
//        int batchSize = 15;
//        int total = ticketChatList.size();
//        for (int i = 0; i < total; i += batchSize) {
//            // 当前组的子列表
//            List<AssessmentTicketChatListDTO> batch = ticketChatList.subList(i, Math.min(i + batchSize, total));
//
//            // user 消息
//            JSONObject userMessage = new JSONObject();
//            userMessage.put("role", "user");
//            userMessage.put("prompt", "[Chat Transcript Below]:\n" + formatChatListToText(batch));
//            chatHistory.add(userMessage);
//
//            // bot 消息（区分是否为最后一组）
//            JSONObject botMessage = new JSONObject();
//            botMessage.put("role", "bot");
//            if (i + batchSize >= total) {
//                botMessage.put("prompt", "Got it. Please provide the quality inspection guideline.");
//            } else {
//                botMessage.put("prompt", "Do you have any chat logs?");
//            }
//            chatHistory.add(botMessage);
//        }
//
//
//        JSONObject result = new JSONObject();
////        result.put("prompt", String.format(prompt, rule.getRuleName(), checkPoints)); // 提示词
//        result.put("chatHistory", chatHistory); // 自定义的上下文聊天内容
//        result.put("ruleName", rule.getRuleName()); // 规则名称
//        result.put("checkPoints", checkPoints); // 规则质检点
//
//        return result;
//    }
    private JSONObject buildLLMContext(AssessmentRuleVO rule, List<AssessmentTicketChatListDTO> ticketChatList) {

        // 质检点列表
        StringBuilder checkPoints = new StringBuilder();
        for (AssessmentRuleQualityCheckpointVO point : rule.getQualityCheckpoints()) {
            checkPoints.append("- ").append(point.getPointDesc()).append("\n");
        }

        // LLM上下文聊天内容
        List<JSONObject> chatHistory = new ArrayList<>();

        // 第一条 user 消息
        JSONObject userMessage1 = new JSONObject();
        userMessage1.put("role", "user");
        userMessage1.put("prompt", "You are a professional AI assistant specializing in customer service quality inspection. Your task is to analyze conversations between an agent and a customer based on a set of quality inspection checkpoints that I will provide later. First, I will share the full chat transcript.");
        chatHistory.add(userMessage1);

        // 第一条 bot 回复
        JSONObject botMessage1 = new JSONObject();
        botMessage1.put("role", "bot");
        botMessage1.put("prompt", "Understood. Please provide the full chat transcript for analysis.");
        chatHistory.add(botMessage1);

        int batchSize = 15;
        int total = ticketChatList.size();
        for (int i = 0; i < total; i += batchSize) {
            // 当前组的子列表
            List<AssessmentTicketChatListDTO> batch = ticketChatList.subList(i, Math.min(i + batchSize, total));

            // user 消息
            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            userMessage.put("prompt", "[Chat Transcript Below]:\n" + formatChatListToText(batch));
            chatHistory.add(userMessage);

            // bot 消息（区分是否为最后一组）
            JSONObject botMessage = new JSONObject();
            botMessage.put("role", "bot");
            if (i + batchSize >= total) {
                botMessage.put("prompt", "Got it. I have received the full chat content. Please provide the quality inspection rule name and the list of inspection checkpoints you want me to evaluate.");
            } else {
                botMessage.put("prompt", "Thank you. Do you have more chat records to provide?");
            }
            chatHistory.add(botMessage);
        }


        JSONObject result = new JSONObject();
        result.put("chatHistory", chatHistory); // 自定义的上下文聊天内容
        result.put("ruleName", rule.getRuleName()); // 规则名称
        result.put("checkPoints", checkPoints); // 规则质检点

        return result;
    }

    /**
     * 调用LLM API
     *
     * @param recordId                   评估记录ID
     * @param companyId                  公司ID
     * @param llmRequest                 请求llm参数
     * @param assessmentRecordRuleItemId 评估记录规则项ID
     * @return LLM 响应结果
     */
    private AssessmentLLMResDTO callLLMAssessmentAPI(String recordId, String companyId, JSONObject llmRequest, String assessmentRecordRuleItemId) {
        AssessmentLLMResDTO assessmentLLMResDTO = new AssessmentLLMResDTO();
        String ruleName = llmRequest.getString("ruleName"); // 规则名称

        try {
            boolean b = aigcRequestCheckUtil.requestCheck(companyId, AIGCModule.Agent_Copilot.getCode());
            if (!b) {
                log.info("【AI质检-评估记录ID: {} - 记录规则ID：{}】规则名称 {}，AIGC次数不足，请求失败", recordId, assessmentRecordRuleItemId, ruleName);
                return null;
            }
            String checkPoints = llmRequest.getString("checkPoints"); // 规则质检点
            List<JSONObject> chatHistory = (List<JSONObject>) llmRequest.get("chatHistory"); // llm 聊天上下文

            // 发送LLM请求
            R<String> result = aigcChatClient.assessmentAIRuleCheck(companyId, ruleName, checkPoints, JSONObject.toJSONString(chatHistory));
            // 如果请求失败了
            if (result.getCode() != 200) {
                return null;
            }
            String answerJSONStr = result.getData();
//            JSONObject llmResultJson = JSONObject.parseObject(answerJSONStr);
//
//            String llmResponse = ragUtils.assessmentQualityWithLLM(companyId, llmRequest.getString("prompt"), (List<JSONObject>) llmRequest.get("chatHistory"));
//
//            String llmResponse = "";
//
//
//            log.info("[评估记录ID: {}] LLM 响应结果: {}", recordId, llmResponse);
//
//            if (StringUtils.isBlank(llmResponse)) return assessmentLLMResDTO;
//
//            JSONObject llmResult = JSONObject.parseObject(llmResponse);
//            String answerStr = llmResult.getString("answer");
//            log.info("[评估记录ID: {}] LLM校验结果 - answerStr：{}", recordId, answerStr);
//
            if (StringUtils.isBlank(answerJSONStr)) return assessmentLLMResDTO;
//
            String llmAnswerStr = extractJsonFromMD(answerJSONStr);
            log.info("[评估记录ID: {}] LLM解析结果 - llmAnswerStr：{}", recordId, llmAnswerStr);
//
            if (StringUtils.isBlank(llmAnswerStr)) return assessmentLLMResDTO;

            JSONObject llmAnswer;
            try {
                llmAnswer = JSONObject.parseObject(llmAnswerStr);
                assessmentLLMResDTO.setResultCode(1000);
            } catch (Exception e) {
                log.info("[评估记录ID: {} - 记录规则ID：{}] answerJSONStr：{}, LLM返回的answer字段格式错误，使用默认值", recordId, assessmentRecordRuleItemId, answerJSONStr);
                llmAnswer = new JSONObject();
                llmAnswer.put("hitCount", 0);
                llmAnswer.put("ruleName", "");
                assessmentLLMResDTO.setResultCode(1001);
            }

            assessmentLLMResDTO.setHitCount(llmAnswer.getInteger("hitCount"));
            assessmentLLMResDTO.setRuleName(llmAnswer.getString("ruleName"));

            JSONArray checkPointResults = llmAnswer.getJSONArray("checkPointResults");
            if (checkPointResults != null && !checkPointResults.isEmpty()) {
                List<AssessmentLLMResQualityCheckPointDTO> checkPointResultList = new ArrayList<>();

                for (int i = 0; i < checkPointResults.size(); i++) {
                    try {
                        JSONObject jsonObject = checkPointResults.getJSONObject(i);
                        AssessmentLLMResQualityCheckPointDTO dto = JSONObject.parseObject(
                                jsonObject.toJSONString(),
                                AssessmentLLMResQualityCheckPointDTO.class
                        );
                        if (dto != null) {
                            checkPointResultList.add(dto);
                        }
                    } catch (Exception e) {
                        // 忽略当前条，继续解析下一条
                        // 可选：记录日志
                        // log.warn("Failed to parse checkPointResult at index {}: {}", i, e.getMessage());
                    }
                }

                if (!checkPointResultList.isEmpty()) {
                    assessmentLLMResDTO.setQualityCheckPoints(checkPointResultList);
                }
            }

        } catch (Exception e) {
            log.info("[评估记录ID: {} - 记录规则ID：{}] LLM 调用出现异常: {}", recordId, assessmentRecordRuleItemId, e.getMessage(), e);
            e.printStackTrace();
            assessmentLLMResDTO.setHitCount(0);
            assessmentLLMResDTO.setRuleName("");
            assessmentLLMResDTO.setResultCode(1001);
        }

        return assessmentLLMResDTO;
    }

    /**
     * 解析md格式的json数据，返回json字符串
     *
     * @param value md json
     * @return json string
     */
    public static String extractJsonFromMD(String value) {
        String jsonString = value.replaceAll("^```json\\s*", "").replaceAll("\\s*```$", "");
        if (jsonString.isEmpty()) {
            return "";
        }
        return jsonString;
    }

    // ====================================================================== AI质检评估 - 调用LLM API相关方法 start


    // ====================================================================== AI质检评估WEBSocket推送相关方法 start

    /**
     * 组装 WEBSocket 绑定的ID
     *
     * @param record 评估记录信息
     * @return sessionId
     */
    private String buildSessionId(AssessmentBriefForRecordVO record) {
        return String.format("%s_%s_%s", record.getAssessmentId(), record.getAssessmentVersionId(), record.getAssessorId());
    }

    /**
     * 发送WEBSocket信息
     *
     * @param sessionId WEBSocket信息id，用于获取websocket
     * @param type      WEBSocket消息类型
     * @param msg       携带消息内容
     */
    private void pushWebSocket(String sessionId, String type, JSONObject msg) {
        /*WebSocketSession session = myWebSocketHandler.getSession(sessionId);
        if (session != null) {
            synchronized (session) {  // 确保线程安全
                if (session.isOpen()) {  // 检查连接是否打开
                    myWebSocketHandler.sendMessage(session, type, msg);
                }
            }
        }*/
        WebSocketMessageDTO webSocketMessageDTO = new WebSocketMessageDTO();
        webSocketMessageDTO.setReceiverId(sessionId);
        JSONObject content = new JSONObject();
        content.put("type", type);
        content.put("data", msg);
        webSocketMessageDTO.setContent(content.toJSONString());
        webSocketMessagePublisher.publish(webSocketMessageDTO);
    }

    /**
     * 更新评估记录规则项的状态并发送ws消息
     *
     * @param ruleItemId           规则项ID
     * @param status               状态值,1信息中 2已完成 3异常
     * @param sessionId            wsID
     * @param aiScore              评估记录规则项ai得分
     * @param assessmentTotalScore 评估记录总得分
     */
    private void updateRuleItemStatus(String ruleItemId, Integer status, String sessionId, BigDecimal aiScore, BigDecimal assessmentTotalScore, List<AssessmentLLMResQualityCheckPointDTO> reasons) {
        JSONObject msg = new JSONObject();
        msg.put("assessmentRecordRuleItemId", ruleItemId);
        msg.put("status", status);
        if (null != aiScore) {
            msg.put("aiScore", aiScore);
        }
        if (null != assessmentTotalScore) {
            msg.put("assessmentTotalScore", assessmentTotalScore);
        }
        if (null != reasons && reasons.size() > 0) {
            msg.put("reasons", reasons);
        }
        pushWebSocket(sessionId, "UPDATE_ASSESSMENT_RULE_STATUS", msg);

        crmTicketAssessmentRecordRuleItemService.updateInfoByAssessmentRecordRuleItemId(ruleItemId, status, aiScore);
    }

    /**
     * 检查未评估规则数量和发送完成事件
     *
     * @param recordId  评估记录ID
     * @param sessionId WEBSocket绑定的ID
     */
    private void checkAndSendCompletionEvent(String recordId, String sessionId) {
        Integer unAssessmentRuleCount = getUnAssessmentRuleCount(recordId);
        if (unAssessmentRuleCount == 0) {
//            myWebSocketHandler.sendAssessmentCompleted(myWebSocketHandler.getSession(sessionId), recordId);
            JSONObject data = new JSONObject();
            data.put("assessmentRecordId", recordId);
            WebSocketMessageDTO webSocketMessageDTO = new WebSocketMessageDTO();
            webSocketMessageDTO.setReceiverId(sessionId);
            JSONObject content = new JSONObject();
            content.put("type", "ASSESSMENT_COMPLETED");
            content.put("data", data);
            webSocketMessageDTO.setContent(content.toJSONString());
            webSocketMessagePublisher.publish(webSocketMessageDTO);
        }
    }

    // ====================================================================== AI质检评估WEBSocket推送相关方法 end


    // ====================================================================== 评估记录打分相关方法 start

    /**
     * 计算规则得分情况。
     *
     * @param aiRuleJson 规则定义的打分规则（JSON字符串）
     * @param llmResDTO  LLM响应结果数据结构（包含总命中次数和质检点）
     * @return BigDecimal 扣分结果
     */
    private BigDecimal calculateRuleScore(String aiRuleJson, AssessmentLLMResDTO llmResDTO) {
        try {
            if (llmResDTO == null || llmResDTO.getHitCount() == null || llmResDTO.getResultCode() == 1001) {
                return BigDecimal.ZERO;
            }

            Integer totalHits = llmResDTO.getHitCount();

            JSONObject json = JSONObject.parseObject(aiRuleJson);
            String type = json.getString("type");

            // type = "1"：每次命中扣分，但有最大限制
            if ("1".equals(type)) {
                int deductPerTime = json.getIntValue("deduct_per_time");
                int maxDeduct = json.getIntValue("max_deduct");

                int deduct = Math.min(deductPerTime * totalHits, maxDeduct);
                return BigDecimal.valueOf(deduct);

                // type = "2"：比较命中次数满足条件就扣固定分数
            } else if ("2".equals(type)) {
                String compare = json.getString("compare");
                int times = json.getIntValue("times");
                int deduct = json.getIntValue("deduct");

                boolean matched = false;
                switch (compare) {
                    case ">":
                        matched = totalHits > times;
                        break;
                    case "<":
                        matched = totalHits < times;
                        break;
                    case "=":
                        matched = totalHits == times;
                        break;
                    default:
                        log.warn("不支持的比较符: {}", compare);
                        break;
                }

                return matched ? BigDecimal.valueOf(deduct) : BigDecimal.ZERO;
            } else {
                log.warn("未知的打分类型: {}", type);
            }
        } catch (Exception e) {
            log.info("评分规则计算失败，规则 JSON: {}", aiRuleJson, e);
            e.printStackTrace();
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getAssessmentRecordScore(String redisKey, int scoringRule, BigDecimal ruleScore, BigDecimal originalScore) {
        // 1. 从 Redis 获取分数，并严格检查空值
        BigDecimal currentScore = getRedisScore(redisKey);

        // 2. 撤销原始得分影响（如果 originalScore 不为 null）
        if (originalScore != null) {
            currentScore = (scoringRule == 1)
                    ? currentScore.subtract(originalScore)  // 原先加分 → 现在撤销减分
                    : currentScore.add(originalScore);       // 原先减分 → 现在撤销加分
        }

        // 3. 应用新规则计算新分数
        BigDecimal newScore = (scoringRule == 1)
                ? currentScore.add(ruleScore)
                : currentScore.subtract(ruleScore);

        // 4. 保证分数不小于 0
        newScore = newScore.max(BigDecimal.ZERO);

        // 5. 写回 Redis（可选，根据需求取消注释）
        redisTemplateForString.opsForValue().set(redisKey, newScore.toPlainString());

        return newScore;
    }

    /**
     * 更新数据库中分数
     *
     * @param recordId  评估记录ID
     * @param lastScore 分数值
     * @return True / False
     */
    private Boolean updateAssessmentRecordTotalScore(String recordId, BigDecimal lastScore) {

        CrmTicketAssessmentRecord byId = this.getById(recordId);
        if (null != byId) {
            byId.setTotalScore(lastScore);
            byId.setModifyTime(new Date());
            return this.saveOrUpdate(byId);
        }

        return Boolean.FALSE;
    }

    // ====================================================================== 评估记录打分相关方法 end


    // ====================================================================== 评估记录WEBSocket相关方法 start

    /**
     * 处理更新人工分数，用于更新AI分数事件 - WEBSocket使用
     *
     * @param assessmentRecordId         评估记录ID
     * @param assessmentRecordRuleItemId 评估记录规则项ID
     * @param manualScore                人工分数
     * @param scoreRule                  评估机制（1加分，2减分）
     * @return 总分数
     */
    @Override
    public BigDecimal handleManualScoreUpdated(String assessmentRecordId, String assessmentRecordRuleItemId, BigDecimal manualScore, Integer scoreRule) {
        BigDecimal originalScore = new BigDecimal(0);

        CrmTicketAssessmentRecordRuleItem recordRuleItem = crmTicketAssessmentRecordRuleItemService.getById(assessmentRecordRuleItemId);
        if (null == recordRuleItem.getManualScore()) {
            originalScore = recordRuleItem.getAiScore();
        } else {
            originalScore = recordRuleItem.getManualScore();
        }
        recordRuleItem.setManualScore(manualScore);
        recordRuleItem.setModifyTime(new Date());
        boolean result = crmTicketAssessmentRecordRuleItemService.saveOrUpdate(recordRuleItem);

        if (result) {
            String redisKey = Constants.ASSESSMENT_SCORE + assessmentRecordId;

            // 获取 Redis 中记录的总分数，并进行计算返回信息的总分值
            BigDecimal lastScore = getAssessmentRecordScore(redisKey, scoreRule, manualScore, originalScore);

            // 更新总分数值
            Boolean aBoolean = updateAssessmentRecordTotalScore(assessmentRecordId, lastScore);
            if (aBoolean) {
                redisTemplateForString.opsForValue().set(redisKey, lastScore.toPlainString());
                return lastScore;
            } else {
                recordRuleItem.setManualScore(null);
                recordRuleItem.setManualOptionName(null);
                recordRuleItem.setManualOptionId(null);
                recordRuleItem.setModifyTime(new Date());
                crmTicketAssessmentRecordRuleItemService.saveOrUpdate(recordRuleItem);
            }
        }
        return null;
    }

    /**
     * 处理更新记录规则项的备注事件 - WEBSocket使用
     *
     * @param assessmentRecordId         评估记录ID
     * @param assessmentRecordRuleItemId 评估记录规则项ID
     * @param assessmentRemark           记录规则备注
     * @return True / False
     */
    @Override
    public Boolean handleAssessmentRemarkAdded(String assessmentRecordId, String assessmentRecordRuleItemId, String assessmentRemark) {
        CrmTicketAssessmentRecordRuleItem recordRuleItem = crmTicketAssessmentRecordRuleItemService.getById(assessmentRecordRuleItemId);
        recordRuleItem.setAssessmentRemark(assessmentRemark);
        recordRuleItem.setModifyTime(new Date());
        return crmTicketAssessmentRecordRuleItemService.saveOrUpdate(recordRuleItem);
    }

    /**
     * 处理人工打分事件 - WEBSocket使用
     *
     * @param assessmentRecordId         评估记录ID
     * @param assessmentRecordRuleItemId 评估记录规则项ID
     * @param manualOptionId             人工选择项ID
     * @param manualOptionName           人工选择项名称
     * @param manualScore                人工分数
     * @param scoreRule                  评估机制（1加分，2减分）
     * @return 总分数
     */
    @Override
    public BigDecimal handleManualScoreSubmitted(String assessmentRecordId, String assessmentRecordRuleItemId, String manualOptionId, String manualOptionName, BigDecimal manualScore, Integer scoreRule) {
        BigDecimal originalScore = new BigDecimal(0);

        CrmTicketAssessmentRecordRuleItem recordRuleItem = crmTicketAssessmentRecordRuleItemService.getById(assessmentRecordRuleItemId);
        if (null == recordRuleItem.getManualScore()) {
            originalScore = recordRuleItem.getAiScore();
        } else {
            originalScore = recordRuleItem.getManualScore();
        }
        recordRuleItem.setAssessmentStatus(2);
        recordRuleItem.setManualScore(manualScore);
        recordRuleItem.setManualOptionId(manualOptionId);
        recordRuleItem.setManualOptionName(manualOptionName);
        recordRuleItem.setModifyTime(new Date());
        boolean result = crmTicketAssessmentRecordRuleItemService.saveOrUpdate(recordRuleItem);

        if (result) {
            String redisKey = Constants.ASSESSMENT_SCORE + assessmentRecordId;

            // 获取 Redis 中记录的总分数，并进行计算返回信息的总分值
            BigDecimal lastScore = getAssessmentRecordScore(redisKey, scoreRule, manualScore, originalScore);

            // 更新总分数值
            Boolean aBoolean = updateAssessmentRecordTotalScore(assessmentRecordId, lastScore);
            if (aBoolean) {
                redisTemplateForString.opsForValue().set(redisKey, lastScore.toPlainString());

                return lastScore;
            } else {
                recordRuleItem.setManualScore(null);
                recordRuleItem.setManualOptionName(null);
                recordRuleItem.setManualOptionId(null);
                recordRuleItem.setModifyTime(new Date());
                crmTicketAssessmentRecordRuleItemService.saveOrUpdate(recordRuleItem);
            }
        }

        return null;
    }

    // ====================================================================== 评估记录WEBSocket相关方法 end

    /**
     * 查询未评估完成的规则数量
     *
     * @param recordId 评估记录ID
     * @return 未评估的规则数量
     */
    @Override
    public Integer getUnAssessmentRuleCount(String recordId) {
        List<CrmTicketAssessmentRecordRuleItem> allRecordRule = crmTicketAssessmentRecordRuleItemService.getAllRecordRule(recordId);
        // 过滤出未开始和进行中的规则。
        List<CrmTicketAssessmentRecordRuleItem> collect = allRecordRule.stream().filter(f -> (f.getAssessmentStatus() == 0 || f.getAssessmentStatus() == 1)).collect(Collectors.toList());
        return collect.size();
    }


}
