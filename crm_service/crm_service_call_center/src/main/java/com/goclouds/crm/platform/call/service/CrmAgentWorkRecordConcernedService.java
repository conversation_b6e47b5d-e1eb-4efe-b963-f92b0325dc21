package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordConcerned;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_concerned(工单关注关系表)】的数据库操作Service
* @createDate 2023-09-27 11:23:18
*/
public interface CrmAgentWorkRecordConcernedService extends IService<CrmAgentWorkRecordConcerned> {

    AjaxResult<Object> concernedWorkOrder(String workRecordId, Integer dataStatus);

    List<String> getConcernedWorkOrder(String userId);
}
