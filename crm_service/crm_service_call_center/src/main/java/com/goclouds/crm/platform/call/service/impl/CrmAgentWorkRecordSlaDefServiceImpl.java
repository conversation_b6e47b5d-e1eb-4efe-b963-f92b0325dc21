package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordSlaDef;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordTypeDef;
import com.goclouds.crm.platform.call.domain.CrmSlaRecordChannel;
import com.goclouds.crm.platform.call.domain.CrmSlaRule;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordSlaDefMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordSlaDefService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordTypeDefService;
import com.goclouds.crm.platform.call.service.CrmSlaRecordChannelService;
import com.goclouds.crm.platform.call.service.CrmSlaRuleService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelDefForSlaVO;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【crm_agent_work_record_sla_def(工单SLA定义;)】的数据库操作Service实现
 * @createDate 2023-09-21 16:50:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CrmAgentWorkRecordSlaDefServiceImpl extends ServiceImpl<CrmAgentWorkRecordSlaDefMapper, CrmAgentWorkRecordSlaDef>
        implements CrmAgentWorkRecordSlaDefService {
    private final CrmSlaRuleService ruleService;
    private final CrmSlaRecordChannelService slaRecordChannelService;
    private final CrmAgentWorkRecordTypeDefService crmAgentWorkRecordTypeDefService;
    private final ChannelClient channelClient;

    private final RedisTemplate redisTemplate;

    private final RedissonClient redissonClient;

    @Override
    public AjaxResult queryWorkRecordSla() {
        //第一步去规则表查询
        List<CrmSlaRule> ruleList = ruleService.list(
                new LambdaQueryWrapper<CrmSlaRule>()
                        .eq(CrmSlaRule::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                        .eq(CrmSlaRule::getDataStatus, 1)
                        .orderByDesc(CrmSlaRule::getCreateTime)
        );
        //进行判断，为空进行初始化
        if (ruleList.size() == 0) {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            //进行 规则表 初始化
            CrmSlaRule crmSlaRule = new CrmSlaRule();
            crmSlaRule.setWorkRecordSlaId(uuid);
            crmSlaRule.setWorkRecordSlaName("默认规则");
            crmSlaRule.setCreator(SecurityUtil.getUserId());
            crmSlaRule.setCreateTime(new Date());
            crmSlaRule.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            crmSlaRule.setInitStatus(1L);
            crmSlaRule.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            crmSlaRule.setDataStatus(1);
            ruleService.save(crmSlaRule);
            //进行 规则定义表 初始化
            List<CrmAgentWorkRecordSlaDef> initCrmAgentWorkRecordSlaDef = CrmAgentWorkRecordSlaDef.getInitCrmAgentWorkRecordSlaDef(uuid,SecurityUtil.getLoginUser().getCompanyId()).stream().sorted(Comparator.comparing(CrmAgentWorkRecordSlaDef::getPriorityLevelId).reversed()).collect(Collectors.toList());
            saveBatch(initCrmAgentWorkRecordSlaDef);
            //进行数据装填
            List<WorkRecordSlaDefRulVo> rulVo = new ArrayList<>();
            WorkRecordSlaDefRulVo workRecordSlaDefRulVo = new WorkRecordSlaDefRulVo();
            workRecordSlaDefRulVo.setWorkRecordSlaId(uuid);
            workRecordSlaDefRulVo.setWorkRecordSlaName("默认规则");
            workRecordSlaDefRulVo.setInitStatus(1L);
            workRecordSlaDefRulVo.setChannelCodes(new ArrayList<>());
            workRecordSlaDefRulVo.setWorkRecordTypeValues(new ArrayList<>());
            List<WorkRecordSlaDefVo> salDefVo =
                    BeanUtil.copyToList(initCrmAgentWorkRecordSlaDef, WorkRecordSlaDefVo.class);
            workRecordSlaDefRulVo.setSlaDefList(salDefVo);
            rulVo.add(workRecordSlaDefRulVo);
            return AjaxResult.ok(rulVo).setMsg(MessageUtils.get("operate.success"));
        } else {
            //进行数据装填
            return AjaxResult.ok(callback(ruleList)).setMsg(MessageUtils.get("operate.success"));
        }
    }


    public List<WorkRecordSlaDefRulVo> callback(List<CrmSlaRule> ruleList) {
        List<WorkRecordSlaDefRulVo> result = new ArrayList<>();
        for (CrmSlaRule crmSlaRule : ruleList) {
            WorkRecordSlaDefRulVo workRecordSlaDefRulVo = BeanUtil.copyProperties(crmSlaRule, WorkRecordSlaDefRulVo.class);
            List<CrmAgentWorkRecordSlaDef> salDef = list(
                    new LambdaQueryWrapper<CrmAgentWorkRecordSlaDef>()
                            .eq(CrmAgentWorkRecordSlaDef::getWorkRecordSlaId, workRecordSlaDefRulVo.getWorkRecordSlaId())
                            .eq(CrmAgentWorkRecordSlaDef::getDataStatus, 1)
                            .orderByDesc(CrmAgentWorkRecordSlaDef::getPriorityLevelId)
            );
            List<WorkRecordSlaDefVo> workRecordSlaDefVos = BeanUtil.copyToList(salDef, WorkRecordSlaDefVo.class);
            workRecordSlaDefRulVo.setSlaDefList(workRecordSlaDefVos);
            //判断如果不是默认规则，存在渠道和工单信息
            if (crmSlaRule.getInitStatus() != 1) {
                List<CrmSlaRecordChannel> source = slaRecordChannelService.list(
                        new LambdaQueryWrapper<CrmSlaRecordChannel>()
                                .eq(CrmSlaRecordChannel::getWorkRecordSlaId, crmSlaRule.getWorkRecordSlaId())
                                .eq(CrmSlaRecordChannel::getCompanyId, crmSlaRule.getCompanyId())
                                .eq(CrmSlaRecordChannel::getDataStatus, 1)
                );
                //对  source - channel 数据进行分组
                List<CrmSlaRecordChannel> channelList = source.stream().filter(
                        CrmSlaRecordChannel -> CrmSlaRecordChannel.getType().equals(com.goclouds.crm.platform.call.domain.CrmSlaRecordChannel.TYPE.CHANNEL_TYPE)
                ).collect(Collectors.toList());
                //获取当前渠道的code值
                List<String> collect = channelList.stream().map(CrmSlaRecordChannel::getTypeValue).collect(Collectors.toList());
                List<CrmChannelDefForSlaVO> listR = channelClient.queryChannelDefList(collect).getData();
                workRecordSlaDefRulVo.setChannelCodes(
                        BeanUtil.copyToList(listR, ChannelVO.class)
                );
                //对  source - workRecord 数据进行分组
                List<CrmSlaRecordChannel> workRecordList = source.stream().filter(
                        CrmSlaRecordChannel -> CrmSlaRecordChannel.getType().equals(com.goclouds.crm.platform.call.domain.CrmSlaRecordChannel.TYPE.WORK_RECORD_TYPE)
                ).collect(Collectors.toList());
                //工单的value值
                List<String> collect1 = workRecordList.stream().map(CrmSlaRecordChannel::getTypeValue).collect(Collectors.toList());
                workRecordSlaDefRulVo.setWorkRecordTypeValues(
                        BeanUtil.copyToList(
                                crmAgentWorkRecordTypeDefService.list(
                                        new LambdaQueryWrapper<CrmAgentWorkRecordTypeDef>()
                                                .in(CrmAgentWorkRecordTypeDef::getWorkRecordTypeValue, collect1)
                                                .eq(CrmAgentWorkRecordTypeDef::getLanguageCode, ServletUtils.getHeaderLanguage())
                                                .eq(CrmAgentWorkRecordTypeDef::getDataStatus, 1)
                                ),
                                RecordTypeVO.class
                        )
                );
            }
            result.add(workRecordSlaDefRulVo);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addOrUpdateWorkOrderSla(AddOrUpdateWorkRecordSlaDefRulVo workRecordSlaDefRulVo) {
        //规则的新增以及修改
        if (ObjectUtil.isEmpty(workRecordSlaDefRulVo.getWorkRecordSlaId()) ||
                ObjectUtil.isNull(workRecordSlaDefRulVo.getWorkRecordSlaId())
        ) {
            //数据新增
            //规则表
            //首先校验规则名称的唯一性
            if (ruleService.count(
                    new LambdaQueryWrapper<CrmSlaRule>()
                            .eq(CrmSlaRule::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                            .eq(CrmSlaRule::getDataStatus, 1)
                            .eq(CrmSlaRule::getWorkRecordSlaName, workRecordSlaDefRulVo.getWorkRecordSlaName())
            ) != 0) {
                //无法新增，进行数据返回
                return AjaxResult.failure(MessageUtils.get("agent.agent.sal.rul.creat.fail", new Object[]{workRecordSlaDefRulVo.getWorkRecordSlaName()}));

            }
            //校验规则-渠道-工单的唯一性
            Map<String, Object> map = checkCanChoose(
                    workRecordSlaDefRulVo.getChannelCodes(),
                    workRecordSlaDefRulVo.getWorkRecordTypeValues(),
                    workRecordSlaDefRulVo.getWorkRecordSlaId());
            //校验规则-渠道-工单的唯一性
            if (!Convert.toBool(map
                    .get("canCreate"))) {
                return returnData(map);
            }
            CrmSlaRule crmSlaRule = new CrmSlaRule();
            crmSlaRule.setWorkRecordSlaId(UUID.randomUUID().toString().replace("-", ""));
            crmSlaRule.setWorkRecordSlaName(workRecordSlaDefRulVo.getWorkRecordSlaName());
            crmSlaRule.setInitStatus(0L);
            crmSlaRule.setCreator(SecurityUtil.getUserId());
            crmSlaRule.setCreateTime(new Date());
            crmSlaRule.setDataStatus(1);
            crmSlaRule.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            ruleService.save(crmSlaRule);
            //规则 - 定义表
            List<CrmAgentWorkRecordSlaDef> crmAgentWorkRecordSlaDefs = BeanUtil.copyToList(workRecordSlaDefRulVo.getSlaDefList(), CrmAgentWorkRecordSlaDef.class);
            crmAgentWorkRecordSlaDefs.forEach(
                    item -> {
                        item.setWorkRecordSlaId(crmSlaRule.getWorkRecordSlaId());
                        item.setDataStatus(1);
                        item.setCreator(SecurityUtil.getUserId());
                        item.setCreateTime(new Date());
                        item.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                    }
            );
            saveBatch(crmAgentWorkRecordSlaDefs);
            //规则 - 工单 - 渠道关联表
            //渠道
            List<String> channelCodes = workRecordSlaDefRulVo.getChannelCodes();
            ;
            List<CrmSlaRecordChannel> salRecordChannel = new ArrayList<>();
            for (String channelCode : channelCodes) {
                CrmSlaRecordChannel channel = new CrmSlaRecordChannel();
                channel.setWorkRecordSlaId(crmSlaRule.getWorkRecordSlaId());
                channel.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                channel.setType(CrmSlaRecordChannel.TYPE.CHANNEL_TYPE);
                channel.setTypeValue(channelCode);
                channel.setDataStatus(1);
                channel.setCreator(SecurityUtil.getUserId());
                channel.setCreateTime(new Date());
                salRecordChannel.add(channel);
            }
            //工单
            List<String> workRecordTypeValues = workRecordSlaDefRulVo.getWorkRecordTypeValues();
            for (String workRecordTypeValue : workRecordTypeValues) {
                CrmSlaRecordChannel recordTypeValue = new CrmSlaRecordChannel();
                recordTypeValue.setWorkRecordSlaId(crmSlaRule.getWorkRecordSlaId());
                recordTypeValue.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                recordTypeValue.setType(CrmSlaRecordChannel.TYPE.WORK_RECORD_TYPE);
                recordTypeValue.setTypeValue(workRecordTypeValue);
                recordTypeValue.setDataStatus(1);
                recordTypeValue.setCreator(SecurityUtil.getUserId());
                recordTypeValue.setCreateTime(new Date());
                salRecordChannel.add(recordTypeValue);
            }
            slaRecordChannelService.saveBatch(salRecordChannel);
        } else {
            //修改
            //规则表
            if (ruleService.count(
                    new LambdaQueryWrapper<CrmSlaRule>()
                            .eq(CrmSlaRule::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                            .eq(CrmSlaRule::getDataStatus, 1)
                            .eq(CrmSlaRule::getWorkRecordSlaName, workRecordSlaDefRulVo.getWorkRecordSlaName())
                            .ne(CrmSlaRule::getWorkRecordSlaId, workRecordSlaDefRulVo.getWorkRecordSlaId())
            ) != 0) {
                //无法新增，进行数据返回
                return AjaxResult.failure(MessageUtils.get("agent.agent.sal.rul.creat.fail", new Object[]{workRecordSlaDefRulVo.getWorkRecordSlaName()}));
            }
            CrmSlaRule crmSlaRule =
                    ruleService.getOne(
                            new LambdaQueryWrapper<CrmSlaRule>()
                                    .eq(CrmSlaRule::getWorkRecordSlaId, workRecordSlaDefRulVo.getWorkRecordSlaId())
                    );
            //判断当前是否为默认规则
            if(crmSlaRule.getInitStatus() == 1){
                //不存在工单和渠道类型 - 无需进行判断
                //默认只允许修改时间，名称也不允许修改
                //根据优先级id进行数据替换
                //规则 - 定义表
                List<CrmAgentWorkRecordSlaDef> source = list(
                        new LambdaQueryWrapper<CrmAgentWorkRecordSlaDef>()
                                .eq(CrmAgentWorkRecordSlaDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                                .eq(CrmAgentWorkRecordSlaDef::getWorkRecordSlaId, crmSlaRule.getWorkRecordSlaId())
                                .eq(CrmAgentWorkRecordSlaDef::getDataStatus, 1)
                );
                Map<String, List<WorkRecordSlaDefVo>> order = workRecordSlaDefRulVo.getSlaDefList().stream()
                        .collect(Collectors.groupingBy(
                                WorkRecordSlaDefVo::getPriorityLevelId
                        ));
                for (CrmAgentWorkRecordSlaDef crmAgentWorkRecordSlaDef : source) {
                    WorkRecordSlaDefVo workRecordSlaDefVo = order.get(crmAgentWorkRecordSlaDef.getPriorityLevelId()).get(0);
                    this.update(
                            new LambdaUpdateWrapper<CrmAgentWorkRecordSlaDef>()
                                    .eq(CrmAgentWorkRecordSlaDef::getWorkRecordSlaId, workRecordSlaDefRulVo.getWorkRecordSlaId())
                                    .eq(CrmAgentWorkRecordSlaDef::getPriorityLevelId, workRecordSlaDefVo.getPriorityLevelId())
                                    .set(CrmAgentWorkRecordSlaDef::getResolveTime, workRecordSlaDefVo.getResolveTime())
                                    .set(CrmAgentWorkRecordSlaDef::getResolveTimeUnit, workRecordSlaDefVo.getResolveTimeUnit())
                                    .set(CrmAgentWorkRecordSlaDef::getNextResponseTime, workRecordSlaDefVo.getNextResponseTime())
                                    .set(CrmAgentWorkRecordSlaDef::getNextResponseTimeUnit, workRecordSlaDefVo.getNextResponseTimeUnit())
                                    .set(CrmAgentWorkRecordSlaDef::getResponseTime, workRecordSlaDefVo.getResponseTime())
                                    .set(CrmAgentWorkRecordSlaDef::getResponseTimeUnit, workRecordSlaDefVo.getResponseTimeUnit())
                                    .set(CrmAgentWorkRecordSlaDef::getModifier, SecurityUtil.getUserId())
                                    .set(CrmAgentWorkRecordSlaDef::getModifyTime, LocalDateTime.now())
                    );
                }
            }else {
                Map<String, Object> map = checkCanChoose(
                        workRecordSlaDefRulVo.getChannelCodes(),
                        workRecordSlaDefRulVo.getWorkRecordTypeValues(),
                        workRecordSlaDefRulVo.getWorkRecordSlaId());
                //校验规则-渠道-工单的唯一性
                if (!Convert.toBool(map
                        .get("canCreate"))) {
                    return returnData(map);
                }
                ruleService.update(
                        new LambdaUpdateWrapper<CrmSlaRule>()
                                .eq(CrmSlaRule::getWorkRecordSlaId, workRecordSlaDefRulVo.getWorkRecordSlaId())
                                .set(CrmSlaRule::getWorkRecordSlaName, workRecordSlaDefRulVo.getWorkRecordSlaName())
                                .set(CrmSlaRule::getModifier, SecurityUtil.getLoginUser().getUserId())
                                .set(CrmSlaRule::getModifyTime, new Date())
                );
                //规则 - 定义表
                List<CrmAgentWorkRecordSlaDef> source = list(
                        new LambdaQueryWrapper<CrmAgentWorkRecordSlaDef>()
                                .eq(CrmAgentWorkRecordSlaDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                                .eq(CrmAgentWorkRecordSlaDef::getWorkRecordSlaId, crmSlaRule.getWorkRecordSlaId())
                                .eq(CrmAgentWorkRecordSlaDef::getDataStatus, 1)
                );
                Map<String, List<WorkRecordSlaDefVo>> order = workRecordSlaDefRulVo.getSlaDefList().stream()
                        .collect(Collectors.groupingBy(
                                WorkRecordSlaDefVo::getPriorityLevelId
                        ));
                //根据优先级id进行数据替换
                for (CrmAgentWorkRecordSlaDef crmAgentWorkRecordSlaDef : source) {
                    WorkRecordSlaDefVo workRecordSlaDefVo = order.get(crmAgentWorkRecordSlaDef.getPriorityLevelId()).get(0);
                    this.update(
                            new LambdaUpdateWrapper<CrmAgentWorkRecordSlaDef>()
                                    .eq(CrmAgentWorkRecordSlaDef::getWorkRecordSlaId,workRecordSlaDefRulVo.getWorkRecordSlaId())
                                    .eq(CrmAgentWorkRecordSlaDef::getPriorityLevelId,workRecordSlaDefVo.getPriorityLevelId())
                                    .set(CrmAgentWorkRecordSlaDef::getResolveTime,workRecordSlaDefVo.getResolveTime())
                                    .set(CrmAgentWorkRecordSlaDef::getResolveTimeUnit,workRecordSlaDefVo.getResolveTimeUnit())
                                    .set(CrmAgentWorkRecordSlaDef::getNextResponseTime,workRecordSlaDefVo.getNextResponseTime())
                                    .set(CrmAgentWorkRecordSlaDef::getNextResponseTimeUnit,workRecordSlaDefVo.getNextResponseTimeUnit())
                                    .set(CrmAgentWorkRecordSlaDef::getResponseTime,workRecordSlaDefVo.getResponseTime())
                                    .set(CrmAgentWorkRecordSlaDef::getResponseTimeUnit,workRecordSlaDefVo.getResponseTimeUnit())
                                    .set(CrmAgentWorkRecordSlaDef::getModifier,SecurityUtil.getUserId())
                                    .set(CrmAgentWorkRecordSlaDef::getModifyTime,LocalDateTime.now())
                    );
                }
                //规则 - 工单 - 渠道关联表
                //直接删除当前规则的从属信息
                slaRecordChannelService.update(
                        new LambdaUpdateWrapper<CrmSlaRecordChannel>()
                                .eq(CrmSlaRecordChannel::getWorkRecordSlaId, crmSlaRule.getWorkRecordSlaId())
                                .set(CrmSlaRecordChannel::getDataStatus, 0)
                );
                //渠道
                List<String> channelCodes = workRecordSlaDefRulVo.getChannelCodes();
                List<CrmSlaRecordChannel> salRecordChannel = new ArrayList<>();
                for (String channelCode : channelCodes) {
                    CrmSlaRecordChannel channel = new CrmSlaRecordChannel();
                    channel.setWorkRecordSlaId(crmSlaRule.getWorkRecordSlaId());
                    channel.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                    channel.setType(CrmSlaRecordChannel.TYPE.CHANNEL_TYPE);
                    channel.setTypeValue(channelCode);
                    channel.setDataStatus(1);
                    channel.setCreator(SecurityUtil.getUserId());
                    channel.setCreateTime(new Date());
                    salRecordChannel.add(channel);
                }
                //工单
                List<String> workRecordTypeValues = workRecordSlaDefRulVo.getWorkRecordTypeValues();
                for (String workRecordTypeValue : workRecordTypeValues) {
                    CrmSlaRecordChannel recordTypeValue = new CrmSlaRecordChannel();
                    recordTypeValue.setWorkRecordSlaId(crmSlaRule.getWorkRecordSlaId());
                    recordTypeValue.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
                    recordTypeValue.setType(CrmSlaRecordChannel.TYPE.WORK_RECORD_TYPE);
                    recordTypeValue.setTypeValue(workRecordTypeValue);
                    recordTypeValue.setDataStatus(1);
                    recordTypeValue.setCreator(SecurityUtil.getUserId());
                    recordTypeValue.setCreateTime(new Date());
                    salRecordChannel.add(recordTypeValue);
                    salRecordChannel.add(recordTypeValue);
                }
                slaRecordChannelService.saveBatch(salRecordChannel);
            }
            }
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    public AjaxResult returnData(Map<String, Object> map) {
        Object channel = map.get("channel");
        Object recordType = map.get("recordType");
        if (ObjectUtil.isNotNull(channel) && ObjectUtil.isNotNull(recordType)) {
            return AjaxResult.failure(MessageUtils.get("agent.agent.sal.rul.channel.record.creat.fail",
                    new Object[]{
                            map.get("channel"), map.get("recordType")
                    }));
        } else if (ObjectUtil.isNotNull(channel) && ObjectUtil.isNull(recordType)) {
            return AjaxResult.failure(MessageUtils.get("agent.agent.sal.rul.channel.creat.fail",
                    new Object[]{
                            map.get("channel")
                    }));
        } else if (ObjectUtil.isNull(channel) && ObjectUtil.isNotNull(recordType)) {
            return AjaxResult.failure(MessageUtils.get("agent.agent.sal.rul.record.creat.fail",
                    new Object[]{
                            map.get("recordType")
                    }));
        }
        return AjaxResult.ok();
    }

    /**
     *  不同的工单可以选择相同的渠道
     * @param channelSource
     * @param recordTypeSource
     * @param salRuleId
     * @return
     */
    public Map<String, Object> checkCanChoose(List<String> channelSource, List<String> recordTypeSource, String salRuleId) {
        Map<String, Object> result = new HashMap<>();
        List<String> channel = channelSource;
        List<String> recordType = recordTypeSource;
        //获取当前所有已使用的渠道和工单类型
        List<CrmSlaRecordChannel> list = slaRecordChannelService.list(
                new LambdaQueryWrapper<CrmSlaRecordChannel>()
                        .eq(CrmSlaRecordChannel::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                        .eq(CrmSlaRecordChannel::getDataStatus, 1)
        );
        if (list.size() == 0) {
            //当前没有规则-渠道-工单数据，可以创建
            result.put("canCreate", true);
        } else {
            //整理每个规则的所携带的渠道和工单数据
            ////进行筛选 - 分别匹配获取对应的交集
            Map<String, List<CrmSlaRecordChannel>> collect = new HashMap<>();
            if (ObjectUtil.isNull(salRuleId)) {
                collect = list.stream().collect(Collectors.groupingBy(CrmSlaRecordChannel::getWorkRecordSlaId));
            } else {
                collect = list
                        .stream().filter(CrmSlaRecordChannel -> !CrmSlaRecordChannel.getWorkRecordSlaId().equals(salRuleId)).collect(Collectors.toList())
                        .stream().collect(Collectors.groupingBy(CrmSlaRecordChannel::getWorkRecordSlaId));
            }
            //获取每个规则下的工单和渠道进行map添加
            Map<String,List<String>> map = new HashMap<>();
            for (Map.Entry<String, List<CrmSlaRecordChannel>> stringListEntry : collect.entrySet()) {
                //获取所有的工单类型
                List<CrmSlaRecordChannel> recordList = stringListEntry.getValue().stream().filter(CrmSlaRecordChannel -> CrmSlaRecordChannel.getType().equals(2L)).collect(Collectors.toList());
                //获取所有的渠道类型
                List<CrmSlaRecordChannel> channelList = stringListEntry.getValue().stream().filter(CrmSlaRecordChannel -> CrmSlaRecordChannel.getType().equals(1L)).collect(Collectors.toList());
                recordList.forEach(
                        item -> {
                            //获取渠道的所有名称
                            List<String> channelNameList = channelList.stream().map(CrmSlaRecordChannel::getTypeValue).collect(Collectors.toList());
                            List<String> strings = map.get(item.getTypeValue());
                            if(ObjectUtil.isNotNull(strings) && strings.size()!=0) {
                                strings.addAll(channelNameList);
                                //进行去重
                                strings = strings.stream().distinct().collect(Collectors.toList());
                            }else {
                                map.put(item.getTypeValue(), channelNameList);
                            }
                        }
                );
            }
            //针对用户的传值进行计算，
            List<String> canRecord = new ArrayList<>();
            List<String> canNoRecord = new ArrayList<>();
            List<String> canChannel = new ArrayList<>();
            List<String> canNoChannel = new ArrayList<>();
            recordType.forEach(
                    item -> {
                        List<String> strings = map.get(item);
                        if(ObjectUtil.isNull(strings)||strings.size()==0) {
                            //没有进行过对应工单创建，
                            canRecord.add(item);
                        } else {
                            //获取当前工单类型中存在的渠道类型，去交集，获取不可可创建的渠道类型
                            List<String> channelNoCanCreate = strings.stream().filter(
                                    a -> channel.contains(a)
                            ).collect(Collectors.toList());
                            if(ObjectUtil.isNull(channelNoCanCreate)||channelNoCanCreate.size()==0){
                                canRecord.add(item);
                            }else {
                                canNoRecord.add(item);
                                canNoChannel.addAll(channelNoCanCreate);
                            }
                        }
                    }
            );
            if(canNoChannel.size() == 0){
                canChannel = channelSource;
            }
            if(canRecord.size() == recordTypeSource.size() && canChannel.size() == channelSource.size()){
                result.put("canCreate",true);
            }else {
                result.put("canCreate",false);
            }

                if (canRecord.size() == recordTypeSource.size()) {
                    result.put("recordType", null);
                } else {
                    //获取差集
                    //不能创建
                    List<String> recordTypeNotCanCreateName = crmAgentWorkRecordTypeDefService.list(
                            new LambdaQueryWrapper<CrmAgentWorkRecordTypeDef>()
                                    .in(CrmAgentWorkRecordTypeDef::getWorkRecordTypeValue, canNoRecord)
                                    .eq(CrmAgentWorkRecordTypeDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                                    .eq(CrmAgentWorkRecordTypeDef::getLanguageCode, ServletUtils.getHeaderLanguage())
                                    .eq(CrmAgentWorkRecordTypeDef::getDataStatus, 1)
                    ).stream().map(CrmAgentWorkRecordTypeDef::getWorkRecordTypeName).collect(Collectors.toList());
                    result.put("recordType", recordTypeNotCanCreateName);
                    }

                    if (canChannel.size() == channelSource.size()) {
                        result.put("channel", null);
                    } else {
                        //获取交集
                        List<String> channelNotCanCreateName = null;
                        if(canNoChannel.size() == 0){
                            channelNotCanCreateName = channelClient.queryChannelDefList(channelSource).getData().stream().map(CrmChannelDefForSlaVO::getName).collect(Collectors.toList());
                            result.put("channel", channelNotCanCreateName);
                        }else {
                            channelNotCanCreateName = channelClient.queryChannelDefList(canNoChannel).getData().stream().map(CrmChannelDefForSlaVO::getName).collect(Collectors.toList());
                            result.put("channel", channelNotCanCreateName);
                        }
                        }
                    }
        return result;
    }

    @Override
    public CrmAgentWorkRecordSlaDef queryLevelSla(String priorityLevelId, String companyId) {

        //如果根据公司id查询不到数据，就返回默认配置数据
        QueryWrapper<CrmAgentWorkRecordSlaDef> queryWrapper = new QueryWrapper<CrmAgentWorkRecordSlaDef>()
                .eq("company_id", companyId)
                .eq("priority_level_id", priorityLevelId)
                .eq("data_status", Constants.NORMAL);
        // TODO 需要修改根据工单类型
        List<CrmAgentWorkRecordSlaDef> workRecordSlaDefs = this.baseMapper.selectList(queryWrapper);

        CrmAgentWorkRecordSlaDef workRecordSlaDef = null;
//        CrmAgentWorkRecordSlaDef workRecordSlaDef = this.baseMapper.selectOne(queryWrapper);
        List<WorkRecordSlaDefVo> defVoList = new ArrayList<>();
        // 未查询到,则查询默认
        if (StringUtil.isNull(workRecordSlaDef)) {
            //查询默认的
            QueryWrapper<CrmAgentWorkRecordSlaDef> queryDefaultWrapper = new QueryWrapper<CrmAgentWorkRecordSlaDef>()
                    .isNull("company_id")
                    .eq("priority_level_id", priorityLevelId)
                    .eq("data_status", Constants.NORMAL);
            workRecordSlaDef = this.baseMapper.selectOne(queryDefaultWrapper);
        } else {
            workRecordSlaDef = workRecordSlaDefs.get(0);
        }
        return workRecordSlaDef;
    }

    /**
     * @param companyId           公司id
     * @param channelCode         渠道类型
     * @param workRecordTypeValue 工单类型
     * @return
     */
    @Override
    public CrmAgentWorkRecordSlaDef queryLevelSla(String companyId, String channelCode, String workRecordTypeValue, String priorityLevelId) {
        RLock lock = redissonClient.getLock(companyId);
        lock.lock();
        try {
            //规则 - 渠道 - 工单
            List<String> channel = slaRecordChannelService.list(
                    new LambdaQueryWrapper<CrmSlaRecordChannel>()
                            .eq(CrmSlaRecordChannel::getCompanyId, companyId)
                            .eq(CrmSlaRecordChannel::getDataStatus, 1)
                            .eq(CrmSlaRecordChannel::getTypeValue, channelCode)
                            .eq(CrmSlaRecordChannel::getType, CrmSlaRecordChannel.TYPE.CHANNEL_TYPE)
            ).stream().map(CrmSlaRecordChannel::getWorkRecordSlaId).collect(Collectors.toList());
            List<String> recordType = slaRecordChannelService.list(
                    new LambdaQueryWrapper<CrmSlaRecordChannel>()
                            .eq(CrmSlaRecordChannel::getCompanyId, companyId)
                            .eq(CrmSlaRecordChannel::getDataStatus, 1)
                            .eq(CrmSlaRecordChannel::getTypeValue, workRecordTypeValue)
                            .eq(CrmSlaRecordChannel::getType, CrmSlaRecordChannel.TYPE.WORK_RECORD_TYPE)
            ).stream().map(CrmSlaRecordChannel::getWorkRecordSlaId).collect(Collectors.toList());
            //获取二者交集
            List<String> collect = channel.stream().filter(
                    a -> recordType.contains(a)
            ).collect(Collectors.toList());
            if (collect.size() == 0) {
                CrmSlaRule one = new CrmSlaRule();
                //返回默认规则
                //规则表
                 one = ruleService.getOne(
                        new LambdaQueryWrapper<CrmSlaRule>()
                                .eq(CrmSlaRule::getCompanyId, companyId)
                                .eq(CrmSlaRule::getInitStatus, 1)
                                .eq(CrmSlaRule::getDataStatus, 1)
                );
                //可能出现默认规则未null的情况，是因为默认规则会在管理员点击工单的时候自动配置
                if(ObjectUtil.isNull(one) || ObjectUtil.isEmpty(one)){
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    //进行 规则表 初始化
                    CrmSlaRule crmSlaRule = new CrmSlaRule();
                    crmSlaRule.setWorkRecordSlaId(uuid);
                    crmSlaRule.setWorkRecordSlaName("默认规则");
                    crmSlaRule.setCreator("system");
                    crmSlaRule.setCreateTime(new Date());
                    crmSlaRule.setInitStatus(1L);
                    crmSlaRule.setCompanyId(companyId);
                    crmSlaRule.setDataStatus(1);
                    ruleService.save(crmSlaRule);
                    //进行 规则定义表 初始化
                    List<CrmAgentWorkRecordSlaDef> initCrmAgentWorkRecordSlaDef = CrmAgentWorkRecordSlaDef.getInitCrmAgentWorkRecordSlaDef(uuid,companyId).stream().sorted(Comparator.comparing(CrmAgentWorkRecordSlaDef::getPriorityLevelId).reversed()).collect(Collectors.toList());
                    saveBatch(initCrmAgentWorkRecordSlaDef);
                     one = ruleService.getOne(
                            new LambdaQueryWrapper<CrmSlaRule>()
                                    .eq(CrmSlaRule::getCompanyId, companyId)
                                    .eq(CrmSlaRule::getInitStatus, 1)
                                    .eq(CrmSlaRule::getDataStatus, 1)
                    );
                }
                return getOne(
                        new LambdaUpdateWrapper<CrmAgentWorkRecordSlaDef>()
                                .eq(CrmAgentWorkRecordSlaDef::getCompanyId, companyId)
                                .eq(CrmAgentWorkRecordSlaDef::getPriorityLevelId, priorityLevelId)
                                .eq(CrmAgentWorkRecordSlaDef::getDataStatus, 1)
                                .eq(CrmAgentWorkRecordSlaDef::getWorkRecordSlaId, one.getWorkRecordSlaId())
                );
            } else {
                String id = collect.get(0);
                //规则定义
                return getOne(
                        new LambdaUpdateWrapper<CrmAgentWorkRecordSlaDef>()
                                .eq(CrmAgentWorkRecordSlaDef::getCompanyId, companyId)
                                .eq(CrmAgentWorkRecordSlaDef::getPriorityLevelId, priorityLevelId)
                                .eq(CrmAgentWorkRecordSlaDef::getDataStatus, 1)
                                .eq(CrmAgentWorkRecordSlaDef::getWorkRecordSlaId, id)
                );
            }
        }finally {
            lock.unlock();
        }
    }

    @Override
    public AjaxResult removeAllSla(String slaRuleId) {
        //规则表
        ruleService.update(
                new LambdaUpdateWrapper<CrmSlaRule>()
                        .eq(CrmSlaRule::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                        .eq(CrmSlaRule::getWorkRecordSlaId, slaRuleId)
                        .set(CrmSlaRule::getDataStatus, 0)
        );
        //规则定义
        update(
                new LambdaUpdateWrapper<CrmAgentWorkRecordSlaDef>()
                        .eq(CrmAgentWorkRecordSlaDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                        .eq(CrmAgentWorkRecordSlaDef::getWorkRecordSlaId, slaRuleId)
                        .set(CrmAgentWorkRecordSlaDef::getDataStatus, 0)
        );
        //规则 - 渠道 - 工单
        slaRecordChannelService.update(
                new LambdaUpdateWrapper<CrmSlaRecordChannel>()
                        .eq(CrmSlaRecordChannel::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                        .eq(CrmSlaRecordChannel::getWorkRecordSlaId, slaRuleId)
                        .set(CrmSlaRecordChannel::getDataStatus, 0)
        );
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));

    }

    /**
     * 进行数据缓存
     *
     * @param workRecordId 工单id
     * @param replyType    回复人类型 1 - 客服、2 - 客户
     * @param now          当前时间
     */
    @Override
    public void redisDataCache(String companyId,String workRecordId, Integer replyType, LocalDateTime now) {
        if(replyType == 1 || replyType ==2 || replyType == 6){
        //首先进行数据读取
        Object o = redisTemplate.opsForValue().get(companyId+workRecordId);
        if (ObjectUtil.isNull(o)) {
            //首次的数据加载
            List<RedisDataCacheVO> result = new ArrayList<>();
            if(replyType == 6){
                replyType = 2;
            }
            result.add(new RedisDataCacheVO(replyType, Convert.toStr(now)));
            //根据传入的工单id、类型，进行数据存储
            redisTemplate.opsForValue().set(
                    companyId+workRecordId, result
            );
        } else {
            if (replyType != 6){
                List<RedisDataCacheVO> redisDataCacheVOS = Convert.toList(RedisDataCacheVO.class, o);
                if(redisDataCacheVOS.get(redisDataCacheVOS.size()-1).getReplyType() != replyType){
                    //获取当前信息的最后一条
                        //进行数据添加
                        redisDataCacheVOS.add(new RedisDataCacheVO(replyType, Convert.toStr(now)));
                    redisTemplate.opsForValue().set(companyId+workRecordId, redisDataCacheVOS);
                }
            }
         }
        }
    }

    /**
     * 根据工单id、时间单位进行平均时间计算
     *
     * @param workRecordId 工单id
     * @param companyId         公司id
     * @return
     */
    @Override
    public CacheTimeVO calculationTime(String companyId,String workRecordId) {
        Object o = redisTemplate.opsForValue().get(companyId+workRecordId);
        if(ObjectUtil.isNull(o)){
            //没有时间
            CacheTimeVO cacheTimeVO = new CacheTimeVO();
            cacheTimeVO.setFirstReplyTime(null);
            cacheTimeVO.setAverageReplyTime(null);
            return cacheTimeVO;
        }else {
            List<RedisDataCacheVO> redisDataCacheVOList = Convert.toList(RedisDataCacheVO.class, o);
            //判断集合长度>=1 - 一定有一次的交互
            if(redisDataCacheVOList.size()>=1){
                //默认最后一条数据一定是客服的信息，否则进行长度-1
                //每次间隔时间相加  /  集合长度/2 = 平均响应时间

                //如果第一条为座席发送，那么进行删除，无意义数据
                if (
                        redisDataCacheVOList.get(0).getReplyType() == 1
                ) {
                    redisDataCacheVOList.remove(0);
                }

                //如果最后一条是用户信息，也进行删除，无意义数据
                if(redisDataCacheVOList.get(redisDataCacheVOList.size()-1).getReplyType() == 2){
                    redisDataCacheVOList.remove(redisDataCacheVOList.size()-1);
                }

                Long result = 0L;
                Integer count = 1;
                for (int i = redisDataCacheVOList.size() - 1; i > 0; i--) {
                    result += calculation(
                            //座席
                            redisDataCacheVOList.get(i).getTime(),
                            //用户
                            redisDataCacheVOList.get(i - 1).getTime()
                    );
                    i --;
                    count++;
                }
                //进行数据过滤之后，集合中的第二条一定会是
                String time = redisDataCacheVOList.get(1).getTime();
                CacheTimeVO cacheTimeVO = new CacheTimeVO();
                cacheTimeVO.setFirstReplyTime(Convert.toDate(time));
                //平均时间

                cacheTimeVO.setAverageReplyTime(Convert.toInt(result)/count);
                return cacheTimeVO;
            }else {
                //没有时间
                CacheTimeVO cacheTimeVO = new CacheTimeVO();
                cacheTimeVO.setFirstReplyTime(null);
                cacheTimeVO.setAverageReplyTime(null);
                return cacheTimeVO;
            }

        }
    }

    public Long calculation(String right, String left) {
        Long rightSecond = convert(Convert.toLocalDateTime(right));
        Long leftSecond = convert(Convert.toLocalDateTime(left));
        Long time = rightSecond - leftSecond;
        //绝对值
        return Math.abs(time);
    }

    /**
     * 将localdatatime转换成秒
     *
     * @param time
     * @return
     */
    public Long convert(LocalDateTime time) {
        // 将LocalDateTime转换为ZonedDateTime，使用系统默认时区
        Date gradeTime = Date.from(time.toInstant(ZoneOffset.ofHours(8)));
        // 将ZonedDateTime转换为Instant
        Instant instant = gradeTime.toInstant();
        // 从Instant获取以秒为单位的时间戳
        return Convert.toLong(instant.getEpochSecond());
    }

    @Override
    public void redisDateCacheDelete(String companyId,String workRecordId) {
        try {
            redisTemplate.delete(companyId+workRecordId);
        } catch (Exception e) {
            log.error("当前公司 => {},和对应的工单 => {}",companyId,workRecordId);
        }
    }
}




