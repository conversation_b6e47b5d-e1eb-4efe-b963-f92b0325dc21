package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.CrmCallPushMessageConfig;
import com.goclouds.crm.platform.call.domain.CrmCallWorkTime;
import com.goclouds.crm.platform.call.domain.CrmCallWorkTimeHoliday;
import com.goclouds.crm.platform.call.domain.CrmCallWorkTimeHour;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.WorkTimeMessageLog;
import com.goclouds.crm.platform.call.domain.vo.WorkTimeDataHandleVO;
import com.goclouds.crm.platform.call.domain.vo.WorkTimeDetailVO;
import com.goclouds.crm.platform.call.domain.vo.WorkTimeInspectionVO;
import com.goclouds.crm.platform.call.mapper.CrmCallWorkTimeMapper;
import com.goclouds.crm.platform.call.service.CrmCallPushMessageConfigService;
import com.goclouds.crm.platform.call.service.CrmCallWorkTimeHolidayService;
import com.goclouds.crm.platform.call.service.CrmCallWorkTimeHourService;
import com.goclouds.crm.platform.call.service.CrmCallWorkTimeService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.enums.WorkTimePushMessageEnum;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.channel.CrmChannelConfigVO;
import com.goclouds.crm.platform.utils.ElasticsearchUtil;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @description 针对表【crm_call_work_time(工作计划配置)】的数据库操作Service实现
 * @createDate 2025-02-07 16:58:25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CrmCallWorkTimeServiceImpl extends ServiceImpl<CrmCallWorkTimeMapper, CrmCallWorkTime>
        implements CrmCallWorkTimeService {

    private final CrmCallWorkTimeHourService crmCallWorkTimeHourService;

    private final CrmCallWorkTimeHolidayService crmCallWorkTimeHolidayService;

    private final ChannelClient channelClient;

    private final ElasticsearchUtil elasticsearchUtil;

    private final RestHighLevelClient restHighLevelClient;

    private final CrmCallPushMessageConfigService crmCallPushMessageConfigService;

    private final RestTemplate restTemplate;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    @Value("${es.work-time-message-log}")
    private String workTimeMessageLogIndex;

    private static final String ALGORITHM = "HmacSHA256";

    @Override
    public AjaxResult<IPage<CrmCallWorkTime>> queryWorkTimeList(IPage<Object> pageParam) {
        QueryWrapper<CrmCallWorkTime> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_status", 1);
        queryWrapper.eq("company_id", SecurityUtil.getLoginUser().getCompanyId());
        queryWrapper.orderByDesc("create_time");
        return AjaxResult.ok(this.baseMapper.queryWorkTimeList(pageParam, queryWrapper));
    }

    @Override
    public WorkTimeDetailVO workTimeDetail(String workTimeId) {

        WorkTimeDetailVO workTimeDetailVO = new WorkTimeDetailVO();
        // 查询出当前工作时间的详细信息
        CrmCallWorkTime crmCallWorkTime = this.baseMapper.selectById(workTimeId);
        BeanUtils.copyProperties(crmCallWorkTime, workTimeDetailVO);
        // 查询工作日工作时间
        List<CrmCallWorkTimeHour> list = crmCallWorkTimeHourService.list(new QueryWrapper<CrmCallWorkTimeHour>().lambda()
                .eq(CrmCallWorkTimeHour::getWorkTimeId, workTimeId)
                .eq(CrmCallWorkTimeHour::getDataStatus, 1)
                .orderByAsc(CrmCallWorkTimeHour::getStartTime));
        // 按 weekType 分组
        Map<Integer, List<CrmCallWorkTimeHour>> groupedMap = list.stream()
                .collect(Collectors.groupingBy(CrmCallWorkTimeHour::getWeekType));

        // 初始化完整的 weekType 1-7，即使没有数据也添加空列表
        Map<Integer, List<CrmCallWorkTimeHour>> completeMap = new HashMap<>();
        IntStream.rangeClosed(1, 7).forEach(week ->
                completeMap.put(week, groupedMap.getOrDefault(week, new ArrayList<>()))
        );
        workTimeDetailVO.setWorkTimeHoursMap(completeMap);
        // 查询节假日信息
        List<CrmCallWorkTimeHoliday> timeHolidaysList = crmCallWorkTimeHolidayService.list(new QueryWrapper<CrmCallWorkTimeHoliday>().lambda()
                .eq(CrmCallWorkTimeHoliday::getWorkTimeId, workTimeId)
                .eq(CrmCallWorkTimeHoliday::getDataStatus, 1)
                .orderByAsc(CrmCallWorkTimeHoliday::getStartDate));
        workTimeDetailVO.setTimeHolidaysList(timeHolidaysList);
        return workTimeDetailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> addOrUpdateWorkTime(WorkTimeDataHandleVO workOrderExtVo) {
        CrmCallWorkTime crmCallWorkTime = new CrmCallWorkTime();
        BeanUtils.copyProperties(workOrderExtVo, crmCallWorkTime);
        // 没有工作时间id则证明新增,有则证明修改
        if (StringUtil.isEmpty(workOrderExtVo.getWorkTimeId())) {
            crmCallWorkTime.setWorkTimeId(UUID.randomUUID().toString().replace("-", ""));
            crmCallWorkTime.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            crmCallWorkTime.setCreateTime(new Date());
            crmCallWorkTime.setCreator(SecurityUtil.getUserId());
            crmCallWorkTime.setModifyTime(new Date());
            crmCallWorkTime.setDataStatus(1);
            this.baseMapper.insert(crmCallWorkTime);

        } else {
            // 首先修改 工作时间信息
            crmCallWorkTime.setModifyTime(new Date());
            crmCallWorkTime.setModifier(SecurityUtil.getUserId());
            crmCallWorkTime.setDataStatus(1);
            this.baseMapper.updateById(crmCallWorkTime);
            // 删除旧的工作时间数据
            crmCallWorkTimeHourService.remove(new QueryWrapper<CrmCallWorkTimeHour>().lambda()
                    .eq(CrmCallWorkTimeHour::getWorkTimeId,crmCallWorkTime.getWorkTimeId()));
            // 删除旧的节假日信息
            crmCallWorkTimeHolidayService.remove(new QueryWrapper<CrmCallWorkTimeHoliday>().lambda()
                    .eq(CrmCallWorkTimeHoliday::getWorkTimeId, crmCallWorkTime.getWorkTimeId()));
        }


        // 开始添加工作日信息
        workOrderExtVo.getWorkTimeHoursList().forEach(item -> {
            item.setHourId(UUID.randomUUID().toString().replace("-", ""));
            item.setWorkTimeId(crmCallWorkTime.getWorkTimeId());
            item.setCreateTime(new Date());
            item.setCreator(SecurityUtil.getUserId());
            item.setModifyTime(new Date());
            item.setDataStatus(1);
        });
        crmCallWorkTimeHourService.saveBatch(workOrderExtVo.getWorkTimeHoursList());
        // 开始添加假期时间信息
        workOrderExtVo.getTimeHolidaysList().forEach(item -> {
            item.setHolidayId(UUID.randomUUID().toString().replace("-", ""));
            item.setWorkTimeId(crmCallWorkTime.getWorkTimeId());
            item.setCreateTime(new Date());
            item.setCreator(SecurityUtil.getUserId());
            item.setModifyTime(new Date());
            item.setDataStatus(1);
        });
        crmCallWorkTimeHolidayService.saveBatch(workOrderExtVo.getTimeHolidaysList());
        return AjaxResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> workTimeDel(String workTimeId) {
        CrmCallWorkTime crmCallWorkTime = new CrmCallWorkTime();
        crmCallWorkTime.setWorkTimeId(workTimeId);
        crmCallWorkTime.setModifyTime(new Date());
        crmCallWorkTime.setModifier(SecurityUtil.getUserId());
        crmCallWorkTime.setDataStatus(0);
        // 首先逻辑删除工作时间信息
        this.baseMapper.updateById(crmCallWorkTime);
        // 删除工作日
        crmCallWorkTimeHourService.update(new UpdateWrapper<CrmCallWorkTimeHour>().lambda()
                .eq(CrmCallWorkTimeHour::getWorkTimeId, workTimeId)
                .set(CrmCallWorkTimeHour::getDataStatus, 0));
        // 删除节假日
        crmCallWorkTimeHolidayService.update(new UpdateWrapper<CrmCallWorkTimeHoliday>().lambda()
                .eq(CrmCallWorkTimeHoliday::getWorkTimeId, workTimeId)
                .set(CrmCallWorkTimeHoliday::getDataStatus, 0));
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult<Object> workTimeInspection(WorkTimeInspectionVO workTimeInspectionVO) {
        // 首先根据channelId,取出对应的work_time_id
        R<Map<String, String>> idConfigInst = channelClient.queryChannelIdConfigInst(workTimeInspectionVO.getChannelId());
        if (AjaxResult.SUCCESS != idConfigInst.getCode()) {
            log.error("根据渠道ID查询渠道配置信息出现异常：{}", idConfigInst.getMsg());
            return AjaxResult.failure();
        }
        // 根据channelId，查询出对应的公司信息
        R<CrmChannelConfigVO> crmChannelConfigVOR = channelClient.queryChannelConfig(workTimeInspectionVO.getChannelId());
        if (AjaxResult.SUCCESS != idConfigInst.getCode()) {
            log.error("根据渠道ID查询渠道配置信息出现异常：{}", idConfigInst.getMsg());
            return AjaxResult.failure();
        }
        // 定义工单编号值，防止查询工单失败，先赋值为工单id
        String ticketCode = workTimeInspectionVO.getTicketId();
        // 取出公司id的值
        String companyId = null;
        String channelType = "";
        String channelName = "";
        if (crmChannelConfigVOR.getData() != null) {
            companyId = crmChannelConfigVOR.getData().getCompanyId();
            channelType = CrmChannelEnum.getNameByCode(crmChannelConfigVOR.getData().getChannelType());
            channelName = crmChannelConfigVOR.getData().getName();
        }
        // 查询出工单信息
        if (StringUtil.isNotEmpty(workTimeInspectionVO.getTicketId()) && StringUtil.isNotEmpty(companyId)) {
            TicketInfoIndex ticketInfoIndex = queryTicket(workTimeInspectionVO.getTicketId(), companyId);
            if (ticketInfoIndex != null) {
                ticketCode = ticketInfoIndex.getWordRecordCode();
            }
        }

        Map<String, String> channelConfigMap = idConfigInst.getData();
        String workTimeId = channelConfigMap.get("agent_worktime_id");
        // 根据work_time_id查询出对应的时区
        CrmCallWorkTime callWorkTime = this.baseMapper.selectById(workTimeId);
        if (callWorkTime == null) {
            log.info("该渠道下没有工作时间配置渠道id为:" + workTimeInspectionVO.getChannelId());
            messageHandle(workTimeInspectionVO, ticketCode, companyId,channelType, channelName);
            return AjaxResult.ok(0);
        }
        // 将当前时区转换为对应的一个时区时间
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.now();
        // 服务器时间为 UTC+8 的时间
        ZonedDateTime zonedDateTime = dateTime.atZone(ZoneId.of("Asia/Shanghai"));
        ZonedDateTime dataDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of(callWorkTime.getTimezoneValue()));
        // 格式化为所需格式
        String formattedReplyTime = dataDateTime.format(inputFormatter);
        log.info("原本的时间为{}，转为的对应时区时间{}: ", dateTime.format(inputFormatter), formattedReplyTime);
        // 首先判断是否在假期中，在假期则直接返回0
        List<CrmCallWorkTimeHoliday> timeHolidays = crmCallWorkTimeHolidayService.list(new QueryWrapper<CrmCallWorkTimeHoliday>()
                .eq("work_time_id", workTimeId)
                .eq("data_status", 1)
                .le("DATE(start_date)", dataDateTime.toLocalDate())
                .ge("DATE(end_date)", dataDateTime.toLocalDate()));
        if (!timeHolidays.isEmpty()) {
            messageHandle(workTimeInspectionVO, ticketCode, companyId,channelType, channelName);
            return AjaxResult.ok(0);
        }
        // 获取转换时区后的星期
        int dayOfWeek = dataDateTime.getDayOfWeek().getValue();
        // 不在假期中，则判断是否在工作时间内
        List<CrmCallWorkTimeHour> hourList = crmCallWorkTimeHourService.list(new QueryWrapper<CrmCallWorkTimeHour>()
                .eq("work_time_id", workTimeId)
                .eq("data_status", 1)
                .eq("week_type", dayOfWeek)
                .le("start_time", dataDateTime.toLocalTime())
                .ge("end_time", dataDateTime.toLocalTime()));
        // 如果数据大于零，则代表在工作时间内，返回1
        if (!hourList.isEmpty()) {
            return AjaxResult.ok(1);
        } else {
            messageHandle(workTimeInspectionVO, ticketCode, companyId,channelType, channelName);
            // 数据小于0，则代表不在工作时间内，返回0
            return AjaxResult.ok(0);
        }
    }

    @Override
    public AjaxResult<Object> channelWorkTimeList() {
        QueryWrapper<CrmCallWorkTime> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_status", 1);
        queryWrapper.eq("company_id", SecurityUtil.getLoginUser().getCompanyId());
        queryWrapper.orderByDesc("create_time");
        List<CrmCallWorkTime> crmCallWorkTimes = this.baseMapper.selectList(queryWrapper);
        return AjaxResult.ok(crmCallWorkTimes);
    }

    /**
     *  日志消息
     * @param workTimeInspectionVO 请求参数
     * @param ticketCode 工单编号
     * @param companyId 公司ID
     */
    private void messageHandle(WorkTimeInspectionVO workTimeInspectionVO, String ticketCode, String companyId,String channelType,String channelName){

        // 添加日志
        String logId = insertMessageLog(workTimeInspectionVO, ticketCode, companyId);
        // 发送消息
//        pushMessage(workTimeInspectionVO, ticketCode, companyId,logId,channelType, channelName);
        // 异步调用 pushMessage 方法
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            pushMessage(workTimeInspectionVO, ticketCode, companyId, logId, channelType, channelName);
        });

    }


    /**
     *  添加推送消息日志
     * @param workTimeInspectionVO 请求参数
     * @param ticketCode 工单编号
     * @param companyId 公司ID
     */
    private String insertMessageLog(WorkTimeInspectionVO workTimeInspectionVO, String ticketCode, String companyId) {
        WorkTimeMessageLog workTimeMessageLog = new WorkTimeMessageLog();
        workTimeMessageLog.setLogId(UUID.randomUUID().toString().replace("-", ""));
        workTimeMessageLog.setCustomerName(workTimeInspectionVO.getCustomerName());
        workTimeMessageLog.setContactInfo(workTimeInspectionVO.getCustomerContactInfo());
        workTimeMessageLog.setTicketCode(ticketCode);
        workTimeMessageLog.setCompanyId(companyId);
        workTimeMessageLog.setChannelId(workTimeInspectionVO.getChannelId());
        workTimeMessageLog.setIsPushCustomer(0);
        workTimeMessageLog.setPushResult(0);
        workTimeMessageLog.setCreateTime(LocalDateTime.now());

        try {
            // 定义索引请求
            IndexRequest indexRequest = new IndexRequest(workTimeMessageLogIndex);
            ObjectMapper objectMapper = new ObjectMapper();

            // 注册 JavaTimeModule
            objectMapper.registerModule(new JavaTimeModule());

            String value = objectMapper.writeValueAsString(workTimeMessageLog);
            indexRequest.source(value, XContentType.JSON);
            // 进行添加
            IndexResponse response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            log.info(String.valueOf(response.getResult()));
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es保存工单聊天数据失败，异常信息：", e);
            }
        }
        return workTimeMessageLog.getLogId();
    }

    /**
     * 查询工单具体信息
     *
     * @param ticketId  工单id
     * @param companyId 公司id
     * @return 工单信息
     */
    private TicketInfoIndex queryTicket(String ticketId, String companyId) {
        SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex, ticketId, companyId);
        if (hit != null) {
            try {
                // 获取到_id
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                return objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错", e);
            }
        }
        return null;
    }


    /**
     *  推送消息代码
     * @param workTimeInspectionVO 请求参数
     * @param ticketCode 工单编号
     * @param companyId 公司id
     */
    private void pushMessage(WorkTimeInspectionVO workTimeInspectionVO, String ticketCode, String companyId, String logId,String channelType,String channelName){
        log.info("开始推送消息");
        String pushContent = WorkTimePushMessageEnum.NOT_IN_WORK_TIME_MSG.getMsg();
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        JSONObject jsonObject = new JSONObject(pushContent);
        // 赋值
        jsonObject.put("mid", logId);
        jsonObject.put("time", Instant.now().toEpochMilli());
        JSONObject context = jsonObject.getJSONObject("context");
        context.put("customerName", workTimeInspectionVO.getCustomerName());
        context.put("robot_tiket_number", ticketCode);
        context.put("source_channel_type", channelType);
        context.put("source_channel_name", channelName);
        context.put("to_agent_time", LocalDateTime.now().format(formatter));
        // 转换为字符串
        pushContent = jsonObject.toString();
        // 定义推送状态
        int pushResult = 1;
        String errorCause = null;
        try {
            // 根据公司id，查询出公司配置的推送url以及secret
            List<CrmCallPushMessageConfig> list = crmCallPushMessageConfigService.list(new QueryWrapper<CrmCallPushMessageConfig>().lambda()
                    .eq(CrmCallPushMessageConfig::getCompanyId, companyId)
                    .eq(CrmCallPushMessageConfig::getDataStatus, 1));
            if(CollectionUtils.isNotEmpty(list)){
                CrmCallPushMessageConfig messageConfig = list.get(0);
                // 2. 生成签名
                String signature = generateSignature(pushContent, messageConfig.getVerifySecret(), ALGORITHM);
                String signatureHeader = "sha256=" + signature;
                errorCause = sendWebhookRequest(pushContent, signatureHeader, messageConfig.getConfigUrl());
            }else{
                pushResult = 3;
            }
        }catch (Exception e){
            log.error("推送消息报错：",e);
            pushResult = 2;
            errorCause = e.getMessage();
        }finally {
            // 进行修改日志表
            UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(workTimeMessageLogIndex);
            updateRequest.setQuery(QueryBuilders.termQuery("log_id", logId));
            // 构建脚本参数
            Map<String, Object> params = new HashMap<>();
            StringBuilder scriptBuilder = new StringBuilder();
            // 设置为推送客户
            params.put("is_push_customer", 1);
            scriptBuilder.append("ctx._source.is_push_customer = params.is_push_customer; ");
            // 推送结果
            params.put("push_result", pushResult);
            scriptBuilder.append("ctx._source.push_result = params.push_result; ");
            // 失败原因
            params.put("error_cause", errorCause);
            scriptBuilder.append("ctx._source.error_cause = params.error_cause; ");
            // 推送内容
            params.put("push_content", pushContent);
            scriptBuilder.append("ctx._source.push_content = params.push_content; ");
            // 定义上修改时间
            params.put("modify_time", LocalDateTime.now().format(formatter));
            scriptBuilder.append("ctx._source.modify_time = params.modify_time; ");
            // 设置脚本
            updateRequest.setScript(
                    new Script(ScriptType.INLINE, "painless",
                            scriptBuilder.toString(),
                            params)
            );
            // 执行更新
            try {
                BulkByScrollResponse bulkResponse = restHighLevelClient.updateByQuery(updateRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            log.info("消息推送成功");
        }
    }



    private String  sendWebhookRequest(String pushContent, String signatureHeader, String configUrl) throws Exception {

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        // 添加签名头
        httpHeaders.set("X-Signature", signatureHeader);
        // 请求参数信息
        HttpEntity<String> request = new HttpEntity<>(pushContent, httpHeaders);
        // 发送请求
        ResponseEntity<String> response = restTemplate.postForEntity(configUrl, request, String.class);
        // 获取返回内容
        String body = response.getBody();
        // 获取 HTTP 状态码
        HttpStatus statusCode = response.getStatusCode();
        String errorCause = null;
        // 判断请求是否成功
        if (statusCode == HttpStatus.OK) {
            System.out.println("请求成功，返回内容：" + body);
        } else if (statusCode == HttpStatus.INTERNAL_SERVER_ERROR) {
            System.out.println("服务器错误，状态码：" + statusCode);
            errorCause = body;
        } else {
            System.out.println("请求失败，状态码：" + statusCode);
            errorCause = body;
        }

        return errorCause;
    }


    public String generateSignature(String pushContent, String secret, String algorithm) {
        try {
            Mac mac = Mac.getInstance(algorithm);
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(), algorithm);
            mac.init(secretKey);
            byte[] hashBytes = mac.doFinal(pushContent.getBytes());
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to generate signature", e);
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            hexString.append(String.format("%02x", b));
        }
        return hexString.toString();
    }

    public Boolean isWithinWorkTime(String workTimeId) {
        // 查询 work_time 信息，获取时区
        CrmCallWorkTime workTime = this.baseMapper.selectById(workTimeId);
        if (workTime == null || workTime.getDataStatus() != 1) {
            return false;
        }

        // 获取当前时间，并转换为指定时区时间
        ZoneId zoneId = ZoneId.of(workTime.getTimezoneValue());
        ZonedDateTime nowZoned = ZonedDateTime.now(zoneId);
        LocalDateTime nowLocalDateTime = nowZoned.toLocalDateTime();
        LocalTime currentTime = nowZoned.toLocalTime();
        int dayOfWeek = nowZoned.getDayOfWeek().getValue(); // 1~7 周一到周日

        // 检查是否是假期（startDate <= now <= endDate）
        List<CrmCallWorkTimeHoliday> holidays = crmCallWorkTimeHolidayService.list(
                new QueryWrapper<CrmCallWorkTimeHoliday>().lambda()
                        .eq(CrmCallWorkTimeHoliday::getWorkTimeId, workTimeId)
                        .eq(CrmCallWorkTimeHoliday::getDataStatus, 1)
                        .orderByAsc(CrmCallWorkTimeHoliday::getStartDate)
        );

        for (CrmCallWorkTimeHoliday holiday : holidays) {
            LocalDateTime start = convertToLocalDateTime(holiday.getStartDate(), zoneId);
            LocalDateTime end = convertToLocalDateTime(holiday.getEndDate(), zoneId);

            if (!nowLocalDateTime.isBefore(start) && !nowLocalDateTime.isAfter(end)) {
                return false; // 当前时间在假期内
            }
        }

        // 查询当天的工作时间段
        List<CrmCallWorkTimeHour> workHours = crmCallWorkTimeHourService.list(
                new QueryWrapper<CrmCallWorkTimeHour>().lambda()
                        .eq(CrmCallWorkTimeHour::getWorkTimeId, workTimeId)
                        .eq(CrmCallWorkTimeHour::getWeekType, dayOfWeek)
                        .eq(CrmCallWorkTimeHour::getDataStatus, 1)
                        .orderByAsc(CrmCallWorkTimeHour::getStartTime)
        );
        for (CrmCallWorkTimeHour hour : workHours) {
            if (hour.getDataStatus() != 1) continue;
            LocalTime startTime = hour.getStartTime();
            LocalTime endTime = hour.getEndTime();

            if (!currentTime.isBefore(startTime) && !currentTime.isAfter(endTime)) {
                return true; // 当前时间在工作时间段内
            }
        }

        return false; // 不在任何时间段内
    }

    private LocalDateTime convertToLocalDateTime(Date date, ZoneId zoneId) {
        return date.toInstant().atZone(zoneId).toLocalDateTime();
    }
}




