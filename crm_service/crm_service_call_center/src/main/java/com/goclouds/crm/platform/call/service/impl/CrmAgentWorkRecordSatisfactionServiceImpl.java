package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordSatisfaction;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordSatisfactionService;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordSatisfactionMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_satisfaction(工单满意度评价表)】的数据库操作Service实现
* @createDate 2024-01-11 19:12:44
*/
@Service
public class CrmAgentWorkRecordSatisfactionServiceImpl extends ServiceImpl<CrmAgentWorkRecordSatisfactionMapper, CrmAgentWorkRecordSatisfaction>
    implements CrmAgentWorkRecordSatisfactionService{

}




