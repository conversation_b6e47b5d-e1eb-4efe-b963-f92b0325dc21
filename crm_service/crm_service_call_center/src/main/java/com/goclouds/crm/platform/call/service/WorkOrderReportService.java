package com.goclouds.crm.platform.call.service;

import com.goclouds.crm.platform.call.domain.vo.ContactDetailHandleVo;
import com.goclouds.crm.platform.call.domain.vo.statis.KinesisContactDetailsVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;

public interface WorkOrderReportService {
    AjaxResult<Object> kinesisDataMonitor(Object kinesisData);

    AjaxResult<Object> kinesisDataClean();

    void kinesisData(Object kinesisData);

    void kinesisDataFlow(Object kinesisData);

    boolean contactDetailPhoneProcessing(ContactDetailHandleVo contactDetailHandleVo) throws Exception;

    void contactDetailChannelProcessing(ContactDetailHandleVo contactDetailHandleVo) throws Exception ;
}
