package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.es.*;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.statis.KinesisContactDetailsVo;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordCountResult;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordFinalEsResult;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordRequest;
import com.goclouds.crm.platform.call.mapper.*;
import com.goclouds.crm.platform.call.service.*;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.constant.RabbitMqConstants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.domain.ExtOptionVO;
import com.goclouds.crm.platform.common.domain.QueryCondition;
import com.goclouds.crm.platform.common.enums.*;
import com.goclouds.crm.platform.common.utils.*;
import com.goclouds.crm.platform.openfeignClient.client.aigc.AigcChatClient;
import com.goclouds.crm.platform.openfeignClient.client.customer.CustomerClient;
import com.goclouds.crm.platform.openfeignClient.client.customer.CustomerTagClient;
import com.goclouds.crm.platform.openfeignClient.client.im.IMClient;
import com.goclouds.crm.platform.openfeignClient.client.platform.S3Client;
import com.goclouds.crm.platform.openfeignClient.client.system.CompanyClient;
import com.goclouds.crm.platform.openfeignClient.client.system.CompanyTranscribeClient;
import com.goclouds.crm.platform.openfeignClient.client.system.InternationalClient;
import com.goclouds.crm.platform.openfeignClient.client.system.UserClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.aigc.AigcGeneratorContent;
import com.goclouds.crm.platform.openfeignClient.domain.aigc.SmartSummaryResultVo;
import com.goclouds.crm.platform.openfeignClient.domain.call.AgentUpdateTicketVO;
import com.goclouds.crm.platform.openfeignClient.domain.call.AgentWorkRecordVo;
import com.goclouds.crm.platform.openfeignClient.domain.call.GetContentDetailsVO;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.PresignedUrlParam;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.S3DeleteObjectParam;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.S3GetObjectParam;
import com.goclouds.crm.platform.openfeignClient.domain.clouds.S3PutObjectParam;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CrmCustomerVO;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CustomerTagCategoryGroupResult;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CustomerTagDefResult;
import com.goclouds.crm.platform.openfeignClient.domain.system.*;
import com.goclouds.crm.platform.utils.*;
import com.goclouds.crm.platform.utils.ElasticsearchUtil;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import com.ibm.icu.text.SimpleDateFormat;
import com.jayway.jsonpath.JsonPath;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.join.ScoreMode;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.admin.indices.refresh.RefreshResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.goclouds.crm.platform.common.constant.Constants.WORK_RECORD_USER_TABLE_HEADER;
import static com.goclouds.crm.platform.common.domain.QueryCondition.createCondition;
import static com.goclouds.crm.platform.utils.SecurityUtil.getUserId;
import static com.goclouds.crm.platform.utils.SecurityUtil.getUsername;

/**
 * <AUTHOR>
 * @description 针对表【crm_agent_work_record(工作记录)】的数据库操作Service实现
 * @createDate 2023-05-25 10:27:34
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CrmAgentWorkRecordServiceImpl extends ServiceImpl<CrmAgentWorkRecordMapper, CrmAgentWorkRecord> implements CrmAgentWorkRecordService {

    // 服务类
    private final CrmAgentWorkRecordDetailService crmAgentWorkRecordDetailService;
    private final CrmAgentWorkRecordExtDefService crmAgentWorkRecordExtDefService;
    private final CrmAgentWorkRecordTypeDefService crmAgentWorkRecordTypeDefService;
    private final CrmAgentWorkRecordPriorityLevelDefService crmAgentWorkRecordPriorityLevelDefService;
    private final CrmAgentWorkRecordExtIntsService crmAgentWorkRecordExtIntsService;
    private final CrmAgentWorkRecordAutoMergeService crmAgentWorkRecordAutoMergeService;
    private final CrmAgentWorkRecordOperationLogService crmAgentWorkRecordOperationLogService;
    private final CrmAgentWorkRecordSlaDefService crmAgentWorkRecordSlaDefService;
    private final CrmAgentWorkRecordAigcSettingsService aigcSettingsService;
    // Mapper类
    private final CrmAwsAccountMapper crmAwsAccountMapper;
    private final CrmAwsConnectMapper crmAwsConnectMapper;
    private final CrmAgentWorkRecordTypeDefMapper crmAgentWorkRecordTypeDefMapper;

    // 客户端
    private final S3Client s3Client;
    private final CompanyTranscribeClient companyTranscribeClient;
    private final UserClient userClient;
    private final IMClient imClient;
    private final CustomerTagClient customerTagClient;
    private final AigcChatClient aigcClient;

    // Elasticsearch相关
    private final RestHighLevelClient restHighLevelClient;
    private final ElasticsearchUtil elasticsearchUtil;

    private final RedisTemplate redisTemplate;

    private final RedissonClient redissonClient;

    private final CustomerClient customerClient;

    private final CompanyClient companyClient;


    // 消息队列
    @Autowired
    private RabbitTemplate rabbitTemplate;

    // 配置项
    @Value("${s3.bucket-name}")
    private String bucketName;

    @Value("${s3.domain-prefix}")
    private String domainPrefix;

    @Value("${s3.public-bucket-name}")
    private String publicBucketName;

    @Value("${s3.access-key}")
    private String accessKey;

    @Value("${s3.secret-key}")
    private String secretKey;

    @Value("${s3.region}")
    private String region;

    @Value("${s3.region-type}")
    private String regionType;

    @Value("${s3.coco-picture}")
    private String cocoPicture;

    @Value("${es.work-record-content-index}")
    private String workContentIndex;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    @Value("${es.ticket-relation-index}")
    private String ticketRelationIndex;

    private final InternationalClient internationalClient;

    // 图片类型集合
    private static final Set<String> IMAGE_EXTENSIONS = new HashSet<>(Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "svg"
    ));

    // 视频类型集合
    private static final Set<String> VIDEO_EXTENSIONS = new HashSet<>(Arrays.asList(
            "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm", "mpeg", "mpg", "3gp"
    ));

    @Override
    public AjaxResult queryWorkRecordInfo(String workRecordId, String channelTypeId) {
        log.info("打印一下查询工单详情请求参数：workRecordId>>>{}..channelTypeId{}", workRecordId, channelTypeId);
        //工单详情
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex, workRecordId, SecurityUtil.getLoginUser().getCompanyId());
        if (ticketHit != null) {
            try {
                // 获取到_id
                esId = ticketHit.getId();
                Map<String, Object> source = ticketHit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错", e);
            }
        }
        log.info("打印一下工单详情查询到的工单{}", ticketInfoIndex);
        // 修改为查询ES数据
        // 定义索引名称
        String indexName = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
        }
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("work_record_id", workRecordId));
        //工单聊天增加删除状态
        boolQueryBuilder.mustNot(QueryBuilders.matchQuery("data_status", "0"));
        // 如果是邮件为正序查询，其他则为倒序查询
        if (CrmChannelEnum.EMAIL.getCode().equals(Integer.valueOf(channelTypeId))) {
            searchSourceBuilder.sort("reply_time", SortOrder.DESC);
            searchSourceBuilder.sort("reply_millis_time", SortOrder.DESC);
        } else {
            searchSourceBuilder.sort("reply_time", SortOrder.ASC);
            searchSourceBuilder.sort("reply_millis_time", SortOrder.ASC);
        }
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(10000);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        // 定义值
        List<TicketContentIndexVO> list = new ArrayList<>();
        String recordS3Bucket = null;
        String recordS3BucketRegion = null;
        String accessKey = null;
        String secretAccessKey = null;
        // 社媒时间状态
        boolean socialMediaTimeStatus = false;
        try {
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSourceAsMap();
                if (hit.getScore() < 0) {
                    continue;
                }
                String s = JSON.toJSONString(source);
                TicketContentIndex contentIndex = JSON.parseObject(s, TicketContentIndex.class);
                TicketContentIndexVO contentIndexVO = new TicketContentIndexVO();
                BeanUtils.copyProperties(contentIndex, contentIndexVO);
                // 如果回复类型是智能总结，则转为json，另存一个字段 前端需要用
                if (contentIndex.getReply_type() != null && contentIndex.getReply_type() == 4) {
                    String content = contentIndexVO.getContent();
                    SmartSummaryResult summaryResult = JSON.parseObject(content, SmartSummaryResult.class);
                    contentIndexVO.setSmart_summary(summaryResult);
                }
                //去除Re
//                if(StringUtil.isNotBlank(contentIndexVO.getEmail_subject())){
//                    if(contentIndexVO.getEmail_subject().contains("Re:")){
//                        String replace = contentIndexVO.getEmail_subject().replace("Re:", "");
//                        contentIndexVO.setEmail_subject(replace);
//                    }
//                }
//                //进行加密 - 公司层面
                String data = companyClient.checkCanPermission(1L).getData();
                if(!data.equals("") && data .equals("1")){
                    contentIndexVO.setEmail_sender(
                            DesensitizationUtils.desensitizeCustomerInfo(contentIndexVO.getEmail_sender())
                    );
                    contentIndexVO.setEmail_recipient(
                            DesensitizationUtils.desensitizeCustomerInfo(contentIndexVO.getEmail_recipient())
                    );
                    contentIndexVO.setEmail_cc_to(
                            DesensitizationUtils.desensitizeCustomerInfo(contentIndexVO.getEmail_cc_to())
                    );
                    contentIndexVO.setEmail_bcc(
                            DesensitizationUtils.desensitizeCustomerInfo(contentIndexVO.getEmail_bcc())
                    );
                }
                list.add(contentIndexVO);
            }
            // 如果是录音文件，则需要生成临时url
            if ("7".equals(channelTypeId) || ticketInfoIndex.getChatVoice() == 1) {
                //在crm_aws_connect表中获取桶名字
                CrmAwsConnect crmAwsConnect = crmAwsConnectMapper.selectOne(new QueryWrapper<CrmAwsConnect>()
                        .eq("connect_id", list.get(0).getConnect_id())
                        .eq("data_status", Constants.NORMAL));
                recordS3Bucket = crmAwsConnect.getRecordS3Bucket();
                recordS3BucketRegion = crmAwsConnect.getRecordS3BucketRegion();

                //在crm_aws_account表中获取ak和sk
                String awsUserId = crmAwsConnect.getAwsUserId();
                CrmAwsAccount crmAwsAccount = crmAwsAccountMapper.selectOne(new QueryWrapper<CrmAwsAccount>()
                        .eq("aws_user_id", awsUserId)
                        .eq("data_status", Constants.NORMAL));
                accessKey = crmAwsAccount.getAccessKey();
                secretAccessKey = crmAwsAccount.getSecretAccessKey();
            }
            for (TicketContentIndexVO item : list) {
                List<TicketFile> ticketFileList = item.getTicket_file();
                if (CollectionUtils.isNotEmpty(ticketFileList)) {
                    //如果是录音形式，要给生成临时url
                    if ("7".equals(channelTypeId) || ticketInfoIndex.getChatVoice() == 1) {
                        // 如果还是机器人工单，则只取完整的一条
                        if (ticketInfoIndex.getAgentId().equals("1001")) {
                            // 移除 file_name 包含 '-' 的元素
                            ticketFileList.removeIf(ticketFile -> ticketFile.getFile_name() != null && ticketFile.getFile_name().contains("-"));
                        }
                        for (TicketFile ticketFile : ticketFileList) {
                            String filePath = ticketFile.getFile_path();
                            //获取临时url
                            String preSignedUrl = null;
                            if (ticketInfoIndex.getAgentId().equals("1001")) {
                                preSignedUrl = getPreSignedUrl(ticketFile.getBucket_name(), accessKey, secretAccessKey, ticketInfoIndex.getRegion(), filePath);
                            } else {
                                preSignedUrl = getPreSignedUrl(recordS3Bucket, accessKey, secretAccessKey, recordS3BucketRegion, filePath);
                            }
                            ticketFile.setFile_path(preSignedUrl);
                        }
                        item.setTicket_file(ticketFileList);
                    }
                }
            }
            // 定义社媒类型是否需要校验客户时间 默认0
            Integer mediaTimeCheckCount = 0;
            if (ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.FACEBOOK.getCode().toString())
                    || ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WHATSAPP.getCode().toString())) {
                mediaTimeCheckCount = CrmChannelEnum.FACEBOOK.getMessageConfine();
            }
            if (ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getCode().toString())
                    || ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WECHAT_OFFICIAL_ACCOUNT.getCode().toString())) {
                mediaTimeCheckCount = CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getMessageConfine();
            }
            if (mediaTimeCheckCount != 0) {
                if (ticketInfoIndex.getCustomerFirstMessageTime() != null) {
                    // 将 Date 转换为 LocalDateTime
                    LocalDateTime replyTime = ticketInfoIndex.getCustomerFirstMessageTime();
                    // 获取当前时间
                    LocalDateTime now = LocalDateTime.now();
                    // 计算当前时间和回复时间的差值
                    long hoursDifference = ChronoUnit.HOURS.between(replyTime, now);
                    // 判断是否超过24小时,超过24小时则不能发送消息
                    if (hoursDifference >= mediaTimeCheckCount) {
                        socialMediaTimeStatus = true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询工单聊天记录报错:", e);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("dataList", list);
        map.put("socialMediaTimeStatus", socialMediaTimeStatus);
        return AjaxResult.ok(map);
    }

    private String getPreSignedUrl(String bucketName, String ak, String sk, String region, String fileUrl) {
        // 获取预签名URL
        PresignedUrlParam urlParam = new PresignedUrlParam()
                .setBucketName(bucketName)
                .setKeyName(fileUrl);
        urlParam.setAccessKeyId(ak);
        urlParam.setSecretAccessKey(sk);
        urlParam.setRegion(region);
        R preSignedUrl = s3Client.getObjectPresignedUrl(urlParam);
        return preSignedUrl.getData().toString();
    }

    /**
     * @Description: array 表头数据 list 具体数据
     */
    private Workbook writeToExcelByList(String[] array, List<List<String>> list) {
        //创建工作薄
        Workbook wb = new XSSFWorkbook();

        //设置列名样式
        CellStyle headerStyle = wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.LEFT);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);//上下居中
        headerStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());//设置背景色
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);//必须设置 否则无效
        setCellBorderStyle(headerStyle);
        Font headerFont = wb.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setFontName("微软雅黑");
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        //创建sheet
        Sheet sheet = wb.createSheet(MessageUtils.get("work.record.file.name"));

        //在sheet中添加表头，由于不涉及到标题，所以表头行数从0开始
        Row row = sheet.createRow((int) 0);
        for (int i = 0; i < array.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(array[i]);
            cell.setCellStyle(headerStyle);
        }

        //数据样式
        CellStyle dataStyle = wb.createCellStyle();
        //设置居中样式，水平居中
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font dataFont = wb.createFont();
        dataFont.setFontHeightInPoints((short) 12);
        dataFont.setFontName("微软雅黑");
        dataStyle.setFont(dataFont);

        //数据填充
        try {
            int index = 1;
            for (List value : list) {
                row = sheet.createRow(index);
                index++;
                List data = value;
                for (int j = 0; j < data.size(); j++) {
                    Cell cell = row.createCell(j);
                    // 为当前列赋值
                    if (data.get(j) != null) {
                        cell.setCellValue(data.get(j).toString());
                    } else {
                        cell.setCellValue("");
                    }
                    //设置数据的样式
                    cell.setCellStyle(dataStyle);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 宽度自适应的问题，必须在单元格设值以后进行
        for (int k = 0; k < array.length; k++) {
            sheet.autoSizeColumn(k);
        }
        return wb;
    }

    private void setCellBorderStyle(CellStyle cellStyle) {
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN); //解决填充背景色没有边框问题
        cellStyle.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> workSummarize(WorkSummarizeVO workSummarizeVO) {
        SmartSummaryResult summaryResult = new SmartSummaryResult();
        // 总结内容
        summaryResult.setSummary(workSummarizeVO.getContentSummary());
        // 客户心情
        if (workSummarizeVO.getCustomerMood() != null) {
            summaryResult.setMood(workSummarizeVO.getCustomerMood().toString());
        }
        // 待办事项
        if (CollectionUtils.isNotEmpty(workSummarizeVO.getWaitExecuteList())) {
            List<WaitExecuteVO> list = new ArrayList<>();
            for (CrmAgentWorkRecordWaitExecute crmAgentWorkRecordWaitExecute : workSummarizeVO.getWaitExecuteList()) {
                WaitExecuteVO waitExecuteVO = new WaitExecuteVO();
                BeanUtils.copyProperties(crmAgentWorkRecordWaitExecute, waitExecuteVO);
                list.add(waitExecuteVO);
            }
            summaryResult.setToDoList(list);
        }
        String contentJson = JSONObject.toJSONString(summaryResult);

        if (workSummarizeVO.getInterfaceType() == 1) {
            // 生成内容记录
            String contentId = UuidUtils.generateUuid();
            TicketContentIndex ticketContentIndex = new TicketContentIndex();
            ticketContentIndex.setWork_record_content_id(contentId);
            ticketContentIndex.setWork_record_id(workSummarizeVO.getWorkRecordId());
            ticketContentIndex.setContent(contentJson);
            ticketContentIndex.setContent_type(1);
            ticketContentIndex.setReply_type(4);
            ticketContentIndex.setReply_person(getUsername());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 智能总结时间
            ticketContentIndex.setReply_time(sdf.format(new Date()));
            ticketContentIndex.setReply_millis_time(Convert.toStr(Convert.toLocalDateTime(sdf.format(new Date())).toInstant(ZoneOffset.UTC).toEpochMilli()));
            // 保存到ES中
            addAgentWorkRecordContent(ticketContentIndex, SecurityUtil.getLoginUser().getCompanyId());
        } else {
            String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();

            Map<String, Object> result = queryEsTicketInfo(workSummarizeVO.getWorkRecordId());
            TicketContentIndex ticketContentIndex = (TicketContentIndex) result.get("data");
            String id = result.get("id").toString();
            ticketContentIndex.setContent(contentJson);
            elasticsearchUtil.updateDocument(index, id, ticketContentIndex);
        }

        return AjaxResult.ok(summaryResult);
    }

    @Override
    public AjaxResult<List<WorkOrderNumberVO>> queryWorkOrderNumber(WorkOrderRecordRequestVO workOrderRecordRequest) {
        try {
            List<WorkOrderNumberVO> numberList = new ArrayList<>();
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            // 首先进行ES索引判断
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在则返回null
            if (!indexExists) {
                return null;
            }
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);

            BoolQueryBuilder boolQueryBuilder = queryConditionMontage(workOrderRecordRequest);
            searchSourceBuilder.aggregation(AggregationBuilders.terms("status_counts").field("status"));
            searchSourceBuilder.sort("create_time", SortOrder.DESC);
            searchSourceBuilder.query(boolQueryBuilder);
            request.source(searchSourceBuilder);
            int allCount = 0;
            int processingCount = 0;
            int undistributedCount = 0;
            int resolvedCount = 0;
            int transferCount = 0;
            int terminationCount = 0;

            SearchResponse searchResponse = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            // 获取聚合结果
            Aggregations aggregations = searchResponse.getAggregations();
            Terms statusCounts = aggregations.get("status_counts");
            // 输出聚合结果
            for (Terms.Bucket bucket : statusCounts.getBuckets()) {
                System.out.println("Status: " + bucket.getKeyAsString() + ", Count: " + bucket.getDocCount());
                // 查询待分配
                if (Integer.parseInt(bucket.getKeyAsString()) == 0) {
                    undistributedCount = Integer.parseInt(String.valueOf(bucket.getDocCount()));
                }
                // 查询待分配
                if (Integer.parseInt(bucket.getKeyAsString()) == 0) {
                    undistributedCount = Integer.parseInt(String.valueOf(bucket.getDocCount()));
                }
                // 查询处理中
                if (Integer.parseInt(bucket.getKeyAsString()) == 1) {
                    processingCount = processingCount + Integer.parseInt(String.valueOf(bucket.getDocCount()));
                }
                // 查询处理中
                if (Integer.parseInt(bucket.getKeyAsString()) == 2) {
                    processingCount = processingCount + Integer.parseInt(String.valueOf(bucket.getDocCount()));
                }
                //查询已解决
                if (Integer.parseInt(bucket.getKeyAsString()) == 3) {
                    resolvedCount = Integer.parseInt(String.valueOf(bucket.getDocCount()));
                }
                // 查询已终止
                if (Integer.parseInt(bucket.getKeyAsString()) == 4) {
                    terminationCount = Integer.parseInt(String.valueOf(bucket.getDocCount()));
                }
                // 查询已转单
                if (Integer.parseInt(bucket.getKeyAsString()) == 5) {
                    transferCount = Integer.parseInt(String.valueOf(bucket.getDocCount()));
                }
            }
            allCount = processingCount + undistributedCount + resolvedCount + transferCount + terminationCount;
            // 查询全部
            numberList.add(new WorkOrderNumberVO().setStatusName("全部").setStatusNumber(allCount));
            // 查询处理中
            numberList.add(new WorkOrderNumberVO().setStatusName("处理中").setStatusNumber(processingCount));
            // 查询待分配
            numberList.add(new WorkOrderNumberVO().setStatusName("待分配").setStatusNumber(undistributedCount));
            //查询已解决
            numberList.add(new WorkOrderNumberVO().setStatusName("已解决").setStatusNumber(resolvedCount));
            // 查询已转单
            numberList.add(new WorkOrderNumberVO().setStatusName("已转单").setStatusNumber(transferCount));
            // 查询已终止
            numberList.add(new WorkOrderNumberVO().setStatusName("已终止").setStatusNumber(terminationCount));

            return AjaxResult.ok(numberList);
        } catch (IOException e) {
            log.error("查询各个工单状态数量报错", e);
            return AjaxResult.failure();
        }

    }

    /**
     * 同步通话记录
     * @param syncCallRecordDTO 通话记录id
     */
    @Override
    public AjaxResult syncCallRecord(SyncCallRecordDTO syncCallRecordDTO){
        syncCallRecordDTO.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        // 直接发mq
        rabbitTemplate.convertAndSend(RabbitMqConstants.SYNC_AWSDATA_EXCHANGE, RabbitMqConstants.CONNECT_RECORD_QUEUE_KEY, JSON.toJSONString(syncCallRecordDTO));
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public IPage<WorkOrderRecordResponseVO> queryWorkOrder(IPage<Object> page, WorkOrderRecordRequestVO workOrderRecordRequest) {
        // 定义索引名称
        String indexName = ticketIndex+SecurityUtil.getLoginUser().getCompanyId();
        // 首先进行ES索引判断
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在则返回null
        if (!indexExists) {
            return null;
        }
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        // 根据前端查询列,查询条件,进行查询条件拼接
        BoolQueryBuilder boolQueryBuilder = queryConditionMontage(workOrderRecordRequest);
        searchSourceBuilder.sort("create_time", SortOrder.DESC);
        //计算当前页的起始下标
        long start = (page.getCurrent() - 1) * page.getSize();
        // 分页查询
        searchSourceBuilder.query(boolQueryBuilder).from((int) start).size((int) page.getSize());
        request.source(searchSourceBuilder);
        List<WorkOrderRecordResponseVO> workListVos = new ArrayList<>();
        IPage<WorkOrderRecordResponseVO> iPage = new Page<>();
        // 查询公司的拓展字段
        List<WorkOrderExtVo> defList = crmAgentWorkRecordExtDefService.queryList(8).getData();
        // 进行查询
        try {
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            long total = response.getHits().getTotalHits().value;
            for (SearchHit hit : hits) {
                WorkOrderRecordResponseVO workOrderRecordResponseVO = new WorkOrderRecordResponseVO();
                Map<String, Object> source = hit.getSourceAsMap();
                TicketInfoIndex contentIndex = BeanUtil.toBean(source, TicketInfoIndex.class);
                BeanUtils.copyProperties(contentIndex, workOrderRecordResponseVO);
                String workRecordTag = null;
                if(ObjectUtil.isNotNull(workOrderRecordResponseVO.getTicketTag()) && ObjectUtil.isNotEmpty(workOrderRecordResponseVO.getTicketTag()) && workOrderRecordResponseVO.getTicketTag().size()!=0){
                    workRecordTag = workOrderRecordResponseVO.getTicketTag().stream().map(TicketTag::getTagName).collect(Collectors.toList()).stream()
                            .collect(Collectors.joining(", "));
                }
                workOrderRecordResponseVO.setWorkRecordTag(workRecordTag);
                String data = companyClient.checkCanPermission(1L).getData();
                if(!data.equals("") && data .equals("1")){
                    workOrderRecordResponseVO.setCustomerTelephone(DesensitizationUtils.desensitizeCustomerInfo(contentIndex.getCustomerTelephone()));
                }
                if(CollectionUtils.isNotEmpty(contentIndex.getTicketExt())){
                    List<TicketExt> ticketExt = contentIndex.getTicketExt();
                    // 目标 map
                    Map<String, Object> result = ticketExt.stream()
                            .collect(Collectors.toMap(
                                    TicketExt::getExtCode, // key: extCode
                                    ext -> defList.stream()
                                            .filter(def -> def.getWorkRecordExtDefCode().equals(ext.getExtCode()))
                                            .flatMap(def -> {
                                                // 根据 propertyTypeId 处理逻辑
                                                switch (def.getPropertyTypeId()) {
                                                    case "1003":
                                                    case "1005":
                                                        // 单选框逻辑
                                                        return def.getWorkOrderExtOptionDefList().stream()
                                                                .filter(option -> option.getOptionValue().equals(ext.getExtValue()))
                                                                .map(WorkOrderExtOptionDefVo::getOptionName);

                                                    case "1004":
                                                    case "1006":
                                                        if(StringUtil.isNotBlank(ext.getExtValue())){
                                                            return Arrays.stream(ext.getExtValue().split(","))
                                                                    .flatMap(value -> def.getWorkOrderExtOptionDefList().stream()
                                                                            .filter(option -> option.getOptionValue().equals(value))
                                                                            .map(WorkOrderExtOptionDefVo::getOptionName));
                                                        }else {
                                                             return Stream.empty();
                                                        }
                                                    default:
                                                        // 不处理
                                                        return Stream.empty();
                                                }
                                            })
                                            .collect(Collectors.joining(",")), // 将多个 optionName 拼接成字符串
                                    (v1, v2) -> v1 // 处理 key 冲突
                            ));
                    workOrderRecordResponseVO.setWorkRecordResVoIPage(result);
                }
                workListVos.add(workOrderRecordResponseVO);
            }
            iPage.setTotal(total);
        } catch (IOException e) {
            log.error("查询工单列表出现问题",e);
        }
        // 查询工单类型
        List<CrmAgentWorkRecordTypeDef> list = crmAgentWorkRecordTypeDefService.list(new QueryWrapper<CrmAgentWorkRecordTypeDef>().lambda()
                .eq(CrmAgentWorkRecordTypeDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                .eq(CrmAgentWorkRecordTypeDef::getLanguageCode, ServletUtils.getHeaderLanguage())
                .eq(CrmAgentWorkRecordTypeDef::getDataStatus, 1));

        if(CollectionUtils.isNotEmpty(list)&&CollectionUtils.isNotEmpty(workListVos)){
            // 根据工单类型code进行匹配，相匹的进行存入
            list.forEach(crmAgent ->
                    workListVos.stream()
                            .filter(workOrder -> StringUtil.isNotEmpty(workOrder.getWorkRecordTypeCode())&&
                                                 StringUtil.isNotEmpty(crmAgent.getWorkRecordTypeValue())&&
                                                 workOrder.getWorkRecordTypeCode().equals(crmAgent.getWorkRecordTypeValue()))
                            .forEach(workOrder -> workOrder.setWorkRecordTypeName(crmAgent.getWorkRecordTypeName()))
            );
        }

        // 计算工单时间 根据状态来判断]
        ticketDataHandle(workListVos, 1);

        iPage.setRecords(workListVos);

        return iPage;
    }

    /**
     * 工单列表添加扩展信息
     * @param work
     */
    private void addWorkOrderExtInfo(WorkOrderRecordResponseVO work) {
        LambdaQueryWrapper<CrmAgentWorkRecordExtInts> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CrmAgentWorkRecordExtInts::getWorkRecordId, work.getWorkRecordId());
        List<CrmAgentWorkRecordExtInts> list = crmAgentWorkRecordExtIntsService.list(queryWrapper);
        Map<String,Object> map = list.stream().collect(Collectors.toMap(CrmAgentWorkRecordExtInts::getWorkRecordExtCode, CrmAgentWorkRecordExtInts::getWorkRecordExtValue));
        work.setWorkRecordResVoIPage(map);
    }

    /**
     * 工单扩展信息动态查询
     * @param workOrderExtVos
     * @return
     */
    private void ticketExtQuery(List<WorkOrderExtRequestVo> workOrderExtVos, BoolQueryBuilder boolQueryBuilder) {
        // 扩展信息查询条件
        List<WorkOrderExtRequestVo> collect = workOrderExtVos.stream().filter(s -> s.getIsSystemDefault().equals(0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        for (WorkOrderExtRequestVo workOrderExtVo : collect) {
            BoolQueryBuilder builderQuery = null;
            if (InputPropTypeEnum.Single_Input.getCode().equals(workOrderExtVo.getPropType()) || InputPropTypeEnum.Multiple_Input.getCode().equals(workOrderExtVo.getPropType())){
                builderQuery = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("ticket_ext.ext_code", workOrderExtVo.getCode()))
                        .must(QueryBuilders.wildcardQuery("ticket_ext.ext_value","*" +workOrderExtVo.getValues().toString() +"*"));
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("ticket_ext", builderQuery, ScoreMode.None);
                boolQueryBuilder.must(nestedQuery);
            } else if (InputPropTypeEnum.Single_Select.getCode().equals(workOrderExtVo.getPropType()) || InputPropTypeEnum.Single_Button.getCode().equals(workOrderExtVo.getPropType()) || InputPropTypeEnum.Date_Select.getCode().equals(workOrderExtVo.getPropType())){
                String value = (String) workOrderExtVo.getValues();
                if (InputPropTypeEnum.Date_Select.getCode().equals(workOrderExtVo.getPropType()) && StringUtil.isNotBlank(value)) {
                    value = TimeZoneUtils.requestTimeConversion(value);
                }
                builderQuery = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("ticket_ext.ext_code", workOrderExtVo.getCode()))
                        .must(QueryBuilders.termQuery("ticket_ext.ext_value",value));
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("ticket_ext", builderQuery, ScoreMode.None);
                boolQueryBuilder.must(nestedQuery);
            }else if(InputPropTypeEnum.Multiple_Select.getCode().equals(workOrderExtVo.getPropType()) || InputPropTypeEnum.Multiple_Button.getCode().equals(workOrderExtVo.getPropType())){
                    String[] split = workOrderExtVo.getValues().toString().split(",");
                    // 构建 Bool 查询
                    builderQuery = QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("ticket_ext.ext_code", workOrderExtVo.getCode()));

                    // 针对每个值，构建一个 wildcardQuery 并添加到 should 中
                    BoolQueryBuilder valueQuery = QueryBuilders.boolQuery();
                    for (String value : split) {
                        valueQuery.should(QueryBuilders.wildcardQuery("ticket_ext.ext_value", "*" + value + "*"));
                    }
                    valueQuery.minimumShouldMatch(1); // 至少匹配一个条件

                    builderQuery.must(valueQuery);

                    // 构建 Nested 查询
                    NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("ticket_ext", builderQuery, ScoreMode.None);

                    // 顶层 Bool 查询
                    boolQueryBuilder.must(nestedQuery);

            } else {
                builderQuery = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("ticket_ext.ext_code", workOrderExtVo.getCode()))
                        .must(QueryBuilders.termQuery("ticket_ext.ext_value",workOrderExtVo.getValues().toString()));
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("ticket_ext", builderQuery, ScoreMode.None);
                boolQueryBuilder.must(nestedQuery);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> updateWork(UpdateWorkOrderRecordVO updateWorkOrderRecordVO) {
        try{
            // 定义索引名称
            String indexName = ticketIndex+SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            // 根据工单id进行查询
            boolQueryBuilder.must(QueryBuilders.termQuery("work_record_id", updateWorkOrderRecordVO.getWorkRecordId()));
            searchSourceBuilder.query(boolQueryBuilder);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            // 拿出数据,转换返回格式
            SearchHit hit = response.getHits().getHits()[0];
            // 获取到_id
            String esId = hit.getId();


            Map<String, Object> source = hit.getSourceAsMap();
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            // 注册 JavaTimeModule
            objectMapper.registerModule(new JavaTimeModule());
            String json = objectMapper.writeValueAsString(source);
            // 将 JSON 字符串转换为 Java 对象
            TicketInfoIndex contentIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            // 工单类型code
            if(updateWorkOrderRecordVO.getWorkRecordTypeId() != null){
                contentIndex.setWorkRecordTypeCode(updateWorkOrderRecordVO.getWorkRecordTypeId());
            }
            if(updateWorkOrderRecordVO.getPriorityLevelId() != null){
                // 根据优先级ID查询优先级
                CrmAgentWorkRecordPriorityLevelDef workLevel = crmAgentWorkRecordPriorityLevelDefService.getById(updateWorkOrderRecordVO.getPriorityLevelId());
                contentIndex.setPriorityLevelId(workLevel.getPriorityLevelId());
                contentIndex.setPriorityLevelName(workLevel.getPriorityLevelCode());
                TimeVO timeVO = countGradeTime(contentIndex.getCreateTime(),contentIndex.getChannelTypeId(), updateWorkOrderRecordVO.getWorkRecordTypeId(), workLevel.getPriorityLevelId(), SecurityUtil.getLoginUser().getCompanyId());
                contentIndex.setShouldResolveTime(timeVO.getShouldResolveTime());
                contentIndex.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
                contentIndex.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
            }
            if(StringUtil.isNotEmpty(updateWorkOrderRecordVO.getWorkRecordTheme())){
                contentIndex.setWorkRecordTheme(updateWorkOrderRecordVO.getWorkRecordTheme());
            }


            // 如果工单拓展属性有进行修改
            if (CollectionUtils.isNotEmpty(updateWorkOrderRecordVO.getExtIntsList())) {
                // 定义新的工单拓展属性List
                List<TicketExt> ticketExtList = new ArrayList<>();
                updateWorkOrderRecordVO.getExtIntsList().forEach(item ->{
                    TicketExt ticketExt = new TicketExt();
                    ticketExt.setExtCode(item.getWorkRecordExtCode());
                    ticketExt.setExtValue(item.getWorkRecordExtValue());
                    ticketExtList.add(ticketExt);
                });
                contentIndex.setTicketExt(ticketExtList);
            }
            // 添加操作日志
            List<TicketOperationLog> ticketOperationLogList = new ArrayList<>();
            // 之前的操作日志不为null，取出之前的操作日志。
            if(CollectionUtils.isNotEmpty(contentIndex.getTicketOperationLog())){
                ticketOperationLogList = contentIndex.getTicketOperationLog();
            }
            // 添加工单操作日志
            TicketOperationLog ticketOperationLog = new TicketOperationLog();
            ticketOperationLog.setOperatorName(SecurityUtil.getLoginUser().getUserName());
            ticketOperationLog.setOperationLogType(TicketOperationTypeEnum.UPDATE_TICKET.getCode());
            ticketOperationLog.setOperatorTime(LocalDateTime.now());
            ticketOperationLogList.add(ticketOperationLog);
            contentIndex.setTicketOperationLog(ticketOperationLogList);

            // 进行修改保存操作日志
            UpdateRequest updateRequest = new UpdateRequest(indexName, esId);

            String value = objectMapper.writeValueAsString(contentIndex);
            updateRequest.doc(value, XContentType.JSON);
            try {
                restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK")&&!e.getMessage().contains("201")) {
                    log.error("es修改文档失败，异常信息：", e);
                    throw new RuntimeException(e);
                }
            }
        }catch (Exception e){
            log.error("修改工单报错:",e);
        }

        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> updateWorkStatus(UpdateWorkOrderRecordVO updateWorkOrderRecordVO) {

        // 首先查询工单
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex,updateWorkOrderRecordVO.getWorkRecordId(),SecurityUtil.getLoginUser().getCompanyId());
        if(ticketHit!=null){
            try {
                // 获取到_id
                esId = ticketHit.getId();
                Map<String, Object> source = ticketHit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错",e);
            }
        }

        if (ticketInfoIndex.getStatus() == 3) {
            return AjaxResult.failure("update.error");
        }
        // 根据工单类型ID查询工单类型
        if (updateWorkOrderRecordVO.getWorkRecordTypeId() != null) {
           LambdaQueryWrapper<CrmAgentWorkRecordTypeDef> queryWrapper  = new LambdaQueryWrapper<CrmAgentWorkRecordTypeDef>()
                   .eq(CrmAgentWorkRecordTypeDef::getLanguageCode,updateWorkOrderRecordVO.getLanguage())
                           .eq(CrmAgentWorkRecordTypeDef::getCompanyId,SecurityUtil.getLoginUser().getCompanyId())
                                   .eq(CrmAgentWorkRecordTypeDef::getDataStatus,1)
                                           .eq(CrmAgentWorkRecordTypeDef::getWorkRecordTypeValue,updateWorkOrderRecordVO.getWorkRecordTypeId());
            CrmAgentWorkRecordTypeDef workRecordTypeDef= crmAgentWorkRecordTypeDefMapper.selectOne(queryWrapper);
            if (ObjectUtils.isNotEmpty(workRecordTypeDef)) {
                ticketInfoIndex.setWorkRecordTypeName(workRecordTypeDef.getWorkRecordTypeName());
                ticketInfoIndex.setWorkRecordTypeCode(updateWorkOrderRecordVO.getWorkRecordTypeId());
            }
        }
        if (updateWorkOrderRecordVO.getPriorityLevelId() != null) {
            // 根据优先级ID查询优先级
            CrmAgentWorkRecordPriorityLevelDef workLevel = crmAgentWorkRecordPriorityLevelDefService.getById(updateWorkOrderRecordVO.getPriorityLevelId());
            ticketInfoIndex.setPriorityLevelId(workLevel.getPriorityLevelId());
            ticketInfoIndex.setPriorityLevelName(workLevel.getPriorityLevelCode());
            TimeVO timeVO = countGradeTime(ticketInfoIndex.getCreateTime(),ticketInfoIndex.getChannelTypeId(), updateWorkOrderRecordVO.getWorkRecordTypeId(), workLevel.getPriorityLevelId(), SecurityUtil.getLoginUser().getCompanyId());
            ticketInfoIndex.setShouldResolveTime(timeVO.getShouldResolveTime());
            ticketInfoIndex.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
            ticketInfoIndex.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
        }
        elasticsearchUtil.updateDocument(ticketIndex+SecurityUtil.getLoginUser().getCompanyId(),esId,ticketInfoIndex);
        // 添加工单操作日志
        addTicketOperationLog(ticketInfoIndex.getWorkRecordId(), null, TicketOperationTypeEnum.UPDATE_TICKET.getCode(), SecurityUtil.getLoginUser());
        return AjaxResult.ok(null, MessageUtils.get("update.success"));

    }

    @Override
    public AjaxResult<Object> workBindingAgent(WorkOrderClaimVO workOrderClaimVO) {
        log.info("打印一下绑定工单时，请求参数:{}",workOrderClaimVO);
        // 首先查询工单
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex,workOrderClaimVO.getWorkRecordId(),workOrderClaimVO.getCompanyId());
        if(ticketHit!=null){
            try {
                // 获取到_id
                esId = ticketHit.getId();
                Map<String, Object> source = ticketHit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错",e);
            }
        }
        // 如果当前工单坐席id存在，并且和绑定坐席id不一致，则直接认为分配失败
        if(StringUtil.isNotEmpty(ticketInfoIndex.getAgentId())&&
                !ticketInfoIndex.getAgentId().equals(workOrderClaimVO.getAgentId())){
            return AjaxResult.ok(MessageUtils.get("work.cannot.claim"));
        }
        if(StringUtil.isNotEmpty(workOrderClaimVO.getDeptId())){
            ticketInfoIndex.setDeptId(workOrderClaimVO.getDeptId());
        }
        // 如果为null或者为机器人工单则直接分配
        if(StringUtil.isNotEmpty(workOrderClaimVO.getAgentId())&&(StringUtil.isEmpty(ticketInfoIndex.getAgentId())||"1001".equals(ticketInfoIndex.getAgentId()))){
            ticketInfoIndex.setAgentId(workOrderClaimVO.getAgentId());
            // 绑定上坐席后，则需要赋值客户消息到达时间
            ticketInfoIndex.setLastMessageDeliveryTime(LocalDateTime.now());
            // 根据座席ID查询座席名称
            R<UserDetailsVO> userDetails = userClient.queryUserDetails(workOrderClaimVO.getAgentId());
            if (userDetails.getCode() == AjaxResult.SUCCESS) {
                ticketInfoIndex.setAgentName(userDetails.getData().getUserName());
                ticketInfoIndex.setDeptId(userDetails.getData().getDeptId());
            }
            ticketInfoIndex.setAcceptType(3);
            ticketInfoIndex.setStatus(1);
        }
        // 如果当前工单为该座席，则直接认定为分配成功,修改工单状态为待坐席回复
        if(StringUtil.isNotEmpty(ticketInfoIndex.getAgentId())&&StringUtil.isNotEmpty(workOrderClaimVO.getAgentId())&&
                ticketInfoIndex.getAgentId().equals(workOrderClaimVO.getAgentId())){
            ticketInfoIndex.setStatus(1);
        }

        // 绑定成功进行返回
        elasticsearchUtil.updateDocument(ticketIndex+workOrderClaimVO.getCompanyId(),esId,ticketInfoIndex);

        return AjaxResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> saveWorkSummarize(WorkSummarizeVO workSummarizeVO) throws IOException {

        String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();

        Map<String , Object> result =queryEsTicketInfo(workSummarizeVO.getWorkRecordId());

        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            workSummarizeVO.setInterfaceType(1);
            workSummarize(workSummarizeVO);
        } else {
            TicketContentIndex ticketContentIndex = (TicketContentIndex) result.get("data");
            String id=result.get("id").toString();
            SmartSummaryResult summaryResult = new SmartSummaryResult();
            String content=ticketContentIndex.getContent();
            if(content!=null){
                summaryResult = JSONObject.parseObject(ticketContentIndex.getContent(), SmartSummaryResult.class);
            }
            if (StringUtil.isNotEmpty(workSummarizeVO.getContentSummary())) {
                summaryResult.setSummary(workSummarizeVO.getContentSummary());
            }
            // 客户心情
            if (workSummarizeVO.getCustomerMood() != null) {
                summaryResult.setMood(workSummarizeVO.getCustomerMood().toString());
            }
            ticketContentIndex.setContent(JSONObject.toJSONString(summaryResult));
            elasticsearchUtil.updateDocument(index,id,ticketContentIndex);
        }

        if(workSummarizeVO.getWorkStatus()!=null && workSummarizeVO.getWorkStatus()){
            WorkOrderUpdateStatusVO workOrderUpdateStatusVO=new WorkOrderUpdateStatusVO();
            workOrderUpdateStatusVO.setWorkRecordId(workSummarizeVO.getWorkRecordId());
            solveWorkStatus(workOrderUpdateStatusVO);
        }
        return AjaxResult.ok(workOrderDetail(workSummarizeVO.getWorkRecordId(), 1));
    }

    private Map<String,Object> queryEsTicketInfo(String workRecordId){
        String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();

        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workRecordId));
        queryConditions.add(createCondition("reply_type", 4));
        queryConditions.add(createCondition(1));
        queryConditions.add(createCondition("reply_time", SortOrder.DESC));
        return elasticsearchUtil.searchInfoMapQuery(queryConditions, index, TicketContentIndex.class);
    }

    @Override
    public AjaxResult<Object> saveWaitExecute(SaveWaitExecuteVO saveWaitExecuteVO)  {

        String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();
        // 定义索引名称
        Map<String , Object> result =queryEsTicketInfo(saveWaitExecuteVO.getWorkRecordId());
        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            return AjaxResult.failure("error.null.record");
        }

        TicketContentIndex ticketContentIndex = (TicketContentIndex) result.get("data");
        String id =result.get("id").toString();

        SmartSummaryResult summaryResult = new SmartSummaryResult();
        if(ticketContentIndex.getContent()!=null) {
            summaryResult=JSONObject.parseObject(ticketContentIndex.getContent(), SmartSummaryResult.class);
        }
        //待办事项
        if (ObjectUtils.isNotEmpty(saveWaitExecuteVO)) {
            String waitExecuteId = saveWaitExecuteVO.getWaitExecuteId();
            //根据待办事项id判断是否新增
            if (StringUtils.isEmpty(waitExecuteId)) {
                WaitExecuteVO waitExecuteVO = new WaitExecuteVO();
                waitExecuteVO.setWaitExecuteId(UUID.randomUUID().toString().replace("-", ""));
                waitExecuteVO.setWaitExecuteStatus(0);
                waitExecuteVO.setWaitExecuteEvent(saveWaitExecuteVO.getWaitExecuteEvent());
                summaryResult.getToDoList().add(waitExecuteVO);
            } else {
                for (WaitExecuteVO waitExecuteVO : summaryResult.getToDoList()) {
                    if (waitExecuteId.equals(waitExecuteVO.getWaitExecuteId())) {
                        if(saveWaitExecuteVO.getWaitExecuteEvent()!=null){
                            waitExecuteVO.setWaitExecuteEvent(saveWaitExecuteVO.getWaitExecuteEvent());
                        }
                        String operationLogDescribe = null;
                        Integer operationLogType = 0;
                        if (saveWaitExecuteVO.getStatus() == null) {
                            waitExecuteVO.setWaitExecuteStatus(0);
                        } else if (saveWaitExecuteVO.getStatus() == 1) {
                            waitExecuteVO.setWaitExecuteStatus(1);
                            operationLogDescribe = waitExecuteVO.getWaitExecuteEvent();
                            operationLogType = TicketOperationTypeEnum.COMPLETE_TICKET_WAIT_EXECUTE.getCode();
                        } else if (saveWaitExecuteVO.getStatus() == 0) {
                            waitExecuteVO.setWaitExecuteStatus(0);
                            operationLogDescribe = waitExecuteVO.getWaitExecuteEvent();
                            operationLogType = TicketOperationTypeEnum.CANCELLATION_TICKET_COMPLETE.getCode();
                        } else {
                            summaryResult.getToDoList().removeIf(waitExecute -> waitExecuteVO.getWaitExecuteId().equals(waitExecute.getWaitExecuteId()));  // 根据ID移除
                            operationLogDescribe = waitExecuteVO.getWaitExecuteEvent();
                            operationLogType = TicketOperationTypeEnum.DELETE_TICKET_WAIT_EXECUTE.getCode();
                            addTicketOperationLog(saveWaitExecuteVO.getWorkRecordId(), operationLogDescribe, operationLogType, SecurityUtil.getLoginUser());
                            break;
                        }
                        // 添加工单操作日志
                        addTicketOperationLog(saveWaitExecuteVO.getWorkRecordId(), operationLogDescribe, operationLogType, SecurityUtil.getLoginUser());
                    }
                }
            }
        }
        ticketContentIndex.setContent(JSONObject.toJSONString(summaryResult));
        //保存到es
        elasticsearchUtil.updateDocument(index,id,ticketContentIndex);
        return AjaxResult.ok(summaryResult);
    }

    @Override
    public AjaxResult<Object> updateSessionStatus(SessionStatusVo acwStatusVo) {
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex,acwStatusVo.getWorkOrderId(),acwStatusVo.getCompanyId());
        if(ticketHit!=null){
            try {
                // 获取到_id
                esId = ticketHit.getId();
                Map<String, Object> source = ticketHit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错",e);
            }
        }
        if (ObjectUtils.isNotEmpty(ticketInfoIndex)) {
            // 数据存在则进行修改
            ticketInfoIndex.setSessionStatus(acwStatusVo.getSessionStatus());
            if(ObjectUtil.isNotNull(ticketInfoIndex.getSessionStatus())&&ticketInfoIndex.getSessionStatus() == 3){
                ticketInfoIndex.setEndTime(LocalDateTime.now());
            }
            // 进行修改acw状态
            elasticsearchUtil.updateDocument(ticketIndex+acwStatusVo.getCompanyId(),esId,ticketInfoIndex);
            return AjaxResult.ok();
        }

        return AjaxResult.failure(MessageUtils.get("marketing.email.template.data.not.exist"));
    }

    @Override
    public IPage<WorkOrderRecordFinalEsResult> queryEmailWorkOrderList(IPage<Object> page, WorkOrderRecordRequest workOrderRecordRequest) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), 0L);
            List<WorkOrderRecordFinalEsResult> results = new ArrayList<>();
            resultPage.setRecords(results);
            return resultPage;
        }
        //只查询处理中的状态
        IPage<WorkOrderRecordFinalEsResult> workOrderRecordResultIPage = queryWorkOrderListFromEs(page, workOrderRecordRequest,1);
        //如果查询出来的分页数据，不为空，将所有的工单id整理出来，用于查询聊天记录，然后根据工单Id，塞回到对应的返回数据中
        List<WorkOrderRecordFinalEsResult> records = workOrderRecordResultIPage.getRecords();
        List<String> workOrderIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(records)) {
            workOrderIdList = records.stream()
                    .map(WorkOrderRecordFinalEsResult::getWorkRecordId)
                    .filter(Objects::nonNull) // 过滤掉 null 值
                    .collect(Collectors.toList());
            Map<String, List<TicketContentIndex>> resultMap = queryEsChatContent(workOrderIdList);
            //查询每个工单的未读内容记录数
            Map<String, Integer> unReadResultMap = queryEsUnReadChatContent(workOrderIdList);
            //将这些返回结果一一对应到查询出来的列表中
            for(WorkOrderRecordFinalEsResult workOrderRecordResult:records){
                String workRecordId = workOrderRecordResult.getWorkRecordId();
                workOrderRecordResult.setTicketContentIndex(resultMap.get(workRecordId));
                workOrderRecordResult.setUnreadCount(unReadResultMap.get(workRecordId));
            }
        }
        return workOrderRecordResultIPage;
    }

    private Map<String, Integer> queryEsUnReadChatContent(List<String> workOrderIdList) {
        Map<String, Integer> resultMap = new HashMap<>();

        for (String workOrderId : workOrderIdList) {
            try {
                List<TicketContentIndex> ticketContentIndexList = queryUnReadContentFromEs(workOrderId);
                resultMap.put(workOrderId, ticketContentIndexList.size());
            } catch (IOException e) {
                log.error("根据工单Id集合，查询es中的未读工单聊天记录出现异常 {}: {}", workOrderId, e.getMessage(), e);
                resultMap.put(workOrderId, 0);
            }
        }

        return resultMap;
    }

    private TicketContentIndex queryWorkSummarToEs(String workRecordId){

        String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();
        // 拍岸的索引是否存在
        boolean indexExists = headIndexExists(index);
        if (!indexExists) {
            return new TicketContentIndex();
        }
        if(StringUtils.isEmpty(workRecordId)){
            return new TicketContentIndex();
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("work_record_id", workRecordId))  // 查询指定的唯一主键
                        .must(QueryBuilders.termQuery("reply_type", 4)))
                .sort("reply_time", SortOrder.DESC)
                .size(1);
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            return new TicketContentIndex();
        }

        TicketContentIndex ticketContentIndex = new TicketContentIndex();
        if (Objects.requireNonNull(searchResponse.getHits().getTotalHits()).value > 0) {
            SearchHits hits = searchResponse.getHits();
            for (SearchHit hit : hits) {
                // 解析JSON字符串
                ticketContentIndex = JSONObject.parseObject(hit.getSourceAsString(), TicketContentIndex.class);
            }
        }
        return ticketContentIndex;
    }

    @Override
    public WorkRecordDetailVo   workOrderDetail(String workRecordId, int type) throws IOException {
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        long begin = System.currentTimeMillis();
        String esId = null;
        SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex,workRecordId,SecurityUtil.getLoginUser().getCompanyId());
        if(hit!=null){
            try {
                // 获取到_id
                esId = hit.getId();
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错",e);
            }
        }
        long end = System.currentTimeMillis();
        long takeTime = end - begin;
        log.info("查询工单es，耗费了:{}毫秒",takeTime);
        WorkRecordDetailVo workRecordDetailVo = new WorkRecordDetailVo();
        BeanUtils.copyProperties(ticketInfoIndex, workRecordDetailVo);
        // 渠道类型名称
        workRecordDetailVo.setChannelTypeName(TransUtil.trans(workRecordDetailVo.getChannelTypeName()));
        workRecordDetailVo.setSummarizeStatus(0);
        // 如果总结内容不为null,则为已总结
//        if(crmAgentWorkRecord.getContentSummary()!=null){
//            workRecordDetailVo.setSummarizeStatus(1);
//        }
        if(StringUtils.isNotBlank(ticketInfoIndex.getWorkRecordTypeCode())){
            workRecordDetailVo.setWorkRecordTypeId(ticketInfoIndex.getWorkRecordTypeCode());
        }
        begin = System.currentTimeMillis();
        // 查询到创建人
        // 根据座席ID查询座席名称
        if(ticketInfoIndex.getAgentId() != null ){
            if(!"1001".equals(ticketInfoIndex.getAgentId())) {
                R<UserDetailsVO> userDetails = userClient.queryUserDetails(ticketInfoIndex.getAgentId());
                if (userDetails.getCode() == AjaxResult.SUCCESS) {
                    workRecordDetailVo.setAgentName(userDetails.getData().getUserName());
                    String url = null;
                    if(StringUtils.isNotEmpty(userDetails.getData().getProfilePhoto())){
                        url= domainPrefix + userDetails.getData().getProfilePhoto();
                    }
                    workRecordDetailVo.setAgentUrl(url);
                }
            } else{
                workRecordDetailVo.setAgentUrl(cocoPicture);
            }
        }
        end = System.currentTimeMillis();
        takeTime = end - begin;
        log.info("查询坐席个人信息，耗费了:{}毫秒",takeTime);

        begin = System.currentTimeMillis();
        // 查询该用户是否关注工单
//        CrmAgentWorkRecordConcerned recordConcerned = crmAgentWorkRecordConcernedService.getOne(new QueryWrapper<CrmAgentWorkRecordConcerned>().lambda()
//                .eq(CrmAgentWorkRecordConcerned::getWorkRecordId, workRecordId)
//                .eq(CrmAgentWorkRecordConcerned::getUserId, getUserId())
//                .eq(CrmAgentWorkRecordConcerned::getDataStatus, 1));
//        begin = System.currentTimeMillis();
        List<TicketFollow> ticketFollowList = ticketInfoIndex.getTicketFollow();
        // 首先判断该工单是否有关注
        if(CollectionUtils.isEmpty(ticketFollowList)){
            workRecordDetailVo.setConcernedWorkStatus(0);
        }else{
            // 检查是否包含指定的 userId
            boolean containsUser = ticketFollowList.stream()
                    .anyMatch(ticket -> getUserId().equals(ticket.getUserId()));
            if(containsUser){
                workRecordDetailVo.setConcernedWorkStatus(1);
            }else {
                workRecordDetailVo.setConcernedWorkStatus(0);
            }
        }

        List<TicketSatisfaction> ticketSatisfactionList = ticketInfoIndex.getTicketSatisfaction();
        if (CollectionUtils.isNotEmpty(ticketSatisfactionList)) {
            // 取出最后一条
            TicketSatisfaction ticketSatisfaction = ticketSatisfactionList.get(ticketSatisfactionList.size() - 1);
            BigDecimal rating = BigDecimal.valueOf(ticketSatisfaction.getRating());
            workRecordDetailVo.setRating(rating);
        } else {
            workRecordDetailVo.setRating(BigDecimal.ZERO);
        }

        // 查询对应工单ID扩展字段值
//        List<CrmAgentWorkRecordExtInts> list = crmAgentWorkRecordExtIntsService.list(new QueryWrapper<CrmAgentWorkRecordExtInts>().lambda().eq(CrmAgentWorkRecordExtInts::getWorkRecordId, workRecordId)
//                .eq(CrmAgentWorkRecordExtInts::getDataStatus, 1));
        // TODO 如果是工作台，则拓展属性、工单扩展字段、内容总结 不进行执行返回
        if(type == 1) {
            List<CrmAgentWorkRecordExtInts> list = new ArrayList<>();
            if (ticketInfoIndex.getTicketExt() != null) {
                List<TicketExt> ticketExtList = ticketInfoIndex.getTicketExt();
                for (TicketExt ticketExt : ticketExtList) {
                    CrmAgentWorkRecordExtInts crmAgentWorkRecordExtInts = new CrmAgentWorkRecordExtInts();
                    crmAgentWorkRecordExtInts.setWorkRecordExtCode(ticketExt.getExtCode());
                    crmAgentWorkRecordExtInts.setWorkRecordExtValue(ticketExt.getExtValue());
                    list.add(crmAgentWorkRecordExtInts);
                }
            }
            workRecordDetailVo.setExtIntsList(list);
            end = System.currentTimeMillis();
            takeTime = end - begin;
            log.info("处理关注工单，拓展字段，耗费了:{}毫秒", takeTime);

            begin = System.currentTimeMillis();
            //工单扩展字段定义
            List<WorkOrderExtVo> defList = crmAgentWorkRecordExtDefService.queryList(8).getData();
            workRecordDetailVo.setDefList(defList);
            end = System.currentTimeMillis();
            takeTime = end - begin;
            log.info("查询拓展属性，耗费了:{}毫秒", takeTime);

            begin = System.currentTimeMillis();
            // 总结内容 待办事项
            TicketContentIndex ticketContentIndex = queryWorkSummarToEs(workRecordId);
            if (ObjectUtils.isNotEmpty(ticketContentIndex) && ticketContentIndex.getContent() != null) {
                SmartSummaryResult summaryResult = JSONObject.parseObject(ticketContentIndex.getContent(), SmartSummaryResult.class);
                //待办事项数据
                List<CrmAgentWorkRecordWaitExecute> recordWaitExecutes = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(summaryResult.getToDoList())) {
                    List<WaitExecuteVO> executeVOList = summaryResult.getToDoList();
                    if (ObjectUtils.isNotEmpty(executeVOList)) {
                        recordWaitExecutes = executeVOList.stream().map(doList -> {
                            CrmAgentWorkRecordWaitExecute recordWaitExecute = new CrmAgentWorkRecordWaitExecute();
                            BeanUtils.copyProperties(doList, recordWaitExecute);
                            return recordWaitExecute;
                        }).collect(Collectors.toList());
                    }
                }
                workRecordDetailVo.setSummarizeStatus(1);
                workRecordDetailVo.setContentSummary(summaryResult.getSummary());
                workRecordDetailVo.setCustomerMood(Integer.parseInt(getMood(summaryResult.getMood())));
                workRecordDetailVo.setWaitExecuteList(recordWaitExecutes);
                log.info("查询工单内容总结详情信息{}", recordWaitExecutes);
            }

            end = System.currentTimeMillis();
            takeTime = end - begin;
            log.info("查询待办事项，耗费了:{}毫秒", takeTime);
            begin = System.currentTimeMillis();
        }
        // 查询客户信息
        // 客户ID查询客户电话
        R<CrmCustomerVO> customerDetails = customerClient.queryCustomerDetails(ticketInfoIndex.getCustomerId());
        if (customerDetails.getCode() == AjaxResult.SUCCESS) {
            CrmCustomerVO customer = customerDetails.getData();
            // 如果手机号字段存在，则进行复制
            if(StringUtil.isNotEmpty(customer.getTelephone())){
                //进行手机号加密
                customer.setTelephone(DesensitizationUtils.desensitizeCustomerInfo(customer.getTelephone()));
                // 判断电话区号是否为null，不为null则进行拼接
                if(StringUtil.isNotEmpty(customer.getTelephonePrefixId())){
                    workRecordDetailVo.setCustomerPhone(customer.getTelephonePrefixId()+customer.getTelephone());
                }else {
                    workRecordDetailVo.setCustomerPhone(customer.getTelephone());
                }
            }

        }
        end = System.currentTimeMillis();
        takeTime = end - begin;
        log.info("查询客户信息，耗费了:{}毫秒",takeTime);
        return workRecordDetailVo;
    }

    private String getMood(String mood){
        try {
            if (StringUtil.isNotEmpty(mood)) {
                // 使用正则表达式提取第一位数字
                Pattern pattern = Pattern.compile("\\d");
                Matcher matcher = pattern.matcher(mood);
                if (matcher.find()) {
                    return (matcher.group(0));
                } else {
                    return ("2");  // 如果没有数字，设置为默认值 2 中立
                }
            } else {
                return ("2");
            }
        } catch (NumberFormatException e) {
            log.warn("无效的 mood 值: {}", mood, e);
        }
        return ("2");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> solveWorkStatus(WorkOrderUpdateStatusVO workOrderUpdateStatusVO) {
        log.info("solveWorkStatus-> 当前工单入参：【{}】",JSONObject.toJSONString(workOrderUpdateStatusVO));
        String indexName=ticketIndex+SecurityUtil.getLoginUser().getCompanyId();

        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workOrderUpdateStatusVO.getWorkRecordId()));

        Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, indexName, TicketInfoIndex.class);
        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
        String id = (String) result.get("id");

        if (Objects.isNull(workRecord)) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        log.info("当前工单状态：【{}】",workRecord.getStatus());
        // 如果工单已经解决过或其他工单状态 则返回成功状态 不进行下面状态流转流程
        if (workRecord.getStatus()==3) {
            return AjaxResult.ok(null, MessageUtils.get("work.update.status.resolution.solved"));
        }
        if (workRecord.getStatus()==4) {
            return AjaxResult.ok(null, MessageUtils.get("work.update.status.resolution.terminated"));
        }
        if (workRecord.getStatus()==5) {
            return AjaxResult.ok(null, MessageUtils.get("work.update.status.resolution.transferred"));
        }
        // 当前工单状态不支持解决
//        if (workRecord.getStatus() != 0 && workRecord.getStatus() != 1 && workRecord.getStatus() != 2) {
//            return AjaxResult.failure(MessageUtils.get("work.update.status.resolution"));
//        }
        // 更新工单状态为 3 并设置解决时间
        workRecord.setStatus(3);
        workRecord.setResolveTime(LocalDateTime.now());

        //用户个人解决
        workRecord.setDeleteSource("1");

        elasticsearchUtil.updateDocument(indexName,id,workRecord);
        try {
            if(workRecord.getChannelTypeId().equals(CrmChannelEnum.EMAIL.getCode().toString())){
                KinesisContactDetailsVo contactDetailsVo = new KinesisContactDetailsVo();
                contactDetailsVo.setWorkOrderId(workRecord.getWorkRecordId());
                contactDetailsVo.setEndTime(new Date());
                contactDetailsVo.setCompanyId(workRecord.getCompanyId());
                contactDetailsVo.setContactId(workRecord.getWorkRecordId());
                ContactDetailHandleVo contactDetailHandleVo = new ContactDetailHandleVo();
                contactDetailHandleVo.setKinesisContactDetailsVo(contactDetailsVo);
                contactDetailHandleVo.setEventType(10);
                // 基础处理完以后，发送MQ，开始数据存储
                String jsonString = JSON.toJSONString(contactDetailHandleVo);
                rabbitTemplate.convertAndSend(RabbitMqConstants.CONTACT_DETAIL_EXCHANGE, RabbitMqConstants.CONTACT_DETAIL_CHANNEL_ROUTING_KEY, jsonString);
            }
        }catch (Exception e){
            log.error("邮件解决工单调用联络明细失败:",e);
        }
        try {
            ResponseEntity<?> responseEntity = imClient.removeSessionAndCloseSocket(workRecord.getCompanyId(), workRecord.getWorkRecordId());
        }catch (Exception e){
            log.error("调用im解决工单报错",e);
        }
        // 记录操作日志
        addTicketOperationLog(workRecord.getWorkRecordId(), null, TicketOperationTypeEnum.SOLVE_TICKET.getCode(), SecurityUtil.getLoginUser());
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override 
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> assignWorkOrder(WorkOrderAssignVO workOrderAssignVO) {
        // 查询工单id是否存在
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex,workOrderAssignVO.getWorkRecordId(),SecurityUtil.getLoginUser().getCompanyId());
        if(hit!=null){
            try {
                // 获取到_id
                esId = hit.getId();
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错",e);
            }
        }
        // 当前工单状态不支持指派
        if(ticketInfoIndex.getStatus() != 0){
            return AjaxResult.failure(MessageUtils.get("work.cannot.assign"));
        }
        ticketInfoIndex.setStatus(1);
        ticketInfoIndex.setAcceptType(0);
        ticketInfoIndex.setInitiationTime(LocalDateTime.now());
        ticketInfoIndex.setAgentId(workOrderAssignVO.getAgentId());
        ticketInfoIndex.setModifier(SecurityUtil.getUserId());
        ticketInfoIndex.setModifyTime(LocalDateTime.now());
        // 分配上坐席后，则需要赋值客户消息到达时间
        ticketInfoIndex.setLastMessageDeliveryTime(LocalDateTime.now());
        // 根据座席ID查询座席名称
        R<UserDetailsVO> userDetails = userClient.queryUserDetails(workOrderAssignVO.getAgentId());
        if (userDetails.getCode() == AjaxResult.SUCCESS) {
            ticketInfoIndex.setAgentName(userDetails.getData().getUserName());
            ticketInfoIndex.setDeptId(userDetails.getData().getDeptId());
        }
        elasticsearchUtil.updateDocument(ticketIndex+SecurityUtil.getLoginUser().getCompanyId(),esId,ticketInfoIndex);

        // 添加工单操作日志
        addTicketOperationLog(ticketInfoIndex.getWorkRecordId(), workOrderAssignVO.getOperationLogReason(), TicketOperationTypeEnum.ASSIGN_TICKET.getCode(), SecurityUtil.getLoginUser());
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    public List<UserDetailsVO> getOnlineUsersByDeptId(String teamId) {
        String currentUserId = SecurityUtil.getLoginUser().getUserId();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();

        // 查询部门下所有人员
        R<List<UserDetailsVO>> userListResponse = userClient.queryUserListByDeptId(teamId);
        if (userListResponse == null || userListResponse.getCode() != 200 || userListResponse.getData() == null) {
            return Collections.emptyList();
        }

        List<UserDetailsVO> userDetailsList = userListResponse.getData();
        List<UserDetailsVO> filteredUserList = userDetailsList.stream()
                .filter(user -> !user.getUserId().equals(currentUserId))
                .collect(Collectors.toList());

        if (filteredUserList.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取在线用户
        ResponseEntity<?> response = imClient.getAvailableAgentList(companyId, teamId);
        if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
            return Collections.emptyList();
        }

        // 解析 response.getBody()
        Set<String> tempOnlineUserIds = new HashSet<>();
        Object responseBody = response.getBody();

        if (responseBody instanceof List<?>) {
            tempOnlineUserIds = ((List<?>) responseBody).stream()
                    .filter(item -> item instanceof String)
                    .map(String.class::cast)
                    .collect(Collectors.toSet());
        } else if (responseBody instanceof Map<?, ?>) {
            Map<?, ?> responseMap = (Map<?, ?>) responseBody;
            Object userListObj = responseMap.get("data");
            if (userListObj instanceof List<?>) {
                tempOnlineUserIds = ((List<?>) userListObj).stream()
                        .filter(item -> item instanceof String)
                        .map(String.class::cast)
                        .collect(Collectors.toSet());
            }
        }

        // **改动：创建一个 final 变量**
        final Set<String> onlineUserIds = tempOnlineUserIds;

        if (onlineUserIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 过滤出在线用户
        return filteredUserList.stream()
                .filter(user -> onlineUserIds.contains(user.getUserId()))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> urgingWorkOrder(WorkOrderUpdateStatusVO workOrderUpdateStatusVO) {
        String indexName=ticketIndex+SecurityUtil.getLoginUser().getCompanyId();

        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workOrderUpdateStatusVO.getWorkRecordId()));

        Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, indexName, TicketInfoIndex.class);
        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
        String id = (String) result.get("id");

        if(workRecord == null){
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }

        workRecord.setReminderStatus(1);
        workRecord.setModifier(SecurityUtil.getUserId());
        workRecord.setModifyTime(LocalDateTime.now());
        elasticsearchUtil.updateDocument(indexName, id, workRecord);
        // 添加工单操作日志
        addTicketOperationLog(workRecord.getWorkRecordId(), workOrderUpdateStatusVO.getOperationLogReason(), TicketOperationTypeEnum.URGING_TICKET.getCode(), SecurityUtil.getLoginUser());
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> workUpgrade(WorkOrderUpgrade workOrderUpgrade){
        String indexName=ticketIndex+SecurityUtil.getLoginUser().getCompanyId();
        // 查询工单是否存在
        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workOrderUpgrade.getWorkRecordId()));

        Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, indexName, TicketInfoIndex.class);
        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
        String id = (String) result.get("id");

        if(workRecord == null){
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        // 判断工单是否调整了等级
        if(workOrderUpgrade.getPriorityLevelId().equals(workRecord.getPriorityLevelId())){
            return AjaxResult.failure(MessageUtils.get("work.update.status.level"));
        }

        // 添加工单操作日志
        addTicketOperationLog(workRecord.getWorkRecordId(), workOrderUpgrade.getOperationLogReason(), TicketOperationTypeEnum.IMPROVE_TICKET.getCode(), SecurityUtil.getLoginUser());
        // 查询优先级名称
        CrmAgentWorkRecordPriorityLevelDef workLevel = crmAgentWorkRecordPriorityLevelDefService.getById(workOrderUpgrade.getPriorityLevelId());
        workRecord.setPriorityLevelId(workOrderUpgrade.getPriorityLevelId());
        workRecord.setPriorityLevelName(workLevel.getPriorityLevelCode());
        TimeVO timeVO = countGradeTime(workRecord.getCreateTime(),workRecord.getChannelTypeId(), workOrderUpgrade.getWorkRecordId(), workOrderUpgrade.getPriorityLevelId(), SecurityUtil.getLoginUser().getCompanyId());
        workRecord.setShouldResolveTime(timeVO.getShouldResolveTime());
        workRecord.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
        workRecord.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
        elasticsearchUtil.updateDocument(indexName, id, workRecord);

        // 判断是否更换了座席
        if(!workOrderUpgrade.getAgentId().equals(workRecord.getAgentId())){
            // 未更换座席,则需要走转派流程
            // 将旧工单状态修改为已转单
            // 进行转单操作
            WorkOrderAssignVO workOrderAssignVO = new WorkOrderAssignVO();
            workOrderAssignVO.setWorkRecordId(workRecord.getWorkRecordId());
            workOrderAssignVO.setAgentId(workOrderUpgrade.getAgentId());
            workOrderAssignVO.setOperationLogReason(workOrderUpgrade.getOperationLogReason());
            transferWorkOrder(workOrderAssignVO);
        }

        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    /**
     * 方法改变
     * 同步计算当前的
     * 解决时间 用户发送时间+SLa对应的解决时间
     * 响应时间 用户发送时间+Sla对应的首次响应时间
     * 下一次平均响应时间 用户发送时间+Sla对应的下一次平均相响应时间
     *
     * @param createTime       创建时间
     * @param channelCode     渠道code
     * @param recordTypeValue 工单value
     * @param priorityLevelId 优先级id
     * @param companyId       公司id
     * @return 返回三种时间的数据集合
     */
    private TimeVO countGradeTime(LocalDateTime createTime,String channelCode, String recordTypeValue, String priorityLevelId, String companyId) {
        TimeVO timeVO = new TimeVO();
        CrmAgentWorkRecordSlaDef recordSlaDef =
                crmAgentWorkRecordSlaDefService.queryLevelSla
                        (companyId, channelCode, recordTypeValue, priorityLevelId);
            //首次响应时间 - 用户首次发消息
            timeVO.setFirstResponseTime(
                    convertToSecond(recordSlaDef.getResponseTimeUnit(), recordSlaDef.getResponseTime())
            );
            //下一次响应时间 - 座席进行过工单回复
            timeVO.setAvgResponseTime(
                    convertToSecond(recordSlaDef.getNextResponseTimeUnit(), recordSlaDef.getNextResponseTime()));
        //解决时间
        timeVO.setShouldResolveTime(convert(check(createTime, recordSlaDef.getResolveTimeUnit(), recordSlaDef.getResolveTime())));
        return timeVO;
    }

    /**
     * 将时间转换为秒
     *
     * @param unit
     * @param time
     * @return
     */
    private Integer convertToSecond(String unit, Integer time) {
        if(ObjectUtil.isNull(time)){
            return 0;
        }
        Integer dateTime = null;
        switch (unit) {
            case "M":
                dateTime = time*60;
                break;
            case "H":
                dateTime = time * 3600;
                break;
            case "D":
                dateTime = time * 24 * 3600;
                break;
            default:
                dateTime = 0;
                break;
        }
        return dateTime;
    }

    public LocalDateTime check(LocalDateTime nowDataTime, String unit, Integer time) {
        LocalDateTime dateTime = null;
        switch (unit) {
            case "M":
                dateTime = nowDataTime.plusMinutes(time);
                break;
            case "H":
                dateTime = nowDataTime.plusHours(time);
                break;
            case "D":
                dateTime = nowDataTime.plusDays(time);
                break;
            default:
                dateTime = nowDataTime;
                break;
        }
        return dateTime;
    }

    public LocalDateTime convert(LocalDateTime time) {
        Date gradeTime = Date.from(time.toInstant(ZoneOffset.ofHours(8)));
        // 将 Date 转换为 Instant
        Instant instant = gradeTime.toInstant();
        // 使用系统默认时区将 Instant 转换为 LocalDateTime
        LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> workRemark(WorkOrderUpdateStatusVO workOrderUpdateStatusVO) {
        TicketInfoIndex workRecord=getWorkRecordFromSearch(workOrderUpdateStatusVO.getWorkRecordId());
        if (Objects.isNull(workRecord)) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        // 生成内容记录
        String contentId = UuidUtils.generateUuid();
        TicketContentIndex ticketContentIndex = new TicketContentIndex();
        ticketContentIndex.setWork_record_content_id(contentId);
        ticketContentIndex.setWork_record_id(workRecord.getWorkRecordId());
        ticketContentIndex.setContent(workOrderUpdateStatusVO.getOperationLogReason());
        ticketContentIndex.setContent_type(1);
        ticketContentIndex.setReply_type(5);
        ticketContentIndex.setReply_person(getUsername());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 邮件回复时间
        ticketContentIndex.setReply_time(sdf.format(new Date()));
        ticketContentIndex.setReply_millis_time(Convert.toStr(Convert.toLocalDateTime(sdf.format(new Date())).toInstant(ZoneOffset.UTC).toEpochMilli()));
        // 保存到ES中
        addAgentWorkRecordContent(ticketContentIndex, SecurityUtil.getLoginUser().getCompanyId());

        // 添加工单操作日志
        addTicketOperationLog(workRecord.getWorkRecordId(), workOrderUpdateStatusVO.getOperationLogReason(), TicketOperationTypeEnum.NOTES_TICKET.getCode(), SecurityUtil.getLoginUser());
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> batchSolveWork(WorkOrderBatchSolveVO workOrderBatchSolveVO) {
        List<String> list = new ArrayList<>();
        String indexName=ticketIndex+SecurityUtil.getLoginUser().getCompanyId();
        for (String workRecordId : workOrderBatchSolveVO.getWorkRecordIdList()) {
            List<QueryCondition> queryConditions = new ArrayList<>();
            queryConditions.add(createCondition("work_record_id", workRecordId));

            Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, indexName, TicketInfoIndex.class);
            if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
                return AjaxResult.failure(MessageUtils.get("error.null.record"));
            }
            TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
            String id = (String) result.get("id");
            if(workRecord == null){
                return AjaxResult.failure(MessageUtils.get("error.null.record"));
            }
            // 当前工单状态不支持解决
            if(workRecord.getStatus() != 0 && workRecord.getStatus() != 1 && workRecord.getStatus() != 2){
                list.add(workRecord.getWordRecordCode());
            }
            workRecord.setStatus(3);
            workRecord.setResolveTime(LocalDateTime.now());
            workRecord.setModifyTime(LocalDateTime.now());
            workRecord.setModifier(getUserId());
            elasticsearchUtil.updateDocument(indexName, id, workRecord);
            // 添加工单操作日志
            addTicketOperationLog(workRecord.getWorkRecordId(), null, TicketOperationTypeEnum.SOLVE_TICKET.getCode(), SecurityUtil.getLoginUser());
            try {
                ResponseEntity<?> responseEntity = imClient.removeSessionAndCloseSocket(workRecord.getCompanyId(), workRecord.getWorkRecordId());
            }catch (Exception e){
                log.error("调用im解决工单报错",e);
            }
        }
        if(list.size()>0){
            String join = StringUtils.join(list, ",");
            return AjaxResult.failure(MessageUtils.get("work.update.status.batch.termination")+join);
        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public void exportTicket(WorkOrderRecordRequestVO workOrderRecordRequest) {

        // 定义索引名称
        String indexName = ticketIndex+SecurityUtil.getLoginUser().getCompanyId();
        // 首先进行ES索引判断
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在则返回null
        if (!indexExists) {
            return;
        }
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        // 根据前端查询列,查询条件,进行查询条件拼接
        BoolQueryBuilder boolQueryBuilder = queryConditionMontage(workOrderRecordRequest);
        searchSourceBuilder.sort("create_time", SortOrder.DESC);
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(9999);
        request.source(searchSourceBuilder);
        List<WorkOrderRecordResponseVO> workOrderList = new ArrayList<>();
        // 查询公司的拓展字段
        List<WorkOrderExtVo> defList = crmAgentWorkRecordExtDefService.queryList(8).getData();
        // 进行查询
        try {
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            long total = response.getHits().getTotalHits().value;
            for (SearchHit hit : hits) {
                WorkOrderRecordResponseVO workOrderRecordResponseVO = new WorkOrderRecordResponseVO();
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                TicketInfoIndex contentIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                BeanUtils.copyProperties(contentIndex, workOrderRecordResponseVO);
                workOrderRecordResponseVO.setCustomerTelephone(DesensitizationUtils.desensitizeCustomerInfo(contentIndex.getCustomerTelephone()));
                if(CollectionUtils.isNotEmpty(contentIndex.getTicketExt())){
                    List<TicketExt> ticketExt = contentIndex.getTicketExt();
                    // 目标 map
                    Map<String, Object> result = ticketExt.stream()
                            .collect(Collectors.toMap(
                                    TicketExt::getExtCode, // key: extCode
                                    ext -> defList.stream()
                                            .filter(def -> def.getWorkRecordExtDefCode().equals(ext.getExtCode()))
                                            .flatMap(def -> {
                                                // 根据 propertyTypeId 处理逻辑
                                                switch (def.getPropertyTypeId()) {
                                                    case "1003":
                                                    case "1005":
                                                        // 单选框逻辑
                                                        return def.getWorkOrderExtOptionDefList().stream()
                                                                .filter(option -> option.getOptionValue().equals(ext.getExtValue()))
                                                                .map(WorkOrderExtOptionDefVo::getOptionName);

                                                    case "1004":
                                                    case "1006":
                                                        // 多选框逻辑
                                                        if(StringUtil.isNotBlank(ext.getExtValue())){
                                                            return Arrays.stream(ext.getExtValue().split(","))
                                                                    .flatMap(value -> def.getWorkOrderExtOptionDefList().stream()
                                                                            .filter(option -> option.getOptionValue().equals(value))
                                                                            .map(WorkOrderExtOptionDefVo::getOptionName));
                                                        }else {
                                                            return Stream.empty();
                                                        }
                                                    default:
                                                        // 不处理
                                                        return Stream.empty();
                                                }
                                            })
                                            .collect(Collectors.joining(",")), // 将多个 optionName 拼接成字符串
                                    (v1, v2) -> v1 // 处理 key 冲突
                            ));

                    workOrderRecordResponseVO.setWorkRecordResVoIPage(result);
                }
                workOrderList.add(workOrderRecordResponseVO);
            }
        } catch (IOException e) {
            log.error("查询工单列表出现问题");
        }
        // 查询工单类型
        List<CrmAgentWorkRecordTypeDef> list = crmAgentWorkRecordTypeDefService.list(new QueryWrapper<CrmAgentWorkRecordTypeDef>().lambda()
                .eq(CrmAgentWorkRecordTypeDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                .eq(CrmAgentWorkRecordTypeDef::getLanguageCode, ServletUtils.getHeaderLanguage())
                .eq(CrmAgentWorkRecordTypeDef::getDataStatus, 1));

        // 根据工单类型code进行匹配，相匹的进行存入
        list.forEach(crmAgent ->
                workOrderList.stream()
                        .filter(workOrder -> StringUtil.isNotEmpty(workOrder.getWorkRecordTypeCode())&&
                                             StringUtil.isNotEmpty(crmAgent.getWorkRecordTypeValue())&&
                                             workOrder.getWorkRecordTypeCode().equals(crmAgent.getWorkRecordTypeValue()))
                        .forEach(workOrder -> workOrder.setWorkRecordTypeName(crmAgent.getWorkRecordTypeName()))
        );


        // 计算工单时间 根据状态来判断
        ticketDataHandle(workOrderList, 2);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 表头国际化
        Map<String, String> extDefMap = crmAgentWorkRecordExtDefService.getExtDefMap();

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 行数据
        List<List<String>> rowList = new ArrayList<>();

        // 如果Redis中记录了用户指定的表头 那么导出指定表头
        List<WorkOrderExtVo> props = RedisCacheUtil.getCacheList(WORK_RECORD_USER_TABLE_HEADER + getUserId());

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(props)) {
//            firstRow.addAll(props.stream().map(WorkOrderExtVo::getWorkRecordExtDefName).collect(Collectors.toList()));
            firstRow.addAll(props.stream()
                    .map(vo -> {
                        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(extDefMap) && StringUtil.isNotBlank(extDefMap.get(vo.getWorkRecordExtDefCode()))) {
                            return extDefMap.get(vo.getWorkRecordExtDefCode());
                        }
                        return vo.getWorkRecordExtDefName();
                    })
                    .collect(Collectors.toList()));
            List<String> firstCodeRow = props.stream().map(WorkOrderExtVo::getWorkRecordExtDefCode).collect(Collectors.toList());
            // 填充数据行
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(workOrderList)) {
                workOrderList.forEach(vo -> {
                    List<String> row = new ArrayList<>();
                    firstCodeRow.forEach(title -> {
                        try {
                            Field field = vo.getClass().getDeclaredField(title);
                            // 如果没有对应title的 field  会抛出异常 NoSuchFieldException
                            field.setAccessible(true);
                            try {
                                String value = null;
                                if(field.get(vo) != null){
                                    if("java.time.LocalDateTime".equals(field.get(vo).getClass().getName())){
                                        String format = ((LocalDateTime) field.get(vo)).format(DateTimeFormatter.ofPattern(Constants.DATE_TIME_FORMATTER));
                                        value = TimeZoneUtils.responseTimeConversion(format);
                                    }else if("reminderStatus".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 0) {
                                                value = MessageUtils.get("work.reminder.no");
                                            } else if (intValue == 1) {
                                                value = MessageUtils.get("work.reminder.yes");
                                            }
                                        }
                                    }else if("status".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 0) {
                                                value = MessageUtils.get("work.status.undistributed");
                                            } else if (intValue == 1) {
                                                value = MessageUtils.get("work.status.agent");
                                            }else if (intValue == 2) {
                                                value = MessageUtils.get("work.status.custom");
                                            }else if (intValue == 3) {
                                                value = MessageUtils.get("work.status.solve");
                                            }else if (intValue == 4) {
                                                value = MessageUtils.get("work.status.termination");
                                            }else if (intValue == 5) {
                                                value = MessageUtils.get("work.status.transfer");
                                            }
                                        }
                                    } else if("acceptType".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 0) {
                                                value = MessageUtils.get("work.accept.type.artificial");
                                            } else if (intValue == 1) {
                                                value = MessageUtils.get("work.accept.type.claim");
                                            } else if (intValue == 2) {
                                                value = MessageUtils.get("work.accept.type.automatic");
                                            }
                                        }
                                    }else if("createType".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 0) {
                                                value = MessageUtils.get("work.create.automatic");
                                            } else if (intValue == 1) {
                                                value = MessageUtils.get("work.create.artificial");
                                            }
                                        }
                                    }else if("serviceStatus".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            int intValue = (Integer) field.get(vo);
                                            if (intValue == 1) {
                                                value = MessageUtils.get("work.beyond.unresolved");
                                            } else if (intValue == 2) {
                                                value = MessageUtils.get("work.status.unresolved");
                                            }else if (intValue == 3) {
                                                value = MessageUtils.get("work.timeout.resolution");
                                            }else if (intValue == 4) {
                                                value = MessageUtils.get("work.time.solve");
                                            }else if (intValue == 5) {
                                                value = MessageUtils.get("work.status.termination");
                                            }else if (intValue == 6) {
                                                value = MessageUtils.get("work.status.transfer");
                                            }
                                        }
                                    }else if("channelTypeName".equals(field.getName())){
                                        if (Objects.nonNull(field.get(vo))) {
                                            String strValue = field.get(vo).toString();
                                            value = TransUtil.trans(strValue);
                                        }
                                    }else{

                                        value = field.get(vo).toString();
                                    }
                                }
                                row.add(value);

                            } catch (IllegalAccessException e) {
                                row.add(null);
                                log.error("获取字段 {} 的值出现异常", field.getName(), e);
                            }
                        } catch (NoSuchFieldException e) {
                            log.info("vo对象中没有这个字段属性，去到扩展属性中继续查找");

                            // 继续在对象的扩展属性中继续查找该title对应的属性值
                            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(vo.getWorkRecordResVoIPage())) {
                                boolean flag = false;
                                if(vo.getWorkRecordResVoIPage().get(title)!=null){
                                    row.add(vo.getWorkRecordResVoIPage().get(title).toString());
                                    flag = true;
                                }
                                // 在扩展字段中没有查询到 填充null
                                if (!flag) {
                                    row.add(null);
                                }
                            } else {
                                // 如果以上都没有  那么就填充null
                                row.add(null);
                            }
                        }
                    });
                    rowList.add(row);
                });
            }
        } else {
            // 如果Redis中没有 那么使用默认表头
            firstRow.addAll(Lists.newArrayList(MessageUtils.get("title.ticket.id"), MessageUtils.get("title.channel.name"), MessageUtils.get("title.priority"), MessageUtils.get("title.service.objectives"), MessageUtils.get("title.ticket.state"), MessageUtils.get("customer.name"), MessageUtils.get("title.custom.phone"), MessageUtils.get("title.agent.name"), MessageUtils.get("title.ticket.describe")));
            // 填充数据行
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(workOrderList)) {
                workOrderList.forEach(vo -> {
                    List<String> row = new ArrayList<>();
                    row.add(vo.getWorkRecordId());
                    row.add(vo.getChannelTypeName());
                    row.add(vo.getPriorityLevelName());
                    row.add(vo.getServiceObjectives());
                    row.add(vo.getStatus().toString());
                    row.add(vo.getCustomerName());
                    row.add(vo.getCustomerTelephone());
                    row.add(vo.getAgentName());
                    row.add(vo.getRemark());
                    rowList.add(row);
                });
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, "工单信息");
        } catch (IOException e) {
            log.error("导出客户资料excel出现异常", e);
        }
    }

    @Override
    public AjaxResult uploadFile(MultipartFile file) {
        // 两个动作： 先上传到S3上  再获取预签名URL
        String fileName = file.getOriginalFilename();
        fileName = FileExtensionChangeUtil.changeFileExtension(fileName);
        String objectKey = "ticket_file/"+IdWorker.get32UUID() + "/" + fileName;
        // 根据文件后缀区分文件类型
        Integer fileType = getFileType(fileName);
        S3PutObjectParam s3PutObjectParam = null;
        try {
            s3PutObjectParam = new S3PutObjectParam()
                    .setBucketName(bucketName)
                    .setObjectName(objectKey)
                    .setContentLength(file.getSize());
            s3PutObjectParam.setAccessKeyId(accessKey);
            s3PutObjectParam.setSecretAccessKey(secretKey);
            s3PutObjectParam.setRegion(region);
        } catch (Exception e) {
            log.error("上传文件出现问题", e);
        }
        // 放入s3
        String param = JSON.toJSONString(s3PutObjectParam);
        R result = s3Client.putVideoPicture(file, param, fileType);

        if (result.getCode() == AjaxResult.SUCCESS) {
            // 获取preSignedUrl
            AjaxResult ajaxResult = this.downloadFile(new ContentFile().setFileUrl(objectKey).setBucketName(bucketName));
            String preSignedUrl = (String) ajaxResult.getData();
            String url = domainPrefix+objectKey;
            Map<String, String> map = new HashMap<>();
            map.put("fileName", fileName);
            map.put("fileUrl", objectKey);
            map.put("bucketName", bucketName);
            map.put("preSignedUrl", preSignedUrl);
            map.put("url", url);
            map.put("uploadTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 先把附件信息放到Redis中 待保存的时候再从Redis中把附件信息删除掉
            // 再启动定时任务 定时删除那些已上传S3但是没有保存到数据库的附件
//            RedisCacheUtil.setCacheObject(Constants.FILE_TEMPORARY_STAGE_PREFIX.concat(objectKey), map, 2, TimeUnit.DAYS);

            return AjaxResult.ok(map);
        }

        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    /**
     * 根据文件名获取文件类型
     * @param fileName 文件名
     * @return 文件类型枚举
     */
    private Integer getFileType(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return 1;
        }

        // 获取文件后缀（不带点）
        String extension = getFileExtension(fileName).toLowerCase();

        if (IMAGE_EXTENSIONS.contains(extension)) {
            return 2;
        } else if (VIDEO_EXTENSIONS.contains(extension)) {
            return 3;
        } else {
            return 1;
        }
    }

    /**
     * 获取文件后缀（不带点）
     * @param fileName 文件名
     * @return 文件后缀
     */
    private  String getFileExtension(String fileName) {
        if (fileName == null) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }
    @Override
    public AjaxResult uploadFileByUrl(String content, String fileName){
        try {
            URL url = new URL(content);
            MultipartFile multipartFile = convert(url, fileName);
            log.info("转换完成===========================");
            //通过地址下载文件
//            String fileName = multipartFile.getOriginalFilename();
            fileName = FileExtensionChangeUtil.changeFileExtension(fileName);
            String objectKey = "ticket_file/" + IdWorker.get32UUID() + "/" + fileName;
            S3PutObjectParam s3PutObjectParam = new S3PutObjectParam()
                    .setBucketName(bucketName)
                    .setObjectName(objectKey)
                    .setContentLength(multipartFile.getSize());
            s3PutObjectParam.setAccessKeyId(accessKey);
            s3PutObjectParam.setSecretAccessKey(secretKey);
            s3PutObjectParam.setRegion(region);
            // 放入s3
            String param = JSON.toJSONString(s3PutObjectParam);
            R result = s3Client.putBucketObjects(multipartFile, param);
            if (result.getCode() == AjaxResult.SUCCESS) {
                String preSignedUrl = domainPrefix+objectKey;
                Map<String, String> map = new HashMap<>();
                map.put("fileName", fileName);
                map.put("fileUrl", objectKey);
                map.put("bucketName", bucketName);
                map.put("preSignedUrl", preSignedUrl);
                map.put("uploadTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                // 先把附件信息放到Redis中 待保存的时候再从Redis中把附件信息删除掉
                // 再启动定时任务 定时删除那些已上传S3但是没有保存到数据库的附件
//                RedisCacheUtil.setCacheObject(Constants.FILE_TEMPORARY_STAGE_PREFIX.concat(objectKey), map, 2, TimeUnit.DAYS);

                return AjaxResult.ok(map);
            }
        } catch (Exception e) {
            log.error("上传文件出现问题", e);
        }

        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }
    public  MultipartFile convert(URL url,String fileName) throws IOException {
        // 连接 URL
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.connect();

        // 检查响应代码
        if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new IOException("Failed to download file: " + connection.getResponseMessage());
        }

        // 读取文件内容到字节数组
        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024*1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 将ByteArrayOutputStream转换为字节数组
            byte[] fileContent = outputStream.toByteArray();

            // 创建MockMultipartFile对象
            MultipartFile multipartFile = new MockMultipartFile(
                    "file",
                    fileName,
                    "text/plain",
                    fileContent
            );
            // 创建 MultipartFile
            return multipartFile;
        }
    }

    @Override
    public AjaxResult downloadFile(ContentFile file) {
        log.info("打印一下file值:{}",file);
        // 获取预签名URL
        PresignedUrlParam urlParam = new PresignedUrlParam()
                .setBucketName(file.getBucketName())
                .setKeyName(file.getFileUrl());
        urlParam.setAccessKeyId(accessKey);
        urlParam.setSecretAccessKey(secretKey);
        urlParam.setRegion(region);
        log.info("打印调用s3参数:{}",urlParam);
        R presignedUrl = s3Client.getObjectPresignedUrl(urlParam);

        return AjaxResult.ok(presignedUrl.getData());
    }
    /**
     * 导出excel
     * @param firstRow  表头字段信息
     * @param rowList   导出具体行数据
     * @param exportName   excel名
     * @throws IOException
     */
    private void exportWithResponse(List<String> firstRow, List<List<String>> rowList, String exportName) throws IOException {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(firstRow)) {
            return;
        }
        String[] firstRowArray = firstRow.toArray(new String[firstRow.size()]);
        Workbook wb = writeToExcelByList(firstRowArray, rowList);

        String fileName = URLEncoder.encode(exportName, "UTF-8");
        ServletUtil.getResponse().setContentType("application/vnd.ms-excel;charset=utf-8");
        ServletUtil.getResponse().setHeader("Content-Disposition","attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream outputStream= ServletUtil.getResponse().getOutputStream();
        wb.write(outputStream);
        outputStream.close();
    }

    /**
     * 将工单connect,详情回复,附件进行复制 存储
     *
     * @param workRecord      旧工单
     * @param agentWorkRecord 新工单
     */
    private void copyWorkConnect(TicketInfoIndex workRecord, TicketInfoIndex agentWorkRecord) {
        String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("work_record_id", workRecord.getWorkRecordId()))).size(10000);
        SearchRequest searchRequest = new SearchRequest(index).source(searchSourceBuilder);
        SearchResponse searchResponse ;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            if (hits.length > 0) {
                BulkRequest bulkRequest = new BulkRequest(index);
                for (SearchHit hit : hits) {
                    TicketContentIndex ticketContentIndex = JSONObject.parseObject(hit.getSourceAsString(), TicketContentIndex.class);
                    ticketContentIndex.setWork_record_content_id(UuidUtils.generateUuid());
                    ticketContentIndex.setWork_record_id(agentWorkRecord.getWorkRecordId());
                    IndexRequest indexRequest = new IndexRequest().source(JSON.toJSONString(ticketContentIndex), XContentType.JSON);
                    bulkRequest.add(indexRequest);
                }
                // 批量插入
                restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            }
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("转派失败-存储es错误，异常信息：", e);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 复制关联表 - ES 版本
     * @param workRecord 旧工单
     * @param agentWorkRecord 新工单
     */
    public void copyWorkRelation(TicketInfoIndex workRecord, TicketInfoIndex agentWorkRecord) {
        String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
            // 构建查询条件
            BoolQueryBuilder query = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("data_status", 1))
                    .must(
                            QueryBuilders.boolQuery()
                                    .should(QueryBuilders.termQuery("work_record_id", workRecord.getWorkRecordId()))
                                    .should(QueryBuilders.termQuery("relation_work_record_id", workRecord.getWorkRecordId()))
                    );

            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(query);
            searchRequest.source(searchSourceBuilder);

            // 执行搜索查询
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
             return;
        }

        // 解析查询结果
            List<CrmAgentWorkRecordRelation> relationList = new ArrayList<>();
            for (SearchHit hit : searchResponse.getHits()) {
                // 假设 fromMap 可以将 Map 数据转换为对象
                CrmAgentWorkRecordRelation relation = JSONObject.parseObject(hit.getSourceAsString(), CrmAgentWorkRecordRelation.class);
                relationList.add(relation);
            }

            // 创建新的关联关系列表
            List<CrmAgentWorkRecordRelation> newRelationList = relationList.stream().map(item -> {
                CrmAgentWorkRecordRelation recordRelation = new CrmAgentWorkRecordRelation();
                BeanUtils.copyProperties(item, recordRelation);

                // 判断是关联的还是被关联的,进行对应替换
                if (recordRelation.getWorkRecordId().equals(workRecord.getWorkRecordId())) {
                    recordRelation.setWorkRecordId(agentWorkRecord.getWorkRecordId());
                }

                if (recordRelation.getRelationWorkRecordId().equals(workRecord.getWorkRecordId())) {
                    recordRelation.setRelationWorkRecordId(agentWorkRecord.getWorkRecordId());
                }
                recordRelation.setRelationId(UuidUtils.generateUuid());
                recordRelation.setModifier(getUserId());
                recordRelation.setModifyTime(new Date());

                return recordRelation;
            }).collect(Collectors.toList());

            // 批量保存新的关联关系
            if (!newRelationList.isEmpty()) {
                BulkRequest bulkRequest = new BulkRequest();
                for (CrmAgentWorkRecordRelation recordRelation : newRelationList) {
                    IndexRequest indexRequest = new IndexRequest(index)
                            .id(recordRelation.getRelationId()) // 设置新的UUID作为文档ID
                            .source(objectMapper.convertValue(recordRelation, Map.class)); // 假设 toMap() 方法会把对象转换成 Map
                    bulkRequest.add(indexRequest);
                }

                try {
                    restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    if (!e.getMessage().contains("200 OK")&&!e.getMessage().contains("201")) {
                        log.error("es修改文档失败，异常信息：", e);
                        throw new RuntimeException(e);
                    }
                }
            }
    }

    /**
     *  复制自定义属性实例化
     * @param workRecord 旧工单
     * @param agentWorkRecord 新工单
     */
    private void copyWorkExtInts(CrmAgentWorkRecord workRecord, CrmAgentWorkRecord agentWorkRecord){
        List<CrmAgentWorkRecordExtInts> extIntsList = crmAgentWorkRecordExtIntsService.list(new QueryWrapper<CrmAgentWorkRecordExtInts>().lambda()
                .eq(CrmAgentWorkRecordExtInts::getWorkRecordId, workRecord.getWorkRecordId())
                .eq(CrmAgentWorkRecordExtInts::getDataStatus, 1));
        List<CrmAgentWorkRecordExtInts> newExtIntsList = new ArrayList<>();
        extIntsList.forEach(item -> {
            CrmAgentWorkRecordExtInts crmAgentWorkRecordExtInts = new CrmAgentWorkRecordExtInts();
            BeanUtils.copyProperties(item, crmAgentWorkRecordExtInts);
            crmAgentWorkRecordExtInts.setWorkRecordExtIntsId(UuidUtils.generateUuid());
            crmAgentWorkRecordExtInts.setWorkRecordId(agentWorkRecord.getWorkRecordId());
            crmAgentWorkRecordExtInts.setModifier(getUserId());
            crmAgentWorkRecordExtInts.setModifyTime(new Date());
            newExtIntsList.add(crmAgentWorkRecordExtInts);
        });
        crmAgentWorkRecordExtIntsService.saveBatch(newExtIntsList);
    }

    /**
     *  计算开始时间于结束时间相差时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 相差时间
     */
    private String computingTime(LocalDateTime startTime, LocalDateTime endTime){

        LocalDateTime tempDateTime = LocalDateTime.from( startTime );

        long days = tempDateTime.until( endTime, ChronoUnit.DAYS);
        tempDateTime = tempDateTime.plusDays( days );


        long hours = tempDateTime.until( endTime, ChronoUnit.HOURS);
        tempDateTime = tempDateTime.plusHours( hours );

        long minutes = tempDateTime.until( endTime, ChronoUnit.MINUTES);
        tempDateTime = tempDateTime.plusMinutes( minutes );

        long seconds = tempDateTime.until( endTime, ChronoUnit.SECONDS);
        String dateTime = " "+days+" " + TransUtil.trans("天") +" "+
                hours+" " + TransUtil.trans("小时")+" ";
        if(days == 0){
            dateTime =" "+ hours +" "+ TransUtil.trans("小时")+" ";
        }

        return  dateTime.replace("-","");
    }

    @Override
    public AjaxResult uploadPictureToPublic(MultipartFile file) {
        // 两个动作： 先上传到S3上  再拼接URL返回
        String fileName = file.getOriginalFilename();
        fileName = FileExtensionChangeUtil.changeFileExtension(fileName);
        String objectKey = "ticket_picture/" + IdWorker.get32UUID() + "/" + fileName;

        S3PutObjectParam s3PutObjectParam = null;
        try {
            s3PutObjectParam = new S3PutObjectParam()
                    .setBucketName(publicBucketName)
                    .setObjectName(objectKey)
                    .setContentLength(file.getSize());
            s3PutObjectParam.setAccessKeyId(accessKey);
            s3PutObjectParam.setSecretAccessKey(secretKey);
            s3PutObjectParam.setRegion(region);
        } catch (Exception e) {
            log.error("上传文件出现问题", e);
        }
        // 放入s3
        String param = JSON.toJSONString(s3PutObjectParam);
        R result = s3Client.putBucketObjects(file, param);

        if (result.getCode() == AjaxResult.SUCCESS) {
            String prefix = domainPrefix;
            Map<String, String> map = new HashMap<>();
            map.put("fileName", fileName);
            map.put("fileUrl", objectKey);
            map.put("bucketName", bucketName);
            map.put("url", prefix.concat(objectKey));
            map.put("uploadTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 先把图片信息放到Redis中 待保存的时候再从Redis中把图片信息删除掉
            // 再启动定时任务 定时删除那些已上传S3但是没有保存到数据库的图片
//            RedisCacheUtil.setCacheObject(Constants.FILE_TEMPORARY_STAGE_PREFIX.concat(objectKey), map, 2, TimeUnit.DAYS);

            return AjaxResult.ok(map);
        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult deleteFile(ContentFile file) {
        // 如果前端传过来的fileId不为空  说明在数据库中存在  则删除
//        if (StringUtil.isNotEmpty(file.getFileId())) {
//            crmAgentWorkRecordFileService.removeById(file.getFileId());
//        }
        // 在s3桶中删除
        S3DeleteObjectParam deleteObjectParam = new S3DeleteObjectParam()
                .setBucketName(file.getBucketName())
                .setObjectName(file.getFileUrl());
        deleteObjectParam.setAccessKeyId(accessKey);
        deleteObjectParam.setSecretAccessKey(secretKey);
        deleteObjectParam.setRegion(region);
        s3Client.deleteBucketObjects(deleteObjectParam);

        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }
    /**
     *  根据前端所传条件,进行查询条件拼接
     * @param workOrderRecordRequest 请求参数
     * @return 查询条件的QueryWrapper
     */
    @Override
    public BoolQueryBuilder queryConditionMontage(WorkOrderRecordRequestVO workOrderRecordRequest){
        //构建请求
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 1. 查询工单自定义属性
        List<WorkOrderExtRequestVo> workOrderExtVos = workOrderRecordRequest.getWorkOrderExtVos();
        QueryWrapper<CrmAgentWorkRecord> queryWrapper = new QueryWrapper<>();
        if (workOrderExtVos != null && workOrderExtVos.size() > 0) {
            // 去除字符串前后空格及换行
            workOrderExtVos.forEach(work ->{
                if (InputPropTypeEnum.Single_Input.getCode().equals(work.getPropType()) || InputPropTypeEnum.Multiple_Input.getCode().equals(work.getPropType())) {
                   if(StringUtil.isNotBlank((String)work.getValues())){
                       String values = (String) work.getValues();
                       work.setValues(values.replaceAll("(^\\s+)|(\\s+$)|[\\r\\n]", ""));
                   }

                }

            });
            // 判断是否有扩展字段，有扩展字段查询时，才去执行
            boolean containsDefaultZero = workOrderExtVos.stream()
                    .anyMatch(vo -> vo.getIsSystemDefault() != null && vo.getIsSystemDefault() == 0);
            if(containsDefaultZero){
                ticketExtQuery(workOrderExtVos, boolQueryBuilder);
            }

            workOrderExtVos.stream().filter(s -> s.getIsSystemDefault().equals(1)).forEach(workOrderExtVo -> {
//                    queryWrapper.eq(workOrderExtVo.getCode(), workOrderExtVo.getValues());
                queryWrapperCustom(workOrderExtVo,queryWrapper,boolQueryBuilder);
            });
        }

        // 工单状态不为空时进行查询
        if(StringUtil.isNotNull(workOrderRecordRequest.getStatus())){
            // 如果状态为处理中,则查询 1-待座席处理，2-待客户回复
            if(workOrderRecordRequest.getStatus() == 1){
                BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                        .should(QueryBuilders.termQuery("status", 1))
                        .should(QueryBuilders.termQuery("status", 2))
                        .minimumShouldMatch(1);
                // 将 statusQuery 添加到主查询中
                boolQueryBuilder.must(statusQuery);
            } else {
                boolQueryBuilder.must(QueryBuilders.termQuery("status", workOrderRecordRequest.getStatus()));
            }
        }
        // 处理菜单必须传入的参数
        queryTyperWrapper(workOrderRecordRequest, queryWrapper, boolQueryBuilder);
        boolQueryBuilder.must(QueryBuilders.termQuery("company_id", SecurityUtil.getLoginUser().getCompanyId()));
        boolQueryBuilder.must(QueryBuilders.termQuery("data_status", 1));
        return boolQueryBuilder;
    }

    private void queryWrapperCustom(WorkOrderExtRequestVo workOrderExtVo, QueryWrapper<CrmAgentWorkRecord> queryWrapper, BoolQueryBuilder boolQueryBuilder) {
        // 不同的输入类型 决定使用什么类型的sql查询
        if (InputPropTypeEnum.Single_Select.getCode().equals(workOrderExtVo.getPropType()) || InputPropTypeEnum.Single_Button.getCode().equals(workOrderExtVo.getPropType()) || InputPropTypeEnum.Date_Select.getCode().equals(workOrderExtVo.getPropType())) {
            String value = (String) workOrderExtVo.getValues();
            // 如果是时间字段 需要转换时区
            if (InputPropTypeEnum.Date_Select.getCode().equals(workOrderExtVo.getPropType()) && StringUtil.isNotBlank(value)) {
                if ("birthday".equals(workOrderExtVo.getCode())) {
                    value = value.concat(" 00:00:00");
                }
                value = TimeZoneUtils.requestTimeConversion(value);
            }
            // 如果是渠道类型的话,则需要转一下中文
            if(!"zh-CH".equals(ServletUtils.getHeaderLanguage()) && "channelTypeName".equals(workOrderExtVo.getCode())){
                // 根据内容以及语言查询ID,去到中文内容
            }
            if ("priorityLevelName".equals(workOrderExtVo.getCode())){
                boolQueryBuilder.must(QueryBuilders.termQuery("priority_level_id", value));
            } else if("channelTypeName".equals(workOrderExtVo.getCode())){
                boolQueryBuilder.must(QueryBuilders.termQuery("channel_type_id", value));
            }else if("serviceObjectives".equals(workOrderExtVo.getCode())){
                    // 判断SLA 是否有选值
                    slaQueryWrapper(queryWrapper, value, boolQueryBuilder);
            }else {
                if("workRecordTypeName".equals(workOrderExtVo.getCode())){
                    //将名称转换为工单类型去查询
                    boolQueryBuilder.must(QueryBuilders.termQuery("work_record_type_code", value));
                }else {
                    boolQueryBuilder.must(QueryBuilders.termQuery(StringUtil.toUnderScoreCase(workOrderExtVo.getCode()), value));
                }
            }

        } else if (InputPropTypeEnum.Single_Input.getCode().equals(workOrderExtVo.getPropType())) {
            String values = (String)workOrderExtVo.getValues();
            // 判断一下 客户联系方式是否只包含数字、加号、减号和空格
            if ("customerTelephone".equals(workOrderExtVo.getCode())){
                if (StringUtil.isNotBlank(values) && values.matches("^[0-9 +-]+$")) {
                    values = values.replaceAll("\\+", "").replaceAll("-", "").replaceAll(" ", "");
                }
            }
            // 文本输入框 包括客户标签查询
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("".concat(StringUtil.toUnderScoreCase(workOrderExtVo.getCode())), "*"+values+"*"));
        } else if (InputPropTypeEnum.Multiple_Button.getCode().equals(workOrderExtVo.getPropType())) {
                // 文本输入框 包括客户标签查询
                String values = (String) workOrderExtVo.getValues();
                String[] split = values.split(",");
                if (split.length > 0) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("".concat(StringUtil.toUnderScoreCase(workOrderExtVo.getCode())), split));
                }
        } else if (InputPropTypeEnum.Date_Range_Select.getCode().equals(workOrderExtVo.getPropType())) {
            // 文本输入框 包括客户标签查询
            String values = (String) workOrderExtVo.getValues();
            String[] split = values.split(",");
            if(split.length > 0){
                boolQueryBuilder.must(QueryBuilders.rangeQuery("".concat(StringUtil.toUnderScoreCase(workOrderExtVo.getCode()))).gte(TimeZoneUtils.requestTimeConversion(split[0])).lte(TimeZoneUtils.requestTimeConversion(split[1])));
            }
        }else if(workOrderExtVo.getCode() .equals("workRecordTag") ){
            BoolQueryBuilder builderQuery = QueryBuilders.boolQuery();
            //进行工单标签筛选
            String[] split = workOrderExtVo.getValues().toString().split(",");
            // 构建 Bool 查询
            // 针对每个值，构建一个 wildcardQuery 并添加到 should 中
            BoolQueryBuilder valueQuery = QueryBuilders.boolQuery();
            for (String value : split) {
                valueQuery.should(QueryBuilders.wildcardQuery("ticket_tag.tag_id", "*" + value + "*"));
            }
            valueQuery.minimumShouldMatch(1); // 至少匹配一个条件

            builderQuery.must(valueQuery);

            // 构建 Nested 查询
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("ticket_tag", builderQuery, ScoreMode.None);

            // 顶层 Bool 查询
            boolQueryBuilder.must(nestedQuery);

        }
        else {
            boolQueryBuilder.must(QueryBuilders.termQuery("".concat(StringUtil.toUnderScoreCase(workOrderExtVo.getCode())), workOrderExtVo.getValues()));
        }
    }

    /**
     * 拼接sla 查询条件
     * @param queryWrapper
     * @param value
     */
    private void slaQueryWrapper(QueryWrapper<CrmAgentWorkRecord> queryWrapper, String value, BoolQueryBuilder boolQueryBuilder){
        // 超时24小时内未完成
        if(String.valueOf(TicketSlaTypeEnum.TIMEOUT_DAY_WITHIN_UNRESOLVED.getCode()).equals(value)){
            // 计算时间范围
            String startDate = LocalDateTime.now().minusHours(24).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String endDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").gte(startDate).lt(endDate));

            BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("status", 1))
                    .should(QueryBuilders.termQuery("status", 2))
                    .minimumShouldMatch(1);
            // 将 statusQuery 添加到主查询中
            boolQueryBuilder.must(statusQuery);
        }
        // 超时一天未完成
        if(String.valueOf(TicketSlaTypeEnum.TIMEOUT_DAY_UNRESOLVED.getCode()).equals(value)){
            // 获取当前时间减去 24 小时的开始时间
            String startDate = LocalDateTime.now().minusHours(48).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 获取当前时间的结束时间
            String endDate = LocalDateTime.now().minusHours(24).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").gte(startDate).lt(endDate));

            BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("status", 1))
                    .should(QueryBuilders.termQuery("status", 2))
                    .minimumShouldMatch(1);
            // 将 statusQuery 添加到主查询中
            boolQueryBuilder.must(statusQuery);

        }
        // 超时两天未完成
        if(String.valueOf(TicketSlaTypeEnum.TIMEOUT_TWO_DAY_UNRESOLVED.getCode()).equals(value)){
            // 获取当前时间减去 24 小时的开始时间
            String startDate = LocalDateTime.now().minusHours(72).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 获取当前时间的结束时间
            String endDate = LocalDateTime.now().minusHours(48).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").gte(startDate).lt(endDate));

            BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("status", 1))
                    .should(QueryBuilders.termQuery("status", 2))
                    .minimumShouldMatch(1);
            // 将 statusQuery 添加到主查询中
            boolQueryBuilder.must(statusQuery);
        }
        // 超时三天未完成
        if(String.valueOf(TicketSlaTypeEnum.TIMEOUT_THREE_DAY_UNRESOLVED.getCode()).equals(value)){
            // 获取当前时间减去 24 小时的开始时间
            String startDate = LocalDateTime.now().minusHours(168).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 获取当前时间的结束时间
            String endDate = LocalDateTime.now().minusHours(72).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").gte(startDate).lt(endDate));

            BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("status", 1))
                    .should(QueryBuilders.termQuery("status", 2))
                    .minimumShouldMatch(1);
            // 将 statusQuery 添加到主查询中
            boolQueryBuilder.must(statusQuery);
        }
        // 超时一周未完成
        if(String.valueOf(TicketSlaTypeEnum.TIMEOUT_WEEK_UNRESOLVED.getCode()).equals(value)){
            // 获取当前时间减去 168 小时的开始时间
            String endDate = LocalDateTime.now().minusHours(168).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").lt(endDate));

            BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("status", 1))
                    .should(QueryBuilders.termQuery("status", 2))
                    .minimumShouldMatch(1);
            // 将 statusQuery 添加到主查询中
            boolQueryBuilder.must(statusQuery);
        }
        // 未超时未完成
        if(String.valueOf(TicketSlaTypeEnum.NO_TIMEOUT_UNRESOLVED.getCode()).equals(value)){
            String endDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").gte(endDate));

            BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("status", 1))
                    .should(QueryBuilders.termQuery("status", 2))
                    .minimumShouldMatch(1);
            // 将 statusQuery 添加到主查询中
            boolQueryBuilder.must(statusQuery);
        }
        // 超时解决
        if(String.valueOf(TicketSlaTypeEnum.TIMEOUT_SOLVE.getCode()).equals(value)){
//            String endDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//            boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").lt(endDate));

            Script script = new Script(
                    ScriptType.INLINE,
                    "painless",
                    "def srt = doc['should_resolve_time'].size() > 0 ? doc['should_resolve_time'].value : null;" +
                            "def rt = doc['resolve_time'].size() > 0 ? doc['resolve_time'].value : null;" +
                            "return srt != null && rt != null && srt.compareTo(rt)  < 0",
                    Collections.emptyMap()
            );

            ScriptQueryBuilder scriptQuery = QueryBuilders.scriptQuery(script);
            boolQueryBuilder.must(scriptQuery);

            boolQueryBuilder.must(QueryBuilders.termQuery("status", 3));

        }
        // 按时解决
        if(String.valueOf(TicketSlaTypeEnum.PUNCTUAL_SOLVE.getCode()).equals(value)){
//            String endDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//            boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").gt(endDate));

            Script script = new Script(
                    ScriptType.INLINE,
                    "painless",
                    "def srt = doc['should_resolve_time'].size() > 0 ? doc['should_resolve_time'].value : null;" +
                            "def rt = doc['resolve_time'].size() > 0 ? doc['resolve_time'].value : null;" +
                            "return srt != null && rt != null && srt.compareTo(rt) > 0",
                    Collections.emptyMap()
            );

            ScriptQueryBuilder scriptQuery = QueryBuilders.scriptQuery(script);
            boolQueryBuilder.must(scriptQuery);

            boolQueryBuilder.must(QueryBuilders.termQuery("status", 3));
        }
        // 已终止
        if(String.valueOf(TicketSlaTypeEnum.TERMINATED.getCode()).equals(value)){
            boolQueryBuilder.must(QueryBuilders.termQuery("status", 4));
        }
        // 已转单
        if(String.valueOf(TicketSlaTypeEnum.TRANSFER_ORDER.getCode()).equals(value)){
            boolQueryBuilder.must(QueryBuilders.termQuery("status", 5));
        }
    }

    /**
     * 菜单权限条件
     * @param workOrderRecordRequest
     * @param queryWrapper
     */
    private void queryTyperWrapper(WorkOrderRecordRequestVO workOrderRecordRequest, QueryWrapper<CrmAgentWorkRecord> queryWrapper, BoolQueryBuilder boolQueryBuilder) {
        // 通过查询类型进行参数拼接
        // 判断是否为座席,如果是座席,则只查询当前座席的
        long count = SecurityUtil.getLoginUser().getRoleList().stream().filter(vo -> UserRoleEnum.ATTENDANT_STAFF.getId().equals(vo.getRoleId())).count();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (workOrderRecordRequest.getQueryType() != null) {
            // 超时未解决工单
            if (workOrderRecordRequest.getQueryType() == 2) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("status", Arrays.asList(1, 2)));
                boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").lt(sdf.format(new Date())));
                if (count>0){
                    boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", SecurityUtil.getLoginUser().getUserId()));
                }
                // 判断是否为座席管理员,如果为座席管理员,则只查询当前部门的
                long leaderCount = SecurityUtil.getLoginUser().getRoleList().stream().filter(vo -> UserRoleEnum.ATTENDANT_LEADER.getId().equals(vo.getRoleId())).count();
                if(leaderCount>0){
                    boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", SecurityUtil.getLoginUser().getDeptId()));
                }
                // 超时未分配工单
            } else if (workOrderRecordRequest.getQueryType() == 3) {
                boolQueryBuilder.must(QueryBuilders.termQuery("status", 0));
                boolQueryBuilder.must(QueryBuilders.rangeQuery("should_resolve_time").lt(sdf.format(new Date())));
                // 判断是否为座席管理员,如果为座席管理员,则只查询当前部门的
                long leaderCount = SecurityUtil.getLoginUser().getRoleList().stream().filter(vo -> UserRoleEnum.ATTENDANT_LEADER.getId().equals(vo.getRoleId())).count();
                if(leaderCount>0){
                    boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", SecurityUtil.getLoginUser().getDeptId()));
                }
                // 处理中工单
            } else if (workOrderRecordRequest.getQueryType() == 4) {
//                queryWrapper.in("status", 1,2);
                // 判断是否为座席,如果是座席,则只查询当前座席的
//                long count = SecurityUtil.getLoginUser().getRoleList().stream().filter(vo -> UserRoleEnum.ATTENDANT_STAFF.getId().equals(vo.getRoleId())).count();
//                if (count>0){
                boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", SecurityUtil.getLoginUser().getUserId()));
//                }
                // 我关注的工单
            } else if (workOrderRecordRequest.getQueryType() == 5) {
                BoolQueryBuilder builderQuery = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("ticket_follow.user_id", SecurityUtil.getLoginUser().getUserId()));
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("ticket_follow", builderQuery, ScoreMode.None);
                boolQueryBuilder.must(nestedQuery);
                // 我创建的工单
            } else if (workOrderRecordRequest.getQueryType() == 6) {
                boolQueryBuilder.must(QueryBuilders.termQuery("creator", SecurityUtil.getUserId()));
                // 我部门的工单
            } else if (workOrderRecordRequest.getQueryType() == 7) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", SecurityUtil.getLoginUser().getDeptId()));
            }else if (workOrderRecordRequest.getQueryType() == 8) {
                queryWrapper.eq("agent_id", "1001");
                boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", "1001"));
            }
            if(workOrderRecordRequest.getQueryType() != 8){
//                queryWrapper.ne("agent_id", "1001");
                // "agent_id" 不等于 "1001" 或者 "agent_id" 为 null
                // 只有不是坐席登录，并且类型不是
                if(count==0&&workOrderRecordRequest.getQueryType() != 4){
                    boolQueryBuilder.must(QueryBuilders.boolQuery()
                            .mustNot(QueryBuilders.termQuery("agent_id", "1001") // 至少匹配一个 should 查询
                    ));
                }
            }
        }
    }

    /**
     * 将查询到的工单,进行数据处理
     * @param list 工单数据
     * @param queryType 查询类型 1 界面查询  2 导出查询
     * @return 拼接好的工单数据
     */
    @Override
    public List<WorkOrderRecordResponseVO> ticketDataHandle(List<WorkOrderRecordResponseVO> list, Integer queryType){
        // 计算工单时间 根据状态来判断
        for (WorkOrderRecordResponseVO work : list) {
//            addWorkOrderExtInfo(work);
            LocalDateTime currentTime = LocalDateTime.now();
//            ZoneId zoneId = ZoneId.systemDefault();
//            LocalDateTime terminationDateTime = LocalDateTime.ofInstant(work.getShouldResolveTime().toInstant(), zoneId);
            LocalDateTime terminationDateTime = work.getShouldResolveTime();
            LocalDateTime resolveTime = work.getResolveTime();
            // 如果是未解决状态,判断时间
            if(work.getStatus() == 0 || work.getStatus() == 1 || work.getStatus() == 2){
                // 终止时间小于当前时间>>>超时 或 终止时间等于当前时间>>>超时
                if(terminationDateTime.isBefore(currentTime)||terminationDateTime.isEqual(currentTime)){
                    String computingTime = computingTime(currentTime, terminationDateTime);
                    if(queryType == 1){
                        work.setServiceObjectives("<span>"+TransUtil.trans("超出")+"<span class=\"serviceObjectivesText1\">"+computingTime+"</span>"+TransUtil.trans("未解决")+"</span>");
                    }else{
                        work.setServiceObjectives(TransUtil.trans("超出")+computingTime+TransUtil.trans("未解决"));
                    }
                    work.setServiceStatus(1);
                }

                // 终止时间大于当前时间>>>未超时
                if(terminationDateTime.isAfter(currentTime)){
                    String computingTime = computingTime(terminationDateTime, currentTime);
                    if(queryType == 1) {
                        work.setServiceObjectives("<span>"+TransUtil.trans("需要")+"<span class=\"serviceObjectivesText2\">" + computingTime + "</span>"+TransUtil.trans("内解决")+"</span>");
                    }else{
                        work.setServiceObjectives(TransUtil.trans("需要") + computingTime + TransUtil.trans("内解决"));
                    }
                    work.setServiceStatus(2);
                }
            }
            if(work.getStatus() == 3){
                // 判断是否超时
                if(terminationDateTime.isBefore(resolveTime)||resolveTime.isEqual(terminationDateTime)){
                    if(queryType == 1) {
                        work.setServiceObjectives("<span class=\"serviceObjectivesText4\">"+TransUtil.trans("超时解决")+"</span>");
                    }else{
                        work.setServiceObjectives(TransUtil.trans("超时解决"));
                    }
                    work.setServiceStatus(3);
                }else{
                    if(queryType == 1) {
                        work.setServiceObjectives("<span class=\"serviceObjectivesText2\">"+TransUtil.trans("按时解决")+"</span>");
                    }else{
                        work.setServiceObjectives(TransUtil.trans("按时解决"));
                    }
                    work.setServiceStatus(4);
                }
            }
            if(work.getStatus() == 4){
                if(queryType == 1) {
                    work.setServiceObjectives("<span>--</span>");
                }else {
                    work.setServiceObjectives("--");
                }
                work.setServiceStatus(5);
            }

            if(work.getStatus() == 5){
                if(queryType == 1) {
                    work.setServiceObjectives("<span class=\"serviceObjectivesText2\">"+TransUtil.trans("已转单")+"</span>");
                }else{
                    work.setServiceObjectives(TransUtil.trans("已转单"));
                }
                work.setServiceStatus(6);
            }

            if(StringUtil.isNotEmpty(work.getAgentId())&&work.getAgentId().equals("1001")){
                if(queryType == 1) {
                    work.setServiceObjectives("<span class=\"serviceObjectivesText2\">"+MessageUtils.get("work.automatic.solve")+"</span>");
                }else{
                    work.setServiceObjectives(MessageUtils.get("work.automatic.solve"));
                }
            }
            // 根据名称查询出 对应的国际化
            work.setChannelTypeName(TransUtil.trans(work.getChannelTypeName()));
        }
        return list;
    }

    /**
     *  添加操作记录
     * @param workRecordId 工单id
     * @param operationLogReason 操作原因
     * @param operationLogDescribe 操作记录描述
     */
    private void addOperationLog(String workRecordId, String operationLogReason, String operationLogDescribe, Integer operationLogType){
        SysUserVo loginUser = new SysUserVo();
        try{
            loginUser = SecurityUtil.getLoginUser();
        }catch (Exception e){
            loginUser.setUserName("CoCo");
            loginUser.setUserId("");
        }

        CrmAgentWorkRecordOperationLog crmAgentWorkRecordOperationLog = new CrmAgentWorkRecordOperationLog();
        crmAgentWorkRecordOperationLog.setOperationLogId(UuidUtils.generateUuid())
                .setWorkRecordId(workRecordId)
                .setOperationLogDescribe(operationLogDescribe)
                .setDataStatus(1)
                .setOperatorName(loginUser.getUserName()).setCreator(loginUser.getUserId()).setCreateTime(new Date()).setOperationLogType(operationLogType);
       // 操作原因不为null则添加
        if(operationLogReason != null){
            crmAgentWorkRecordOperationLog.setOperationLogReason(operationLogReason);
        }
        crmAgentWorkRecordOperationLogService.save(crmAgentWorkRecordOperationLog);
    }

    /**
     *  添加工单操作日志
     * @param workRecordId 工单id
     * @param operationLogReason 操作原因
     * @param operationLogType 操作类型
     * @param loginUser 登录人
     */
    private void addTicketOperationLog(String workRecordId, String operationLogReason, Integer operationLogType, SysUserVo loginUser){
        try {
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            // 注册 JavaTimeModule
            objectMapper.registerModule(new JavaTimeModule());
            TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
            String esId = null;
            SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex,workRecordId,loginUser.getCompanyId());
            if(ticketHit!=null){
                try {
                    // 获取到_id
                    esId = ticketHit.getId();
                    Map<String, Object> source = ticketHit.getSourceAsMap();
                    String json = objectMapper.writeValueAsString(source);
                    // 将 JSON 字符串转换为 Java 对象
                    ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                } catch (JsonProcessingException e) {
                    log.error("查询es报错",e);
                }
            }

            // 取出操作日志
            List<TicketOperationLog> ticketOperationLogList = new ArrayList<>();
            // 之前的操作日志不为null，取出之前的操作日志。
            if(CollectionUtils.isNotEmpty(ticketInfoIndex.getTicketOperationLog())){
                ticketOperationLogList = ticketInfoIndex.getTicketOperationLog();
            }
            TicketOperationLog ticketOperationLog = new TicketOperationLog();
            if(StringUtil.isNotEmpty(operationLogReason)){
                ticketOperationLog.setOperationLogReason(operationLogReason);
            }
            ticketOperationLog.setOperatorName(loginUser.getUserName());
            ticketOperationLog.setOperationLogType(operationLogType);
            ticketOperationLog.setOperatorTime(LocalDateTime.now());
            ticketOperationLogList.add(ticketOperationLog);
            ticketInfoIndex.setTicketOperationLog(ticketOperationLogList);
            // 进行修改保存操作日志
            UpdateRequest updateRequest = new UpdateRequest(ticketIndex+loginUser.getCompanyId(), esId);
            String value = objectMapper.writeValueAsString(ticketInfoIndex);
            updateRequest.doc(value, XContentType.JSON);
            try {
                restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK")&&!e.getMessage().contains("201")) {
                    log.error("es修改文档失败，异常信息：", e);
                    throw new RuntimeException(e);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 查询语音转录文本
     * @param contactId
     * @return
     */
    @Override
    public AjaxResult voiceAnalysis(String contactId) {
        log.info("查询转录信息contactId： {} ",contactId);
        if(StringUtils.isEmpty(contactId)){
            return AjaxResult.ok(null, MessageUtils.get("operate.success"));
        }
        // 获取工作记录详情
        // 定义索引名称
        String indexName = workContentIndex+SecurityUtil.getLoginUser().getCompanyId();
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("contact_id", contactId));
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        TicketContentIndex contentIndex = null;
        String esId = null;
        try {
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            log.info("查询工单，工单数量：{}",hits.length);
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSourceAsMap();
                if (hit.getScore() < 0) {
                    continue;
                }
                esId = hit.getId();
                String s = JSON.toJSONString(source);
                contentIndex = JSON.parseObject(s, TicketContentIndex.class);
            }
        }catch (Exception e){
            log.error("查询工单聊天记录报错:",e);
        }

        if(contentIndex == null){
            return AjaxResult.ok(null, MessageUtils.get("operate.success"));
        }
        log.info("工单信息为：{} ",contentIndex);

        // 获取s3 文件 - 获取aws账号信息
        CrmAwsConnect crmAwsConnect = crmAwsConnectMapper.selectById(contentIndex.getConnect_id());
        CrmAwsAccount awsAccount = crmAwsAccountMapper.selectById(crmAwsConnect.getAwsUserId());

        List<TicketFile> ticketFileList = contentIndex.getTicket_file();
        if (ticketFileList.isEmpty()) {
            log.info("未查到附件录音文件");
            Map<String, Object> map = getStringObjectMap(contentIndex);
            return AjaxResult.ok(map);
        }
        TicketFile ticketFile = ticketFileList.get(0);
        if (ticketFile == null) {
            log.info("未查到附件录音文件");
            // 查询一下
            Map<String, Object> map = getStringObjectMap(contentIndex);
            return AjaxResult.ok(map);
        } else {
            String key= "audio/result/" + contentIndex.getWork_record_content_id() + ".json";
            S3GetObjectParam s3GetObjectParam = new S3GetObjectParam();
            s3GetObjectParam.setBucketName(ticketFile.getBucket_name());
            s3GetObjectParam.setObjectName(key);
            s3GetObjectParam.setAccessKeyId(awsAccount.getAccessKey());
            s3GetObjectParam.setSecretAccessKey(awsAccount.getSecretAccessKey());
            s3GetObjectParam.setRegion(crmAwsConnect.getRecordS3BucketRegion());
            R voiceAnalysisRes;
            try {
                voiceAnalysisRes = s3Client.getBucketObject(s3GetObjectParam);
                if (voiceAnalysisRes.getCode() !=200 ){
                    // 查询一下
                    Map<String, Object> map = getStringObjectMap(contentIndex);
                    return AjaxResult.ok(map);
                }
            } catch (Exception e){
                // 查询一下
                Map<String, Object> map = getStringObjectMap(contentIndex);
                return AjaxResult.ok(map);
            }
            log.info("成功获取到contactId:【{}】的转录语音【{}】",contactId,contentIndex.getWork_record_content_id());
            JSONObject result = JSONObject.parseObject((String) voiceAnalysisRes.getData());
//            获取到Json数据中的content内容和participantRoles角色
            List<String> contents = JsonPath.read(result, "$.Transcript[*].Content");
            List<String> participantRoles = JsonPath.read(result, "$.Transcript[*].ParticipantRole");
            List<TranslateVO> translateVOS = new ArrayList<>();
            for (int i = 0; i < contents.size(); i++) {
                TranslateVO translateVO = new TranslateVO();
                translateVO.setId(String.valueOf(UUID.randomUUID()));
                translateVO.setContent(contents.get(i));
                translateVO.setType(participantRoles.get(i));
                translateVOS.add(translateVO);
            }
            Map<String,Object> map = new HashMap<>();
            R<SysCompanyTranscribeConfigVo> transcribeConfig = companyTranscribeClient.getTranscribeConfig(awsAccount.getCompanyId());
            if (transcribeConfig.getCode() == R.SUCCESS) {
                if (transcribeConfig.getData() != null && org.apache.commons.lang3.StringUtils.isNotBlank(transcribeConfig.getData().getTranscribeConfigId())) {
                    map.put("translateType","whisper");
                } else {
                    map.put("translateType","aws");
                }
            } else {
                map.put("translateType","aws");
            }
            map.put("translateVOS",translateVOS);
            List<String> listText = new ArrayList<>();
            listText.add(result.getString("text"));
            map.put("text", listText);
//
//            //判断AIGC辅助规则
//            AigcSettingsVO aigcSettings = aigcSettingsService.getSettings();
//            if (aigcSettings != null) {
//                String contentQuestion = String.format("内容：%s", result.getString("text"));
//
//                if (aigcSettings.getEnableSmartSummary()) {
//                    List<String> extInfoByCode = crmAgentWorkRecordExtDefService.queryExtInfoByCode(aigcSettings.getSummaryWorkTypes());
//                    if (!extInfoByCode.isEmpty()) {
//                        contentQuestion += String.format("，围绕主题：%s", String.join(", ", extInfoByCode));
//                    }
//                    log.info("转录完成生成标题参数 {}",contentQuestion);
//                    AigcGeneratorContent summaryRequestVo = buildAigcGeneratorContent(contentQuestion);
//                    summaryRequestVo.setLanguage(ServletUtils.getHeaderLanguage());
//                    SmartSummaryResult summaryResult = fetchAigcSummary(summaryRequestVo);
//                    if (summaryResult != null) {
//                        contentIndex.setContent(JSONObject.toJSONString(summaryResult));
//                        elasticsearchUtil.updateDocument(indexName, esId, contentIndex);
//                    }
//                }
//
//                if (aigcSettings.getEnableTitleGen()) {
//                    AigcGeneratorContent titleRequestVo = buildAigcGeneratorContent(result.getString("text"));
//                    titleRequestVo.setWorkRecordId(contentIndex.getWork_record_id());
//                    log.info("转录完成生成标题参数 {}",titleRequestVo);
//                    createWorkOrderTitle(titleRequestVo);
//                }
//            }
//
            return AjaxResult.ok(map);
        }

    }

    private AigcGeneratorContent buildAigcGeneratorContent(String content) {
        AigcGeneratorContent generatorContent = new AigcGeneratorContent();
        generatorContent.setContent(content);
        generatorContent.setUserId(SecurityUtil.getUserId());
        generatorContent.setCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        return generatorContent;
    }

    private SmartSummaryResult fetchAigcSummary(AigcGeneratorContent generatorContent) {
        try {
            R<SmartSummaryResultVo> summaryResultVo = aigcClient.smartSummaryInner(generatorContent.getCompanyId(),generatorContent.getUserId(),generatorContent);
            if (summaryResultVo != null && summaryResultVo.getCode() == R.SUCCESS && summaryResultVo.getData() != null) {
                SmartSummaryResult summaryResult = new SmartSummaryResult();
//                try {
//
//                    String mood = summaryResultVo.getData().getMood();
//                    if (mood != null) {
//                        // 使用正则表达式提取第一位数字
//                        Pattern pattern = Pattern.compile("\\d");
//                        Matcher matcher = pattern.matcher(mood);
//                        if (matcher.find()) {
//                            summaryResult.setMood(matcher.group(0));
//                        } else {
//                            summaryResult.setMood("2");  // 如果没有数字，设置为默认值 2 中立
//                        }
//                    } else {
//                        summaryResult.setMood("2");
//                    }
//                } catch (NumberFormatException e) {
//                    log.warn("无效的 mood 值: {}", summaryResultVo.getData().getMood(), e);
//                    summaryResult.setMood("2");
//                }

                List<WaitExecuteVO> toDoList = summaryResultVo.getData().getToDoList().stream()
                        .map(todo -> {
                            WaitExecuteVO waitExecuteVO = new WaitExecuteVO();

                            BeanUtils.copyProperties(todo, waitExecuteVO);
                            return waitExecuteVO;
                        })
                        .collect(Collectors.toList());
                BeanUtils.copyProperties(summaryResultVo.getData(), summaryResult);
                summaryResult.setToDoList(toDoList);
                return summaryResult;

            } else {
                log.error("AIGC 接口调用失败，返回数据: {}", summaryResultVo);
            }
        } catch (Exception e) {
            log.error("调用AIGC接口失败", e);
        }
        return null;
    }

    @Override
    public AjaxResult createWorkOrderSmartSummary(AigcGeneratorContent generatorContent) {
        if (StringUtil.isEmpty(generatorContent.getUserId())) {
            generatorContent.setUserId(UUID.randomUUID().toString().replace("-", ""));
        }

        SmartSummaryResult summaryResult = fetchAigcSummary(generatorContent);
        if (summaryResult == null) {
            return AjaxResult.failure("生成智能总结失败");
        }

        TicketContentIndex ticketContentIndex = buildTicketContentIndex(generatorContent, summaryResult);
        addAgentWorkRecordContent(ticketContentIndex, generatorContent.getCompanyId());

        WorkSummarizeVO workSummarizeVO = buildWorkSummarizeVO(generatorContent, summaryResult);
        return AjaxResult.ok(workSummarizeVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> allocationTicket(WorkOrderAssignVO workOrderAssignVO) {
        // 区分是分配团队还是分配坐席，如果如是分配到团队的话，则随机分配坐席
        if (workOrderAssignVO.getRuleType() == 0) {
            // 团队人员
            List<UserDetailsVO> filteredUserList = new ArrayList<>();
            filteredUserList = getOnlineUsersByDeptId(workOrderAssignVO.getTeamId());

            if(filteredUserList.isEmpty()){
                // 查询部门下所有人员
                R<List<UserDetailsVO>> userListResponse = userClient.queryUserListByDeptId(workOrderAssignVO.getTeamId());
                if (userListResponse == null || userListResponse.getCode() != 200 || userListResponse.getData() == null) {
                    return AjaxResult.failure(MessageUtils.get("error.null.record"));
                }
                filteredUserList = userListResponse.getData();
            }
            // 随机分配座席
            if(!filteredUserList.isEmpty()) {
                workOrderAssignVO.setAgentId(filteredUserList.get(ThreadLocalRandom.current().nextInt(filteredUserList.size())).getUserId());
            }
        }
        // 工单是批量分配
        String[] split = workOrderAssignVO.getWorkRecordId().split(",");
        for (String ticketId : split){
            workOrderAssignVO.setWorkRecordId(ticketId);
            assignWorkOrder(workOrderAssignVO);
        }
        return AjaxResult.ok();
    }

    @Override
    public IPage<WorkOrderRecordFinalEsResult> queryRobotWorkOrderList(IPage<Object> page, WorkOrderRecordRequest workOrderRecordRequest) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), 0L);
            List<WorkOrderRecordFinalEsResult> results = new ArrayList<>();
            resultPage.setRecords(results);
            return resultPage;
        }
        IPage<WorkOrderRecordFinalEsResult> workOrderRecordResultIPage = queryWorkOrderListFromEs(page, workOrderRecordRequest,2);
        //如果查询出来的分页数据，不为空，将所有的工单id整理出来，用于查询聊天记录，然后根据工单Id，塞回到对应的返回数据中
        List<WorkOrderRecordFinalEsResult> records = workOrderRecordResultIPage.getRecords();
        List<String> workOrderIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(records)) {
            workOrderIdList = records.stream()
                    .map(WorkOrderRecordFinalEsResult::getWorkRecordId)
                    .filter(Objects::nonNull) // 过滤掉 null 值
                    .collect(Collectors.toList());
            Map<String, List<TicketContentIndex>> resultMap = queryEsChatContent(workOrderIdList);
            //将这些返回结果一一对应到查询出来的列表中
            for(WorkOrderRecordFinalEsResult workOrderRecordResult:records){
                String workRecordId = workOrderRecordResult.getWorkRecordId();
                workOrderRecordResult.setTicketContentIndex(resultMap.get(workRecordId));
            }
        }
        return workOrderRecordResultIPage;
    }

    @Override
    public AjaxResult queryRobotTicketTotalCount() throws Exception {
        long totalCount = queryDiffStatusTicketCount(2,null);
        log.info("新工作台聊天类型渠道列表，不需要筛选条件的统计数量-机器人工单数量：{}",totalCount);
        return AjaxResult.ok(totalCount);


    }

    private TicketContentIndex buildTicketContentIndex(AigcGeneratorContent generatorContent, SmartSummaryResult summaryResult) {
        TicketContentIndex ticketContentIndex = new TicketContentIndex();
        ticketContentIndex.setWork_record_content_id(UuidUtils.generateUuid());
        ticketContentIndex.setWork_record_id(generatorContent.getWorkRecordId());
        ticketContentIndex.setContent(JSONObject.toJSONString(summaryResult));
        ticketContentIndex.setContent_type(1);
        ticketContentIndex.setReply_type(4);
        ticketContentIndex.setReply_time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        ticketContentIndex.setReply_millis_time(Convert.toStr(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli()));

        return ticketContentIndex;
    }

    private WorkSummarizeVO buildWorkSummarizeVO(AigcGeneratorContent generatorContent, SmartSummaryResult summaryResult) {
        WorkSummarizeVO workSummarizeVO = new WorkSummarizeVO();
        workSummarizeVO.setWorkRecordId(generatorContent.getWorkRecordId());
        workSummarizeVO.setContentSummary(summaryResult.getSummary());
        workSummarizeVO.setCustomerMood(parseMood(summaryResult.getMood()));

        if (summaryResult.getToDoList() != null) {
            workSummarizeVO.setWaitExecuteList(summaryResult.getToDoList().stream()
                    .map(item -> {
                        CrmAgentWorkRecordWaitExecute execute = new CrmAgentWorkRecordWaitExecute();
                        BeanUtils.copyProperties(item, execute);
                        return execute;
                    }).collect(Collectors.toList()));
        }
        return workSummarizeVO;
    }

    private int parseMood(String mood) {
        if (mood != null) {
            Matcher matcher = Pattern.compile("\\d").matcher(mood);
            if (matcher.find()) {
                try {
                    return Integer.parseInt(matcher.group(0));
                } catch (NumberFormatException e) {
                    log.warn("无效的 mood 值: {}", mood, e);
                }
            }
        }
        return 0;
    }

    @Override
    public AjaxResult createWorkOrderTitle(AigcGeneratorContent generatorContent) {
        log.info("生成标题参数 {}",generatorContent);
        String companyId = generatorContent.getCompanyId();
        String workRecordId = generatorContent.getWorkRecordId();
        String index = ticketIndex + companyId;

        if(StringUtil.isEmpty(generatorContent.getUserId())){
            generatorContent.setUserId(UUID.randomUUID().toString().replace("-", ""));
        }
        String text = null;
        AigcSettingsVO aigcSettings = aigcSettingsService.getAigcSettingsInfo(companyId);

        if (aigcSettings != null && aigcSettings.getEnableTitleGen()) {
            // 查询 Elasticsearch 获取工单数据
            List<QueryCondition> queryConditions = new ArrayList<>();
            queryConditions.add(createCondition("work_record_id", workRecordId));

            Map<String, Object> resultMap = elasticsearchUtil.searchInfoMapQuery(queryConditions, index, TicketInfoIndex.class);

            if (resultMap != null && resultMap.containsKey("data") && resultMap.containsKey("id")) {
                TicketInfoIndex workRecord = (TicketInfoIndex) resultMap.get("data");
                String id = (String) resultMap.get("id");

                // 如果工单记录存在，调用 AIGC 接口生成标题
                if (workRecord != null) {
                    R<Object> texts = aigcClient.titleText(generatorContent.getCompanyId(),generatorContent.getUserId(),generatorContent);
                    if (texts != null && texts.getCode() == R.SUCCESS && texts.getData() != null) {
                        text = texts.getData().toString();
                        workRecord.setWorkRecordTheme(text);

                        elasticsearchUtil.updateDocument(index, id, workRecord);

                    } else {
                        log.error("AIGC 标题生成失败，返回数据为空或错误");
                    }
                } else {
                    log.warn("未找到工单数据，工单ID: {}", workRecordId);
                }
            }
        }
        // 返回操作结果
        return AjaxResult.ok(text);
    }

    @NotNull
    private Map<String, Object> getStringObjectMap(TicketContentIndex contentIndex) {
        List<String> listText = voiceAnalysis2(contentIndex.getWork_record_id());
        Map<String,Object> map = new HashMap<>();
        List<TranslateVO> translateVOS = new ArrayList<>();
        for (int i = 0; i < listText.size(); i++) {
            TranslateVO translateVO = new TranslateVO();
            translateVO.setId(String.valueOf(UUID.randomUUID()));
            translateVO.setContent(listText.get(i));
            translateVO.setType("CUSTOMER");
            translateVOS.add(translateVO);
        }
        map.put("translateVOS",translateVOS);
        map.put("translateType","whisper");
        map.put("text",listText);
        return map;
    }

    //暂定让所有角色都能看到，由于机器人工单只有一种状态，所以查询条件中不用考虑状态条件了
    @Override
    public AjaxResult<List<WorkRecordAutoMergeVo>> queryTicketRecordAutoMerge() {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
//        List<WorkRecordAutoMergeVo> list = crmAgentWorkRecordAutoMergeService.queryAutoMergeSetting(companyId);
        //需求修改，暂时只留着邮件
        List<Integer> targetKeyList = Stream.of(
                CrmChannelEnum.EMAIL.getCode()
//                ,
//                CrmChannelEnum.WHATSAPP.getCode(),
//                CrmChannelEnum.PHONE.getCode(),
//                CrmChannelEnum.WEB_CHAT.getCode(),
//                CrmChannelEnum.APP_CHAT.getCode(),
//                CrmChannelEnum.AMAZON.getCode(),
//                CrmChannelEnum.FACEBOOK.getCode(),
//                CrmChannelEnum.INSTAGRAM.getCode(),
//                CrmChannelEnum.LINE.getCode(),
//                CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getCode(),
//                CrmChannelEnum.WECHAT_OFFICIAL_ACCOUNT.getCode(),
//                CrmChannelEnum.WEB_VIDEO.getCode(),
//                CrmChannelEnum.APP_VIDEO.getCode(),
//                CrmChannelEnum.WEB_ONLINE_VOICE.getCode(),
//                CrmChannelEnum.APP_ONLINE_VOICE.getCode(),
//                CrmChannelEnum.SHOPIFY.getCode()
        ).collect(Collectors.toList());
        LambdaQueryWrapper<CrmAgentWorkRecordAutoMerge> queryWrapper = new QueryWrapper<CrmAgentWorkRecordAutoMerge>().lambda()
                .eq(CrmAgentWorkRecordAutoMerge::getCompanyId, companyId)
                .eq(CrmAgentWorkRecordAutoMerge::getDataStatus, Constants.NORMAL);
        List<CrmAgentWorkRecordAutoMerge> list = crmAgentWorkRecordAutoMergeService.list(queryWrapper);
        //没有数据 邮件渠道默认为开启，其他默认关闭
        List<WorkRecordAutoMergeVo> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            for (Integer eachChannelCode : targetKeyList) {
                if (eachChannelCode == CrmChannelEnum.EMAIL.getCode()) {
                    resultList.add(new WorkRecordAutoMergeVo().setChannelTypeId(eachChannelCode).setChannelTypeName(TransUtil.trans(CrmChannelEnum.getNameByCode(eachChannelCode))).setAutoMergeFlag(YesOrNoEnum.YES.getCode()));
                } else {
                    resultList.add(new WorkRecordAutoMergeVo().setChannelTypeId(eachChannelCode).setChannelTypeName(TransUtil.trans(CrmChannelEnum.getNameByCode(eachChannelCode))).setAutoMergeFlag(YesOrNoEnum.NO.getCode()));
                }
            }
            return AjaxResult.ok(resultList, MessageUtils.get("operate.success"));
        }
        //如果有数据，为了避免数据不全，将其他数据也给补充上
        Map<Integer, Integer> map = list.stream().collect(Collectors.toMap(CrmAgentWorkRecordAutoMerge::getChannelTypeId, CrmAgentWorkRecordAutoMerge::getAutoMergeFlag));
        //处理已有的数据，没有的内容要给补充上
        for (Integer eachChannelCode : targetKeyList) {
            handleMergeSetting(CrmChannelEnum.getByCode(eachChannelCode),map,resultList);
        }
        return AjaxResult.ok(resultList, MessageUtils.get("operate.success"));
    }

    private void handleMergeSetting(CrmChannelEnum channelEnum, Map<Integer, Integer> map, List<WorkRecordAutoMergeVo> resultList) {
        Integer autoMergeFlag = map.get(channelEnum.getCode());
        if (null == autoMergeFlag) {
            if(channelEnum.getCode().equals(CrmChannelEnum.EMAIL.getCode())){
                resultList.add(new WorkRecordAutoMergeVo().setChannelTypeId(CrmChannelEnum.EMAIL.getCode()).setChannelTypeName(TransUtil.trans(CrmChannelEnum.EMAIL.getName())).setAutoMergeFlag(YesOrNoEnum.YES.getCode()));
            }else{
                resultList.add(new WorkRecordAutoMergeVo().setChannelTypeId(channelEnum.getCode()).setChannelTypeName(TransUtil.trans(channelEnum.getName())).setAutoMergeFlag(YesOrNoEnum.NO.getCode()));
            }
        } else {
            resultList.add(new WorkRecordAutoMergeVo().setChannelTypeId(channelEnum.getCode()).setChannelTypeName(TransUtil.trans(channelEnum.getName())).setAutoMergeFlag(autoMergeFlag));
        }
    }

    @Transactional
    @Override
    public AjaxResult updateTicketRecordAutoMerge(List<CrmAgentWorkRecordAutoMerge> workRecordAutoMergeList) {
        if (CollectionUtils.isEmpty(workRecordAutoMergeList)) {
            return AjaxResult.failure().setMsg(MessageUtils.get("data.can.not.be.empty"));
        }
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String userId = SecurityUtil.getLoginUser().getUserId();
        //物理删除旧的配置信息
        LambdaQueryWrapper<CrmAgentWorkRecordAutoMerge> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(CrmAgentWorkRecordAutoMerge::getCompanyId, companyId);
        crmAgentWorkRecordAutoMergeService.remove(lambdaQueryWrapper);
        //添加新的配置信息
        for (CrmAgentWorkRecordAutoMerge workRecordAutoMerge : workRecordAutoMergeList) {
            workRecordAutoMerge.setCompanyId(companyId);
            workRecordAutoMerge.setCreator(userId);
            workRecordAutoMerge.setCreateTime(new Date());
            workRecordAutoMerge.setModifier(userId);
            workRecordAutoMerge.setModifyTime(new Date());
            workRecordAutoMerge.setDataStatus(Constants.NORMAL);
        }
        crmAgentWorkRecordAutoMergeService.saveBatch(workRecordAutoMergeList);
        return AjaxResult.ok(MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult queryWorkOrderIdByContactId(String contactId) {
        //根据contactId去crm_agent_work_record_detail表中查询工单Id(考虑到测试环境有脏数据，所以限制取一条)
        LambdaQueryWrapper<CrmAgentWorkRecordDetail> eq = new LambdaQueryWrapper<CrmAgentWorkRecordDetail>()
                .eq(CrmAgentWorkRecordDetail::getContactId, contactId)
                .eq(CrmAgentWorkRecordDetail::getDataStatus, Constants.NORMAL)
                .last("LIMIT 1");
        CrmAgentWorkRecordDetail crmAgentWorkRecordDetail = crmAgentWorkRecordDetailService.getBaseMapper().selectOne(eq);
        if (null == crmAgentWorkRecordDetail) {
            log.info("入参contactId【"+contactId+"】数据无效，根据这个参数在数据库中查询不到对应的工单Id");
            return AjaxResult.failure(MessageUtils.get("contactId.data.is.invalid"));
        }
        String workRecordId = crmAgentWorkRecordDetail.getWorkRecordId();
        return AjaxResult.ok(workRecordId, MessageUtils.get("operate.success"));
    }

    @Override
    public WorkRecordDetailVo innerAuthWorkOrderDetail(String workRecordId, String companyId) {
        String indexName=ticketIndex+companyId;
        // 查询工单是否存在
        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workRecordId));

        TicketInfoIndex ticketInfoIndex = elasticsearchUtil.searchInfoQuery(queryConditions, indexName, TicketInfoIndex.class);
        if (ticketInfoIndex == null) {
            return new WorkRecordDetailVo();
        }
        WorkRecordDetailVo workRecordDetailVo = new WorkRecordDetailVo();
        BeanUtils.copyProperties(ticketInfoIndex, workRecordDetailVo);
        return workRecordDetailVo;
    }

    public void addAgentWorkRecordContent(TicketContentIndex ticketContentIndex, String companyId){
        try {
            // 定义索引名称
            String indexName = workContentIndex+companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
            }

            // 插入消息前判断该消息是否存在 根据contentId判断
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("work_record_content_id", ticketContentIndex.getWork_record_content_id())));
            SearchRequest searchRequest = new SearchRequest(indexName).source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            if (searchResponse.getHits().getTotalHits().value > 0) {
                return;
            }

            // 定义索引请求
            IndexRequest indexRequest = new IndexRequest(indexName);
            String value = new ObjectMapper().writeValueAsString(ticketContentIndex);
            indexRequest.source(value, XContentType.JSON);
            // 进行添加
            IndexResponse response = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            restHighLevelClient.indices().refresh(new RefreshRequest(indexName), RequestOptions.DEFAULT);
            log.info(String.valueOf(response.getResult()));
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es保存工单聊天数据失败，异常信息：", e);
            }
        }
    }

    @Override
    public IPage<WorkOrderRecordFinalEsResult> queryWorkBenchWorkOrderList(IPage<Object> page, WorkOrderRecordRequest workOrderRecordRequest) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), 0L);
            List<WorkOrderRecordFinalEsResult> results = new ArrayList<>();
            resultPage.setRecords(results);
            return resultPage;
        }
        IPage<WorkOrderRecordFinalEsResult> workOrderRecordResultIPage = queryWorkOrderListFromEs(page, workOrderRecordRequest,1);
        //如果查询出来的分页数据，不为空，将所有的工单id整理出来，用于查询聊天记录，然后根据工单Id，塞回到对应的返回数据中
        List<WorkOrderRecordFinalEsResult> records = workOrderRecordResultIPage.getRecords();
        List<String> workOrderIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(records)) {
            workOrderIdList = records.stream()
                    .map(WorkOrderRecordFinalEsResult::getWorkRecordId)
                    .filter(Objects::nonNull) // 过滤掉 null 值
                    .collect(Collectors.toList());
            Map<String, List<TicketContentIndex>> resultMap = queryEsChatContent(workOrderIdList);
            //将这些返回结果一一对应到查询出来的列表中
            for(WorkOrderRecordFinalEsResult workOrderRecordResult:records){
                String workRecordId = workOrderRecordResult.getWorkRecordId();
                workOrderRecordResult.setTicketContentIndex(resultMap.get(workRecordId));
                TimeVO timeVO = countGradeTime(
                        workOrderRecordResult.getCreateTime(),
                        workOrderRecordResult.getChannelTypeId(),
                        workOrderRecordResult.getWorkRecordTypeCode(),
                        workOrderRecordResult.getPriorityLevelId(),
                        companyId
                );
                workOrderRecordResult.setShouldResolveTime(timeVO.getShouldResolveTime());
                workOrderRecordResult.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
                workOrderRecordResult.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
            }
            //进行时间信息添加
            if(workOrderRecordRequest.getWorkOrderStatus() == 1){
                workOrderRecordResultIPage.setRecords(addTimeDetails(records));
            }
        }
        return workOrderRecordResultIPage;
    }

    /**
     * 列表加载时，默认将时间信息进行添加
     *
     * 存在前置异常：
     * 用户回复消息的时间、客服回复消息的时间均为空
     * 解决方法：
     * 为空数据不进行计算
     *
     * @param records
     * @return
     */
    public List<WorkOrderRecordFinalEsResult> addTimeDetails(List<WorkOrderRecordFinalEsResult> records) {
        //进行国际化数据装填
        //已经存在过redis缓存无需二次装填
        String day = internationalClient.systemLanguages("天").getData();
        String hour = internationalClient.systemLanguages("时").getData();
        String minute = internationalClient.systemLanguages("分").getData();
        //针对上述梳理好的工单列表，进行时间的装填
        records.forEach(
                item -> {
                        //首先判断当前工单的处理状态
                        if (ObjectUtil.isNull(item.getSessionStatus()) || item.getSessionStatus() != 3) {
                            //当前工单处于未解决状态-进行响应时间计算
                            if (item.getStatus() == 1) {
                                //当前是待座席回复，可以进行时间计算
                                Object o = redisTemplate.opsForValue().get(item.getCompanyId() + item.getWorkRecordId());
                                if (ObjectUtil.isNull(o) || ObjectUtil.isEmpty(o)) {
                                    //证明这是历史遗留数据，按照原定逻辑处理
                                    //时间非空判断 - 用户的首次信息
                                    if (ObjectUtil.isNotNull(item.getLastMessageDeliveryTime())
                                            && ObjectUtil.isNotNull(item.getFirstResponseTime())
                                            && item.getFirstResponseTime()!=0) {
                                        log.info("用户的首次信息");
                                    //用当前时间+首次响应时间
                                    LocalDateTime dateTime = item.getLastMessageDeliveryTime().plusSeconds(item.getFirstResponseTime());
                                    LocalDateTime later = LocalDateTime.now();
                                    if (dateTime.compareTo(later) < 1) {
                                        item.setTimeStatus(1L);
                                        Duration duration = Duration.between(dateTime, later);
                                        String convert = convert(duration,day,hour,minute);
                                        item.setScheduleTime(convert);
                                    } else {
                                        item.setTimeStatus(2L);
                                        Duration duration = Duration.between(later, dateTime);
                                        String convert = convert(duration,day,hour,minute);
                                        item.setScheduleTime(convert);
                                    }
                                }
                                } else {
                                    List<RedisDataCacheVO> redisDataCacheVOList = Convert.toList(RedisDataCacheVO.class, o);
                                    //进行二次判断，
                                    List<Integer> collect = redisDataCacheVOList.stream().map(RedisDataCacheVO::getReplyType).collect(Collectors.toList());

                                    if (collect.contains(1)){
                                        //存在客服回复 -- 计算下一次平均响应时间
                                        //时间非空判断
                                        if (ObjectUtil.isNotNull(item.getAvgResponseTime())
                                                && item.getAvgResponseTime() != 0) {
                                            log.info("计算下一次平均响应时间");
                                            Integer replyType = redisDataCacheVOList.get(redisDataCacheVOList.size()-1).getReplyType();
                                            LocalDateTime time;
                                            if(replyType == 2){
                                                 time = Convert.toLocalDateTime(redisDataCacheVOList.get(redisDataCacheVOList.size()-1).getTime());

                                            }else {
                                                time = Convert.toLocalDateTime(redisDataCacheVOList.get(redisDataCacheVOList.size()-2).getTime());
                                            }
                                            //用用户发消息时间+下一次平均响应时间
                                            LocalDateTime dateTime = time.plusSeconds(item.getAvgResponseTime());
                                            LocalDateTime later = LocalDateTime.now();
                                            if (dateTime.compareTo(LocalDateTime.now()) < 1) {
                                                item.setTimeStatus(1L);
                                                Duration duration = Duration.between(dateTime, later);
                                                String convert = convert(duration,day,hour,minute);
                                                item.setScheduleTime(convert);
                                            } else {
                                                item.setTimeStatus(2L);
                                                Duration duration = Duration.between(later, dateTime);
                                                String convert = convert(duration,day,hour,minute);
                                                item.setScheduleTime(convert);
                                            }
                                        }
                                    }else {
                                        //时间非空判断 - 用户的首次信息
                                        if (ObjectUtil.isNotNull(item.getLastMessageDeliveryTime())
                                                && ObjectUtil.isNotNull(item.getFirstResponseTime())
                                                && item.getFirstResponseTime()!=0) {
                                            log.info("计算首次响应时间");
                                            //用当前时间+首次响应时间
                                            LocalDateTime dateTime = Convert.toLocalDateTime(redisDataCacheVOList.get(0).getTime()).plusSeconds(item.getFirstResponseTime());
                                            LocalDateTime later = LocalDateTime.now();
                                            if (dateTime.compareTo(later) < 1) {
                                                item.setTimeStatus(1L);
                                                Duration duration = Duration.between(dateTime, later);
                                                String convert = convert(duration,day,hour,minute);
                                                item.setScheduleTime(convert);
                                            } else {
                                                item.setTimeStatus(2L);
                                                Duration duration = Duration.between(later, dateTime);
                                                String convert = convert(duration,day,hour,minute);
                                                item.setScheduleTime(convert);
                                            }
                                        }


                                    }
                                }
                            }
                        } else {
                            //时间非空判断
                            if (ObjectUtil.isNotNull(item.getShouldResolveTime())) {
                               LocalDateTime dateTime =LocalDateTime.now();
                                if (dateTime.compareTo(item.getShouldResolveTime()) < 1) {
                                    item.setTimeStatus(2L);
                                    Duration duration = Duration.between(dateTime,item.getShouldResolveTime());
                                    String convert = convert(duration,day,hour,minute);
                                    item.setScheduleTime(convert);
                                } else {
                                    item.setTimeStatus(1L);
                                    Duration duration = Duration.between(item.getShouldResolveTime(),dateTime);
                                    String convert = convert(duration,day,hour,minute);
                                    item.setScheduleTime(convert);
                                }
                            }
                        }
                }
        );
        return records;
    }


    /**
     * 返回计算好的时间字符串，天、时、分
     * 去除秒字段
     *
     * @param duration
     * @return
     */
    public String convert(Duration duration,String day,String hour,String minute) {
        String date = "";
        Integer seconds = Convert.toInt(duration.getSeconds());
        int days = seconds / (24 * 60 * 60);
        int remainingSeconds = seconds % (24 * 60 * 60);
        int hours = remainingSeconds / (60 * 60);
        remainingSeconds = remainingSeconds % (60 * 60);
        int minutes = remainingSeconds / 60;
        remainingSeconds = remainingSeconds % 60;

        // 若有剩余秒数，进行向上取整
        if (remainingSeconds > 0) {
            minutes++;
        }

        // 处理分钟数满 60 进 1 到小时
        hours += minutes / 60;
        minutes = minutes % 60;

        // 处理小时数满 24 进 1 到天
        days += hours / 24;
        hours = hours % 24;
        String daysString = days == 0 ? "" : days + day;
        String hoursString = hours == 0 ? "" : hours + hour;
        String minutesString = minutes == 0 ? "" : minutes + minute;
        date = daysString+hoursString+minutesString;
        return date;
    }


    //type 1-真人工单 2-机器人工单
    //机器人工单不涉及到渠道的排除
    private IPage<WorkOrderRecordFinalEsResult> queryWorkOrderListFromEs(IPage<Object> page, WorkOrderRecordRequest workOrderRecordRequest,Integer type) {
        List<WorkOrderRecordFinalEsResult> results = new ArrayList<>();
        try {
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义工单表索引名称
            String indexName = ticketIndex + companyId;

            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            // 工单状态
            Integer workOrderStatus = workOrderRecordRequest.getWorkOrderStatus();
            //待分配的时候不需要考虑部门和座席之类的，因为都没有分配出去，就没有这两个值（只考虑当前公司下的就行）
            BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(type,workOrderStatus);
            if (workOrderStatus != null) {
                int[] statuses = {}; // Initialize statuses to an empty array
                if (workOrderStatus == 0) {
                    //未分配工单，没有座席
                    statuses = new int[]{0};
                } else if (workOrderStatus == 1) {
                    statuses = new int[]{1, 2};
                } else {
                    // 如果有其他情况要处理，在此处coding
                }
                Integer[] statusObjects = Arrays.stream(statuses).boxed().toArray(Integer[]::new);
                boolQueryBuilder.must(QueryBuilders.termsQuery("status", statusObjects));
            }

            // 客户标签条件筛选
            List<String> customerIdList = new ArrayList<>();
            String customerTagIds = workOrderRecordRequest.getCustomerTagIds();
            if (StringUtil.isNotEmpty(customerTagIds)) {
                //根据这个标签id集合查询都有哪些客户打了这个标签，从而得到一个客户Id集合，用于下边条件的筛选
                R<List<String>> listR = customerTagClient.queryCustomerIdByTagIds(customerTagIds);
                if (AjaxResult.SUCCESS == listR.getCode()) {
                    customerIdList = listR.getData();
                }
                boolQueryBuilder.must(QueryBuilders.termsQuery("customer_id", customerIdList));
            }

            //工单标签筛选（和上级确定了，是根据id筛选）
            String ticketTagIds = workOrderRecordRequest.getTicketTagIds();
            if (StringUtil.isNotEmpty(ticketTagIds)) {
                // 将逗号分隔的字符串转换为List
                List<String> tagIds = Arrays.asList(ticketTagIds.split(","));
                // 创建嵌套查询
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery(
                        "ticket_tag", // 嵌套字段路径
                        QueryBuilders.termsQuery("ticket_tag.tag_id", tagIds), // 嵌套字段中的查询
                        ScoreMode.None // 评分模式
                );
                // 将嵌套查询添加到bool查询中
                boolQueryBuilder.must(nestedQuery);
            }

            // 工单类型
            String workRecordTypeCode = workOrderRecordRequest.getWorkRecordTypeCode();
            if (StringUtils.isNotEmpty(workRecordTypeCode)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("work_record_type_code", workRecordTypeCode));
            }

            // 时间范围
            String startTime = workOrderRecordRequest.getStartTime();
            String endTime = workOrderRecordRequest.getEndTime();
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startTime).lte(endTime));
            }

            // 工单ID
            String workOrderId = workOrderRecordRequest.getWorkOrderId();
            if (StringUtils.isNotEmpty(workOrderId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("work_record_id", workOrderId));
            }

            // 渠道类型
            Integer channelType = workOrderRecordRequest.getChannelType();
            if (channelType != null) {
                boolQueryBuilder.must(QueryBuilders.termQuery("channel_type_id", channelType));
            }

            //机器人工单不涉及到部分渠道的排除，所以只为真人工单添加如下筛选条件
            if (1 == type) {
                if (CrmChannelEnum.EMAIL.getCode() != channelType) {
                    //排除部分渠道
                    Integer[] channelTypes = {
                            CrmChannelEnum.EMAIL.getCode(),
                            CrmChannelEnum.PHONE.getCode(),
                            CrmChannelEnum.WEB_VIDEO.getCode(),
                            CrmChannelEnum.APP_VIDEO.getCode(),
                            CrmChannelEnum.WEB_ONLINE_VOICE.getCode(),
                            CrmChannelEnum.APP_ONLINE_VOICE.getCode(),
                            CrmChannelEnum.GOOGLE_PLAY.getCode()
                    };
                    boolQueryBuilder.mustNot(QueryBuilders.termsQuery("channel_type_id", channelTypes));
                }
            }

            // 搜索条件
            String search = workOrderRecordRequest.getSearch();
            if (StringUtils.isNotEmpty(search)) {
                //先查询从表中哪些内容可能符合入参的筛选条件，从而得到一堆工单id，or到下述内容中
                List<String> contentMatchWorkRecordIdList = queryMatchSearchContent(companyId, search);
                // 使用 should 子句（类似于mysql中的or）
                // 使用 must 子句（类似于mysql中的and）
                // 这些搜索条件，用should
                BoolQueryBuilder searchQuery = QueryBuilders.boolQuery()
                        .should(QueryBuilders.wildcardQuery("customer_name", "*" + search + "*"))
                        .should(QueryBuilders.wildcardQuery("customer_telephone", "*" + search + "*"))
                        .should(QueryBuilders.wildcardQuery("word_record_code", "*" + search + "*"));
                if (CollectionUtils.isNotEmpty(contentMatchWorkRecordIdList)) {
                    searchQuery.should(QueryBuilders.termsQuery("work_record_id", contentMatchWorkRecordIdList));
                }
                searchQuery.minimumShouldMatch(1); // 至少匹配一个
                boolQueryBuilder.must(searchQuery);
            }

            // 排序
            Integer orderType = workOrderRecordRequest.getOrderType();
            if (orderType == null) {
                orderType = 1;
            }
            searchSourceBuilder.sort("is_top", SortOrder.DESC);
            String sortField = (orderType == 1 || orderType == 2) ? "last_message_delivery_time" : "last_message_reply_time";
            SortOrder sortOrder = (orderType == 1 || orderType == 3) ? SortOrder.DESC : SortOrder.ASC;
            searchSourceBuilder.sort(sortField, sortOrder);
            //置顶后进行倒叙展示
            searchSourceBuilder.query(boolQueryBuilder);

            //计算当前页的起始下标
            long start = (page.getCurrent() - 1) * page.getSize();
            // 分页查询
            searchSourceBuilder.from((int) start).size((int) page.getSize());
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            //获取总数量
            long totalCount = searchResponse.getHits().getTotalHits().value;
            log.info("新工作台列表查询内容展示，查询的工单类型是：{}，渠道类型代号（为空串表示所有）是：{}，" +
                            "工单状态码(0-待分配，1-处理中)是：{}，当前搜索条件下，" +
                            "当前条件下查询得到的记录数是：{}", (1 == type) ? "真人工单" : "机器人工单", channelType,
                    (null == workOrderStatus) ? "机器人工单不考虑状态，此处代号为空" : workOrderStatus, totalCount);
            if (totalCount > 0) {
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    TicketInfoIndex result = BeanUtil.toBean(source, TicketInfoIndex.class);
                    WorkOrderRecordFinalEsResult workOrderRecordFinalEsResult = new WorkOrderRecordFinalEsResult();
                    BeanUtils.copyProperties(result,workOrderRecordFinalEsResult);
//                    workOrderRecordFinalEsResult.setCustomerTelephone(DesensitizationUtils.desensitizeCustomerInfo(result.getCustomerTelephone()));
                    results.add(workOrderRecordFinalEsResult);
                }
            }

            IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), totalCount);
            resultPage.setRecords(results);
            return resultPage;
        } catch (IOException e) {
            log.error("从es中查询工单列表数据出现异常：", e);
            //如果出现异常，就返回空数据
            IPage<WorkOrderRecordFinalEsResult> resultPage = new Page<>(page.getCurrent(), page.getSize(), 0L);
            results = new ArrayList<>();
            resultPage.setRecords(results);
            return resultPage;
        }
    }

    private List<String> queryMatchSearchContent(String companyId,String searchTerm){
        List<String> workRecordIds = new ArrayList<>();
        //定义工单内容表索引数据
        String indexContentName = workContentIndex + companyId;
        // 查询工单内容表索引是否存在
        boolean indexContentExists = headIndexExists(indexContentName);
        // 工单内容表索引不存在直接创建索引
        if (!indexContentExists) {
            createIndex(indexContentName);
        }

        try {
            SearchRequest searchRequest = new SearchRequest(indexContentName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 模糊查询
            searchSourceBuilder.query(QueryBuilders.matchQuery("content", searchTerm).fuzziness("AUTO")); // 使用AUTO fuzziness
            searchSourceBuilder.size(10000);
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                if (sourceAsMap.containsKey("work_record_id")) {
                    workRecordIds.add(sourceAsMap.get("work_record_id").toString());
                }
            }
        } catch (IOException e) {
            log.error("根据搜索关键字，到es的工单内容表中匹配符合的聊天记录数据出现异常：", e);
            //如果出现异常，就返回空数据
            return workRecordIds;
        }
        return workRecordIds;
    }

    @Override
    public AjaxResult<AgentWorkRecordVo> innerQueryWorkRecordById(String workRecordId, String companyId) {
        List<TicketInfoIndex> infoIndexList = elasticsearchUtil.searchListQuery(
                Lists.newArrayList(createCondition("work_record_id", workRecordId), createCondition("status", 1)),
                ticketIndex + companyId,
                TicketInfoIndex.class);

        if (CollectionUtils.isNotEmpty(infoIndexList)) {
            TicketInfoIndex ticketInfoIndex = infoIndexList.get(0);
            AgentWorkRecordVo workRecordVo = new AgentWorkRecordVo();
            BeanUtils.copyProperties(ticketInfoIndex, workRecordVo);
            workRecordVo.setExtraProperty(JSONArray.parseArray(JSONObject.toJSONString(ticketInfoIndex.getTicketExt())));

            return AjaxResult.ok(workRecordVo);
        }

        return AjaxResult.failure();
    }

    @Override
    public AjaxResult queryEmailTotalCount() throws Exception{
        //只要处理中状态
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处直接返回空数据
            return AjaxResult.ok(0);
        }

        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1,1);

        boolQueryBuilder.must(QueryBuilders.termQuery("data_status", Constants.NORMAL));
        // 渠道类型
        boolQueryBuilder.must(QueryBuilders.termQuery("channel_type_id", CrmChannelEnum.EMAIL.getCode()));

        BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                .should(QueryBuilders.termQuery("status", 1))
                .should(QueryBuilders.termQuery("status", 2))
                .minimumShouldMatch(1);
        // 将 statusQuery 添加到主查询中
        boolQueryBuilder.must(statusQuery);

        searchSourceBuilder.query(boolQueryBuilder);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        log.info("新工作台邮件类型渠道列表，不需要筛选条件的统计数量-处理中：{}",totalCount);

        return AjaxResult.ok(totalCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateReadStatus(String workRecordId) {
        try {
            //将未读状态更新为已读状态
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            // 定义工单表索引名称
            String indexName = workContentIndex + companyId;
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
                return AjaxResult.ok();
            }

            SearchRequest searchRequest = new SearchRequest(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("work_record_id", workRecordId));
//            // 使用should子句添加reply_type为1或2的条件
//            // reply_type 回复类型（1、客服 2、客户 3、机器人 4、智能总结 5、备注 6、其他）

            // 先确保 reply_type 为 1 或 2
            BoolQueryBuilder replyTypeBoolQuery = QueryBuilders.boolQuery();
            replyTypeBoolQuery.should(QueryBuilders.termQuery("reply_type", 1));
            replyTypeBoolQuery.should(QueryBuilders.termQuery("reply_type", 2));
            // minimum_should_match 设置为 1，确保至少有一个 should 条件匹配
            replyTypeBoolQuery.minimumShouldMatch(1);

            // 确保 read_status 存在且为 0
            BoolQueryBuilder readStatusBoolQuery = QueryBuilders.boolQuery();
            readStatusBoolQuery.must(QueryBuilders.termQuery("read_status", 0));
            readStatusBoolQuery.must(QueryBuilders.existsQuery("read_status"));

            // 将两个嵌套的 bool query 用 must 连接
            boolQuery.must(replyTypeBoolQuery);
            boolQuery.must(readStatusBoolQuery);

            searchSourceBuilder.query(boolQuery);
            //加如下条件设置返回条数的上限，因为默认返回10条数据
            searchSourceBuilder.size(10000);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            //获取总数量
            long totalCount = searchResponse.getHits().getTotalHits().value;
            if (totalCount > 0) {
                SearchHits hits = searchResponse.getHits();
                for (SearchHit hit : hits) {
                    String id = hit.getId();
                    UpdateRequest request = new UpdateRequest(indexName, id);
                    // 解析JSON字符串
                    TicketContentIndex ticketContentIndex = JSONObject.parseObject(hit.getSourceAsString(), TicketContentIndex.class);
                    //将这些内容，更新为已读状态
                    ticketContentIndex.setRead_status(1);
                    request.doc(JSONObject.toJSONString(ticketContentIndex), XContentType.JSON);
                    try {
                        // 执行 Update By Query 请求
                        UpdateResponse updateResponse = restHighLevelClient.update(request, RequestOptions.DEFAULT);
                        int updateStatus = updateResponse.status().getStatus();
                        log.info("更新发送状态成功：{}", updateStatus);
                    } catch (IOException e) {
                        if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                            log.error("es更新读取字段read_status数据失败，异常信息：", e);
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
            return AjaxResult.ok();
        } catch (IOException e) {
            log.error("将es中的聊天记录，未读更新为已读，失败", e);
            return AjaxResult.failure(MessageUtils.get("operate.failure"));
        }
    }

    @Override
    public AjaxResult queryWorkBenchTicketTotalCount() throws Exception {
        long pendingCount = queryDiffStatusTicketCount(1,0);
        log.info("新工作台聊天类型渠道列表，不需要筛选条件的统计数量-待分配：{}",pendingCount);
        long processingCount = queryDiffStatusTicketCount(1,1);
        log.info("新工作台聊天类型渠道列表，不需要筛选条件的统计数量-处理中：{}",processingCount);

        WorkOrderRecordCountResult workOrderRecordCountResult = new WorkOrderRecordCountResult()
                .setPendingCount(pendingCount)
                .setProcessingCount(processingCount);
        return AjaxResult.ok(workOrderRecordCountResult);
    }

    //type 1-真人工单 2-机器人工单
    //新工作台，返回列表总数量（非邮件-处理中和待分配）+机器人工单，这不涉及到页面上的筛选条件
    private long queryDiffStatusTicketCount(Integer type,Integer workOrderStatus) throws Exception {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            //如果索引不存在，意味着也不会有数据，此处返回0
            return 0;
        }

        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(type, workOrderStatus);

        if (null != type && 1 == type) {
            //如果是真人工单，需要用到如下条件
            BoolQueryBuilder statusQuery = QueryBuilders.boolQuery();
            //查询处理中和待分配
            if (workOrderStatus == 0) {
                //未分配工单，没有座席
                statusQuery.must(QueryBuilders.termQuery("status", 0));
            } else if (workOrderStatus == 1) {
                statusQuery.should(QueryBuilders.termQuery("status", 1));
                statusQuery.should(QueryBuilders.termQuery("status", 2));
                statusQuery.minimumShouldMatch(1);
            } else {
                // 如果有其他情况要处理，在此处coding
            }
            // 将 statusQuery 添加到主查询中
            boolQueryBuilder.must(statusQuery);

            //排除下边这些渠道类型
            Integer[] channelTypes = {
                    CrmChannelEnum.EMAIL.getCode(),
                    CrmChannelEnum.PHONE.getCode(),
                    CrmChannelEnum.WEB_VIDEO.getCode(),
                    CrmChannelEnum.APP_VIDEO.getCode(),
                    CrmChannelEnum.WEB_ONLINE_VOICE.getCode(),
                    CrmChannelEnum.APP_ONLINE_VOICE.getCode(),
                    CrmChannelEnum.GOOGLE_PLAY.getCode()
            };
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("channel_type_id", channelTypes));
        }

        boolQueryBuilder.must(QueryBuilders.termQuery("data_status", Constants.NORMAL));

        searchSourceBuilder.query(boolQueryBuilder);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        return totalCount;
    }

    public List<String> voiceAnalysis2(String workRecordId) {
        R<List<TranscribeLogVo>> transcribeLogList = companyTranscribeClient.getTranscribeLogList(workRecordId);
        if (transcribeLogList.getCode() == R.SUCCESS) {
            List<TranscribeLogVo> data = transcribeLogList.getData();
            log.info("转录信息为：{}",data);
            List<String> list = new ArrayList<>();
            data.forEach(s -> {
                list.add(s.getWhisperText());
            });
            return list;
        } else {
            return new ArrayList<>();
        }
    }

    private Map<String, List<TicketContentIndex>> queryEsChatContent(List<String> workOrderIdList) {
        Map<String, List<TicketContentIndex>> resultMap = new HashMap<>();

        for (String workOrderId : workOrderIdList) {
            try {
                List<TicketContentIndex> ticketContentIndex = queryLastContentEs(workOrderId);
                resultMap.put(workOrderId, ticketContentIndex);
            } catch (IOException e) {
                log.error("根据工单Id集合，查询es中的工单聊天记录出现异常 {}: {}", workOrderId, e.getMessage(), e);
                resultMap.put(workOrderId, null);
            }
        }

        return resultMap;
    }

    /**
     *  验证索引是否存在
     * @param index 索引名称
     * @return 存在状态
     * @throws IOException
     */
    private boolean headIndexExists(String index){
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        }catch (Exception e){
            log.error("验证索引失败:",e);
        }
        return false;
    }

    /**
     *  创建索引
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName){
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        }catch (Exception e){
            log.error("创建索引报错",e);
        }
    }

    private List<TicketContentIndex> queryLastContentEs(String workRecordId) throws IOException {
        // 定义索引名称
        String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();
        // 查询索引是否存在
        boolean indexExists = headIndexExists(index);
        if (!indexExists) {
            return Arrays.asList();
        }
        List<TicketContentIndex> ticketContentIndexList = new ArrayList<>();
        //termQuery 只适用于精确匹配已知值的情况。 如果你想根据 work_record_id 字段查找数据，但该字段可能包含 null 值或者你并不确定 workRecordId 是否存在，那么 termQuery 不是合适的查询方式。
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
//                .must(QueryBuilders.termQuery("work_record_id", workRecordId)); // 查询指定的唯一主键

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("work_record_id", workRecordId))
                .mustNot(QueryBuilders.termQuery("data_status", 0));
        // 使用should子句添加reply_type为1或2的条件
        // reply_type 回复类型（1、客服 2、客户 3、机器人 4、智能总结 5、备注 6、其他）

//        boolQuery.should(QueryBuilders.termQuery("reply_type", 1));
//        boolQuery.should(QueryBuilders.termQuery("reply_type", 2));
//        boolQuery.minimumShouldMatch(1);

        searchSourceBuilder.query(boolQuery)
                .sort("reply_time", SortOrder.ASC)
                .size(1000);

        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        if (Objects.requireNonNull(searchResponse.getHits().getTotalHits()).value > 0) {
            SearchHits hits = searchResponse.getHits();
            for (SearchHit hit : hits) {
                // 解析JSON字符串
                TicketContentIndex ticketContentIndex = new TicketContentIndex();
                ticketContentIndex = JSONObject.parseObject(hit.getSourceAsString(), TicketContentIndex.class);
                ticketContentIndexList.add(ticketContentIndex);
            }
        }
        return ticketContentIndexList;
    }

    private List<TicketContentIndex> queryUnReadContentFromEs(String workRecordId) throws IOException {
        List<TicketContentIndex> list = new ArrayList<>();
        // 定义索引名称
        String index = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();
        // 查询索引是否存在
        boolean indexExists = headIndexExists(index);
        if (!indexExists) {
            return list;
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("work_record_id", workRecordId));

        // reply_type 回复类型（1、客服 2、客户 3、机器人 4、智能总结 5、备注 6、其他）
        // 先确保 reply_type 为 1 或 2
        BoolQueryBuilder replyTypeBoolQuery = QueryBuilders.boolQuery();
        replyTypeBoolQuery.should(QueryBuilders.termQuery("reply_type", 1));
        replyTypeBoolQuery.should(QueryBuilders.termQuery("reply_type", 2));
        // minimum_should_match 设置为 1，确保至少有一个 should 条件匹配
        replyTypeBoolQuery.minimumShouldMatch(1);

        // 确保 read_status 存在且为 0
        BoolQueryBuilder readStatusBoolQuery = QueryBuilders.boolQuery();
        readStatusBoolQuery.must(QueryBuilders.termQuery("read_status", 0));
        readStatusBoolQuery.must(QueryBuilders.existsQuery("read_status"));

        // 将两个嵌套的 bool query 用 must 连接
        boolQuery.must(replyTypeBoolQuery);
        boolQuery.must(readStatusBoolQuery);

        searchSourceBuilder.query(boolQuery);

        SearchRequest searchRequest = new SearchRequest(index);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        if (Objects.requireNonNull(searchResponse.getHits().getTotalHits()).value > 0) {
            SearchHits hits = searchResponse.getHits();
            for (SearchHit hit : hits) {
                // 解析JSON字符串
                TicketContentIndex ticketContentIndex = JSONObject.parseObject(hit.getSourceAsString(), TicketContentIndex.class);
                list.add(ticketContentIndex);
            }
        }
        return list;
    }

    //认领工单
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> claimWorkOrder(WorkOrderClaimVO workOrderClaimVO) {

        String indexName=ticketIndex+SecurityUtil.getLoginUser().getCompanyId();

        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workOrderClaimVO.getWorkRecordId()));

        Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, indexName, TicketInfoIndex.class);
        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
        String id = (String) result.get("id");
        if(Objects.isNull(workRecord)){
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }

        // 当前工单状态不支持认领
        if (workRecord.getStatus() != 0 && !workRecord.getAgentId().equals("1001")) {
            return AjaxResult.failure(MessageUtils.get("work.cannot.claim"));
        }

        // 更新工单状态
        workRecord.setStatus(1);
        workRecord.setAcceptType(0);
        workRecord.setInitiationTime(LocalDateTime.now());
        workRecord.setAgentId(getUserId());
        workRecord.setAgentName(getUsername());
        workRecord.setDeptId(SecurityUtil.getLoginUser().getDeptId());
        workRecord.setModifier(getUserId());
        workRecord.setModifyTime(LocalDateTime.now());
        // 认领工单后，添加消息到达时间
        workRecord.setLastMessageDeliveryTime(LocalDateTime.now());
        elasticsearchUtil.updateDocument(indexName, id ,workRecord);
        addTicketOperationLog(workOrderClaimVO.getWorkRecordId(), null, TicketOperationTypeEnum.CLAIM_TICKET.getCode(), SecurityUtil.getLoginUser());
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    //终止工单
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> terminationWorkStatus(WorkOrderUpdateStatusVO workOrderUpdateStatusVO) {
        log.info("terminationWorkStatus-> 当前工单入参：【{}】",JSONObject.toJSONString(workOrderUpdateStatusVO));
        String index = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();

        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workOrderUpdateStatusVO.getWorkRecordId()));
        Map<String,Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, index, TicketInfoIndex.class);
        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }

        TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
        String id = (String) result.get("id");

        if (Objects.isNull(workRecord)) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        log.info("当前工单状态：【{}】",workRecord.getStatus());
        // 如果工单已经终止过 则返回成功状态 不进行下面状态流转
        if (workRecord.getStatus()==3) {
            return AjaxResult.ok(null, MessageUtils.get("work.update.status.resolution.solved"));
        }
        if (workRecord.getStatus()==4) {
            return AjaxResult.ok(null, MessageUtils.get("work.update.status.resolution.terminated"));
        }
        if (workRecord.getStatus()==5) {
            return AjaxResult.ok(null, MessageUtils.get("work.update.status.resolution.transferred"));
        }
        // 当前工单状态不支持终止
//        if (workRecord.getStatus() != 0 && workRecord.getStatus() != 1 && workRecord.getStatus() != 2) {
//            return AjaxResult.failure(MessageUtils.get("work.update.status.termination"));
//        }

        workRecord.setTerminateTime(LocalDateTime.now());
        workRecord.setModifier(SecurityUtil.getUserId());
        workRecord.setModifyTime(LocalDateTime.now());
        workRecord.setStatus(4); // 终止状态

        elasticsearchUtil.updateDocument(index , id ,workRecord);

        addTicketOperationLog(workRecord.getWorkRecordId(), workOrderUpdateStatusVO.getOperationLogReason(), TicketOperationTypeEnum.TERMINATION_TICKET.getCode(), SecurityUtil.getLoginUser());
        try {
            ResponseEntity<?> responseEntity = imClient.removeSessionAndCloseSocket(workRecord.getCompanyId(), workRecord.getWorkRecordId());
        }catch (Exception e){
            log.error("调用im解决工单报错",e);
        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    //关联工单
    @Override
    public AjaxResult<Object> updateWorkAssociation(UpdateWorkAssociationVO updateWorkAssociationVO) {

        String index = ticketRelationIndex + SecurityUtil.getLoginUser().getCompanyId();

        for (String relationWorkRecordId : updateWorkAssociationVO.getRelationWorkRecordIdList()) {
            List<QueryCondition> queryConditions=new ArrayList<>();
            queryConditions.add(createCondition("relation_work_record_id", relationWorkRecordId));
            queryConditions.add(createCondition("work_record_id",  updateWorkAssociationVO.getWorkRecordId()));
            List<TicketRelation> ticketRelationList=elasticsearchUtil.searchListQuery(queryConditions,ticketRelationIndex + SecurityUtil.getLoginUser().getCompanyId(),TicketRelation.class);
            if (!ticketRelationList.isEmpty()) {
                // 如果有关联，不能继续添加
                return AjaxResult.failure(MessageUtils.get("work.update.status.Association") + relationWorkRecordId);
            }
            // 准备需要写入的数据对象
            TicketRelation recordRelation = new TicketRelation();
            recordRelation.setRelation_id(UuidUtils.generateUuid());
            recordRelation.setRelation_work_record_id(relationWorkRecordId);
            recordRelation.setData_status(1);
            recordRelation.setCreator(SecurityUtil.getUserId());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = dateFormat.format(new Date());
            recordRelation.setCreate_time(formattedDate);
            recordRelation.setWork_record_id(updateWorkAssociationVO.getWorkRecordId());
            IndexRequest indexRequest = new IndexRequest(index)
                    .source(JSON.toJSONString(recordRelation), XContentType.JSON);
            try {
                restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201 Created")) {
                    log.error("es新增文档失败，异常信息：", e);
                    throw new RuntimeException(e);
                }
            }
            // 记录操作日志
            addTicketOperationLog(updateWorkAssociationVO.getWorkRecordId(), null, TicketOperationTypeEnum.ASSOCIATION_TICKET.getCode(), SecurityUtil.getLoginUser());

        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    //查询历史工单
    @Override
    public IPage<HistoryCustomerWorkOrderVO> queryHistoryCustomerWorkOrder(IPage<Object> page, String customerId, String workRecordId,String workRecordInfo) {

        String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();

        List<QueryCondition> queryConditions=new ArrayList<>();
        queryConditions.add(createCondition("customer_id", customerId));
        queryConditions.add(createCondition("data_status", 1));
        if(workRecordId!=null) {
            queryConditions.add(createCondition("work_record_id", workRecordId));
        }
        // 如果有查询信息 模糊查询
        if (StringUtils.isNotEmpty(workRecordInfo)) {
            queryConditions.add(createCondition("work_record_theme", workRecordInfo, QueryTypeEnum.SHOULD));
            queryConditions.add(createCondition("customer_name", workRecordInfo, QueryTypeEnum.SHOULD));
            queryConditions.add(createCondition("customer_telephone", workRecordInfo, QueryTypeEnum.SHOULD));
        }
        queryConditions.add(createCondition(page));
        queryConditions.add(createCondition("create_time", SortOrder.DESC));
        IPage<TicketInfoIndex> contentIndexIPage=elasticsearchUtil.searchPageQuery(queryConditions,
                indexName,
                TicketInfoIndex.class);

      List<HistoryCustomerWorkOrderVO>  resultList=new ArrayList<>();
      for(TicketInfoIndex contentInfo:contentIndexIPage.getRecords()){
            HistoryCustomerWorkOrderVO workOrderVO=new HistoryCustomerWorkOrderVO();
            BeanUtils.copyProperties(contentInfo,workOrderVO);
            // 智能总结
            TicketContentIndex contentIndex =contentIndex = queryWorkSummarToEs(workOrderVO.getWorkRecordId());


            if (ObjectUtils.isNotEmpty(contentIndex)) {
                SmartSummaryResult summaryResult = JSONObject.parseObject(contentIndex.getContent(), SmartSummaryResult.class);
                if (ObjectUtils.isNotEmpty(summaryResult) && StringUtil.isNotEmpty(summaryResult.getMood())) {
                    workOrderVO.setCustomerMood(summaryResult.getMood());
                }
                if (ObjectUtils.isNotEmpty(summaryResult) && StringUtil.isNotEmpty(summaryResult.getSummary())) {
                    workOrderVO.setContentSummary(summaryResult.getSummary());
                }
            }
            resultList.add(workOrderVO);
        }
        // 封装分页信息
        IPage<HistoryCustomerWorkOrderVO> customerWorkOrderPage = new Page<>(contentIndexIPage.getCurrent(), contentIndexIPage.getSize());
        customerWorkOrderPage.setRecords(resultList);
        customerWorkOrderPage.setTotal(contentIndexIPage.getTotal());
        return customerWorkOrderPage;
    }

    public AjaxResult<Object> concernedWorkOrder(String workRecordId, Integer status) {
        // 根据公司 ID 获取索引名称
        String index = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();

        // 创建查询条件
        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workRecordId));

        // 获取查询结果
        Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, index, TicketInfoIndex.class);

        // 结果判断
        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }

        TicketInfoIndex ticketInfo = (TicketInfoIndex) result.get("data");
        String id = (String) result.get("id");

        if (Objects.isNull(ticketInfo)) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }

        String userId = SecurityUtil.getLoginUser().getUserId();
        List<TicketFollow> ticketFollowList = ticketInfo.getTicketFollow() != null ? ticketInfo.getTicketFollow() : new ArrayList<>();

        // 判断是否已经存在该用户收藏记录，status 为 0 时移除该记录
        boolean isUserFollowing = false;
        for (Iterator<TicketFollow> iterator = ticketFollowList.iterator(); iterator.hasNext(); ) {
            TicketFollow follow = iterator.next();
            if (follow.getUserId().equals(userId)) {
                if (status == 0) {
                    iterator.remove(); // 移除当前用户的跟进
                }
                isUserFollowing = true;
                break;
            }
        }

        // 如果用户未关注且 status 不为 0，新增跟进记录
        if (!isUserFollowing && status ==1) {
            TicketFollow ticketFollow = new TicketFollow();
            ticketFollow.setUserId(userId);
            ticketFollow.setFollowTime(LocalDateTime.now()); // 设置当前时间
            ticketFollowList.add(ticketFollow);
        }

        // 更新 TicketInfo 对象
        ticketInfo.setTicketFollow(ticketFollowList);

        // 更新 Elasticsearch 中的文档
        elasticsearchUtil.updateDocument(index, id, ticketInfo);

        // 返回成功信息
        return AjaxResult.ok(MessageUtils.get("ticket.already.collect"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> transferWorkOrder(WorkOrderAssignVO workOrderAssignVO) {

        String index = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();

        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workOrderAssignVO.getWorkRecordId()));

        Map<String,Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, index, TicketInfoIndex.class);
        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
        String id = (String) result.get("id");
        if (Objects.isNull(workRecord)) {
            return AjaxResult.failure(MessageUtils.get("error.null.record"));
        }
        if (workRecord.getStatus()==3) {
            return AjaxResult.ok(null, MessageUtils.get("work.update.status.resolution.solved"));
        }
        if (workRecord.getStatus()==4) {
            return AjaxResult.ok(null, MessageUtils.get("work.update.status.resolution.terminated"));
        }

        // 团队人员
        List<UserDetailsVO> filteredUserList = new ArrayList<>();
        if (workOrderAssignVO.getRuleType() == 0) {

            List<UserDetailsVO> UserList = getOnlineUsersByDeptId(workOrderAssignVO.getTeamId());
            // 如果是工作台，则需要判断，是否还有其他在线人数
            if(workOrderAssignVO.getPosition() == 1){
                // 判断在线人数是否为1，是否只剩自己
                if(UserList.isEmpty()){
                    return AjaxResult.ok(null, MessageUtils.get("work.update.agent.fail"));
                }
                if(UserList.size() == 1){
                    String userId = UserList.get(0).getUserId();
                    if(userId.equals(workRecord.getAgentId())){
                        // 返回当前没有其他在线坐席，禁止转单
                        return AjaxResult.failure(null, MessageUtils.get("work.update.agent.fail"));
                    }
                }
            }else{
                if(UserList.isEmpty()){
                    // 查询部门下所有人员
                    R<List<UserDetailsVO>> userListResponse = userClient.queryUserListByDeptId(workOrderAssignVO.getTeamId());
                    if (userListResponse == null || userListResponse.getCode() != 200 || userListResponse.getData() == null) {
                        return AjaxResult.failure(MessageUtils.get("error.null.record"));
                    }
                    UserList = userListResponse.getData();
                }
            }

            filteredUserList = UserList.stream()
                    .filter(user -> !workRecord.getAgentId().equals(user.getUserId()))
                    .collect(Collectors.toList());
            if(filteredUserList.isEmpty()){
                // 返回没有其他有效坐席
                return AjaxResult.failure(null, MessageUtils.get("work.update.not.effective.agent"));
            }
        }

        // 修改工单状态为已转单
        workRecord.setStatus(5);
        workRecord.setTransferTime(LocalDateTime.now());
        workRecord.setModifier(getUserId());
        workRecord.setModifyTime(LocalDateTime.now());

        // 如果传入了优先级ID，则更新优先级
        if (StringUtils.isNotEmpty(workOrderAssignVO.getPriorityLevelId())) {
            CrmAgentWorkRecordPriorityLevelDef workLevel = crmAgentWorkRecordPriorityLevelDefService.getById(workOrderAssignVO.getPriorityLevelId());
            workRecord.setPriorityLevelId(workLevel.getPriorityLevelId());
            workRecord.setPriorityLevelName(workLevel.getPriorityLevelCode());
            TimeVO timeVO = countGradeTime(workRecord.getCreateTime(),workRecord.getChannelTypeId(), workRecord.getWorkRecordTypeCode(), workLevel.getPriorityLevelId(), SecurityUtil.getLoginUser().getCompanyId());
            workRecord.setShouldResolveTime(timeVO.getShouldResolveTime());
            workRecord.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
            workRecord.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
        }

       elasticsearchUtil.updateDocument(index,id,workRecord);
        // 复制旧工单记录，生成新工单
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");

        TicketInfoIndex ticketInfoIndex=new TicketInfoIndex();
        BeanUtils.copyProperties(workRecord, ticketInfoIndex);

        ticketInfoIndex.setWorkRecordId(UuidUtils.generateUuid());
        ticketInfoIndex.setOldWorkRecordCode(workRecord.getWordRecordCode());
        ticketInfoIndex.setWordRecordCode(sdf.format(new Date()));
        ticketInfoIndex.setStatus(1);
        ticketInfoIndex.setAcceptType(0);
        // 转派工单后，新增消息到达时间
        ticketInfoIndex.setLastMessageDeliveryTime(LocalDateTime.now());
        ticketInfoIndex.setInitiationTime(LocalDateTime.now());
//        ticketInfoIndex.setCreator(getUserId());
        ticketInfoIndex.setCreateTime(LocalDateTime.now());

        if (workOrderAssignVO.getRuleType() == 1) {
            ticketInfoIndex.setAgentId(workOrderAssignVO.getAgentId());
        } else {
            ticketInfoIndex.setDeptId(workOrderAssignVO.getTeamId());
            // 随机分配座席
            if(!filteredUserList.isEmpty()) {
                ticketInfoIndex.setAgentId(filteredUserList.get(ThreadLocalRandom.current().nextInt(filteredUserList.size())).getUserId());
            }

        }
        R<UserDetailsVO> userDetails = userClient.queryUserDetails(ticketInfoIndex.getAgentId());
        if (userDetails.getCode() == AjaxResult.SUCCESS) {
            ticketInfoIndex.setAgentName(userDetails.getData().getUserName());
            ticketInfoIndex.setDeptId(userDetails.getData().getDeptId());
        }

        List<TicketOperationLog> ticketOperationLog = new ArrayList<>();
        if(workRecord.getTicketOperationLog()!=null){
             ticketOperationLog=workRecord.getTicketOperationLog();
        }
        TicketOperationLog operationLog=new TicketOperationLog();
        operationLog.setOperatorName(ticketInfoIndex.getAgentName());
        operationLog.setOperatorTime(LocalDateTime.now());
        operationLog.setOperationLogType(TicketOperationTypeEnum.TRANSFER_TICKET.getCode());
        operationLog.setOperationLogReason(workOrderAssignVO.getOperationLogReason());
        ticketOperationLog.add(operationLog);
        ticketInfoIndex.setTicketOperationLog(ticketOperationLog);
        elasticsearchUtil.insertDocument(index,ticketInfoIndex);
        if(workOrderAssignVO.getPosition() == 2){
            try {
                String agentId = null;
                String deptId = null;
                if(workOrderAssignVO.getRuleType() == 0){
                    deptId = workOrderAssignVO.getTeamId();
                }else {
                    agentId = workOrderAssignVO.getAgentId();
                }
                ResponseEntity<?> responseEntity = imClient.reassignTicketAndCloseSocket(
                        workRecord.getCompanyId(),
                        workRecord.getWorkRecordId(),
                        workRecord.getAgentId(),
                        agentId,
                        deptId,
                        ticketInfoIndex.getWorkRecordId()
                );
                log.info("调用im转派工单删除会话请求参数:公司id:{},工单id:{},坐席id:{},目标坐席id:{}.目标部门id:{}.转派工单id:{}",
                        workRecord.getCompanyId(),
                        workRecord.getWorkRecordId(),
                        workRecord.getAgentId(),
                        ticketInfoIndex.getAgentId(),
                        ticketInfoIndex.getDeptId(),
                        ticketInfoIndex.getWorkRecordId());
            }catch (Exception e){
                log.error("调用im转派工单报错",e);
            }
        }
        // 复制connect.详情.附件,进行存储
        copyWorkConnect(workRecord, ticketInfoIndex);

        // 复制关联关系表
        copyWorkRelation(workRecord,  ticketInfoIndex);

        // 记录操作日志
        addTicketOperationLog(workOrderAssignVO.getWorkRecordId(), workOrderAssignVO.getOperationLogReason(), TicketOperationTypeEnum.TRANSFER_TICKET.getCode(), SecurityUtil.getLoginUser());
        return AjaxResult.ok(ticketInfoIndex.getWorkRecordId(), MessageUtils.get("operate.success"));
    }

    public IPage<WorkOrderRecordResponseVO> associationWorkOrder(String workRecordId,IPage<Object> pageParam){

        String indexName = ticketIndex+SecurityUtil.getLoginUser().getCompanyId();

        List<TicketRelation> ticketRelationList=queryTiketList(workRecordId,null);

        List<QueryCondition> queryConditions = new ArrayList<>();
        if (ticketRelationList != null && !ticketRelationList.isEmpty()) {
            // 获取关联的 workRecordIds
            List<String> relatedWorkRecordIds = ticketRelationList.stream()
                    .map(TicketRelation::getRelation_work_record_id)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        queryConditions.add(createCondition("work_record_id",relatedWorkRecordIds, QueryTypeEnum.MUST_NOTS));
        }
        queryConditions.add(createCondition("work_record_id", workRecordId, QueryTypeEnum.MUST_NOT));
        queryConditions.add(createCondition(pageParam));
        queryConditions.add(createCondition("create_time", SortOrder.DESC));
        IPage<TicketInfoIndex> responsePage = elasticsearchUtil.searchPageQuery(queryConditions, indexName, TicketInfoIndex.class);
        IPage<WorkOrderRecordResponseVO> response = new Page<>();

        // 判断查询结果是否为空
        if (CollectionUtils.isNotEmpty(responsePage.getRecords())) {
            // 填充返回数据
            List<WorkOrderRecordResponseVO> workOrderRecordResponseList = new ArrayList<>();
            for (TicketInfoIndex ticketInfo : responsePage.getRecords()) {
                WorkOrderRecordResponseVO workOrderRecordResponse = new WorkOrderRecordResponseVO();
                BeanUtils.copyProperties(ticketInfo, workOrderRecordResponse);
                workOrderRecordResponse.setChannelTypeName(TransUtil.trans(ticketInfo.getChannelTypeName()));  // 国际化字段处理

                // 获取并填充智能总结内容
                TicketContentIndex contentIndex = queryWorkSummarToEs(ticketInfo.getWorkRecordId());
                if (ObjectUtils.isNotEmpty(contentIndex)) {
                    SmartSummaryResult summaryResult = JSONObject.parseObject(contentIndex.getContent(), SmartSummaryResult.class);
                    if (ObjectUtils.isNotEmpty(summaryResult)) {
                        if (StringUtil.isNotEmpty(summaryResult.getMood())) {
                            workOrderRecordResponse.setCustomerMood(summaryResult.getMood());  // 设置情感分析
                        }
                        if (StringUtil.isNotEmpty(summaryResult.getSummary())) {
                            workOrderRecordResponse.setContentSummary(summaryResult.getSummary());  // 设置总结内容
                        }
                    }
                }
                workOrderRecordResponseList.add(workOrderRecordResponse);
            }

            // 填充分页结果
            response.setRecords(workOrderRecordResponseList);
            response.setTotal(responsePage.getTotal());
            response.setSize(responsePage.getSize());
            response.setCurrent(responsePage.getCurrent());
        }
        return response;
    }

    private List<TicketRelation> queryTiketList(String  workRecordId,String workRecordInfo){
        String indexName = ticketRelationIndex + SecurityUtil.getLoginUser().getCompanyId();
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
        }
        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workRecordId));
        queryConditions.add(createCondition("data_status", 1));

        // 如果有查询信息 模糊查询
        if (StringUtils.isNotEmpty(workRecordInfo)) {
            queryConditions.add(createCondition("work_record_theme", workRecordInfo, QueryTypeEnum.SHOULD));
            queryConditions.add(createCondition("customer_name", workRecordInfo, QueryTypeEnum.SHOULD));
            queryConditions.add(createCondition("customer_telephone", workRecordInfo, QueryTypeEnum.SHOULD));
        }

        return elasticsearchUtil.searchListQuery(
                queryConditions,
                indexName,
                TicketRelation.class
        );
    }
    @Override
    public AjaxResult<IPage<WorkOrderRecordResponseVO>> associationWorkOrderTable(String workRecordId, String workRecordInfo, IPage<Object> pageParam) {
        List<TicketRelation> ticketRelationList=queryTiketList(workRecordId,workRecordInfo);
        if (ticketRelationList == null || ticketRelationList.isEmpty()) {
            return AjaxResult.ok(new Page<>(pageParam.getCurrent(), pageParam.getSize(), 0));
        }
        // 获取关联的 workRecordIds
        List<String> relatedWorkRecordIds = ticketRelationList.stream()
                .map(TicketRelation::getRelation_work_record_id)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (relatedWorkRecordIds.isEmpty()) {
            return AjaxResult.ok(new Page<>(pageParam.getCurrent(), pageParam.getSize(), 0));
        }

        List<QueryCondition> recordConditions = new ArrayList<>();
        recordConditions.add(createCondition("work_record_id", relatedWorkRecordIds, QueryTypeEnum.TERMS));  // 批量查询
        recordConditions.add(createCondition("data_status", 1));  // 过滤已删除的数据
        recordConditions.add(createCondition(pageParam));  // 分页条件

        IPage<TicketInfoIndex> ticketInfoPage = elasticsearchUtil.searchPageQuery(
                recordConditions,
                ticketIndex + SecurityUtil.getLoginUser().getCompanyId(),
                TicketInfoIndex.class
        );

        List<WorkOrderRecordResponseVO> resultList = ticketInfoPage.getRecords().stream()
                .map(ticketInfoIndex -> {
                    WorkOrderRecordResponseVO workOrderVO = new WorkOrderRecordResponseVO();
                    BeanUtils.copyProperties(ticketInfoIndex, workOrderVO);
                    workOrderVO.setChannelTypeName(TransUtil.trans(workOrderVO.getChannelTypeName()));
                    return workOrderVO;
                })
                .collect(Collectors.toList());

        IPage<WorkOrderRecordResponseVO> workOrderTablePage = new Page<>(pageParam.getCurrent(), pageParam.getSize(), ticketInfoPage.getTotal());
        workOrderTablePage.setRecords(resultList);

        return AjaxResult.ok(workOrderTablePage);
    }

    // 获取工单记录公共方法
    private TicketInfoIndex getWorkRecordFromSearch(String workRecordId) {
        if(Objects.isNull(workRecordId)) {
            return new TicketInfoIndex();
        }
        List<QueryCondition> queryConditions=new ArrayList<>();
        queryConditions.add(createCondition("data_status", 1));
        queryConditions.add(createCondition("work_record_id", workRecordId));
        return elasticsearchUtil.searchInfoQuery(queryConditions,ticketIndex + SecurityUtil.getLoginUser().getCompanyId(),TicketInfoIndex.class);
    }

    //type 1-真人工单 2-机器人工单
    //workOrderStatus 0-待分配 1-处理中
    //未分配的有部门，但是没有坐席
    private BoolQueryBuilder packageCommonSearchBuilder(Integer type,Integer workOrderStatus) {
        //获取用户信息
        SysUserVo loginUser = SecurityUtil.getLoginUser();
        String deptId = loginUser.getDeptId();
        String userId = loginUser.getUserId();
        SysRoleVo sysRoleVo = loginUser.getRoleList().get(0);
        String roleId = sysRoleVo.getRoleId();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (type == 2) {
            boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", "1001"));
        } else {
            if (workOrderStatus == 0) {
                if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
                } else {
                    boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
                }
            } else {
                if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
                } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
                } else if (UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", userId));
                }
            }
        }
        return boolQueryBuilder;
    }

    // 查询下拉框选项
    public AjaxResult<List<ExtOptionVO>> ticketExtOption() {
        // 查询扩展属性定义列表
        List<WorkOrderExtVo> defList = crmAgentWorkRecordExtDefService.queryDefineExtList().getData();

        // 枚举生成的options
        List<ExtOptionVO> enumOptions = Arrays.stream(TicketExtFieldEnum.values())
                .map(e ->new ExtOptionVO( TransUtil.trans(e.getLabel()),e.getField(),1,null)).collect(Collectors.toList());

        if(defList != null && !defList.isEmpty()) {
            // 将defList中的每一项转换为ExtOptionVO
            List<ExtOptionVO> defOptions = defList.stream()
                    .map(def -> new ExtOptionVO(def.getWorkRecordExtDefName(), def.getWorkRecordExtDefCode(), 2,def.getPropertyTypeId()))
                    .collect(Collectors.toList());


            // 合并两个列表
            enumOptions.addAll(defOptions);
        }

        return AjaxResult.ok(enumOptions);
    }

    @Override
    public AjaxResult isTop(WorkRecordTopVO workRecordTopVO) {
        if(workRecordTopVO.getWordRecordId().contains(",")){
            //多个工单进行操作
            List<String> strings = Arrays.asList(
                    workRecordTopVO.getWordRecordId().split(",")
            );
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            strings.forEach(
                    item ->{
                        List<QueryCondition> queryConditions = Collections.singletonList(
                                createCondition("work_record_id", item)
                        );
                        Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, indexName, TicketInfoIndex.class);
                        TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
                        String id = (String) result.get("id");
                        if(workRecordTopVO.getIsTop()){
                            workRecord.setIsTop("2");
                        }else {
                            workRecord.setIsTop(null);
                        }
                        elasticsearchUtil.updateDocument(indexName, id, workRecord);
                    }
            );
        }else {
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            List<QueryCondition> queryConditions = Collections.singletonList(
                    createCondition("work_record_id", workRecordTopVO.getWordRecordId())
            );
            Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, indexName, TicketInfoIndex.class);
            TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
            String id = (String) result.get("id");
            if(workRecordTopVO.getIsTop()){
                workRecord.setIsTop("2");
            }else {
                workRecord.setIsTop(null);
            }
            elasticsearchUtil.updateDocument(indexName, id, workRecord);
        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult getMultipartFile(String fileUrl) {
        FileInputStream fileInputStream = null;
        MultipartFile multipartFileData=null;
        try {
            URL url = new URL(fileUrl);
            String fileName = getFileNameFromUrl(url);
            BufferedInputStream inputStream  = new BufferedInputStream(url.openStream());
            multipartFileData  = new MockMultipartFile(fileName, fileName,"image/jpeg",inputStream);
            String objectKey = "whatsapp/"+"pic/" + fileName;
            S3PutObjectParam s3PutObjectParam = new S3PutObjectParam()
                    .setBucketName(bucketName)
                    .setObjectName(objectKey)
                    .setContentLength(multipartFileData.getSize());
            s3PutObjectParam.setAccessKeyId(accessKey);
            s3PutObjectParam.setSecretAccessKey(secretKey);
            s3PutObjectParam.setRegion(region);
            // 放入s3
            String param = JSON.toJSONString(s3PutObjectParam);
            s3Client.putVideoPicture(multipartFileData, param, 2);
            log.info("存入s3成功");
            PresignedUrlParam urlParam = new PresignedUrlParam()
                    .setBucketName(bucketName)
                    .setKeyName(objectKey);
            urlParam.setAccessKeyId(accessKey);
            urlParam.setSecretAccessKey(secretKey);
            urlParam.setRegion(region);
            R preSignedUrl = s3Client.getObjectPresignedUrl(urlParam);
            log.info("拿到s3上的图片->[{}]", preSignedUrl.getData());
            return AjaxResult.ok(preSignedUrl.getData());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String getFileNameFromUrl(URL url) {
        String path = url.getPath();
        int lastSlashIndex = path.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < path.length() - 1) {
            return path.substring(lastSlashIndex + 1);
        }
        return path;
    }

    @Override
    public AjaxResult<WorkRecordDetailsVO> getDetails(String workRecordId) {
        WorkRecordDetailsVO workRecordDetailsVO = new WorkRecordDetailsVO();
        workRecordDetailsVO.setWorkRecordId(workRecordId);
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex,workRecordId,companyId);
        if(hit!=null){
            try {
                // 获取到_id
                esId = hit.getId();
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错",e);
            }
        }
        WorkRecordDetailVo workRecordDetailVo = new WorkRecordDetailVo();
        BeanUtils.copyProperties(ticketInfoIndex, workRecordDetailVo);
        // 渠道类型名称
        workRecordDetailVo.setChannelTypeName(TransUtil.trans(workRecordDetailVo.getChannelTypeName()));
        workRecordDetailVo.setSummarizeStatus(0);
        if(StringUtils.isNotBlank(ticketInfoIndex.getWorkRecordTypeCode())){
            workRecordDetailVo.setWorkRecordTypeId(ticketInfoIndex.getWorkRecordTypeCode());
        }
        // 查询到创建人
        // 根据座席ID查询座席名称
        if(ticketInfoIndex.getAgentId() != null ){
            if(!"1001".equals(ticketInfoIndex.getAgentId())) {
                R<UserDetailsVO> userDetails = userClient.queryUserDetails(ticketInfoIndex.getAgentId());
                if (userDetails.getCode() == AjaxResult.SUCCESS) {
                    workRecordDetailVo.setAgentName(userDetails.getData().getUserName());
                    String url = null;
                    if(StringUtils.isNotEmpty(userDetails.getData().getProfilePhoto())){
                        url= domainPrefix + userDetails.getData().getProfilePhoto();
                    }
                    workRecordDetailVo.setAgentUrl(url);
                }
            } else{
                workRecordDetailVo.setAgentUrl(cocoPicture);
            }
        }
        List<TicketSatisfaction> ticketSatisfactionList = ticketInfoIndex.getTicketSatisfaction();
        if (CollectionUtils.isNotEmpty(ticketSatisfactionList)) {
            // 取出最后一条
            TicketSatisfaction ticketSatisfaction = ticketSatisfactionList.get(ticketSatisfactionList.size() - 1);
            BigDecimal rating = BigDecimal.valueOf(ticketSatisfaction.getRating());
            workRecordDetailVo.setRating(rating);
        } else {
            workRecordDetailVo.setRating(BigDecimal.ZERO);
        }

            TicketContentIndex ticketContentIndex = queryWorkSummarToEs(workRecordId);
            if (ObjectUtils.isNotEmpty(ticketContentIndex) && ticketContentIndex.getContent() != null) {
                SmartSummaryResult summaryResult = JSONObject.parseObject(ticketContentIndex.getContent(), SmartSummaryResult.class);
                //待办事项数据
                List<CrmAgentWorkRecordWaitExecute> recordWaitExecutes = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(summaryResult.getToDoList())) {
                    List<WaitExecuteVO> executeVOList = summaryResult.getToDoList();
                    if (ObjectUtils.isNotEmpty(executeVOList)) {
                        recordWaitExecutes = executeVOList.stream().map(doList -> {
                            CrmAgentWorkRecordWaitExecute recordWaitExecute = new CrmAgentWorkRecordWaitExecute();
                            BeanUtils.copyProperties(doList, recordWaitExecute);
                            return recordWaitExecute;
                        }).collect(Collectors.toList());
                    }
                }
                workRecordDetailVo.setSummarizeStatus(1);
                workRecordDetailVo.setContentSummary(summaryResult.getSummary());
                workRecordDetailVo.setCustomerMood(Integer.parseInt(getMood(summaryResult.getMood())));
                workRecordDetailVo.setWaitExecuteList(recordWaitExecutes);
            }
        // 查询客户信息
        // 客户ID查询客户电话
        R<CrmCustomerVO> customerDetails = customerClient.queryCustomerDetails(ticketInfoIndex.getCustomerId());
        if (customerDetails.getCode() == AjaxResult.SUCCESS) {
            CrmCustomerVO customer = customerDetails.getData();
            //客户信息增加
            customer.setTelephone(DesensitizationUtils.desensitizeCustomerInfo(customer.getTelephone()));
            if(StringUtil.isNotEmpty(customer.getTelephonePrefixId())){
                customer.setTelephone(customer.getTelephonePrefixId()+customer.getTelephone());
            }else {
                customer.setTelephone(customer.getTelephone());
            }
            customer.setEmailAddress(DesensitizationUtils.desensitizeCustomerInfo(customer.getEmailAddress()));
            workRecordDetailsVO.setCrmCustomerVO(customer);
        }
        workRecordDetailsVO.setWorkRecordDetailVo(workRecordDetailVo);
        //备注
        List<WorkOrderRemarksVO> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 查询出该工单下的备注
        // 定义索引名称
        String indexName = workContentIndex+ companyId;
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("work_record_id", workRecordId));
        boolQueryBuilder.must(QueryBuilders.matchQuery("reply_type", 5));
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        try {
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            if (hits.length > 0) {
                for (SearchHit remarkHit : hits) {
                    Map<String, Object> source = remarkHit.getSourceAsMap();
                    if (remarkHit.getScore() < 0) {
                        continue;
                    }
                    String s = JSON.toJSONString(source);
                    TicketContentIndex contentIndex = JSON.parseObject(s, TicketContentIndex.class);
                    WorkOrderRemarksVO orderRemarksVO = new WorkOrderRemarksVO();
                    // 将字符串转为LocalDateTime
                    LocalDateTime localDateTime = LocalDateTime.parse(contentIndex.getReply_time(), formatter);

                    // 将LocalDateTime转为java.util.Date
                    Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
                    orderRemarksVO.setOperatorName(contentIndex.getReply_person());
                    orderRemarksVO.setCreateTime(date);
                    orderRemarksVO.setOperationLogReason(contentIndex.getContent());
                    list.add(orderRemarksVO);
                }
            }
        } catch (Exception e) {
            log.info("查询工单备注报错", e);
        }
        if(ObjectUtil.isNotNull(list) && list.size()!=0){
            List<WorkOrderRemarksVO> sortedList = list.stream()
                    .sorted(Comparator.comparing(WorkOrderRemarksVO::getCreateTime).reversed())
                    .collect(Collectors.toList());
            workRecordDetailsVO.setWorkOrderRemarksVO(sortedList.get(0));
        }else {
            workRecordDetailsVO.setWorkOrderRemarksVO(new WorkOrderRemarksVO());
        }
        //客户标签
        workRecordDetailsVO.setCustomerTag(
                customerTagClient.queryCustomerTag(workRecordDetailVo.getCustomerId())
                        .getData()
        );
        return AjaxResult.ok(workRecordDetailsVO);
    }

    @Override
    public AjaxResult<WorkRecordDetailsVO> getDetailsForAIAssessment(String workRecordId, String companyId) {
        WorkRecordDetailsVO workRecordDetailsVO = new WorkRecordDetailsVO();
        workRecordDetailsVO.setWorkRecordId(workRecordId);

        if (StringUtil.isEmpty(companyId)) {
            companyId = SecurityUtil.getLoginUser().getCompanyId();
        }

        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex,workRecordId,companyId);
        if(hit!=null){
            try {
                // 获取到_id
                esId = hit.getId();
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错",e);
            }
        }
        WorkRecordDetailVo workRecordDetailVo = new WorkRecordDetailVo();
        BeanUtils.copyProperties(ticketInfoIndex, workRecordDetailVo);
        // 渠道类型名称
        workRecordDetailVo.setChannelTypeName(TransUtil.trans(workRecordDetailVo.getChannelTypeName()));
        workRecordDetailVo.setSummarizeStatus(0);
        if(StringUtils.isNotBlank(ticketInfoIndex.getWorkRecordTypeCode())){
            workRecordDetailVo.setWorkRecordTypeId(ticketInfoIndex.getWorkRecordTypeCode());
        }
        // 查询到创建人
        // 根据座席ID查询座席名称
        if(ticketInfoIndex.getAgentId() != null ){
            if(!"1001".equals(ticketInfoIndex.getAgentId())) {
                R<UserDetailsVO> userDetails = userClient.queryUserDetails(ticketInfoIndex.getAgentId());
                if (userDetails.getCode() == AjaxResult.SUCCESS) {
                    workRecordDetailVo.setAgentName(userDetails.getData().getUserName());
                    String url = null;
                    if(StringUtils.isNotEmpty(userDetails.getData().getProfilePhoto())){
                        url= domainPrefix + userDetails.getData().getProfilePhoto();
                    }
                    workRecordDetailVo.setAgentUrl(url);
                }
            } else{
                workRecordDetailVo.setAgentUrl(cocoPicture);
            }
        }
//        List<TicketSatisfaction> ticketSatisfactionList = ticketInfoIndex.getTicketSatisfaction();
//        if (CollectionUtils.isNotEmpty(ticketSatisfactionList)) {
//            // 取出最后一条
//            TicketSatisfaction ticketSatisfaction = ticketSatisfactionList.get(ticketSatisfactionList.size() - 1);
//            BigDecimal rating = BigDecimal.valueOf(ticketSatisfaction.getRating());
//            workRecordDetailVo.setRating(rating);
//        } else {
//            workRecordDetailVo.setRating(BigDecimal.ZERO);
//        }

//        TicketContentIndex ticketContentIndex = queryWorkSummarToEs(workRecordId);
//        if (ObjectUtils.isNotEmpty(ticketContentIndex) && ticketContentIndex.getContent() != null) {
//            SmartSummaryResult summaryResult = JSONObject.parseObject(ticketContentIndex.getContent(), SmartSummaryResult.class);
//            //待办事项数据
//            List<CrmAgentWorkRecordWaitExecute> recordWaitExecutes = new ArrayList<>();
//            if (CollectionUtils.isNotEmpty(summaryResult.getToDoList())) {
//                List<WaitExecuteVO> executeVOList = summaryResult.getToDoList();
//                if (ObjectUtils.isNotEmpty(executeVOList)) {
//                    recordWaitExecutes = executeVOList.stream().map(doList -> {
//                        CrmAgentWorkRecordWaitExecute recordWaitExecute = new CrmAgentWorkRecordWaitExecute();
//                        BeanUtils.copyProperties(doList, recordWaitExecute);
//                        return recordWaitExecute;
//                    }).collect(Collectors.toList());
//                }
//            }
//            workRecordDetailVo.setSummarizeStatus(1);
//            workRecordDetailVo.setContentSummary(summaryResult.getSummary());
//            workRecordDetailVo.setCustomerMood(Integer.parseInt(getMood(summaryResult.getMood())));
//            workRecordDetailVo.setWaitExecuteList(recordWaitExecutes);
//        }
        workRecordDetailsVO.setWorkRecordDetailVo(workRecordDetailVo);

        return AjaxResult.ok(workRecordDetailsVO);
    }

    @Override
    public AjaxResult getContentDetails(GetContentDetailsVO getContentDetailsVO) {
        //首先进行全体数据拉取
        // 定义工单表索引名称
        String indexName = ticketIndex + getContentDetailsVO.getCompanyId();
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        SearchSourceBuilder builder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        builder.query(
                boolQueryBuilder.must(
                        QueryBuilders.termQuery("work_record_id",getContentDetailsVO.getWorkRecordId())
                )
        );
        builder.from(0).size(10);
        request.source(builder);
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<TicketInfoIndex> ticketInfoIndexList = new ArrayList<>();
        for (SearchHit hit : response.getHits().getHits()) {
            Map<String, Object> source = hit.getSourceAsMap();
            TicketInfoIndex result = BeanUtil.toBean(source, TicketInfoIndex.class);
            ticketInfoIndexList.add(result);
        }
        //查询结束后，也只可能存在一个，为了防止两个工单的问题，获取第一个
        TicketInfoIndex ticketInfoIndex = ticketInfoIndexList.get(0);
        //获取当前工单的渠道类型
        int channelTypeId = Integer.parseInt(ticketInfoIndex.getChannelTypeId());
        if(
                CrmChannelEnum.PHONE.getCode().equals(channelTypeId)
                || CrmChannelEnum.WEB_ONLINE_VOICE.getCode().equals(channelTypeId)
                || CrmChannelEnum.APP_ONLINE_VOICE.getCode().equals(channelTypeId)
        ){
            //进行语音转录获取
            try {
                return voiceAnalysisDetails(ticketInfoIndex.getWorkRecordId(),ticketInfoIndex.getChannelTypeId(),getContentDetailsVO.getCompanyId());
            } catch (IOException e) {
                log.info("报错啦，快来看看参数啊！！");
            }
        }else {
            try {
                return queryWorkRecordInfoDetails(ticketInfoIndex.getWorkRecordId(),ticketInfoIndex.getChannelTypeId(),getContentDetailsVO.getCompanyId());
            } catch (Exception e) {
                log.info("报错啦，快来看看参数啊！！");
            }
        }
        return null;
    }

    public AjaxResult voiceAnalysisDetails(String workRecordId,String channelTypeId,String companyId) throws IOException {
        String contactId = null;
        // 修改为查询ES数据
        // 定义索引名称
        String indexName = workContentIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
        }
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("work_record_id", workRecordId));
        // 如果是邮件为正序查询，其他则为倒序查询
        if (CrmChannelEnum.EMAIL.getCode().equals(Integer.valueOf(channelTypeId))) {
            searchSourceBuilder.sort("reply_time", SortOrder.DESC);
            searchSourceBuilder.sort("reply_millis_time", SortOrder.DESC);
        } else {
            searchSourceBuilder.sort("reply_time", SortOrder.ASC);
            searchSourceBuilder.sort("reply_millis_time", SortOrder.ASC);
        }
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(10000);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        // 进行查询
        SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        SearchHit[] hits = response.getHits().getHits();
        log.info("查询ES数据是否存在： {} ", null != hits);
        for (SearchHit hit : hits) {
            Map<String, Object> source = hit.getSourceAsMap();
            if (hit.getScore() < 0) {
                continue;
            }
            String s = JSON.toJSONString(source);
            TicketContentIndex contentIndex = JSON.parseObject(s, TicketContentIndex.class);
            log.info("查询ES数据是TicketContentIndex： {} ", contentIndex);
            if(null != contentIndex){
                contactId = contentIndex.getContact_id();
            }
        };
        log.info("查询转录信息contactId： {} ", contactId);
        if (StringUtils.isEmpty(contactId)) {
            return AjaxResult.ok(null, MessageUtils.get("operate.success"));
        }
        // 获取工作记录详情
        // 定义索引名称
        String indexNameWorkContentIndex = workContentIndex + companyId;
        //获取索引
        SearchRequest requestWorkContentIndex = new SearchRequest();
        requestWorkContentIndex.indices(indexNameWorkContentIndex);
        //构建请求
        SearchSourceBuilder searchSourceBuilderWorkContentIndex = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilderWorkContentIndex = QueryBuilders.boolQuery();
        boolQueryBuilderWorkContentIndex.must(QueryBuilders.matchQuery("contact_id", contactId));
        // 查询条件
        searchSourceBuilderWorkContentIndex.query(boolQueryBuilderWorkContentIndex);
        searchSourceBuilderWorkContentIndex.trackTotalHits(true);
        requestWorkContentIndex.source(searchSourceBuilderWorkContentIndex);
        TicketContentIndex contentIndex = null;
        String esId = null;
        try {
            // 进行查询
            SearchResponse responseWorkContentIndex = restHighLevelClient.search(requestWorkContentIndex, RequestOptions.DEFAULT);
            SearchHit[] hitsWorkContentIndex = responseWorkContentIndex.getHits().getHits();
            log.info("查询工单，工单数量：{}", hitsWorkContentIndex.length);
            for (SearchHit hit : hitsWorkContentIndex) {
                Map<String, Object> source = hit.getSourceAsMap();
                if (hit.getScore() < 0) {
                    continue;
                }
                esId = hit.getId();
                String s = JSON.toJSONString(source);
                contentIndex = JSON.parseObject(s, TicketContentIndex.class);
            }
        } catch (Exception e) {
            log.error("查询工单聊天记录报错:", e);
        }

        if (contentIndex == null) {
            return AjaxResult.ok(null, MessageUtils.get("operate.success"));
        }
        log.info("工单信息为：{} ", contentIndex);

        // 获取s3 文件 - 获取aws账号信息
        CrmAwsConnect crmAwsConnect = crmAwsConnectMapper.selectById(contentIndex.getConnect_id());
        if (null == crmAwsConnect) {
            return AjaxResult.failure(null, "获取aws账号信息异常，Connect实例ID："+contentIndex.getConnect_id());
        }
        CrmAwsAccount awsAccount = crmAwsAccountMapper.selectById(crmAwsConnect.getAwsUserId());

        List<TicketFile> ticketFileList = contentIndex.getTicket_file();
        if (ticketFileList.isEmpty()) {
            log.info("未查到附件录音文件");
            Map<String, Object> map = getStringObjectMap(contentIndex);
            return AjaxResult.ok(map);
        }
        TicketFile ticketFile = ticketFileList.get(0);
        if (ticketFile == null) {
            log.info("未查到附件录音文件");
            // 查询一下
            Map<String, Object> map = getStringObjectMap(contentIndex);
            return AjaxResult.ok(map);
        } else {
            String key = "audio/result/" + contentIndex.getWork_record_content_id() + ".json";
            S3GetObjectParam s3GetObjectParam = new S3GetObjectParam();
            s3GetObjectParam.setBucketName(ticketFile.getBucket_name());
            s3GetObjectParam.setObjectName(key);
            s3GetObjectParam.setAccessKeyId(awsAccount.getAccessKey());
            s3GetObjectParam.setSecretAccessKey(awsAccount.getSecretAccessKey());
            s3GetObjectParam.setRegion(crmAwsConnect.getRecordS3BucketRegion());
            R voiceAnalysisRes;
            try {
                voiceAnalysisRes = s3Client.getBucketObject(s3GetObjectParam);
                if (voiceAnalysisRes.getCode() != 200) {
                    // 查询一下
                    Map<String, Object> map = getStringObjectMap(contentIndex);
                    return AjaxResult.ok(map);
                }
            } catch (Exception e) {
                // 查询一下
                Map<String, Object> map = getStringObjectMap(contentIndex);
                return AjaxResult.ok(map);
            }
            log.info("成功获取到contactId:【{}】的转录语音【{}】", contactId, contentIndex.getWork_record_content_id());
            JSONObject result = JSONObject.parseObject((String) voiceAnalysisRes.getData());
//            获取到Json数据中的content内容和participantRoles角色
            List<String> contents = JsonPath.read(result, "$.Transcript[*].Content");
            List<String> participantRoles = JsonPath.read(result, "$.Transcript[*].ParticipantRole");
            List<TranslateVO> translateVOS = new ArrayList<>();
            for (int i = 0; i < contents.size(); i++) {
                TranslateVO translateVO = new TranslateVO();
                translateVO.setId(String.valueOf(UUID.randomUUID()));
                translateVO.setContent(contents.get(i));
                translateVO.setType(participantRoles.get(i));
                translateVOS.add(translateVO);
            }
            Map<String, Object> map = new HashMap<>();
            R<SysCompanyTranscribeConfigVo> transcribeConfig = companyTranscribeClient.getTranscribeConfig(awsAccount.getCompanyId());
            if (transcribeConfig.getCode() == R.SUCCESS) {
                if (transcribeConfig.getData() != null && org.apache.commons.lang3.StringUtils.isNotBlank(transcribeConfig.getData().getTranscribeConfigId())) {
                    map.put("translateType", "whisper");
                } else {
                    map.put("translateType", "aws");
                }
            } else {
                map.put("translateType", "aws");
            }
            map.put("translateVOS", translateVOS);
            List<String> listText = new ArrayList<>();
            listText.add(result.getString("text"));
            map.put("text", listText);
            return AjaxResult.ok(map);
        }
    }

    public AjaxResult queryWorkRecordInfoDetails(String workRecordId , String channelTypeId,String companyId){
        log.info("打印一下查询工单详情请求参数：workRecordId>>>{}..channelTypeId{}", workRecordId, channelTypeId);
        //工单详情
        TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
        String esId = null;
        SearchHit ticketHit = elasticsearchUtil.queryEsTicket(ticketIndex, workRecordId, companyId);
        if (ticketHit != null) {
            try {
                // 获取到_id
                esId = ticketHit.getId();
                Map<String, Object> source = ticketHit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
            } catch (JsonProcessingException e) {
                log.error("查询es报错", e);
            }
        }
        log.info("打印一下工单详情查询到的工单{}", ticketInfoIndex);
        // 修改为查询ES数据
        // 定义索引名称
        String indexName = workContentIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
        }
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("work_record_id", workRecordId));
        // 如果是邮件为正序查询，其他则为倒序查询
        if (CrmChannelEnum.EMAIL.getCode().equals(Integer.valueOf(channelTypeId))) {
            searchSourceBuilder.sort("reply_time", SortOrder.DESC);
            searchSourceBuilder.sort("reply_millis_time", SortOrder.DESC);
        } else {
            searchSourceBuilder.sort("reply_time", SortOrder.ASC);
            searchSourceBuilder.sort("reply_millis_time", SortOrder.ASC);
        }
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(10000);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        // 定义值
        List<TicketContentIndexVO> list = new ArrayList<>();
        String recordS3Bucket = null;
        String recordS3BucketRegion = null;
        String accessKey = null;
        String secretAccessKey = null;
        // 社媒时间状态
        boolean socialMediaTimeStatus = false;
        try {
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSourceAsMap();
                if (hit.getScore() < 0) {
                    continue;
                }
                String s = JSON.toJSONString(source);
                TicketContentIndex contentIndex = JSON.parseObject(s, TicketContentIndex.class);
                TicketContentIndexVO contentIndexVO = new TicketContentIndexVO();
                BeanUtils.copyProperties(contentIndex, contentIndexVO);
                // 如果回复类型是智能总结，则转为json，另存一个字段 前端需要用
                if (contentIndex.getReply_type() != null && contentIndex.getReply_type() == 4) {
                    String content = contentIndexVO.getContent();
                    SmartSummaryResult summaryResult = JSON.parseObject(content, SmartSummaryResult.class);
                    contentIndexVO.setSmart_summary(summaryResult);
                }
//                //进行加密
                contentIndexVO.setEmail_sender(
                        DesensitizationUtils.desensitizeCustomerInfo(contentIndexVO.getEmail_sender())
                );
                contentIndexVO.setEmail_recipient(
                        DesensitizationUtils.desensitizeCustomerInfo(contentIndexVO.getEmail_recipient())
                );
                contentIndexVO.setEmail_cc_to(
                        DesensitizationUtils.desensitizeCustomerInfo(contentIndexVO.getEmail_cc_to())
                );
                contentIndexVO.setEmail_bcc(
                        DesensitizationUtils.desensitizeCustomerInfo(contentIndexVO.getEmail_bcc())
                );
                list.add(contentIndexVO);
            }
            // 如果是录音文件，则需要生成临时url
            if ("7".equals(channelTypeId) || ticketInfoIndex.getChatVoice() == 1) {
                //在crm_aws_connect表中获取桶名字
                CrmAwsConnect crmAwsConnect = crmAwsConnectMapper.selectOne(new QueryWrapper<CrmAwsConnect>()
                        .eq("connect_id", list.get(0).getConnect_id())
                        .eq("data_status", Constants.NORMAL));
                recordS3Bucket = crmAwsConnect.getRecordS3Bucket();
                recordS3BucketRegion = crmAwsConnect.getRecordS3BucketRegion();

                //在crm_aws_account表中获取ak和sk
                String awsUserId = crmAwsConnect.getAwsUserId();
                CrmAwsAccount crmAwsAccount = crmAwsAccountMapper.selectOne(new QueryWrapper<CrmAwsAccount>()
                        .eq("aws_user_id", awsUserId)
                        .eq("data_status", Constants.NORMAL));
                accessKey = crmAwsAccount.getAccessKey();
                secretAccessKey = crmAwsAccount.getSecretAccessKey();
            }
            for (TicketContentIndexVO item : list) {
                List<TicketFile> ticketFileList = item.getTicket_file();
                if (CollectionUtils.isNotEmpty(ticketFileList)) {
                    //如果是录音形式，要给生成临时url
                    if ("7".equals(channelTypeId) || ticketInfoIndex.getChatVoice() == 1) {
                        // 如果还是机器人工单，则只取完整的一条
                        if (ticketInfoIndex.getAgentId().equals("1001")) {
                            // 移除 file_name 包含 '-' 的元素
                            ticketFileList.removeIf(ticketFile -> ticketFile.getFile_name() != null && ticketFile.getFile_name().contains("-"));
                        }
                        for (TicketFile ticketFile : ticketFileList) {
                            String filePath = ticketFile.getFile_path();
                            //获取临时url
                            String preSignedUrl = null;
                            if (ticketInfoIndex.getAgentId().equals("1001")) {
                                preSignedUrl = getPreSignedUrl(ticketFile.getBucket_name(), accessKey, secretAccessKey, ticketInfoIndex.getRegion(), filePath);
                            } else {
                                preSignedUrl = getPreSignedUrl(recordS3Bucket, accessKey, secretAccessKey, recordS3BucketRegion, filePath);
                            }
                            ticketFile.setFile_path(preSignedUrl);
                        }
                        item.setTicket_file(ticketFileList);
                    }
                }
            }
            // 定义社媒类型是否需要校验客户时间 默认0
            Integer mediaTimeCheckCount = 0;
            if (ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.FACEBOOK.getCode().toString())
                    || ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WHATSAPP.getCode().toString())) {
                mediaTimeCheckCount = CrmChannelEnum.FACEBOOK.getMessageConfine();
            }
            if (ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getCode().toString())
                    || ticketInfoIndex.getChannelTypeId().equals(CrmChannelEnum.WECHAT_OFFICIAL_ACCOUNT.getCode().toString())) {
                mediaTimeCheckCount = CrmChannelEnum.WECHAT_CUSTOMER_SERVICE.getMessageConfine();
            }
            if (mediaTimeCheckCount != 0) {
                if (ticketInfoIndex.getCustomerFirstMessageTime() != null) {
                    // 将 Date 转换为 LocalDateTime
                    LocalDateTime replyTime = ticketInfoIndex.getCustomerFirstMessageTime();
                    // 获取当前时间
                    LocalDateTime now = LocalDateTime.now();
                    // 计算当前时间和回复时间的差值
                    long hoursDifference = ChronoUnit.HOURS.between(replyTime, now);
                    // 判断是否超过24小时,超过24小时则不能发送消息
                    if (hoursDifference >= mediaTimeCheckCount) {
                        socialMediaTimeStatus = true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询工单聊天记录报错:", e);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("dataList", list);
        map.put("socialMediaTimeStatus", socialMediaTimeStatus);
        return AjaxResult.ok(map);
    }

    @Override
    public Boolean agentUpdateTicket(AgentUpdateTicketVO updateTicketVO) {
        log.info("更新工单接受参数, updateTicketVO={}, ", updateTicketVO);
        if(updateTicketVO.getSessionId()==null){
            return true;
        }
        String indexName = ticketIndex + updateTicketVO.getCompanyId();
        String workRecordId = updateTicketVO.getSessionId();
        Map<String, Object> updates = updateTicketVO.getUpdates();

        if (updates == null || updates.isEmpty()) {
            log.warn("更新失败: updates 为空, workRecordId={}", workRecordId);
            return false;
        }

        log.info("开始更新工单了:, workRecordId={}, updates={}", workRecordId, updates);

        List<QueryCondition> queryConditions = Collections.singletonList(
                createCondition("work_record_id", workRecordId)
        );

        Map<String, Object> result = elasticsearchUtil.searchInfoMapQuery(queryConditions, indexName, TicketInfoIndex.class);

        if (result == null || !result.containsKey("data") || !result.containsKey("id")) {
            log.warn("未找到工单, workRecordId={}, indexName={}", workRecordId, indexName);
            return false;
        }

        TicketInfoIndex workRecord = (TicketInfoIndex) result.get("data");
        String id = (String) result.get("id");

        if (workRecord == null) {
            log.warn("查询结果为空, workRecordId={}", workRecordId);
            return false;
        }

        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            String attributeName = entry.getKey();
            Object attributeValue = entry.getValue();

            TicketExtFieldEnum fieldEnum = TicketExtFieldEnum.getByField(attributeName);
            if (fieldEnum == null) {
                log.warn("未识别字段: {} -> {}", attributeName, attributeValue);
                continue;
            }

            if ("customerId".equals(attributeName)) {
                if(updateTicketVO.getTelephone()!=null) {
                    workRecord.setCustomerTelephone(updateTicketVO.getTelephone());
                }
                if(updateTicketVO.getCustomName()!=null) {
                    workRecord.setCustomerName(updateTicketVO.getCustomName());
                }
            } else {
                switch (fieldEnum) {
                    case STATUS:
                        if (attributeValue.toString().equals("resolved")) {
                            workRecord.setStatus(3);

                        } else if (attributeValue.toString().equals("resolved")) {
                            workRecord.setStatus(4);

                        } else {
                            return false;
                        }
                        break;
                    case WORD_RECORD_THEME:
                        workRecord.setWorkRecordTheme(attributeValue.toString());
                        log.info("更新工单名称: {}", attributeValue);
                        break;

                    case PRIORITY_LEVEL_ID:
                        CrmAgentWorkRecordPriorityLevelDef workLevel =
                                crmAgentWorkRecordPriorityLevelDefService.getOne(
                                        new LambdaQueryWrapper<CrmAgentWorkRecordPriorityLevelDef>()
                                                .eq(CrmAgentWorkRecordPriorityLevelDef::getPriorityLevelCode, attributeValue.toString())
                                );
                        if (workLevel != null) {
                            workRecord.setPriorityLevelId(workLevel.getPriorityLevelId());
                            workRecord.setPriorityLevelName(workLevel.getPriorityLevelCode());
                            log.info("更新优先级: {}", attributeValue);
                        } else {
                            log.warn("未找到优先级定义: {}", attributeValue);
                        }
                        break;

                    case WORK_RECORD_TYPE_ID:
                        workRecord.setWorkRecordTypeCode(attributeValue.toString());
                        log.info("更新工单类型: {}", attributeValue);
                        break;
                    default:
                        // 将无法匹配的字段当作拓展字段处理
                        TicketExt ticketExt = new TicketExt();
                        ticketExt.setExtCode(attributeName);
                        ticketExt.setExtValue(attributeValue.toString());
                        workRecord.getTicketExt().add(ticketExt);
                        log.info("添加拓展字段: {} -> {}", attributeName, attributeValue);
                        break;
                }
        }
        }

        boolean updateSuccess = elasticsearchUtil.updateDocument(indexName, id, workRecord);
        log.info("工单更新完成, workRecordId={}, success={}", workRecordId, updateSuccess);
        return updateSuccess;
    }


    @Override
    public List<ScheduleWorkVO> cacheRefresh(WorkRedisCacheRefreshVO workRedisCacheRefreshVO){
        //对公司级别进行锁处理 - 避免同步触发引起的异常
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String userId = SecurityUtil.getUserId();
        RLock lock = redissonClient.getLock("RecordSchedule" + companyId);
        lock.lock();
        try {
            if(ObjectUtil.isNull(workRedisCacheRefreshVO.getWorkOrderId())||ObjectUtil.isEmpty(workRedisCacheRefreshVO.getWorkOrderId())){
                log.info("单个工单倒计时刷新,对应座席人员 => {},工单id => {}",userId,workRedisCacheRefreshVO.getWorkOrderId());
                return listWorkCacheRefresh(companyId,userId);
            }else {
                log.info("列表工单倒计时刷新,对应座席人员 => {}",SecurityUtil.getUserId());
                return workCacheRefresh(workRedisCacheRefreshVO,companyId);
            }
        } catch (Exception e) {
            log.error("工单倒计时出现异常，请检查工单属性或SLA配置", e);
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }

    }

    public List<ScheduleWorkVO> workCacheRefresh(WorkRedisCacheRefreshVO workRedisCacheRefreshVO,String companyId){
        //首先进行全体数据拉取
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        SearchSourceBuilder builder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        //只需要对用户agent_id和当前工单的类型（1、2进行判断）
        builder.query(
                boolQueryBuilder.must(
                        QueryBuilders.termQuery("work_record_id",workRedisCacheRefreshVO.getWorkOrderId())
                )
        );
        builder.from(0).size(10);
        request.source(builder);
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<WorkOrderRecordFinalEsResult> workOrderRecordFinalEsResult = new ArrayList<>();
        for (SearchHit hit : response.getHits().getHits()) {
            Map<String, Object> source = hit.getSourceAsMap();
            TicketInfoIndex result = BeanUtil.toBean(source, TicketInfoIndex.class);
            workOrderRecordFinalEsResult.add(
                            BeanUtil.toBean(result,WorkOrderRecordFinalEsResult.class)
            );
        }
        WorkOrderRecordFinalEsResult resultWork = workOrderRecordFinalEsResult.get(0);
        //需要进行当前工单的工单类型修改，同时需要重新计算当前的时间信息
        TimeVO timeVO = countGradeTime(
                resultWork.getCreateTime(),
                resultWork.getChannelTypeId(),
                ObjectUtil.isNull(workRedisCacheRefreshVO.getWorkRecordTypeCode())
                        ||ObjectUtil.isEmpty(workRedisCacheRefreshVO.getWorkRecordTypeCode())
                        ?resultWork.getWorkRecordTypeCode():workRedisCacheRefreshVO.getWorkRecordTypeCode(),
                ObjectUtil.isNull(workRedisCacheRefreshVO.getPriorityLevelId())
                        ||ObjectUtil.isEmpty(workRedisCacheRefreshVO.getPriorityLevelId())
                        ?resultWork.getPriorityLevelId():workRedisCacheRefreshVO.getPriorityLevelId(),
                companyId
        );
        resultWork.setShouldResolveTime(timeVO.getShouldResolveTime());
        resultWork.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
        resultWork.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
        resultWork =
                addTimeDetails(
                        Arrays.asList(resultWork)
                ).get(0);
        ScheduleWorkVO scheduleWorkVO = new ScheduleWorkVO();
        scheduleWorkVO.setScheduleTime(resultWork.getScheduleTime());
        scheduleWorkVO.setTimeStatus(resultWork.getTimeStatus());
        scheduleWorkVO.setWorkRecordId(resultWork.getWorkRecordId());
        //如果当前工单类型为解决：那么时间类型就是2 - 待解决否则是 1 - 待响应
        if(ObjectUtil.isNotNull(resultWork.getSessionStatus()) && resultWork.getSessionStatus() == 3){
            //已完成 -待解决
            scheduleWorkVO.setTimeType(2L);
        }else {
            //未完成 - 待响应
            scheduleWorkVO.setTimeType(1L);
        }
        return Arrays.asList(scheduleWorkVO);
    }


    public List<ScheduleWorkVO> listWorkCacheRefresh(String companyId,String userId){
        List<ScheduleWorkVO> results = new ArrayList<>();
        //首先进行全体数据拉取
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        SearchSourceBuilder builder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        List<String> collect = SecurityUtil.getLoginUser().getRoleList().stream().map(SysRoleVo::getRoleId).collect(Collectors.toList());
        if(collect.contains("1005")){
            String deptId = SecurityUtil.getLoginUser().getDeptId();
            //存在座席管理员账号
            builder.query(
                    boolQueryBuilder.must(
                            QueryBuilders.termsQuery("dept_id", deptId)
                    )
            );
            builder.query(
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"))
            );
        }else {
            //只需要对用户agent_id和当前工单的类型（1、2进行判断）
            builder.query(
                    boolQueryBuilder.must(
                            QueryBuilders.termQuery("agent_id", userId)
                    )
            );
        }
        builder.query(
                boolQueryBuilder.must(
                        QueryBuilders.termsQuery("status", new Integer[]{1, 2})
                )
        );
        //排除部分渠道
        Integer[] channelTypes = {
                CrmChannelEnum.EMAIL.getCode(),
                CrmChannelEnum.PHONE.getCode(),
                CrmChannelEnum.WEB_VIDEO.getCode(),
                CrmChannelEnum.APP_VIDEO.getCode(),
                CrmChannelEnum.WEB_ONLINE_VOICE.getCode(),
                CrmChannelEnum.APP_ONLINE_VOICE.getCode(),
                CrmChannelEnum.GOOGLE_PLAY.getCode()
        };
        builder.query(
                boolQueryBuilder.mustNot(
                        QueryBuilders.termsQuery("channel_type_id", channelTypes)
                )
        );
        builder.from(0).size(1000);
        request.source(builder);
        SearchResponse response = null;
        try {
            response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        for (SearchHit hit : response.getHits().getHits()) {
            Map<String, Object> source = hit.getSourceAsMap();
            TicketInfoIndex result = BeanUtil.toBean(source, TicketInfoIndex.class);
            WorkOrderRecordFinalEsResult workOrderRecordFinalEsResult = new WorkOrderRecordFinalEsResult();
            TimeVO timeVO = countGradeTime(
                    result.getCreateTime(),
                    result.getChannelTypeId(),
                    result.getWorkRecordTypeCode(),
                    result.getPriorityLevelId(),
                    companyId
            );
            workOrderRecordFinalEsResult =
                    BeanUtil.toBean(result,WorkOrderRecordFinalEsResult.class);
            workOrderRecordFinalEsResult.setShouldResolveTime(timeVO.getShouldResolveTime());
            workOrderRecordFinalEsResult.setFirstResponseTime(ObjectUtil.isNull(timeVO.getFirstResponseTime())?0:timeVO.getFirstResponseTime());
            workOrderRecordFinalEsResult.setAvgResponseTime(ObjectUtil.isNull(timeVO.getAvgResponseTime())?0:timeVO.getAvgResponseTime());
            workOrderRecordFinalEsResult =
                    addTimeDetails(
                            Arrays.asList(workOrderRecordFinalEsResult)
                    ).get(0);
            //进行数据过滤以及对应的时间缓存
            ScheduleWorkVO scheduleWorkVO = new ScheduleWorkVO();
            scheduleWorkVO.setWorkRecordId(result.getWorkRecordId());
            //如果当前工单类型为解决：那么时间类型就是2 - 待解决否则是 1 - 待响应
            if(ObjectUtil.isNotNull(result.getSessionStatus()) && result.getSessionStatus() == 3){
                //已完成 -待解决
                scheduleWorkVO.setTimeType(2L);
            }else {
                //未完成 - 待响应
                scheduleWorkVO.setTimeType(1L);
            }
            scheduleWorkVO.setScheduleTime(workOrderRecordFinalEsResult.getScheduleTime());
            scheduleWorkVO.setTimeStatus(workOrderRecordFinalEsResult.getTimeStatus());
            results.add(scheduleWorkVO);
        }
        return results;
    }

    /**
     * 进行消息撤回后的数据处理
     * @param updateContentDetailsVOS
     * @return
     */
    public AjaxResult updateContentDetails(UpdateContentDetailsVO updateContentDetailsVOS)  {
        // 定义索引名称
        String indexName = workContentIndex + SecurityUtil.getLoginUser().getCompanyId();
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
        }
        updateContentDetailsVOS.getContentId().forEach(
                item -> {
                    // 插入消息前判断该消息是否存在 根据contentId判断
                    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
                    searchSourceBuilder.query(QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("reference_work_record_content_id", item))
                            .must(QueryBuilders.termQuery("work_record_id", updateContentDetailsVOS.getWorkRecordId()))
                    );
                    SearchRequest searchRequest = new SearchRequest(indexName).source(searchSourceBuilder);
                    SearchResponse searchResponse = null;

                    try {
                        searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    //当前消息不存在
                    if (searchResponse.getHits().getTotalHits().value == 0) {
                    }else {
                        SearchHits hits = searchResponse.getHits();
                        String esId = hits.getHits()[0].getId();
                        if (hits != null) {
                            try {
                                TicketContentIndex ticketContentIndex = new TicketContentIndex();
                                for (SearchHit hit : hits) {
                                    Map<String, Object> source = hit.getSourceAsMap();
                                    ticketContentIndex = BeanUtil.toBean(source, TicketContentIndex.class);
                                    ticketContentIndex.setReference_content(updateContentDetailsVOS.getContent());
                                    ticketContentIndex.setReference_reply_person(updateContentDetailsVOS.getReplyPerson());
                                    ticketContentIndex.setReference_content_type(updateContentDetailsVOS.getContentType());
                                    elasticsearchUtil.updateDocument(indexName,esId,ticketContentIndex);
                                }
                            } catch (Exception e) {
                                log.error("查询es报错", e);
                            }
                        }
                    }
                }
        );
        return AjaxResult.ok();
    }

}