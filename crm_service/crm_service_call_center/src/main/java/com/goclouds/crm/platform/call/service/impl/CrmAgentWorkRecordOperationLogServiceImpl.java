package com.goclouds.crm.platform.call.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.TicketOperationLog;
import com.goclouds.crm.platform.call.domain.es.TicketSatisfaction;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderRemarksVO;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderSatisfactionVo;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordOperationLogVO;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordEsResult;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordOperationLogMapper;
import com.goclouds.crm.platform.call.service.*;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.domain.QueryCondition;
import com.goclouds.crm.platform.common.enums.TicketOperationTypeEnum;
import com.goclouds.crm.platform.openfeignClient.domain.call.PhoneWorkOrderSatisfactionVo;
import com.goclouds.crm.platform.utils.ElasticsearchUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.script.ScriptType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.goclouds.crm.platform.common.domain.QueryCondition.createCondition;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_operation_log(工单操作日志记录;)】的数据库操作Service实现
* @createDate 2023-09-28 14:33:26
*/
@Slf4j
@Service
@RequiredArgsConstructor
public class CrmAgentWorkRecordOperationLogServiceImpl extends ServiceImpl<CrmAgentWorkRecordOperationLogMapper, CrmAgentWorkRecordOperationLog>
    implements CrmAgentWorkRecordOperationLogService {

    private final CrmAgentWorkRecordOperationLogDefService crmAgentWorkRecordOperationLogDefService;

    @Value("${es.work-record-content-index}")
    private String workContentIndex;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    private final RestHighLevelClient restHighLevelClient;

    private final CrmAwsConnectService crmAwsConnectService;

    @Autowired
    @Lazy
    private CrmAgentWorkRecordService crmAgentWorkRecordService;

    @Autowired
    private CrmAgentWorkRecordDetailService crmAgentWorkRecordDetailService;
    @Autowired
    private CrmAgentWorkRecordSatisfactionService crmAgentWorkRecordSatisfactionService;

    @Autowired
    private ElasticsearchUtil elasticsearchUtil;


    @Override
    public AjaxResult<List<WorkRecordOperationLogVO>> workOrderOperationRecords(String workRecordId) {
        try {
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            // 注册 JavaTimeModule
            objectMapper.registerModule(new JavaTimeModule());


            TicketInfoIndex ticketInfoIndex = new TicketInfoIndex();
            SearchHit hit = elasticsearchUtil.queryEsTicket(ticketIndex,workRecordId,SecurityUtil.getLoginUser().getCompanyId());
            if(hit!=null){
                Map<String, Object> source = hit.getSourceAsMap();
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);

            }

            List<TicketOperationLog> ticketOperationLogList = ticketInfoIndex.getTicketOperationLog();
            List<WorkRecordOperationLogVO> logList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(ticketOperationLogList)) {
                for (TicketOperationLog ticketOperationLog : ticketOperationLogList) {
                    WorkRecordOperationLogVO logInfo = new WorkRecordOperationLogVO();
                    String content = crmAgentWorkRecordOperationLogDefService.queryLogContent(ServletUtils.getHeaderLanguage(), ticketOperationLog.getOperationLogType());
                    log.info("当前语言为{}", ServletUtils.getHeaderLanguage());
                    if (StringUtil.isBlank(content)) {
                        log.info("查询的数据为null,不进行拼接,type为{},语言为{}", ticketOperationLog.getOperationLogType(), ServletUtils.getHeaderLanguage());
                        continue;
                    }
                    // 替换操作者名称
                    if (content.contains("#{userName}")) {
                        content = content.replace("#{userName}", ticketOperationLog.getOperatorName());
                    }
                    // 替换操作原因
                        if (content.contains("#{reason}")) {
                            if(ObjectUtil.isNotNull(ticketOperationLog.getOperationLogReason()) && ObjectUtil.isNotEmpty(ticketOperationLog.getOperationLogReason())){
                                content = content.replace("#{reason}", ticketOperationLog.getOperationLogReason());
                        }else {
                                content = content.replace("#{reason}", "");
                            }
                    }

                    // 替换座席名称
                    if (content.contains("#{agentName}")) {
                        content = content.replace("#{agentName}", ticketInfoIndex.getAgentName());
                    }

                    // 替换工单编号
                    if (content.contains("#{ticketCode}")) {
                        content = content.replace("#{ticketCode}", ticketInfoIndex.getWordRecordCode());
                    }

                    // 替换旧工单编号
                    if (content.contains("#{oldWorkRecord}")) {
                        String oldWorkCode = "";
                        // 如果有旧工单编号，则查询旧工单
                        if (StringUtil.isNotEmpty(ticketInfoIndex.getOldWorkRecordCode())) {
                            oldWorkCode = ticketInfoIndex.getOldWorkRecordCode();
                        }
                        content = content.replace("#{oldWorkRecord}", oldWorkCode);
                    }
                    logInfo.setOperationLogDescribe(content);
                    logInfo.setOperationLogType(ticketOperationLog.getOperationLogType());
                    logInfo.setOperatorName(ticketOperationLog.getOperatorName());
                    logInfo.setCreateTime(ticketOperationLog.getOperatorTime());
                    logList.add(logInfo);
                }
            }

            return AjaxResult.ok(logList);
        } catch (IOException e) {
            log.error("查询报错：", e);
            return AjaxResult.failure();
        }

    }

    @Override
    public AjaxResult<List<WorkOrderRemarksVO>> queryByTicketRemarks(String workRecordId) {
        List<WorkOrderRemarksVO> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 查询出该工单下的备注
        // 定义索引名称
        String indexName = workContentIndex+ SecurityUtil.getLoginUser().getCompanyId();
        //获取索引
        SearchRequest request = new SearchRequest();
        request.indices(indexName);
        //构建请求
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("work_record_id", workRecordId));
        boolQueryBuilder.must(QueryBuilders.matchQuery("reply_type", 5));
        // 查询条件
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.trackTotalHits(true);
        request.source(searchSourceBuilder);
        try {
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            if (hits.length > 0) {
                for (SearchHit hit : hits) {
                    Map<String, Object> source = hit.getSourceAsMap();
                    if (hit.getScore() < 0) {
                        continue;
                    }
                    String s = JSON.toJSONString(source);
                    TicketContentIndex contentIndex = JSON.parseObject(s, TicketContentIndex.class);
                    WorkOrderRemarksVO orderRemarksVO = new WorkOrderRemarksVO();
                    // 将字符串转为LocalDateTime
                    LocalDateTime localDateTime = LocalDateTime.parse(contentIndex.getReply_time(), formatter);

                    // 将LocalDateTime转为java.util.Date
                    Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
                    orderRemarksVO.setOperatorName(contentIndex.getReply_person());
                    orderRemarksVO.setCreateTime(date);
                    orderRemarksVO.setOperationLogReason(contentIndex.getContent());
                    list.add(orderRemarksVO);
                }
            }
        } catch (Exception e) {
            log.info("查询工单备注报错", e);
        }
        return AjaxResult.ok(list);
    }

   /* @Override
    public AjaxResult ticketSatisfaction(WorkOrderSatisfactionVo workOrderSatisfactionVo) {
        String contactId = workOrderSatisfactionVo.getContactId();
        //根据contactId去crm_agent_work_record_detail表中查询工单Id(考虑到测试环境有脏数据，所以限制取一条)
        LambdaQueryWrapper<CrmAgentWorkRecordDetail> eq = new LambdaQueryWrapper<CrmAgentWorkRecordDetail>()
                .eq(CrmAgentWorkRecordDetail::getContactId, contactId)
                .eq(CrmAgentWorkRecordDetail::getDataStatus, Constants.NORMAL)
                .last("LIMIT 1");
        CrmAgentWorkRecordDetail crmAgentWorkRecordDetail = crmAgentWorkRecordDetailService.getBaseMapper().selectOne(eq);
        if (null == crmAgentWorkRecordDetail) {
            log.info("入参contactId【" + contactId + "】数据无效，根据这个参数在数据库中查询不到对应的工单Id");
            return AjaxResult.failure("您的入参contactId数据无效");
        }
        String workRecordId = crmAgentWorkRecordDetail.getWorkRecordId();
        //满意度评分是0-5分
        BigDecimal rating = workOrderSatisfactionVo.getRating();
        if (rating.compareTo(BigDecimal.ZERO) < 0 || rating.compareTo(BigDecimal.valueOf(5)) > 0) {
            return AjaxResult.failure("满意度评分值必须在0到5之间");
        }
        CrmAgentWorkRecordSatisfaction crmAgentWorkRecordSatisfaction = new CrmAgentWorkRecordSatisfaction();
        BeanUtils.copyProperties(workOrderSatisfactionVo, crmAgentWorkRecordSatisfaction);
        crmAgentWorkRecordSatisfaction.setId(IdWorker.get32UUID());
        crmAgentWorkRecordSatisfaction.setWorkRecordId(workRecordId);
        crmAgentWorkRecordSatisfaction.setCommentTime(new Date());
        crmAgentWorkRecordSatisfactionService.save(crmAgentWorkRecordSatisfaction);
        //还需要存储操作记录
        CrmAgentWorkRecordOperationLog crmAgentWorkRecordOperationLog = new CrmAgentWorkRecordOperationLog();
        crmAgentWorkRecordOperationLog.setOperationLogId(IdWorker.get32UUID());
        crmAgentWorkRecordOperationLog.setOperationLogType(TicketOperationTypeEnum.TICKET_SATISFACTION_RATING.getCode());
        crmAgentWorkRecordOperationLog.setOperationLogReason(workOrderSatisfactionVo.getRating().toString());
        crmAgentWorkRecordOperationLog.setOperatorName(workOrderSatisfactionVo.getCommentUser());
        crmAgentWorkRecordOperationLog.setWorkRecordId(workRecordId);
        crmAgentWorkRecordOperationLog.setDataStatus(Constants.NORMAL);
        crmAgentWorkRecordOperationLog.setCreateTime(new Date());
        crmAgentWorkRecordOperationLog.setModifyTime(new Date());
        this.save(crmAgentWorkRecordOperationLog);
        return AjaxResult.ok();
    }*/

    @Override
    public AjaxResult<IPage<WorkOrderRemarksVO>> queryByTicketRemarksPage(IPage<Object> pageParam, String workRecordId) {

        String indexName = workContentIndex+SecurityUtil.getLoginUser().getCompanyId();
        List<QueryCondition> queryConditions = new ArrayList<>();
        queryConditions.add(createCondition("work_record_id", workRecordId));
        queryConditions.add(createCondition("reply_type", 5));//条件查询
        queryConditions.add(createCondition("reply_time", SortOrder.DESC));//排序
        queryConditions.add(createCondition(pageParam));//分页
        IPage<TicketContentIndex> ticketContentIndex=elasticsearchUtil.searchPageQuery(queryConditions,indexName,TicketContentIndex.class);

        List<WorkOrderRemarksVO> workOrderRemarksVOList=new ArrayList<>();
        ticketContentIndex.getRecords().forEach(ticketContent->{
            WorkOrderRemarksVO workOrderRemarksVO = new WorkOrderRemarksVO();
            workOrderRemarksVO.setOperatorName(ticketContent.getReply_person());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"); // 根据实际情况调整格式
            LocalDateTime localDateTime = LocalDateTime.parse(ticketContent.getReply_time(), formatter);
            workOrderRemarksVO.setCreateTime(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            workOrderRemarksVO.setOperationLogReason(ticketContent.getContent());
            workOrderRemarksVOList.add(workOrderRemarksVO);
        });
        IPage<WorkOrderRemarksVO> remarksVOPage=new Page<>();
        remarksVOPage.setRecords(workOrderRemarksVOList);
        remarksVOPage.setTotal(ticketContentIndex.getTotal());
        remarksVOPage.setSize(ticketContentIndex.getSize());
        remarksVOPage.setCurrent(ticketContentIndex.getCurrent());
        return AjaxResult.ok(remarksVOPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> ticketSatisfactionToEs(WorkOrderSatisfactionVo workOrderSatisfactionVo) throws Exception {
        String companyId = workOrderSatisfactionVo.getCompanyId();
        String indexName = ticketIndex + companyId;
        log.info("开始处理工单满意度，companyId={}, indexName={}", companyId, indexName);

        boolean indexExists = headIndexExists(indexName);
        log.info("索引是否存在：{}", indexExists);

        if (!indexExists) {
            log.info("索引不存在，创建索引：{}", indexName);
            createIndex(indexName);
            return AjaxResult.ok();
        }

        BigDecimal rating = workOrderSatisfactionVo.getRating();
        log.info("收到评分：{}", rating);
        if (rating.compareTo(BigDecimal.ZERO) < 0 || rating.compareTo(BigDecimal.valueOf(5)) > 0) {
            log.info("评分值非法：{}", rating);
            return AjaxResult.failure("满意度评分值必须在0到5之间");
        }

        String contactId = workOrderSatisfactionVo.getContactId();
        List<WorkOrderRecordEsResult> esResultList = new ArrayList<>();
        String finalWorkRecordId;
        if (StringUtil.isNotEmpty(contactId)) {
            log.info("优先用contactId查询，contactId={}", contactId);
            List<TicketContentIndex> ticketContentIndexList = queryWorkOrderRecordContentFromEs(companyId, contactId);
            if (CollectionUtils.isEmpty(ticketContentIndexList)) {
                log.info("contactId[{}]无效，未查到工单信息", contactId);
                return AjaxResult.failure("您的入参contactId数据无效");
            }
            String queryWorkRecordId = ticketContentIndexList.get(0).getWork_record_id();
            log.info("根据contactId查到workRecordId={}", queryWorkRecordId);
            esResultList = queryWorkOrderRecordFromEs(queryWorkRecordId, indexName);
            if (CollectionUtils.isEmpty(esResultList)) {
                log.info("workRecordId[{}]无效，未查到工单信息", queryWorkRecordId);
                return AjaxResult.failure("您的入参contactId数据无效");
            }
            finalWorkRecordId = queryWorkRecordId;
        } else {
            String workRecordId = workOrderSatisfactionVo.getWorkRecordId();
            log.info("contactId为空，使用workRecordId查询，workRecordId={}", workRecordId);
            if (StringUtil.isEmpty(workRecordId)) {
                log.info("workRecordId为空，无法处理");
                return AjaxResult.failure("在入参contactId为空的前提下，需要用到入参workRecordId，这个入参也为空，请检查入参");
            }
            esResultList = queryWorkOrderRecordFromEs(workRecordId, indexName);
            if (CollectionUtils.isEmpty(esResultList)) {
                log.info("workRecordId[{}]无效，未查到工单信息", workRecordId);
                return AjaxResult.failure("您的入参workRecordId数据无效");
            }
            finalWorkRecordId = workRecordId;
        }

        WorkOrderRecordEsResult crmAgentWorkRecordDetail = esResultList.get(0);
        List<TicketSatisfaction> ticketSatisfactionList = crmAgentWorkRecordDetail.getTicketSatisfaction();
        if (ticketSatisfactionList == null) {
            log.info("原有ticketSatisfactionList为null，初始化为空集合");
            ticketSatisfactionList = new ArrayList<>();
        }
        TicketSatisfaction ticketSatisfaction = new TicketSatisfaction();
        ticketSatisfaction.setRating(workOrderSatisfactionVo.getRating().doubleValue());
        ticketSatisfaction.setCommentContent(workOrderSatisfactionVo.getCommentContent());
        ticketSatisfaction.setCommentUser(workOrderSatisfactionVo.getCommentUser());
        ticketSatisfaction.setCommentTime(LocalDateTime.now());
        if(StringUtil.isNotEmpty(workOrderSatisfactionVo.getContactId())){
            ticketSatisfaction.setContactId(workOrderSatisfactionVo.getContactId());
        }
        ticketSatisfactionList.add(ticketSatisfaction);
        log.info("更新ticketSatisfactionList，当前总数={}", ticketSatisfactionList.size());

        String docId = crmAgentWorkRecordDetail.getDocId();
        log.info("准备更新ES满意度数据，indexName={}, workRecordId={}, docId={}", indexName, finalWorkRecordId, docId);
        updateTicketSatisfaction(indexName, finalWorkRecordId, ticketSatisfactionList, docId);

        List<TicketOperationLog> ticketOperationLogList = crmAgentWorkRecordDetail.getTicketOperationLog();
        if (ticketOperationLogList == null) {
            log.info("原有ticketOperationLogList为null，初始化为空集合");
            ticketOperationLogList = new ArrayList<>();
        }
        TicketOperationLog crmAgentWorkRecordOperationLog = new TicketOperationLog();
        crmAgentWorkRecordOperationLog.setOperationLogType(TicketOperationTypeEnum.TICKET_SATISFACTION_RATING.getCode());
        crmAgentWorkRecordOperationLog.setOperationLogReason(workOrderSatisfactionVo.getRating().toString());
        crmAgentWorkRecordOperationLog.setOperatorName(workOrderSatisfactionVo.getCommentUser());
        crmAgentWorkRecordOperationLog.setOperatorTime(LocalDateTime.now());
        ticketOperationLogList.add(crmAgentWorkRecordOperationLog);
        log.info("更新ticketOperationLogList，当前总数={}", ticketOperationLogList.size());

        log.info("准备更新ES操作日志，indexName={}, workRecordId={}, docId={}", indexName, finalWorkRecordId, docId);
        updateTicketOperationLog(indexName, finalWorkRecordId, ticketOperationLogList, docId);

        log.info("处理完成，companyId={}, workRecordId={}, docId={}", companyId, finalWorkRecordId, docId);
        return AjaxResult.ok();
    }

    //从内容表中查询数据
    private List<TicketContentIndex> queryWorkOrderRecordContentFromEs(String companyId, String contactId) throws Exception {
        List<TicketContentIndex> recordList = new ArrayList<>();
        // 定义工单表索引名称
        String indexName = workContentIndex + companyId;
        // 查询索引是否存在
        boolean indexExists = headIndexExists(indexName);
        // 索引不存在直接创建索引
        if (!indexExists) {
            createIndex(indexName);
            return recordList;
        }
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("contact_id", contactId));
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                TicketContentIndex result = objectMapper.readValue(json, TicketContentIndex.class);
                recordList.add(result);
            }
        }
        return recordList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Object> phoneTicketSatisfaction(PhoneWorkOrderSatisfactionVo phoneWorkOrderSatisfactionVo) throws Exception {
        // 首先根据connectId查询公司的ID
        CrmAwsConnect awsConnect = crmAwsConnectService.getById(phoneWorkOrderSatisfactionVo.getConnectId());
        // 定义满意度评价需要参数
        WorkOrderSatisfactionVo workOrderSatisfactionVo = new WorkOrderSatisfactionVo();
        workOrderSatisfactionVo.setContactId(phoneWorkOrderSatisfactionVo.getContactId());
        workOrderSatisfactionVo.setCommentUser(phoneWorkOrderSatisfactionVo.getCommentUser());
        workOrderSatisfactionVo.setRating(phoneWorkOrderSatisfactionVo.getRating());
        workOrderSatisfactionVo.setCompanyId(awsConnect.getCompanyId());
        // 进行满意度评价操作
        return ticketSatisfactionToEs(workOrderSatisfactionVo);
    }

    private List<WorkOrderRecordEsResult> queryWorkOrderRecordFromEs(String workRecordId, String indexName) throws Exception {
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("work_record_id", workRecordId));
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        List<WorkOrderRecordEsResult> recordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult result = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex, result);
                String docId = hit.getId();
                result.setDocId(docId);
                recordList.add(result);
            }
        }
        return recordList;
    }

    /**
     * 验证索引是否存在
     *
     * @param index 索引名称
     * @return 存在状态
     * @throws IOException
     */
    private boolean headIndexExists(String index) {
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        } catch (Exception e) {
            log.error("验证索引失败:", e);
        }
        return false;
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        } catch (Exception e) {
            log.error("创建索引报错", e);
        }
    }

    private void updateTicketSatisfaction(String indexName, String workRecordId, List<TicketSatisfaction> ticketSatisfactionList,String docId) throws IOException {
        // 将 List<TicketSatisfaction> 转换为 List<Map<String, Object>>
        List<Map<String, Object>> ticketSatisfactionMaps = ticketSatisfactionList.stream()
                .map(this::convertToMap)
                .collect(Collectors.toList());

        // 构建更新脚本
        String script = "if (ctx._source.work_record_id == params.work_record_id) { " +
                "ctx._source.ticket_satisfaction = params.ticket_satisfaction; }";

        // 构建参数
        Map<String, Object> params = new HashMap<>();
        params.put("work_record_id", workRecordId);
        params.put("ticket_satisfaction", ticketSatisfactionMaps);

        // 创建 UpdateRequest
        UpdateRequest updateRequest = new UpdateRequest(indexName, docId)
                .script(new Script(ScriptType.INLINE, "painless", script, params));
        // 执行更新请求
        try {
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es更新字段数据失败，异常信息：", e);
                throw new RuntimeException(e);
            }
        }
    }

    private void updateTicketOperationLog(String indexName, String workRecordId, List<TicketOperationLog> ticketOperationLogList,String docId) throws IOException {
        List<Map<String, Object>> maps = ticketOperationLogList.stream()
                .map(this::convertLogToMap)
                .collect(Collectors.toList());

        // 构建更新脚本
        String script = "if (ctx._source.work_record_id == params.work_record_id) { " +
                "ctx._source.ticket_operation_log = params.ticket_operation_log; }";

        // 构建参数
        Map<String, Object> params = new HashMap<>();
        params.put("work_record_id", workRecordId);
        params.put("ticket_operation_log", maps);

        // 创建 UpdateRequest
        UpdateRequest updateRequest = new UpdateRequest(indexName, docId)
                .script(new Script(ScriptType.INLINE, "painless", script, params));
        // 执行更新请求
        try {
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                log.error("es更新字段数据失败，异常信息：", e);
                throw new RuntimeException(e);
            }
        }
    }

    private Map<String, Object> convertToMap(TicketSatisfaction ticketSatisfaction) {
//        ObjectMapper objectMapper = new ObjectMapper()
//                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE) // 设置下划线命名策略
//                .registerModule(new JavaTimeModule()); // 注册 JavaTimeModule 以支持 Java 8 时间类型
//        return objectMapper.convertValue(ticketSatisfaction, Map.class);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));

        ObjectMapper objectMapper = new ObjectMapper()
                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                .registerModule(javaTimeModule)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS); // 禁用时间戳格式
        return objectMapper.convertValue(ticketSatisfaction, Map.class);
    }

    private Map<String, Object> convertLogToMap(TicketOperationLog ticketOperationLog) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));

        ObjectMapper objectMapper = new ObjectMapper()
                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                .registerModule(javaTimeModule)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS); // 禁用时间戳格式
        return objectMapper.convertValue(ticketOperationLog, Map.class);
    }
}




