package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordFinalEsResult;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordRequest;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.domain.ExtOptionVO;
import com.goclouds.crm.platform.openfeignClient.domain.aigc.AigcGeneratorContent;
import com.goclouds.crm.platform.openfeignClient.domain.call.AgentUpdateTicketVO;
import com.goclouds.crm.platform.openfeignClient.domain.call.AgentWorkRecordVo;
import com.goclouds.crm.platform.openfeignClient.domain.call.GetContentDetailsVO;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record(工作记录)】的数据库操作Service
* @createDate 2023-05-25 10:27:34
*/
public interface CrmAgentWorkRecordService extends IService<CrmAgentWorkRecord> {


    AjaxResult queryWorkRecordInfo (String workRecordId , String channelTypeId);


    AjaxResult syncCallRecord(SyncCallRecordDTO syncCallRecordDTO);

    IPage<WorkOrderRecordResponseVO> queryWorkOrder(IPage<Object> pageParam, WorkOrderRecordRequestVO workOrderRecordRequest);

    AjaxResult<Object> updateWork(UpdateWorkOrderRecordVO updateWorkOrderRecordVO);

    /**
     *  查询工单详情
     * @param workRecordId 工单ID
     * @param type 1、 工单列表查看详情 2、 工作台查看详情
     * @return 工单详情
     */
    WorkRecordDetailVo workOrderDetail(String workRecordId, int type) throws IOException;

    /**
     *  终止工单
     * @param workOrderUpdateStatusVO 请求参数
     * @return 处理状态
     */
    AjaxResult<Object> solveWorkStatus(WorkOrderUpdateStatusVO workOrderUpdateStatusVO);

    /**
     * 终止工单
     * @param workOrderUpdateStatusVO 请求参数
     * @return 处理状态
     */
    AjaxResult<Object> terminationWorkStatus(WorkOrderUpdateStatusVO workOrderUpdateStatusVO);

    /**
     *  指派工单
     * @param workOrderAssignVO 指派工单参数
     * @return 指派返回状态
     */
    AjaxResult<Object> assignWorkOrder(WorkOrderAssignVO workOrderAssignVO);

    /**
     *  转派工单
     * @param workOrderAssignVO 转派工单参数
     * @return 转派返回状态
     */
    AjaxResult<Object> transferWorkOrder(WorkOrderAssignVO workOrderAssignVO);

    /**
     *  催办工单
     * @param workOrderUpdateStatusVO  催办工单参数
     * @return 催办状态
     */
    AjaxResult<Object> urgingWorkOrder(WorkOrderUpdateStatusVO workOrderUpdateStatusVO);


    /**
     *  工单升级
     * @param workOrderUpgrade 升级请求参数
     * @return 操作状态
     */
    AjaxResult<Object> workUpgrade(WorkOrderUpgrade workOrderUpgrade);

    /**
     *  工单备注
     * @param workOrderUpdateStatusVO 工单备注请求参数
     * @return 操作状态
     */
    AjaxResult<Object> workRemark(WorkOrderUpdateStatusVO workOrderUpdateStatusVO);


    /**
     *  查询关联工单操作中工单列表
     * @param workRecordId 工单id
     * @return 关联工单操作中工单列表
     */
    AjaxResult<IPage<WorkOrderRecordResponseVO>> associationWorkOrderTable(String workRecordId, String workRecordInfo, IPage<Object> pageParam);

    /**
     *  标记为已解决
     * @param workOrderBatchSolveVO 多个工单ID
     * @return 操作状态
     */
    AjaxResult<Object> batchSolveWork(WorkOrderBatchSolveVO workOrderBatchSolveVO);

    /**
     *  认领工单
     * @param workOrderClaimVO 认领工单参数
     * @return 认领返回状态
     */
    AjaxResult<Object> claimWorkOrder(WorkOrderClaimVO workOrderClaimVO);

    /**
     * 导出customer list
     * @param workOrderRecordRequest 请求参数
     * @return
     */
    void exportTicket(WorkOrderRecordRequestVO workOrderRecordRequest);

    AjaxResult uploadFile(MultipartFile file);
    AjaxResult uploadFileByUrl(String content, String fileName);

    AjaxResult downloadFile(ContentFile file);


    AjaxResult uploadPictureToPublic(MultipartFile file);

    AjaxResult deleteFile(ContentFile file);

    BoolQueryBuilder queryConditionMontage(WorkOrderRecordRequestVO workOrderRecordRequest);

    List<WorkOrderRecordResponseVO> ticketDataHandle(List<WorkOrderRecordResponseVO> list, Integer queryType);

    AjaxResult voiceAnalysis(String contactId);

    /**
     *  工单内容总结
     * @param workSummarizeVO 请求参数
     * @return 返回结果
     */
    AjaxResult<Object> workSummarize(WorkSummarizeVO workSummarizeVO);

    /**
     *  查询各个状态的工单数量
     * @param workOrderRecordRequest 选择的工单类型
     * @return 工单数量
     */
    AjaxResult<List<WorkOrderNumberVO>> queryWorkOrderNumber(WorkOrderRecordRequestVO workOrderRecordRequest);

    IPage<HistoryCustomerWorkOrderVO> queryHistoryCustomerWorkOrder(IPage<Object> pageParam, String customerId,String workRecordId,String workInfo);

    AjaxResult<List<WorkRecordAutoMergeVo>> queryTicketRecordAutoMerge();

    AjaxResult updateTicketRecordAutoMerge(List<CrmAgentWorkRecordAutoMerge> workRecordAutoMergeVoList);

    AjaxResult queryWorkOrderIdByContactId(String contactId);

    /**
     * 查询工单详情
     * @param workRecordId 工单id
     * @return 工单详情
     */
    WorkRecordDetailVo innerAuthWorkOrderDetail(String workRecordId, String companyId);

    /**
     *  添加工单聊天记录
     * @param ticketContentIndex 工单聊天记录
     * @throws Exception
     */
    void addAgentWorkRecordContent(TicketContentIndex ticketContentIndex, String companyId);

    /**
     * 工单变更操作
     * @param updateWorkOrderRecordVO
     * @return
     */
    AjaxResult<Object> updateWorkStatus(UpdateWorkOrderRecordVO updateWorkOrderRecordVO);

    /**
     * 新工作台-查询工单列表
     * @return
     */
    IPage<WorkOrderRecordFinalEsResult> queryWorkBenchWorkOrderList(IPage<Object> pageParam, WorkOrderRecordRequest workOrderRecordRequest);

    /**
     *  工单绑定座席
     * @param workOrderClaimVO 工单绑定座席参数
     * @return 工单绑定座席
     */
    AjaxResult<Object> workBindingAgent(WorkOrderClaimVO workOrderClaimVO);

    /**
     *
     * @param workSummarizeVO
     * @return
     */
    AjaxResult<Object> saveWorkSummarize(WorkSummarizeVO workSummarizeVO) throws IOException;

    /* * 新增待办事项 保存es
     * @param addWaitExecuteVO 请求参数
     * @return 返回状态
     */
    AjaxResult<Object> saveWaitExecute(SaveWaitExecuteVO saveWaitExecuteVO) throws IOException;

    /**
     *  修改acw状态
     * @param acwStatusVo 请求参数
     * @return 修改状态
     */
    AjaxResult<Object> updateSessionStatus(SessionStatusVo acwStatusVo);

    IPage<WorkOrderRecordFinalEsResult> queryEmailWorkOrderList(IPage<Object> pageParam, WorkOrderRecordRequest workOrderRecordRequest);

    /**
     * 查询工单详细信息 包含扩展字段
     * @param workRecordId workRecordId
     * @return AjaxResult
     */
    AjaxResult<AgentWorkRecordVo> innerQueryWorkRecordById(String workRecordId, String companyId);



    /**
     * 关注工单
     * @param workRecordId
     * @return
     */
    AjaxResult<Object> concernedWorkOrder(String workRecordId,Integer status);

    /**
     * 工单关联操作
     * @param updateWorkAssociationVO
     * @return
     */
    AjaxResult<Object> updateWorkAssociation(UpdateWorkAssociationVO updateWorkAssociationVO);

    AjaxResult queryEmailTotalCount() throws Exception;

    AjaxResult updateReadStatus(String workRecordId);

    AjaxResult queryWorkBenchTicketTotalCount() throws Exception;

    IPage<WorkOrderRecordResponseVO> associationWorkOrder(String workRecordId, IPage<Object> pageParam);

    AjaxResult createWorkOrderTitle(AigcGeneratorContent workOrderUpdateVO);

    AjaxResult createWorkOrderSmartSummary(AigcGeneratorContent workOrderUpdateVO);

    /**
     *  工作台分配工单
     * @param workOrderAssignVO 分配工单参数
     * @return 指派返回状态
     */
    AjaxResult<Object> allocationTicket(WorkOrderAssignVO workOrderAssignVO);

    IPage<WorkOrderRecordFinalEsResult> queryRobotWorkOrderList(IPage<Object> pageParam, WorkOrderRecordRequest workOrderRecordRequest);

    AjaxResult queryRobotTicketTotalCount() throws Exception;

    Boolean agentUpdateTicket(AgentUpdateTicketVO agentUpdateCustomerVO);

    /**
     * 进行redis的工单信息重新缓存
     * @param workRedisCacheRefreshVO
     * @return
     */
    List<ScheduleWorkVO> cacheRefresh(WorkRedisCacheRefreshVO workRedisCacheRefreshVO);

    AjaxResult<List<ExtOptionVO>> ticketExtOption();

    AjaxResult<WorkRecordDetailsVO> getDetails(String workRecordId);

    AjaxResult<WorkRecordDetailsVO> getDetailsForAIAssessment(String workRecordId, String companyId);

    AjaxResult getContentDetails(GetContentDetailsVO getContentDetailsVO);

    AjaxResult updateContentDetails(UpdateContentDetailsVO updateContentDetailsVOS);

    AjaxResult isTop(WorkRecordTopVO workRecordTopVO);

    AjaxResult getMultipartFile(String fileUrl);
}
