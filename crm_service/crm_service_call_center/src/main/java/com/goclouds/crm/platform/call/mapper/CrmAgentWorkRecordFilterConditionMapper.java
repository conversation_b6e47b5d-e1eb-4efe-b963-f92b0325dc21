package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilterCondition;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordFilterConditionResponseVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_filter_condition(工单筛选器条件;)】的数据库操作Mapper
* @createDate 2023-09-19 15:10:09
* @Entity generator.domain.CrmAgentWorkRecordFilterCondition
*/
public interface CrmAgentWorkRecordFilterConditionMapper extends BaseMapper<CrmAgentWorkRecordFilterCondition> {

    @Select("select cawrfc.work_record_filter_condition_id, cawrfc.work_record_ext_def_code, cawrfc.work_record_ext_def_value, cawred.property_type_id from crm_agent_work_record_filter_condition as cawrfc " +
            "left join crm_agent_work_record_ext_def cawred on cawrfc.work_record_ext_def_code = cawred.work_record_ext_def_code " +
            "where work_record_filter_id = #{workRecordFilterId} and cawrfc.data_status = 1 and cawred.company_id = #{companyId} and cawred.language_code = #{language} " +
            "order by cawrfc.create_time")
    List<WorkRecordFilterConditionResponseVO> queryFilterCondition(@Param("workRecordFilterId")String workRecordFilterId, @Param("companyId")String companyId,@Param("language") String language);
}




