package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmChannelConfig;
import com.goclouds.crm.platform.call.domain.SysUser;
import com.goclouds.crm.platform.call.domain.vo.ChannelConfigMappingDTO;
import com.goclouds.crm.platform.call.domain.vo.ChannelConfigVo;
import com.goclouds.crm.platform.call.mapper.CrmChannelConfigMapper;
import com.goclouds.crm.platform.call.service.CrmChannelConfigService;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
* <AUTHOR>
* @description 针对表【crm_channel_config(平台渠道配置)】的数据库操作Service实现
* @createDate 2023-05-25 11:04:45
*/
@Service
public class CrmChannelConfigServiceImpl extends ServiceImpl<CrmChannelConfigMapper, CrmChannelConfig>
    implements CrmChannelConfigService {


    @Override
    public List<ChannelConfigMappingDTO> getChannelMappingRules(String instanceId) {
        return this.baseMapper.getChannelMappingRules(instanceId);
    }

}




