package com.goclouds.crm.platform.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordOperationLog;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderRemarksVO;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordOperationLogVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_operation_log(工单操作日志记录;)】的数据库操作Mapper
* @createDate 2023-09-28 14:33:26
* @Entity generator.domain.CrmAgentWorkRecordOperationLog
*/
public interface CrmAgentWorkRecordOperationLogMapper extends BaseMapper<CrmAgentWorkRecordOperationLog> {

    @Select("select * from crm_agent_work_record_operation_log " +
            "where data_status = 1 and work_record_id = #{workRecordId} " +
            "order by create_time desc ")
    List<WorkRecordOperationLogVO> WorkOrderOperationRecords(@Param("workRecordId") String workRecordId);

    @Select("select operation_log_reason, operator_name, create_time from crm_agent_work_record_operation_log\n" +
            "where work_record_id = #{workRecordId} and data_status = 1 and operation_log_type = 10 order by create_time desc ")
    List<WorkOrderRemarksVO> queryByTicketRemarks(@Param("workRecordId") String workRecordId);

    @Select("select operation_log_reason, operator_name, create_time from crm_agent_work_record_operation_log\n" +
            "where work_record_id = #{workRecordId} and data_status = 1 and operation_log_type = 10 order by create_time desc ")
    IPage<WorkOrderRemarksVO> queryByTicketRemarksPage(IPage<Object> pageParam,@Param("workRecordId")  String workRecordId);
}




