package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordAutoMerge;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordAutoMergeVo;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordAutoMergeService;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordAutoMergeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_auto_merge(是否开启合并工单表)】的数据库操作Service实现
* @createDate 2024-02-20 14:17:14
*/
@Service
public class CrmAgentWorkRecordAutoMergeServiceImpl extends ServiceImpl<CrmAgentWorkRecordAutoMergeMapper, CrmAgentWorkRecordAutoMerge>
    implements CrmAgentWorkRecordAutoMergeService{

    @Override
    public List<WorkRecordAutoMergeVo> queryAutoMergeSetting(String companyId) {
        return this.baseMapper.queryAutoMergeSetting(companyId);
    }
}




