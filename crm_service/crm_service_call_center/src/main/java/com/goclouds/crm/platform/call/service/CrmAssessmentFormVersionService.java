package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAssessmentFormVersion;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentFormVersionVo;

import java.util.List;

/**
 * 评估表版本Service接口
 */
public interface CrmAssessmentFormVersionService extends IService<CrmAssessmentFormVersion> {


    String saveVersion(CrmAssessmentFormVersionVo versionVo,Boolean deploy);

    List<CrmAssessmentFormVersion> getVersionList(String assessmentId);
}