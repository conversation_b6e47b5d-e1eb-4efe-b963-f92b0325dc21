package com.goclouds.crm.platform.call.strategy;

import com.goclouds.crm.platform.call.domain.smartFill.AddAigcTicketSmartFillRequest;
import com.goclouds.crm.platform.call.domain.smartFill.AigcTicketSmartFillDTO;

/**
 * <AUTHOR> pengliang.sun
 * @description : 智能填单保存策略类
 */
public interface AigcTicketSmartFillStrategy {


    AigcTicketSmartFillEnum aigcTicketSmartFillEnum();

    /**
     * 保存智能填单
     */
    void addAigcTicketSmartFillStrategy(AigcTicketSmartFillDTO aigcTicketSmartFillDetail, String customerId, String workRecordId);
}
