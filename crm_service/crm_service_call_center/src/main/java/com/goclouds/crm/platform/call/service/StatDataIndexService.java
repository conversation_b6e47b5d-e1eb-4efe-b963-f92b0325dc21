package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.statis.KinesisContactVo;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.io.IOException;
import java.text.ParseException;

/**
 * @Description:
 * @Author: zhumengyang
 * @Date: 2024/07/03/11:02
 */
public interface StatDataIndexService {

    /**
     * 统计-数据明细查询
     * @return 数据明细页
     * @param isPage 是否分页，分页：查询用：不分页：导出excel用
     */
    AjaxResult<IPage<StatDataDetailsVO>> queryStatDataDetails(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean isPage) throws Exception;

    /**
     * 统计-数据明细-excel导出
     * @return 数据明细导出
     */
    void exportDataDetails(StatIndexDTO statIndexDTO, boolean isPage);

    /**
     * 统计-座席工作指标-分页查询
     * @return 座席工作指标页
     * @param isPage 是否分页，分页：查询用：不分页：导出excel用
     */
    AjaxResult<IPage<StatAgentIndexVO>> queryStatAgentIndex(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean isPage) throws Exception;
    /**
     * 统计-座席指标-excel导出
     * @return 座席指标导出
     */
    void exportAgentIndex(StatIndexDTO statIndexDTO, boolean isPage);

    /**
     * 统计-队列指标-分页查询
     * @return 队列指标页
     * @param isPage 是否分页，分页：查询用：不分页：导出excel用
     */
    AjaxResult<IPage<StatQueueIndexVO>> queryStatQueueIndex(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean isPage) throws Exception;

    /**
     * 统计-队列指标-excel导出
     * @return 队列指标导出
     */
    void exportQueueIndex(StatIndexDTO statIndexDTO, boolean isPage);

    AjaxResult<IPage<KinesisContactVo>> queryStatDataDetailsNew(IPage<Object> pageParam, StatIndexDTO statIndexDTO, boolean b, Integer queryType) throws IOException, ParseException;

    AjaxResult<Object> addPhoneContactDetail(ContactDetailPhoneAddVo detailAddVo);

    AjaxResult<Object> addChannelContactDetail(ContactDetailChannelAddVo detailAddVo);

    AjaxResult<Object> contactDetailsUpdate();
}
