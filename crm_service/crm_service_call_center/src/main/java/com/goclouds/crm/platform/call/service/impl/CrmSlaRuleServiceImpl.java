package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmSlaRule;
import com.goclouds.crm.platform.call.service.CrmSlaRuleService;
import com.goclouds.crm.platform.call.mapper.CrmSalRuleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_sal_rule(规则表)】的数据库操作Service实现
* @createDate 2025-03-13 16:45:32
*/
@Service
@RequiredArgsConstructor
public class CrmSlaRuleServiceImpl extends ServiceImpl<CrmSalRuleMapper, CrmSlaRule>
    implements CrmSlaRuleService {

}




