package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecord;
import com.goclouds.crm.platform.call.domain.TicketContentIndex;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.statis.*;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordMapper;
import com.goclouds.crm.platform.call.service.WorkOrderStatisticsService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.domain.QueryCondition;
import com.goclouds.crm.platform.common.enums.CrmChannelEnum;
import com.goclouds.crm.platform.common.enums.QueryTypeEnum;
import com.goclouds.crm.platform.common.enums.UserRoleEnum;
import com.goclouds.crm.platform.openfeignClient.client.system.UserClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysDeptVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.UserDetailsVO;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.lucene.search.join.ScoreMode;
import org.apache.poi.ss.formula.functions.T;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.filter.Filter;
import org.elasticsearch.search.aggregations.bucket.nested.Nested;
import org.elasticsearch.search.aggregations.bucket.nested.NestedAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Avg;
import org.elasticsearch.search.aggregations.metrics.AvgAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ValueCount;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortMode;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.goclouds.crm.platform.common.domain.QueryCondition.createCondition;

/**
 * @description:
 * @author: sunlinan
 **/

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkOrderStatisticsServiceImpl implements WorkOrderStatisticsService {

    private final UserClient userClient;
    private final CrmAgentWorkRecordMapper crmAgentWorkRecordMapper;
    @Value("${es.agent-log-index}")
    private String agentLogindex;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    private final RestHighLevelClient restHighLevelClient;


    @Override
    public List<SysDeptVo> queryCallCenterDeptList() {
        List<SysDeptVo> list = Lists.newArrayList();
        R<List<SysDeptVo>> listR = userClient.queryCallCenterDeptList();
        if (listR.getCode() == AjaxResult.SUCCESS && CollectionUtils.isNotEmpty(listR.getData())) {
            return listR.getData();
        }
        return list;
    }

    @Override
    public AjaxResult<Object> queryAgentWorkloadSummarize(SeatingWorkloadNewVo seatingWorkloadVo) {

        // 获取登录用户，判断是管理员还是座席管理员
        String roleId = SecurityUtil.getLoginUser().getRoleList().get(0).getRoleId();
        // 部门id
        String deptId = SecurityUtil.getLoginUser().getDeptId();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 计算出对应天数之前的数据
        // 解析字符串为LocalDate
        LocalDateTime startDate = LocalDateTime.parse(seatingWorkloadVo.getStartDate(), formatter);
        LocalDateTime endDate = LocalDateTime.parse(seatingWorkloadVo.getEndDate(), formatter);
        // 计算两者之间的天数
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        // 分别减去对应天数
        LocalDateTime newStartDate = startDate.minusDays(daysBetween);
        LocalDateTime newEndDate = endDate.minusDays(daysBetween);

        // 当前时间工单总数
        Integer nowWorkTotalNumber = queryWorkTotalNumber(startDate, endDate, roleId, deptId, 1);
        // 当前时间未完成工单总数
        Integer nowIncompleteWorkTotalNumber = queryWorkTotalNumber(startDate, endDate, roleId, deptId, 2);

        // 之前时间工单总数
        Integer oldWorkTotalNumber = queryWorkTotalNumber(newStartDate, newEndDate, roleId, deptId, 1);
        // 之前时间未完成工单总数
        Integer oldIncompleteWorkTotalNumber = queryWorkTotalNumber(newStartDate, newEndDate, roleId, deptId, 2);

        // 计算两个数据比例
        BigDecimal workTotal = calculatePercentageChange(nowWorkTotalNumber, oldWorkTotalNumber);
        BigDecimal incompleteWorkTotal = calculatePercentageChange(nowIncompleteWorkTotalNumber, oldIncompleteWorkTotalNumber);
        // 查询出当前公司下所有的坐席信息
        R<List<UserDetailsVO>> list = userClient.queryUserListByCompanyId(SecurityUtil.getLoginUser().getCompanyId());
        List<UserDetailsVO> userDetailList = list.getData();
        Map<String, UserDetailsVO> userDetailMap = userDetailList.stream()
                .collect(Collectors.toMap(UserDetailsVO::getUserId, userDetailsVO -> userDetailsVO));
        log.info("打印查询的坐席信息{}", userDetailList);
        // 当前座席或者座席组处理最高top3
        List<AgentWorkNumberVo> nowHighestNumberList = agentWorkNumberTo3(startDate, endDate, roleId, deptId, 1, userDetailMap);
        // 当前座席或者座席组处理最低top3
        List<AgentWorkNumberVo> nowMinimumNumberList = agentWorkNumberTo3(startDate, endDate, roleId, deptId, 2, userDetailMap);

        // 当前座席或者座席组处理的数据
        List<AgentWorkNumberVo> nowNumberList = agentWorkNumberTo3(startDate, endDate, roleId, deptId, 3, userDetailMap);
        // 之前座席或者座席组处理的数据
        List<AgentWorkNumberVo> oldNumberList = agentWorkNumberTo3(newStartDate, newEndDate, roleId, deptId, 3, userDetailMap);

        // 将list2转化为map，方便查找
        Map<String, AgentWorkNumberVo> map2 = nowNumberList.stream()
                .collect(Collectors.toMap(AgentWorkNumberVo::getAgentId, vo -> vo));

        // 创建新的列表来存储差值
        List<AgentWorkNumberVo> differenceList = new ArrayList<>();

        // 计算每个agentId的差值
        for (AgentWorkNumberVo vo1 : oldNumberList) {
            AgentWorkNumberVo vo2 = map2.get(vo1.getAgentId());
            if (vo2 != null) {
                int difference = vo2.getWorkNumber() - vo1.getWorkNumber();
                differenceList.add(new AgentWorkNumberVo(difference, vo1.getAgentId(), vo1.getAgentName()));
            }
        }

        // 上升TOP3
        List<AgentWorkNumberVo> topIncrease = differenceList.stream()
                .filter(item -> item.getWorkNumber() > 0)
                .sorted(Comparator.comparingInt(AgentWorkNumberVo::getWorkNumber).reversed())
                .limit(3)
                .map(item -> new AgentWorkNumberVo(item.getWorkNumber(), item.getAgentId(), item.getAgentName()))
                .collect(Collectors.toList());

        // 下降TO3
        List<AgentWorkNumberVo> topDecrease = differenceList.stream()
                .filter(item -> item.getWorkNumber() < 0)
                .sorted(Comparator.comparingInt(AgentWorkNumberVo::getWorkNumber))
                .limit(3)
                .map(item -> new AgentWorkNumberVo(item.getWorkNumber(), item.getAgentId(), item.getAgentName()))
                .collect(Collectors.toList());
        // 将下降参数取绝对值
        topDecrease.forEach(item -> {
            item.setWorkNumber(Math.abs(item.getWorkNumber()));
        });
        Map<String, Object> map = new HashMap<>();
        // 工单完成总数
        map.put("workTotalNumber", nowWorkTotalNumber);
        // 工单总数 比例
        map.put("workTotalProportion", workTotal);
        // 未完成工单总数
        map.put("incompleteWorkTotalNumber", nowIncompleteWorkTotalNumber);
        // 未完成比例
        map.put("incompleteWorkTotalProportion", incompleteWorkTotal);
        // 对比天数
        map.put("comparisonDay", daysBetween);
        // 最高工单座席或座席组名称
        map.put("highestName", !nowHighestNumberList.isEmpty() ? nowHighestNumberList.get(0).getAgentName() : "");
        // 最高工单座席或座席组数量
        map.put("highestNumber", !nowHighestNumberList.isEmpty() ? nowHighestNumberList.get(0).getWorkNumber() : 0);
        // 最低工单座席或座席组名称
        map.put("minimumName", !nowMinimumNumberList.isEmpty() ? nowMinimumNumberList.get(0).getAgentName() : "");
        // 最低工单座席或座席组数量
        map.put("minimumNumber", !nowMinimumNumberList.isEmpty() ? nowMinimumNumberList.get(0).getWorkNumber() : 0);
        // 上升最高名称
        map.put("riseHighestName", !topIncrease.isEmpty() ? topIncrease.get(0).getAgentName() : "");
        // 上升最高数量
        map.put("riseHighestNumber", !topIncrease.isEmpty() ? topIncrease.get(0).getWorkNumber() : 0);
        // 下降最高名称
        map.put("declineHighestName", !topDecrease.isEmpty() ? topDecrease.get(0).getAgentName() : "");
        // 下降最高数量
        map.put("declineHighestNumber", !topDecrease.isEmpty() ? topDecrease.get(0).getWorkNumber() : 0);
        // 最高工单座席或座席组
        map.put("highestNumberList", nowHighestNumberList);
        // 最低工单座席或座席组
        map.put("minimumNumberList", nowMinimumNumberList);
        // 上升最高工单座席或座席组
        map.put("riseHighestNumberList", topIncrease);
        // 下降最高工单座席或座席组
        map.put("declineNumberList", topDecrease);

        return AjaxResult.ok(map);
    }

    /**
     * 查询时间段内工单总数量
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param roleId    角色ID
     * @param deptId    部门id
     * @param queryType 查询类型 1 查询全部， 2查询未完成的
     * @return 工单数量
     */
    private Integer queryWorkTotalNumber(LocalDateTime startDate, LocalDateTime endDate, String roleId, String deptId, int queryType) {

        try {
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).lte(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            // 如果是座席管理员，则查询当前座席组的数据，也就是当前部门的数据
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
            }
            // 不查询机器人工单
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));

            // 如果为1则查询全部工单，但不包括已转单的，为2则查询未完成工单
            if (queryType == 1) {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("status", 5));
            } else {
                BoolQueryBuilder statusQuery = QueryBuilders.boolQuery()
                        .should(QueryBuilders.termQuery("status", 0))
                        .should(QueryBuilders.termQuery("status", 1))
                        .should(QueryBuilders.termQuery("status", 2))
                        .minimumShouldMatch(1);
                // 将 statusQuery 添加到主查询中
                boolQueryBuilder.must(statusQuery);
            }
            boolQueryBuilder.must(QueryBuilders.termQuery("data_status", 1));
            searchSourceBuilder.query(boolQueryBuilder);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            long value = response.getHits().getTotalHits().value;
            return Long.valueOf(value).intValue();
        } catch (Exception e) {
            log.error("座席工作量报表汇总查询工单ES报错:", e);
            return 0;
        }
    }

    /**
     * 查询时间段内工单总数量
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param roleId    角色ID
     * @param deptId    部门id
     * @param queryType 查询类型 1 最高TO3， 2最低TO3 3、工单对比数据
     * @return 工单数量
     */
    private List<AgentWorkNumberVo> agentWorkNumberTo3(LocalDateTime startDate, LocalDateTime endDate, String roleId, String deptId, int queryType, Map<String, UserDetailsVO> userDetailMap) {
        try {
            List<AgentWorkNumberVo> agentWorkNumberList = new ArrayList<>();
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            // 查询时间段内的
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).lte(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));

            // 不查询机器人工单
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));
            boolQueryBuilder.must(QueryBuilders.termQuery("status", 3));

            // 如果是座席管理员，则查询当前座席组的数据，也就是当前部门的数据
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
            }
            // 根据agent_id来进行聚合
            TermsAggregationBuilder agentIdAgg = AggregationBuilders
                    .terms("agent_id_agg")
                    .field("agent_id")
                    .size(10000);
            // 判断类型 1 降序排序， 2升序排序
            if (queryType == 1) {
                agentIdAgg.order(BucketOrder.count(false));
            } else {
                agentIdAgg.order(BucketOrder.count(true));
            }
            searchSourceBuilder.query(boolQueryBuilder).size(0);
            searchSourceBuilder.aggregation(agentIdAgg);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            Aggregations aggregations = response.getAggregations();

            Terms agentIdData = aggregations.get("agent_id_agg");
            List<AgentQueryWorkNumberVo> to3List = new ArrayList<>();
            // 取到数据值
            for (Terms.Bucket bucket : agentIdData.getBuckets()) {
                AgentQueryWorkNumberVo agentQueryWorkNumberVo = new AgentQueryWorkNumberVo();
                String agentId = bucket.getKeyAsString();
                // 根据坐席id，查询出当前坐席其他信息
                UserDetailsVO userDetails = userDetailMap.get(agentId);
                if(userDetails == null){
                    continue;
                }
                // 存储坐席信息
                agentQueryWorkNumberVo.setAgentId(agentId);
                agentQueryWorkNumberVo.setWorkNumber(Long.valueOf(bucket.getDocCount()).intValue());
                agentQueryWorkNumberVo.setAgentName(userDetails.getUserName());
                agentQueryWorkNumberVo.setDeptId(userDetails.getDeptId());
                agentQueryWorkNumberVo.setDeptName(userDetails.getDeptName());

                to3List.add(agentQueryWorkNumberVo);
            }

            List<AgentQueryWorkNumberVo> firstThree;
            // 如果是座席管理员，则取座席前三
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                // 确保列表至少有三条数据，并且是查询最高或者最低
                if (to3List.size() >= 3 && (queryType == 1 || queryType == 2)) {
                    firstThree = new ArrayList<>(to3List.subList(0, 3));
                } else {
                    firstThree = new ArrayList<>(to3List);
                }
                firstThree.forEach(item -> {
                    AgentWorkNumberVo agentWorkNumberVo = new AgentWorkNumberVo();
                    agentWorkNumberVo.setWorkNumber(item.getWorkNumber());
                    agentWorkNumberVo.setAgentId(item.getAgentId());
                    agentWorkNumberVo.setAgentName(item.getAgentName());
                    agentWorkNumberList.add(agentWorkNumberVo);
                });
                return agentWorkNumberList;
            }

            // 如果是管理员，则将数据以部门id分组
            // 根据deptId分组并计算每个分组的workNumber总和
            Map<String, Integer> workNumberByDeptId = to3List.stream()
                    .collect(Collectors.groupingBy(
                            AgentQueryWorkNumberVo::getDeptId,
                            Collectors.summingInt(AgentQueryWorkNumberVo::getWorkNumber)
                    ));
            // 获取deptId与deptName的映射关系
            Map<String, String> deptIdToNameMap = to3List.stream()
                    .collect(Collectors.toMap(
                            AgentQueryWorkNumberVo::getDeptId,
                            AgentQueryWorkNumberVo::getDeptName,
                            (existing, replacement) -> existing
                    ));
            List<Map.Entry<String, Integer>> topThreeDept = new ArrayList<>();
            // 判断类型
            if (queryType == 1) {
                // 按workNumber总和进行倒序排序，并取前三个分组
                topThreeDept = workNumberByDeptId.entrySet().stream()
                        .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                        .limit(3)
                        .collect(Collectors.toList());
            } else if (queryType == 2) {
                // 按workNumber总和进行正序排序，并取前三个分组
                topThreeDept = workNumberByDeptId.entrySet().stream()
                        .sorted(Map.Entry.comparingByValue())
                        .limit(3)
                        .collect(Collectors.toList());
            } else {
                topThreeDept = workNumberByDeptId.entrySet().stream()
                        .sorted(Map.Entry.comparingByValue())
                        .collect(Collectors.toList());
            }
            // 打印结果
            topThreeDept.forEach(entry -> {
                AgentWorkNumberVo agentWorkNumberVo = new AgentWorkNumberVo();
                agentWorkNumberVo.setWorkNumber(entry.getValue());
                agentWorkNumberVo.setAgentId(entry.getKey());
                agentWorkNumberVo.setAgentName(deptIdToNameMap.get(entry.getKey()));
                agentWorkNumberList.add(agentWorkNumberVo);
            });

            return agentWorkNumberList;

        } catch (Exception e) {
            log.info("工作量报表汇总TO3查询工单ES报错", e);
            return null;
        }
//
//        // 查询座席最高top3
//        QueryWrapper<CrmAgentWorkRecord> queryWrapper = new QueryWrapper<>();
//        // 查询当前公司下的
//        queryWrapper.eq("cawr.company_id", SecurityUtil.getLoginUser().getCompanyId())
//                .ne("cawr.agent_id", 1001).ne("cawr.agent_id", "");
//        // 查询当前时间内的
//        queryWrapper.ge("cawr.create_time", startDate)
//                .le("cawr.create_time", endDate);
//        // TODO 是否要查询完成状态的工单
//        queryWrapper.eq("cawr.status", 3);
//        if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
//            queryWrapper.eq("cawr.dept_id", deptId);
//        }
//        queryWrapper.ne("cawr.agent_id", "1001");
//        // 判断类型
//        if (queryType == 1) {
//            queryWrapper.orderByDesc("workNumber");
//        } else {
//            queryWrapper.orderByAsc("workNumber");
//        }
//        queryWrapper.groupBy("cawr.agent_id");
//
//
//        List<AgentQueryWorkNumberVo> to3List = crmAgentWorkRecordMapper.agentWorkNumberTo3(queryWrapper);
//
//        List<AgentQueryWorkNumberVo> firstThree = new ArrayList<>();
//        // 如果是座席管理员，则取座席前三
//        if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
//            // 确保列表至少有三条数据，并且是查询最高或者最低
//            if (to3List.size() >= 3 && (queryType == 1 || queryType == 2)) {
//                firstThree = new ArrayList<>(to3List.subList(0, 3));
//            } else {
//                firstThree = new ArrayList<>(to3List);
//            }
//            firstThree.forEach(item -> {
//                AgentWorkNumberVo agentWorkNumberVo = new AgentWorkNumberVo();
//                agentWorkNumberVo.setWorkNumber(item.getWorkNumber());
//                agentWorkNumberVo.setAgentId(item.getAgentId());
//                agentWorkNumberVo.setAgentName(item.getAgentName());
//                agentWorkNumberList.add(agentWorkNumberVo);
//            });
//            return agentWorkNumberList;
//        }
//        // 如果是管理员，则将数据以部门id分组
//        // 根据deptId分组并计算每个分组的workNumber总和
//        Map<String, Integer> workNumberByDeptId = to3List.stream()
//                .collect(Collectors.groupingBy(
//                        AgentQueryWorkNumberVo::getDeptId,
//                        Collectors.summingInt(AgentQueryWorkNumberVo::getWorkNumber)
//                ));
//        // 获取deptId与deptName的映射关系
//        Map<String, String> deptIdToNameMap = to3List.stream()
//                .collect(Collectors.toMap(
//                        AgentQueryWorkNumberVo::getDeptId,
//                        AgentQueryWorkNumberVo::getDeptName,
//                        (existing, replacement) -> existing
//                ));
//        List<Map.Entry<String, Integer>> topThreeDept = new ArrayList<>();
//        // 判断类型
//        if (queryType == 1) {
//            // 按workNumber总和进行倒序排序，并取前三个分组
//            topThreeDept = workNumberByDeptId.entrySet().stream()
//                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
//                    .limit(3)
//                    .collect(Collectors.toList());
//        } else if (queryType == 2) {
//            // 按workNumber总和进行正序排序，并取前三个分组
//            topThreeDept = workNumberByDeptId.entrySet().stream()
//                    .sorted(Map.Entry.comparingByValue())
//                    .limit(3)
//                    .collect(Collectors.toList());
//        } else {
//            topThreeDept = workNumberByDeptId.entrySet().stream()
//                    .sorted(Map.Entry.comparingByValue())
//                    .collect(Collectors.toList());
//        }
//        // 打印结果
//        topThreeDept.forEach(entry -> {
//            AgentWorkNumberVo agentWorkNumberVo = new AgentWorkNumberVo();
//            agentWorkNumberVo.setWorkNumber(entry.getValue());
//            agentWorkNumberVo.setAgentId(entry.getKey());
//            agentWorkNumberVo.setAgentName(deptIdToNameMap.get(entry.getKey()));
//            agentWorkNumberList.add(agentWorkNumberVo);
//        });
//
//        return agentWorkNumberList;
    }

    /**
     * 计算数的百分比
     *
     * @param nowNumber 当前数
     * @param oldNumber 之前数
     * @return 提升或者下降百分比
     */
    private BigDecimal calculatePercentageChange(Integer nowNumber, Integer oldNumber) {
        if (oldNumber == 0) {
            return null;
        }
        double change = nowNumber - oldNumber;
        double percentageChange = (change / oldNumber) * 100;
        // 使用 BigDecimal 保留两位小数
        BigDecimal bd = BigDecimal.valueOf(percentageChange);
        bd = bd.setScale(2, RoundingMode.HALF_UP);

        // 将结果转换为 double
        return bd;
    }

    @Override
    public AjaxResult<Object> queryRobotWorkloadSummarize(SeatingWorkloadNewVo seatingWorkloadVo) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 计算出对应天数之前的数据
        // 解析字符串为LocalDate
        LocalDateTime startDate = LocalDateTime.parse(seatingWorkloadVo.getStartDate(), formatter);
        LocalDateTime endDate = LocalDateTime.parse(seatingWorkloadVo.getEndDate(), formatter);
        // 计算两者之间的天数
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        // 分别减去对应天数
        LocalDateTime newStartDate = startDate.minusDays(daysBetween);
        LocalDateTime newEndDate = endDate.minusDays(daysBetween);

        try {
            // 当前时间的机器人工单数
            int nowRobotWorkTotalNumber = 0;
            SearchResponse nowRobotWorkSearchResponse = queryRobotWorkTotalNumber(startDate, endDate, 1);
            if (nowRobotWorkSearchResponse != null) {
                nowRobotWorkTotalNumber = Long.valueOf(nowRobotWorkSearchResponse.getHits().getTotalHits().value).intValue();
            }

            // 之前时间的机器人工单数
            int oldRobotWorkTotalNumber = 0;
            SearchResponse oldRobotWorkSearchResponse = queryRobotWorkTotalNumber(newStartDate, newEndDate, 1);
            if (oldRobotWorkSearchResponse != null) {
                oldRobotWorkTotalNumber = Long.valueOf(oldRobotWorkSearchResponse.getHits().getTotalHits().value).intValue();
            }
            // 计算机器人工单总数比例
            BigDecimal robotWorkTotal = calculatePercentageChange(nowRobotWorkTotalNumber, oldRobotWorkTotalNumber);

            // 当前时间人工工单数
            int nowArtificialWorkTotalNumber = 0;
            SearchResponse nowArtificialWorkSearchResponse = queryRobotWorkTotalNumber(startDate, endDate, 2);
            if (nowArtificialWorkSearchResponse != null) {
                nowArtificialWorkTotalNumber = Long.valueOf(nowArtificialWorkSearchResponse.getHits().getTotalHits().value).intValue();
            }

            // 机器人对比人工
            int robotContrastArtificialNumber = nowRobotWorkTotalNumber - nowArtificialWorkTotalNumber;
            // 机器人对比之前时间数量
            int robotContrastTimeNumber = nowRobotWorkTotalNumber - oldRobotWorkTotalNumber;


            //当前时间各个渠道机器人工单
            List<RobotTicketSummarizeVO> robotChannelTicket = new ArrayList<>();
            //之前时间各个渠道机器人工单
            SearchResponse oldChannelTypeWorkSearchResponse = queryRobotWorkTotalNumber(newStartDate, newEndDate, 3);
            if (oldChannelTypeWorkSearchResponse != null) {
                Aggregations aggregations = oldChannelTypeWorkSearchResponse.getAggregations();
                Terms channelTypeAgg = aggregations.get("channel_type_agg");
                // 取到数据值
                for (Terms.Bucket bucket : channelTypeAgg.getBuckets()) {
                    RobotTicketSummarizeVO robotWorkOrderResult = new RobotTicketSummarizeVO();
                    String channelTypeId = bucket.getKeyAsString();
                    robotWorkOrderResult.setChannelTypeId(channelTypeId);
                    robotWorkOrderResult.setTotalCount(bucket.getDocCount());
                    robotChannelTicket.add(robotWorkOrderResult);
                }
            }

            Map<String, RobotTicketSummarizeVO> robotTicketMap = robotChannelTicket.stream()
                    .collect(Collectors.toMap(RobotTicketSummarizeVO::getChannelTypeId, robotTicketSummarizeVO -> robotTicketSummarizeVO));



            //当前时间各个渠道机器人工单
            List<RobotTicketSummarizeVO> robotTicketSummarizeList = new ArrayList<>();
            SearchResponse nowChannelTypeWorkSearchResponse = queryRobotWorkTotalNumber(startDate, endDate, 3);
            if (nowChannelTypeWorkSearchResponse != null) {
                Aggregations aggregations = nowChannelTypeWorkSearchResponse.getAggregations();
                Terms channelTypeAgg = aggregations.get("channel_type_agg");
                // 取到数据值
                for (Terms.Bucket bucket : channelTypeAgg.getBuckets()) {
                    RobotTicketSummarizeVO robotWorkOrderResult = new RobotTicketSummarizeVO();
                    String channelTypeId = bucket.getKeyAsString();
                    // 取出之前对应的工单数量
                    RobotTicketSummarizeVO ticketSummarizeVO = robotTicketMap.get(channelTypeId);
                    if(ticketSummarizeVO!=null&&ticketSummarizeVO.getChannelTypeId()!=null){
                        // 计算机器人web工单总数比例
                        BigDecimal robotWorkProportion = calculatePercentageChange(
                                Long.valueOf(bucket.getDocCount()).intValue(),
                                Long.valueOf(ticketSummarizeVO.getTotalCount()).intValue());
                        robotWorkOrderResult.setRobotProportion(robotWorkProportion);
                    }else{
                        robotWorkOrderResult.setRobotProportion(BigDecimal.ZERO);
                    }
                    robotWorkOrderResult.setChannelTypeId(channelTypeId);
                    robotWorkOrderResult.setTotalCount(bucket.getDocCount());
                    robotTicketSummarizeList.add(robotWorkOrderResult);
                }
            }




            Map<String, Object> map = new HashMap<>();
            // 机器人工单总数
            map.put("robotWorkTotalNumber", nowRobotWorkTotalNumber);
            // 机器人对比之前时间比例
            map.put("robotWorkTotalProportion", robotWorkTotal);
            // 机器人对比人工工单数量
            map.put("robotContrastArtificialNumber", robotContrastArtificialNumber);
            // 机器人对比之前时间数量
            map.put("robotContrastTimeNumber", robotContrastTimeNumber);
            // 对比天数
            map.put("comparisonDay", daysBetween);
            // 机器人工单统计列表
            map.put("robotChannelTicket", robotTicketSummarizeList);

            return AjaxResult.ok(map);
        } catch (Exception e) {
            log.error("机器人工单统计查询出现异常", e);
            return AjaxResult.failure();
        }
    }

    @Override
    public AjaxResult<Object> queryAgentWorkingEfficiency(SeatingWorkloadNewVo seatingWorkloadVo) {
        // 获取登录用户，判断是管理员还是座席管理员
        String roleId = SecurityUtil.getLoginUser().getRoleList().get(0).getRoleId();
        // 部门id
        String deptId = SecurityUtil.getLoginUser().getDeptId();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 计算出对应天数之前的数据
        // 解析字符串为LocalDate
        LocalDateTime startDate = LocalDateTime.parse(seatingWorkloadVo.getStartDate(), formatter);
        LocalDateTime endDate = LocalDateTime.parse(seatingWorkloadVo.getEndDate(), formatter);
        // 计算两者之间的天数
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        // 分别减去对应天数
        LocalDateTime newStartDate = startDate.minusDays(daysBetween);
        LocalDateTime newEndDate = endDate.minusDays(daysBetween);
        // 获取到座席或座席组的平均时间
        String avgResolutionTime = agentAvgResolutionTime(startDate, endDate, roleId, deptId);

        // 最长top3
        List<AgentResolutionTimeVo> timeLongestTop3 = agentResolutionTime(startDate, endDate, roleId, deptId, 1);

        // 最短top3
        List<AgentResolutionTimeVo> timeShortestTop3 = agentResolutionTime(startDate, endDate, roleId, deptId, 2);
        // 当前时间平均数据
        List<AgentResolutionTimeVo> nowTimeDate = agentResolutionTime(startDate, endDate, roleId, deptId, 3);
        // 之前时间的平均数据
        List<AgentResolutionTimeVo> oldTimeDate = agentResolutionTime(newStartDate, newEndDate, roleId, deptId, 3);

        // 将list2转化为map，方便查找
        Map<String, AgentResolutionTimeVo> map2 = nowTimeDate.stream()
                .collect(Collectors.toMap(AgentResolutionTimeVo::getAgentId, vo -> vo));

        // 创建新的列表来存储差值
        List<AgentResolutionTimeVo> differenceList = new ArrayList<>();

        // 计算每个agentId的差值
        for (AgentResolutionTimeVo vo1 : oldTimeDate) {
            AgentResolutionTimeVo vo2 = map2.get(vo1.getAgentId());
            if (vo2 != null) {
                BigDecimal difference = vo2.getAverageTime().subtract(vo1.getAverageTime());
                differenceList.add(new AgentResolutionTimeVo(vo1.getAgentId(), vo1.getAgentName(), difference, String.format("%02d:%02d:%02d", difference.longValue() / 3600, (difference.longValue() % 3600) / 60, difference.longValue() % 60)));
            }
        }

        // 上升TOP3
        List<AgentResolutionTimeVo> topIncrease = differenceList.stream()
                .filter(item -> item.getAverageTime().compareTo(BigDecimal.ZERO) > 0)
                .sorted(Comparator.comparing(AgentResolutionTimeVo::getAverageTime).reversed())
                .limit(3)
                .map(item -> new AgentResolutionTimeVo(item.getAgentId(), item.getAgentName(), item.getAverageTime(), item.getAverageTimeString()))
                .collect(Collectors.toList());

        // 下降TO3
        List<AgentResolutionTimeVo> topDecrease = differenceList.stream()
                .filter(item -> item.getAverageTime().compareTo(BigDecimal.ZERO) < 0)
                .sorted(Comparator.comparing(AgentResolutionTimeVo::getAverageTime))
                .limit(3)
                .map(item -> new AgentResolutionTimeVo(item.getAgentId(), item.getAgentName(), item.getAverageTime(), item.getAverageTimeString()))
                .collect(Collectors.toList());
        // 将下降参数取绝对值
        topDecrease.forEach(item -> {
            item.setAverageTime(item.getAverageTime().abs());
            item.setAverageTimeString(String.format("%02d:%02d:%02d", item.getAverageTime().longValue() / 3600, (item.getAverageTime().longValue() % 3600) / 60, item.getAverageTime().longValue() % 60));
        });

        Map<String, Object> map = new HashMap<>();
        // 平均时间
        map.put("averageTime", avgResolutionTime);
        // 最长处理时间座席或者座席组名称
        map.put("timeLongestName", !timeLongestTop3.isEmpty() ? timeLongestTop3.get(0).getAgentName() : "");
        // 最长处理时间座席或者座席组
        map.put("timeLongestTime", !timeLongestTop3.isEmpty() ? timeLongestTop3.get(0).getAverageTimeString() : "");
        // 最短处理时间座席或者座席组名称
        map.put("timeShortestName", !timeShortestTop3.isEmpty() ? timeShortestTop3.get(0).getAgentName() : "");
        // 最短处理时间座席或者座席组
        map.put("timeShortestTime", !timeShortestTop3.isEmpty() ? timeShortestTop3.get(0).getAverageTimeString() : "");
        // 提升最大名称
        map.put("timeIncreaseName", !topIncrease.isEmpty() ? topIncrease.get(0).getAgentName() : "");
        // 提升最大时间
        map.put("timeIncreaseTime", !topIncrease.isEmpty() ? topIncrease.get(0).getAverageTimeString() : "");
        // 下降最大名称
        map.put("timeDecreaseName", !topDecrease.isEmpty() ? topDecrease.get(0).getAgentName() : "");
        // 下降最大时间
        map.put("timeDecreaseTime", !topDecrease.isEmpty() ? topDecrease.get(0).getAverageTimeString() : "");
        // 平均最长数据
        map.put("timeLongestData", timeLongestTop3);
        // 平均最短数据
        map.put("timeShortestData", timeShortestTop3);
        // 平均提升最小数据
        map.put("timeIncreaseData", topIncrease);
        // 平均提升最小数据
        map.put("timeDecreaseData", topDecrease);

        return AjaxResult.ok(map);
    }

    @Override
    public AjaxResult<Object> queryAgentSatisfactionLevel(SeatingWorkloadNewVo seatingWorkloadVo) {
        // 获取登录用户，判断是管理员还是座席管理员
        String roleId = SecurityUtil.getLoginUser().getRoleList().get(0).getRoleId();
        // 部门id
        String deptId = SecurityUtil.getLoginUser().getDeptId();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 计算出对应天数之前的数据
        // 解析字符串为LocalDate
        LocalDateTime startDate = LocalDateTime.parse(seatingWorkloadVo.getStartDate(), formatter);
        LocalDateTime endDate = LocalDateTime.parse(seatingWorkloadVo.getEndDate(), formatter);
        // 计算两者之间的天数
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        // 分别减去对应天数
        LocalDateTime newStartDate = startDate.minusDays(daysBetween);
        LocalDateTime newEndDate = endDate.minusDays(daysBetween);
        // 满意度平均分
        BigDecimal avgSatisfactionLevel = avgSatisfactionLevel(startDate, endDate, roleId, deptId);
        // 满意度回平率
        BigDecimal satisfactionLevelFeedbackRate = satisfactionLevelFeedbackRate(startDate, endDate, roleId, deptId);
        // 查询平均满意度最高TOP 3
        List<AgentSatisfactionLevelVo> highestSatisfactionLevelVoList = agentAvgSatisfactionLevel(startDate, endDate, roleId, deptId, 1);
        // 查询平均满意度最低TOP 3
        List<AgentSatisfactionLevelVo> shortestSatisfactionLevelVoList = agentAvgSatisfactionLevel(startDate, endDate, roleId, deptId, 2);
        // 当前时间平均满意度
        List<AgentSatisfactionLevelVo> nowSatisfactionLevelVoList = agentAvgSatisfactionLevel(startDate, endDate, roleId, deptId, 3);
        // 之前时间平均满意度
        List<AgentSatisfactionLevelVo> oldSatisfactionLevelVoList = agentAvgSatisfactionLevel(newStartDate, newEndDate, roleId, deptId, 3);

        // 将list2转化为map，方便查找
        Map<String, AgentSatisfactionLevelVo> map2 = nowSatisfactionLevelVoList.stream()
                .collect(Collectors.toMap(AgentSatisfactionLevelVo::getAgentId, vo -> vo));

        // 创建新的列表来存储差值
        List<AgentSatisfactionLevelVo> differenceList = new ArrayList<>();

        // 计算每个agentId的差值
        for (AgentSatisfactionLevelVo vo1 : oldSatisfactionLevelVoList) {
            AgentSatisfactionLevelVo vo2 = map2.get(vo1.getAgentId());
            if (vo2 != null) {
                BigDecimal difference = vo2.getRating().subtract(vo1.getRating());
                differenceList.add(new AgentSatisfactionLevelVo(vo1.getAgentId(), vo1.getAgentName(), difference));
            }
        }

        // 上升TOP3
        List<AgentSatisfactionLevelVo> topIncrease = differenceList.stream()
                .filter(item -> item.getRating().compareTo(BigDecimal.ZERO) > 0)
                .sorted(Comparator.comparing(AgentSatisfactionLevelVo::getRating).reversed())
                .limit(3)
                .map(item -> new AgentSatisfactionLevelVo(item.getAgentId(), item.getAgentName(), item.getRating()))
                .collect(Collectors.toList());

        // 下降TO3
        List<AgentSatisfactionLevelVo> topDecrease = differenceList.stream()
                .filter(item -> item.getRating().compareTo(BigDecimal.ZERO) < 0)
                .sorted(Comparator.comparing(AgentSatisfactionLevelVo::getRating))
                .limit(3)
                .map(item -> new AgentSatisfactionLevelVo(item.getAgentId(), item.getAgentName(), item.getRating()))
                .collect(Collectors.toList());
        // 将下降参数取绝对值
        topDecrease.forEach(item -> {
            item.setRating(item.getRating().abs());
        });
        Map<String, Object> map = new HashMap<>();
        // 满意度平均分
        map.put("averageSatisfactionScore", avgSatisfactionLevel);
        // 满意度回评率
        map.put("satisfactionRatingRate", satisfactionLevelFeedbackRate);
        // 满意度评分最高名称
        map.put("satisfactionLongestName", !highestSatisfactionLevelVoList.isEmpty() ? highestSatisfactionLevelVoList.get(0).getAgentName() : "");
        // 满意度评分最高平均评分
        map.put("satisfactionLongestNumber", !highestSatisfactionLevelVoList.isEmpty() ? highestSatisfactionLevelVoList.get(0).getRating() : 0);
        // 满意度评分最低名称
        map.put("satisfactionShortestName", !shortestSatisfactionLevelVoList.isEmpty() ? shortestSatisfactionLevelVoList.get(0).getAgentName() : "");
        // 满意度评分最低平均评分
        map.put("satisfactionShortestTime", !shortestSatisfactionLevelVoList.isEmpty() ? shortestSatisfactionLevelVoList.get(0).getRating() : 0);
        // 提升最大名称
        map.put("satisfactionIncreaseName", !topIncrease.isEmpty() ? topIncrease.get(0).getAgentName() : "");
        // 提升最大评分
        map.put("satisfactionIncreaseNumber", !topIncrease.isEmpty() ? topIncrease.get(0).getRating() : 0);
        // 下降最大名称
        map.put("satisfactionDecreaseName", !topDecrease.isEmpty() ? topDecrease.get(0).getAgentName() : "");
        // 下降最大满意评分
        map.put("satisfactionDecreaseNumber", !topDecrease.isEmpty() ? topDecrease.get(0).getRating() : 0);
        // 平均最高数据
        map.put("satisfactionLongestData", highestSatisfactionLevelVoList);
        // 平均最低数据
        map.put("satisfactionShortestData", shortestSatisfactionLevelVoList);
        // 平均提升最大数据
        map.put("satisfactionIncreaseData", topIncrease);
        // 平均下降最大数据
        map.put("satisfactionDecreaseData", topDecrease);
        return AjaxResult.ok(map);
    }

    @Override
    public AjaxResult agentStatusSwitch(AgentStatusSwitchInfoVo agentStatusSwitchInfoVo) throws JsonProcessingException {
        //公司id
        String index = agentLogindex + SecurityUtil.getLoginUser().getCompanyId();
        BulkRequest bulkRequest = new BulkRequest(index);
        AgentStatusSwitchEsInfoVo agentStatusSwitchEsInfoVo = new AgentStatusSwitchEsInfoVo();
        BeanUtils.copyProperties(agentStatusSwitchInfoVo, agentStatusSwitchEsInfoVo);
        String logId = UUID.randomUUID().toString().replace("-", "");
        //拼接数据
        agentStatusSwitchEsInfoVo.setDel_status(0);
        agentStatusSwitchEsInfoVo.setLog_id(logId);
        agentStatusSwitchEsInfoVo.setCompany_id(SecurityUtil.getLoginUser().getCompanyId());
        agentStatusSwitchEsInfoVo.setEvent_time(new Date());
        agentStatusSwitchEsInfoVo.setTeam_id(SecurityUtil.getLoginUser().getDeptId());
        // 将对象转为 JSON
        String value = new ObjectMapper().writeValueAsString(agentStatusSwitchEsInfoVo);
        // 定义更新请求
        UpdateRequest updateRequest = new UpdateRequest(index, logId)
                .doc(value, XContentType.JSON)
                .upsert(value, XContentType.JSON);
        // 添加更新请求到批量请求
        bulkRequest.add(updateRequest);

        if (bulkRequest.numberOfActions() != 0) {
            try {
                BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                if (!e.getMessage().contains("200 OK") && !e.getMessage().contains("201")) {
                    log.error("es新增文档失败，异常信息：", e);
                    throw new RuntimeException(e);
                }
            }
        }
        return AjaxResult.ok("ADD SUCCESS ");
    }

    /**
     * 查询满意度回评率
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param roleId    角色ID
     * @param deptId    部门id
     * @param queryType 查询类型 1、满意度最高top3 2、 满意度最低top3 3、满意度所有数据
     * @return 满意度回评率
     */
    private List<AgentSatisfactionLevelVo> agentAvgSatisfactionLevel(LocalDateTime startDate, LocalDateTime endDate, String roleId, String deptId, int queryType) {

        List<AgentSatisfactionLevelVo> satisfactionLevelList = new ArrayList<>();
        try {
            // 查询出当前公司下所有的坐席信息
            R<List<UserDetailsVO>> list = userClient.queryUserListByCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            List<UserDetailsVO> userDetailList = list.getData();
            Map<String, UserDetailsVO> userDetailMap = userDetailList.stream()
                    .collect(Collectors.toMap(UserDetailsVO::getUserId, userDetailsVO -> userDetailsVO));
            Map<String, UserDetailsVO> deptMap = userDetailList.stream()
                    // 将 List 转换成 Map，以 deptId 为键，UserDetailsVO 为值
                    .collect(Collectors.toMap(
                            UserDetailsVO::getDeptId,
                            userDetailsVO -> userDetailsVO,
                            (existing, replacement) -> existing
                    ));
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).lte(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            // 如果是座席管理员，则查询当前座席组的数据，也就是当前部门的数据
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
            }
            // 不查询机器人工单
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));

            boolQueryBuilder.must(QueryBuilders.termQuery("data_status", 1));
            // 查询有满意度的数据
            boolQueryBuilder.must(QueryBuilders.nestedQuery(
                    "ticket_satisfaction", // 指定嵌套路径
                    QueryBuilders.existsQuery("ticket_satisfaction.rating"), // 只匹配存在 rating 字段的文档
                    ScoreMode.None
            ));

            // 构建聚合
            TermsAggregationBuilder termsAggregation = AggregationBuilders.terms("by_agent_id")
                    .size(10000); // 设置返回桶的最大数量

            // 不同角色，根据不同的id进行聚合
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                termsAggregation.field("agent_id");
            } else {
                termsAggregation.field("dept_id");
            }

            // 嵌套聚合：针对 ticket_satisfaction 字段
            NestedAggregationBuilder nestedAggregation = AggregationBuilders.nested("nested_satisfaction", "ticket_satisfaction")
                    .subAggregation(
                            AggregationBuilders.avg("avg_rating").field("ticket_satisfaction.rating") // 平均评分
                    );

            // 添加到按 agent_id 的分组中
            termsAggregation.subAggregation(nestedAggregation);

            // 将主聚合添加到查询中
            searchSourceBuilder.aggregation(termsAggregation);


            searchSourceBuilder.query(boolQueryBuilder).size(0);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            // 获取聚合结果
            Aggregations aggregations = response.getAggregations();

            // 获取 agent_id 聚合结果
            Terms agentAgg = aggregations.get("by_agent_id");
            for (Terms.Bucket bucket : agentAgg.getBuckets()) {

                AgentSatisfactionLevelVo agentSatisfactionLevelVo = new AgentSatisfactionLevelVo();
                String agentId = bucket.getKeyAsString();
                System.out.println("Agent ID: " + agentId);
                // 获取嵌套聚合结果
                Nested nestedAgg = bucket.getAggregations().get("nested_satisfaction");
                // 获取 avg_rating 聚合结果
                Avg avgRating = nestedAgg.getAggregations().get("avg_rating");
                double avgRatingValue = avgRating.getValue();
                if(StringUtils.isNotEmpty(agentId)){
                    if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                        UserDetailsVO userDetailsVO = userDetailMap.get(agentId);
                        if(userDetailsVO != null){
                            agentSatisfactionLevelVo.setAgentName(userDetailsVO.getUserName());
                        }

                    } else {
                        UserDetailsVO userDetailsVO = deptMap.get(agentId);
                        agentSatisfactionLevelVo.setAgentName(userDetailsVO.getDeptName());
                    }
                }
                agentSatisfactionLevelVo.setAgentId(agentId);
                agentSatisfactionLevelVo.setRating(BigDecimal.valueOf(avgRatingValue)
                        .setScale(2, RoundingMode.HALF_UP));

                satisfactionLevelList.add(agentSatisfactionLevelVo);
            }
            if(satisfactionLevelList.isEmpty()){
                return satisfactionLevelList;
            }
            if (queryType == 1) {
                return satisfactionLevelList.stream()
                        // 按照 rating 字段倒序排序
                        .sorted((agent1, agent2) -> agent2.getRating().compareTo(agent1.getRating()))
                        // 只取前 3 个
                        .limit(3)
                        .collect(Collectors.toList());
            }else if (queryType == 2) {
                return satisfactionLevelList.stream()
                        // 按照 rating 字段正序排序
                        .sorted((agent1, agent2) -> agent1.getRating().compareTo(agent2.getRating()))
                        // 只取前 3 个
                        .limit(3)
                        .collect(Collectors.toList());
            }else{
                return satisfactionLevelList;
            }
            // 保留两位小数
        } catch (Exception e) {
            log.error("查询满意度top3报错", e);
            return satisfactionLevelList;
        }
    }


    /**
     * 查询满意度回评率
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param roleId    角色ID
     * @param deptId    部门id
     * @return 满意度回评率
     */
    private BigDecimal satisfactionLevelFeedbackRate(LocalDateTime startDate, LocalDateTime endDate, String roleId, String deptId) {
        try {
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).lte(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            // 如果是座席管理员，则查询当前座席组的数据，也就是当前部门的数据
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
            }
            // 不查询机器人工单
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));

            boolQueryBuilder.mustNot(QueryBuilders.termQuery("status", 5));

            boolQueryBuilder.must(QueryBuilders.termQuery("data_status", 1));
            // 添加聚合
            searchSourceBuilder.aggregation(
                    AggregationBuilders.count("total_docs").field("work_record_id")
            );
            searchSourceBuilder.aggregation(
                    AggregationBuilders.filter("empty_ticket_satisfaction_docs",
                            QueryBuilders.boolQuery()
                                    .must(
                                            // 嵌套查询，确保 ticket_satisfaction.rating 字段存在
                                            QueryBuilders.nestedQuery(
                                                    "ticket_satisfaction",
                                                    QueryBuilders.existsQuery("ticket_satisfaction.rating"),
                                                    ScoreMode.None
                                            )
                                    )
                    )
            );


            searchSourceBuilder.query(boolQueryBuilder).size(0);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            // 获取聚合结果
            Aggregations aggregations = response.getAggregations();
            ValueCount totalDocsAgg = aggregations.get("total_docs");
            Filter emptyTicketSatisfactionAgg = aggregations.get("empty_ticket_satisfaction_docs");

            long nowWorkTotalNumber = totalDocsAgg.getValue();
            long satisfactionLevelNumber = emptyTicketSatisfactionAgg.getDocCount();

            // 如果工单数据为0，则满意度也为0
            if (nowWorkTotalNumber == 0) {
                return BigDecimal.ZERO;
            }
            BigDecimal satisfactionLevel = new BigDecimal(satisfactionLevelNumber);
            BigDecimal totalNumber = new BigDecimal(nowWorkTotalNumber);
            BigDecimal percentage = satisfactionLevel.divide(totalNumber, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));

            // 保留两位小数
            return percentage.setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("查询满意度回评率报错", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 查询平均满意度
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param roleId    角色ID
     * @param deptId    部门id
     * @return 平均满意度
     */
    private BigDecimal avgSatisfactionLevel(LocalDateTime startDate, LocalDateTime endDate, String roleId, String deptId) {
        try {
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).lte(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            // 不查询机器人工单
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            // 如果是座席管理员，则查询当前座席组的数据，也就是当前部门的数据
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
            }
            // 查询有满意度的数据
            boolQueryBuilder.must(QueryBuilders.nestedQuery(
                    "ticket_satisfaction", // 指定嵌套路径
                    QueryBuilders.existsQuery("ticket_satisfaction.rating"), // 只匹配存在 rating 字段的文档
                    ScoreMode.None
            ));
            boolQueryBuilder.must(QueryBuilders.termQuery("data_status", 1));
            // 嵌套聚合：计算 ticket_satisfaction.rating 的平均值
            searchSourceBuilder.aggregation(
                    AggregationBuilders.nested("ticket_satisfaction_agg", "ticket_satisfaction")
                            .subAggregation(
                                    AggregationBuilders.avg("average_rating").field("ticket_satisfaction.rating")
                            )
            );
            searchSourceBuilder.query(boolQueryBuilder);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            // 获取聚合结果
            Aggregations aggregations = response.getAggregations();
            Nested nestedAgg = aggregations.get("ticket_satisfaction_agg");
            if (nestedAgg != null&&nestedAgg.getDocCount()!=0) {
                // 获取嵌套聚合结果
                Avg averageRatingAgg = nestedAgg.getAggregations().get("average_rating");
                if (averageRatingAgg != null) {
                    return BigDecimal.valueOf(averageRatingAgg.getValue())
                            .setScale(2, RoundingMode.HALF_UP);
                }
            }
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("查询平均满意度报错", e);
            return BigDecimal.ZERO;
        }
    }


    /**
     * 查询座席平均处理时间
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param roleId    角色ID
     * @param deptId    部门id
     * @return 座席平均处理时间
     */
    private String agentAvgResolutionTime(LocalDateTime startDate, LocalDateTime endDate, String roleId, String deptId) {
        try {
            List<AgentQueryResolutionTimeVo> timeVos = new ArrayList<>();
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).lte(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            // 不查询机器人工单
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));
            boolQueryBuilder.must(QueryBuilders.termQuery("status", 3));

            // 如果是座席管理员，则查询当前座席组的数据，也就是当前部门的数据
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
            }
            boolQueryBuilder.must(QueryBuilders.termQuery("data_status", 1));
            searchSourceBuilder.query(boolQueryBuilder);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                // 计算时间差
                long secondsDiff = Duration.between(ticketInfoIndex.getCreateTime(), ticketInfoIndex.getResolveTime()).getSeconds();
                AgentQueryResolutionTimeVo agentQueryResolutionTimeVo = new AgentQueryResolutionTimeVo();
                agentQueryResolutionTimeVo.setTimeDifferenceSeconds(secondsDiff);
                timeVos.add(agentQueryResolutionTimeVo);
            }
            // 使用 Java 8 的流 API 计算 timeDifferenceSeconds 的平均值
            OptionalDouble average = timeVos.stream()
                    .mapToLong(AgentQueryResolutionTimeVo::getTimeDifferenceSeconds)
                    .average();
            // 打印平均值，如果存在的话
            if (average.isPresent()) {
                long averageSeconds = (long) average.getAsDouble();
                return String.format("%02d:%02d:%02d",
                        averageSeconds / 3600,
                        (averageSeconds % 3600) / 60,
                        averageSeconds % 60);
            } else {
                return "00:00:00";
            }

        } catch (Exception e) {
            log.error("座席工作效率统计查询工单ES报错:", e);
            return "00:00:00";
        }
    }


    /**
     * 查询座席处理时间排名
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param roleId    角色ID
     * @param deptId    部门id
     * @param queryType 查询类型 1、最长top3 2、最短top3 3、所有数据
     * @return 座席处理时间排名
     */
    private List<AgentResolutionTimeVo> agentResolutionTime(LocalDateTime startDate, LocalDateTime endDate, String roleId, String deptId, int queryType) {
        List<AgentResolutionTimeVo> sortedList = new ArrayList<>();
        try {
            // 查询出当前公司下所有的坐席信息
            R<List<UserDetailsVO>> list = userClient.queryUserListByCompanyId(SecurityUtil.getLoginUser().getCompanyId());
            List<UserDetailsVO> userDetailList = list.getData();
            Map<String, UserDetailsVO> userDetailMap = userDetailList.stream()
                    .collect(Collectors.toMap(UserDetailsVO::getUserId, userDetailsVO -> userDetailsVO));

            List<AgentQueryResolutionTimeVo> timeVos = new ArrayList<>();
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).lte(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            // 不查询机器人工单
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            boolQueryBuilder.must(QueryBuilders.existsQuery("agent_id"));
            boolQueryBuilder.must(QueryBuilders.termQuery("status", 3));

            // 如果是座席管理员，则查询当前座席组的数据，也就是当前部门的数据
            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
            }
            boolQueryBuilder.must(QueryBuilders.termQuery("data_status", 1));
            searchSourceBuilder.query(boolQueryBuilder);
            request.source(searchSourceBuilder);
            // 进行查询
            SearchResponse response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                // 计算时间差
                long secondsDiff = Duration.between(ticketInfoIndex.getCreateTime(), ticketInfoIndex.getResolveTime()).getSeconds();
                AgentQueryResolutionTimeVo agentQueryResolutionTimeVo = new AgentQueryResolutionTimeVo();
                // 用户信息部门信息
                UserDetailsVO detailsVO = userDetailMap.get(ticketInfoIndex.getAgentId());
                if(detailsVO == null){
                    continue;
                }
                agentQueryResolutionTimeVo.setTimeDifferenceSeconds(secondsDiff);
                agentQueryResolutionTimeVo.setAgentId(ticketInfoIndex.getAgentId());
                agentQueryResolutionTimeVo.setAgentName(detailsVO.getUserName());
                agentQueryResolutionTimeVo.setDeptId(detailsVO.getDeptId());
                agentQueryResolutionTimeVo.setDeptName(detailsVO.getDeptName());
                timeVos.add(agentQueryResolutionTimeVo);
            }

            if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
                sortedList = timeDataHandle(timeVos,
                        AgentQueryResolutionTimeVo::getAgentId,
                        AgentQueryResolutionTimeVo::getAgentName,
                        queryType);
            } else {
                sortedList = timeDataHandle(timeVos,
                        AgentQueryResolutionTimeVo::getDeptId,
                        AgentQueryResolutionTimeVo::getDeptName,
                        queryType);
            }
        } catch (Exception e) {
            log.error("座席工作效率统计查询工单ES报错:", e);
        }
        return sortedList;
    }

    /**
     * 将坐席数据按时间排名处理
     *
     * @param timeVos       坐席时间数据
     * @param idExtractor   按id分组
     * @param nameExtractor 按名称分组
     * @param queryType     查询类型 1、最长top3 2、最短top3 3、所有数据
     * @return 排序后的数据
     */
    private List<AgentResolutionTimeVo> timeDataHandle(List<AgentQueryResolutionTimeVo> timeVos,
                                                       Function<AgentQueryResolutionTimeVo, String> idExtractor,
                                                       Function<AgentQueryResolutionTimeVo, String> nameExtractor,
                                                       int queryType) {
        List<AgentResolutionTimeVo> sortedList = new ArrayList<>();
        // 获取 agentId 和 agentName 的映射
        Map<String, String> idNames = timeVos.stream()
                .collect(Collectors.toMap(
                        idExtractor,
                        nameExtractor,
                        (existing, replacement) -> existing
                ));
        // 以 agentId 分组并计算每个座席的平均时间，同时保留 agentName
        Map<String, BigDecimal> averageTimePerAgent = timeVos.stream()
                .collect(Collectors.groupingBy(
                        idExtractor,
                        Collectors.collectingAndThen(
                                Collectors.averagingLong(AgentQueryResolutionTimeVo::getTimeDifferenceSeconds),
                                avg -> BigDecimal.valueOf(avg).setScale(2, RoundingMode.HALF_UP)
                        )
                ));
        // 查询最久TOP3
        if (queryType == 1) {
            // 创建一个包含 agentId, agentName 和平均时间的列表，并按平均时间倒序排序
            sortedList = averageTimePerAgent.entrySet().stream()
                    .map(entry -> new AgentResolutionTimeVo(entry.getKey(), idNames.get(entry.getKey()), entry.getValue(), String.format("%02d:%02d:%02d", entry.getValue().longValue() / 3600, (entry.getValue().longValue() % 3600) / 60, entry.getValue().longValue() % 60)))
                    .sorted(Comparator.comparing(AgentResolutionTimeVo::getAverageTime).reversed())
                    .limit(3)
                    .collect(Collectors.toList());
        } else if (queryType == 2) {
            // 计算最短TOP3
            // 创建一个包含 agentId, agentName 和平均时间的列表，并排序
            sortedList = averageTimePerAgent.entrySet().stream()
                    .map(entry -> new AgentResolutionTimeVo(entry.getKey(), idNames.get(entry.getKey()), entry.getValue(), String.format("%02d:%02d:%02d", entry.getValue().longValue() / 3600, (entry.getValue().longValue() % 3600) / 60, entry.getValue().longValue() % 60)))
                    .sorted(Comparator.comparing(AgentResolutionTimeVo::getAverageTime))
                    .limit(3)
                    .collect(Collectors.toList());
        } else {
            // 创建一个包含 agentId, agentName 和平均时间的列表，并排序
            sortedList = averageTimePerAgent.entrySet().stream()
                    .map(entry -> new AgentResolutionTimeVo(entry.getKey(), idNames.get(entry.getKey()), entry.getValue(), String.format("%02d:%02d:%02d", entry.getValue().longValue() / 3600, (entry.getValue().longValue() % 3600) / 60, entry.getValue().longValue() % 60)))
                    .sorted(Comparator.comparing(AgentResolutionTimeVo::getAverageTime))
                    .collect(Collectors.toList());
        }
        return sortedList;
    }


    /**
     * 查询时间段内机器人工单总数量
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param queryType 查询类型 1机器人工单全部类型, 2人工, 3机器人对应类型的  2 web, 3 whatsApp, 4 APP, 5人工工单, 6电话
     * @return 工单数量
     */
    private SearchResponse queryRobotWorkTotalNumber(LocalDateTime startDate, LocalDateTime endDate, int queryType) {

        try {
            // 定义索引名称
            String indexName = ticketIndex + SecurityUtil.getLoginUser().getCompanyId();
            //获取索引
            SearchRequest request = new SearchRequest();
            request.indices(indexName);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().trackTotalHits(true);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).lte(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            // 如果是座席管理员，则查询当前座席组的数据，也就是当前部门的数据

            // 为5则查询不是机器人工单
            if (queryType == 2) {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));

            } else {
                boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", "1001"));
            }
            if (queryType == 3) {
                // 根据渠道类型来进行聚合
                TermsAggregationBuilder typeAgg = AggregationBuilders
                        .terms("channel_type_agg")
                        .field("channel_type_id")
                        .size(10000);
                searchSourceBuilder.aggregation(typeAgg);
            }

            boolQueryBuilder.must(QueryBuilders.termQuery("data_status", 1));
            searchSourceBuilder.query(boolQueryBuilder).size(0);
            request.source(searchSourceBuilder);
            // 进行查询
            return restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("座席工作量报表汇总查询工单ES报错:", e);
            return null;
        }
    }
}

