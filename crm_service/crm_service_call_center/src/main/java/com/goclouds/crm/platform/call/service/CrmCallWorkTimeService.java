package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmCallWorkTime;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordDetailVo;
import com.goclouds.crm.platform.call.domain.vo.WorkTimeDataHandleVO;
import com.goclouds.crm.platform.call.domain.vo.WorkTimeDetailVO;
import com.goclouds.crm.platform.call.domain.vo.WorkTimeInspectionVO;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
* <AUTHOR>
* @description 针对表【crm_call_work_time(工作计划配置)】的数据库操作Service
* @createDate 2025-02-07 16:58:25
*/
public interface CrmCallWorkTimeService extends IService<CrmCallWorkTime> {

    /**
     *  查询工作时间列表
     * @return 工作时间列表
     */
    AjaxResult<IPage<CrmCallWorkTime>> queryWorkTimeList(IPage<Object> pageParam);

    /**
     *  查询工作时间详情
     * @param workTimeId 工作时间ID
     * @return 工作时间详情
     */
    WorkTimeDetailVO workTimeDetail(String workTimeId);

    /**
     *  添加或修改工作时间
     * @param workOrderExtVo 工作时间信息
     * @return 状态
     */
    AjaxResult<Object> addOrUpdateWorkTime(WorkTimeDataHandleVO workOrderExtVo);

    /**
     *  删除工作时间
     * @param workTimeId 工作时间ID
     * @return 状态
     */
    AjaxResult<Object> workTimeDel(String workTimeId);

    /**
     * 校验工作时间接口
     * @param workTimeInspectionVO 请求参数
     * @return 校验时间状态
     */
    AjaxResult<Object> workTimeInspection(WorkTimeInspectionVO workTimeInspectionVO);

    /**
     *  添加渠道时，工作时间下拉
     * @return 当前公司的工作时间
     */
    AjaxResult<Object> channelWorkTimeList();

    Boolean isWithinWorkTime(String workTimeId);
}
