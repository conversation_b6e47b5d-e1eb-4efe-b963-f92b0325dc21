package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordPriorityLevelDef;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordLevelVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_priority_level_def(工单优先级定义表;)】的数据库操作Service
* @createDate 2023-09-21 16:09:26
*/
public interface CrmAgentWorkRecordPriorityLevelDefService extends IService<CrmAgentWorkRecordPriorityLevelDef> {

    List<WorkRecordLevelVO> queryWorkRecordLevel();
}
