package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordDetail;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordDetailMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordDetailService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_detail(工作记录明细)】的数据库操作Service实现
* @createDate 2023-05-25 10:30:19
*/
@Service
public class CrmAgentWorkRecordDetailServiceImpl extends ServiceImpl<CrmAgentWorkRecordDetailMapper, CrmAgentWorkRecordDetail>
    implements CrmAgentWorkRecordDetailService {

}




