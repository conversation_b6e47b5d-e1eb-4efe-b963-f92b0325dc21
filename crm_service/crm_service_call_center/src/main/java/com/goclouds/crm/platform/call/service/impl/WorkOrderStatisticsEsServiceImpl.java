package com.goclouds.crm.platform.call.service.impl;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.goclouds.crm.platform.call.domain.es.TicketInfoIndex;
import com.goclouds.crm.platform.call.domain.es.TicketSatisfaction;
import com.goclouds.crm.platform.call.domain.vo.*;
import com.goclouds.crm.platform.call.domain.vo.statis.*;
import com.goclouds.crm.platform.call.domain.vo.statis.report.*;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordEsResult;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordTypeDefService;
import com.goclouds.crm.platform.call.service.WorkOrderStatisticsEsService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.TicketSlaTypeEnum;
import com.goclouds.crm.platform.common.enums.UserRoleEnum;
import com.goclouds.crm.platform.common.utils.DateUtils;
import com.goclouds.crm.platform.common.utils.ServletUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.system.UserClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysRoleVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysUserVo;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import com.goclouds.crm.platform.utils.TimeZoneUtils;
import com.goclouds.crm.platform.utils.trans.ExportExcelUtil;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 统计中心部分接口 —— 改从es中查询
 * @author: sunlinan
 **/

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkOrderStatisticsEsServiceImpl implements WorkOrderStatisticsEsService {

    private final UserClient userClient;
    private final CrmAgentWorkRecordTypeDefService crmAgentWorkRecordTypeDefService;
    private final RestHighLevelClient restHighLevelClient;

    @Value("${es.ticket-info-index}")
    private String ticketIndex;

    @Override
    public AjaxResult queryAvgHandleTime(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<AgentWorkRecordAvgTimeVo> resultList = new ArrayList<>();
        //查询非机器人工单
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }

        //查询已解决的工单
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        boolQueryBuilder.must(QueryBuilders.termQuery("status", 3));
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        recordList = handleWorkRecordEsResultList(recordList);

        if (CollectionUtils.isNotEmpty(recordList)) {
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, List<WorkOrderRecordEsResult>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
            }
            for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : mapResult.entrySet()) {
                String unionKey = entry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                List<WorkOrderRecordEsResult> eachAgentResultList = entry.getValue();
                //统计当前座席下总的已解决工单数量
                if (CollectionUtils.isNotEmpty(eachAgentResultList)) {
                    long totalTimeMillis = 0; // 总的解决时间，单位为毫秒
                    for (WorkOrderRecordEsResult workRecordResult : eachAgentResultList) {
                        Date createTime = Date.from(workRecordResult.getCreateTime().atZone(ZoneId.systemDefault()).toInstant());
                        Date resolveTime = Date.from(workRecordResult.getResolveTime().atZone(ZoneId.systemDefault()).toInstant());
                        // 计算解决时间时长，单位为毫秒
                        long timeDifference = resolveTime.getTime() - createTime.getTime();
                        totalTimeMillis += timeDifference;
                    }
                    // 计算平均解决时间，单位为小时
                    double averageTimeHours = (double) totalTimeMillis / (eachAgentResultList.size() * 3600000);
                    BigDecimal averageTimeHoursBD = BigDecimal.valueOf(averageTimeHours).setScale(2, RoundingMode.HALF_UP);
                    AgentWorkRecordAvgTimeVo workRecordReportVo = new AgentWorkRecordAvgTimeVo()
                            .setAgentName(agentName)
                            .setRecordTotalTime(totalTimeMillis)
                            .setEachRecordCount(eachAgentResultList.size())
                            .setRecordAvgTime(averageTimeHoursBD);
                    resultList.add(workRecordReportVo);
                }
            }
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult queryWorkOrderIncrement(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<GroupTableDataResult> tableResultList = new ArrayList<>();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(tableResultList);
        }
        // 1-日，2-周，3-月 (默认是周) 4-小时
        Integer timeDimension = seatingWorkloadVo.getTimeDimension();
        if (null == timeDimension) {
            timeDimension = 2;
        }
        Integer renderType = seatingWorkloadVo.getRenderType();
        //先查询真人工单
        List<WorkRecordCommonUseResult> workRecordResult1 = queryDiffTypeWorkOrder(seatingWorkloadVo, 1);
        //再查询机器人工单
        List<WorkRecordCommonUseResult> workRecordResult2 = queryDiffTypeWorkOrder(seatingWorkloadVo, 2);

        //将时间范围进行划分，划分为 ：时/日/周/月
        List<String> timeRangeX = splitDateRange(seatingWorkloadVo.getStartDate(), seatingWorkloadVo.getEndDate(), timeDimension);

        //处理返回的工单数据，根据时间范围进行一一对应
        List<DateWorkOrderCount> realWorkOrderY = handleTimeRangeDataCount(workRecordResult1, timeDimension, timeRangeX);
        List<DateWorkOrderCount> robotWorkOrderY = handleTimeRangeDataCount(workRecordResult2, timeDimension, timeRangeX);

        //由于没有数据也会根据时间维度补0，所以下边的方法，不用对各list进行判空
        HandleDiffTimeDataResult realWorkOrderYResult = handleDiffTimeData(timeRangeX, realWorkOrderY, timeDimension);
        HandleDiffTimeDataResult robotWorkOrderYResult = handleDiffTimeData(timeRangeX, robotWorkOrderY, timeDimension);

        List<GroupBarDataResult> barResultList = new ArrayList<>();
        if (renderType == 2) {
            GroupBarDataResult workOrderIncrement1 = new GroupBarDataResult()
                    .setGroupTypeName(MessageUtils.get("real.work.ticket"))
                    .setBarWorkOrderList(realWorkOrderYResult.getBarDataList());
            GroupBarDataResult workOrderIncrement2 = new GroupBarDataResult()
                    .setGroupTypeName(MessageUtils.get("robot.work.ticket"))
                    .setBarWorkOrderList(robotWorkOrderYResult.getBarDataList());

            barResultList.add(workOrderIncrement1);
            barResultList.add(workOrderIncrement2);
            BarDataResult barDataResult = new BarDataResult().setTimeRangeList(realWorkOrderYResult.getTimeRangeList()).setBarDataResultList(barResultList);
            return AjaxResult.ok(barDataResult);
        } else {
            GroupTableDataResult workOrderIncrement1 = new GroupTableDataResult()
                    .setGroupTypeName(MessageUtils.get("real.work.ticket"))
                    .setTableWorkOrderList(realWorkOrderYResult.getTableDataList());
            GroupTableDataResult workOrderIncrement2 = new GroupTableDataResult()
                    .setGroupTypeName(MessageUtils.get("robot.work.ticket"))
                    .setTableWorkOrderList(robotWorkOrderYResult.getTableDataList());

            tableResultList.add(workOrderIncrement1);
            tableResultList.add(workOrderIncrement2);
            return AjaxResult.ok(tableResultList);
        }
    }

    //处理不同日期格式的字符串，返回精简格式，避免前端渲染过多
    private HandleDiffTimeDataResult handleDiffTimeData(List<String> timeRangeX, List<DateWorkOrderCount> dateWorkOrderCountY, Integer timeDimension) {
        Map<String, BigDecimal> dataMap = new HashMap<>();
        for (DateWorkOrderCount item : dateWorkOrderCountY) {
            dataMap.put(item.getDate(), item.getWorkOrderCount());
        }

        List<String> timeRangeXList = new ArrayList<>();
        List<BigDecimal> workOrderYList = new ArrayList<>();
        List<DiffTimeTableDataResult> tableResultList = new ArrayList<>();
        if (timeDimension == 1) {
            for (String timeRange : timeRangeX) {
                //将yyyy-MM-dd日期格式字符串，只保留MM-dd，且变为MM/dd格式
                String formattedTimeRange = timeRange.substring(5).replace("-", "/");
                timeRangeXList.add(formattedTimeRange);
                BigDecimal num = dataMap.getOrDefault(timeRange, BigDecimal.ZERO);
                workOrderYList.add(num);
                DiffTimeTableDataResult workOrderDateIncrement = new DiffTimeTableDataResult().setTimeRange(formattedTimeRange).setNum(num);
                tableResultList.add(workOrderDateIncrement);
            }
        } else if (timeDimension == 2) {
            for (String timeRange : timeRangeX) {
                String[] dates = timeRange.split("至");
                String startDate = dates[0];
                String endDate = dates[1];
                //将yyyy-MM-dd日期格式字符串，只保留MM-dd，且变为MM/dd格式
                String formattedTimeRange = startDate.substring(5).replace("-", "/") + "-" + endDate.substring(5).replace("-", "/");
                timeRangeXList.add(formattedTimeRange);
                BigDecimal num = dataMap.getOrDefault(timeRange, BigDecimal.ZERO);
                workOrderYList.add(num);
                DiffTimeTableDataResult workOrderDateIncrement = new DiffTimeTableDataResult().setTimeRange(formattedTimeRange).setNum(num);
                tableResultList.add(workOrderDateIncrement);
            }
        } else if (timeDimension == 3) {
            for (String timeRange : timeRangeX) {
                timeRangeXList.add(timeRange);
                BigDecimal num = dataMap.getOrDefault(timeRange, BigDecimal.ZERO);
                workOrderYList.add(num);
                DiffTimeTableDataResult workOrderDateIncrement = new DiffTimeTableDataResult().setTimeRange(timeRange).setNum(num);
                tableResultList.add(workOrderDateIncrement);
            }
        } else if (timeDimension == 4) {
          /*  //将输入的 List<String> 时间范围列表，按照时间顺序进行排序，以便后续能正确地转化为 0-1, 1-2 这样的顺序的格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 使用 Comparator 对时间范围列表进行排序
            Collections.sort(timeRangeX, Comparator.comparing(
                    timeRange -> LocalDateTime.parse(timeRange.split(" 至 ")[0], formatter)));*/
            //由于timeRangeX已经按顺序的了，所以省略如上代码
            for (String timeRange : timeRangeX) {
                String hourAndFormat = getHourAndFormat(timeRange);
                timeRangeXList.add(hourAndFormat);
                BigDecimal num = dataMap.getOrDefault(timeRange, BigDecimal.ZERO);
                workOrderYList.add(num);
                DiffTimeTableDataResult workOrderDateIncrement = new DiffTimeTableDataResult().setTimeRange(hourAndFormat).setNum(num);
                tableResultList.add(workOrderDateIncrement);
            }
        }
        return new HandleDiffTimeDataResult().setTimeRangeList(timeRangeXList).setBarDataList(workOrderYList).setTableDataList(tableResultList);
    }

    @Override
    public AjaxResult querySla(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        //查询非机器人工单
        List<WorkOrderSla> resultList = new ArrayList<>();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }

        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        recordList = handleWorkRecordEsResultList(recordList);

        //如下顺序按照前端来
        Integer[] slaTypes = {
                TicketSlaTypeEnum.PUNCTUAL_SOLVE.getCode(),  // 按时解决
                TicketSlaTypeEnum.TIMEOUT_SOLVE.getCode(), // 超时解决
                TicketSlaTypeEnum.NO_TIMEOUT_UNRESOLVED.getCode(), // 未超时未完成
                TicketSlaTypeEnum.TIMEOUT_DAY_UNRESOLVED.getCode(), // 超时一天未完成
                TicketSlaTypeEnum.TIMEOUT_TWO_DAY_UNRESOLVED.getCode(), // 超时两天未完成
                TicketSlaTypeEnum.TIMEOUT_THREE_DAY_UNRESOLVED.getCode(), // 超时三天未完成
                TicketSlaTypeEnum.TIMEOUT_WEEK_UNRESOLVED.getCode(), // 超时一周未完成
        };
        if (CollectionUtils.isNotEmpty(recordList)) {
            //处理recordList中的时间相关属性，进行时区转换处理
            for (WorkOrderRecordEsResult recordResult : recordList) {
                recordResult.setCreateTime(convertTimeToActualUtcTime(recordResult.getCreateTime()));
                recordResult.setResolveTime(convertTimeToActualUtcTime(recordResult.getResolveTime()));
                recordResult.setShouldResolveTime(convertTimeToActualUtcTime(recordResult.getShouldResolveTime()));
            }
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, List<WorkOrderRecordEsResult>> mapResult = new HashMap<>();
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
            }
            for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : mapResult.entrySet()) {
                String unionKey = entry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                List<WorkOrderRecordEsResult> eachAgentResultList = entry.getValue();
                List<SlaWorkOrderCount> workOrderNumList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(eachAgentResultList)) {
                    for (Integer slaType : slaTypes) {
                        Map<Integer, Integer> resultMap = slaQuerySummary(eachAgentResultList, slaType);
                        if (resultMap.containsKey(slaType)) {
                            workOrderNumList.add(new SlaWorkOrderCount()
                                    .setTypeCode(slaType)
                                    .setTypeName(TransUtil.trans(TicketSlaTypeEnum.getSlaTypeByName(slaType)))
                                    .setEachTotalCount(resultMap.get(slaType)));
                        } else {
                            workOrderNumList.add(new SlaWorkOrderCount()
                                    .setTypeCode(slaType)
                                    .setTypeName(TransUtil.trans(TicketSlaTypeEnum.getSlaTypeByName(slaType)))
                                    .setEachTotalCount(0));
                        }
                    }
                }
                resultList.add(new WorkOrderSla()
                        .setAgentName(agentName)
                        .setWorkOrderNumList(workOrderNumList));

            }
        }
        //处理返回值，使之能够对应前端渲染格式 （如果是堆叠图，用如下格式渲染）
        Integer renderType = seatingWorkloadVo.getRenderType();
        if (renderType == 2) {
            if (CollectionUtils.isNotEmpty(resultList)) {
                List<String> agentNameList = new ArrayList<>();

                Map<String, List<Integer>> finalDataMap = processWorkOrderSla(resultList, slaTypes, agentNameList);

                WorkOrderSlaResult workOrderSlaResult = new WorkOrderSlaResult()
                        .setAgentNameList(agentNameList)
                        .setDataMap(finalDataMap);
                return AjaxResult.ok(workOrderSlaResult);
            }
        }
        return AjaxResult.ok(resultList);
    }

    private Map<String, List<Integer>> processWorkOrderSla(List<WorkOrderSla> resultList, Integer[] slaTypes, List<String> agentNameList) {
        //如下必须用LinkedHashMap，才能保证和slaTypes中的顺序返回一致
        Map<String, List<Integer>> dataMap = new LinkedHashMap<>();

        // 使用 Map<Integer, String> 存储 code 和 slaType 的映射关系
        Map<Integer, String> codeToSlaTypeMap = new LinkedHashMap<>();

        // 初始化 dataMap，根据 slaTypes 的顺序创建空列表
        for (Integer slaType : slaTypes) {
            // 找到对应 slaType 的 SlaWorkOrderCount 对象，并将 code 和 slaType 映射到 codeToSlaTypeMap
            for (WorkOrderSla workOrderSla : resultList) {
                for (SlaWorkOrderCount slaWorkOrderCount : workOrderSla.getWorkOrderNumList()) {
                    if (slaWorkOrderCount.getTypeCode().equals(slaType)) {
                        codeToSlaTypeMap.put(slaType, slaWorkOrderCount.getTypeName());
                        break; // 找到对应 slaType 之后退出循环
                    }
                }
            }

            // 将 code 作为 key 添加到 dataMap 中
            dataMap.put(slaType.toString(), new ArrayList<>());
        }

        for (WorkOrderSla workOrderSla : resultList) {
            // 提取 agentName
            agentNameList.add(workOrderSla.getAgentName());

            // 处理 workOrderNumList
            for (SlaWorkOrderCount slaWorkOrderCount : workOrderSla.getWorkOrderNumList()) {
                Integer code = slaWorkOrderCount.getTypeCode();
                Integer workOrderCount = slaWorkOrderCount.getEachTotalCount();

                // 使用 code 作为 key 在 dataMap 中获取对应的列表，并添加 workOrderCount
                dataMap.get(code.toString()).add(workOrderCount);
            }
        }

        // 将 dataMap 中的 key 替换为 slaType
        Map<String, List<Integer>> finalDataMap = new LinkedHashMap<>();
        for (Integer code : slaTypes) {
            // 使用 codeToSlaTypeMap 获取 slaType
            String slaType = codeToSlaTypeMap.get(code);
            // 将 slaType 作为 key 添加到 finalDataMap 中
            finalDataMap.put(slaType, dataMap.get(code.toString()));
        }
        return finalDataMap;
    }

    @Override
    public AjaxResult queryCustomerWorkRecordTop(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        //查询非机器人工单
        List<WorkRecordTopResult> recordList = new ArrayList<>();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(recordList);
        }

        //先取值客户名称，如果客户名称为空，就取联系方式
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        // 定义脚本，用于模拟SQL中的CASE语句逻辑
        String scriptCode = "if (doc['customer_name'].size() > 0 && doc['customer_name'].value != '') { " +
                " return doc['customer_name'].value; " +
                "} else if (doc['customer_telephone'].size() > 0 && doc['customer_telephone'].value != '') { " +
                " return doc['customer_telephone'].value; " +
                "} else { " +
                " return 'Others'; " +
                "}";

        // 构建聚合部分，模拟SQL中的GROUP BY、CASE、COUNT等
        // 构建聚合部分，直接根据脚本的结果分组
        TermsAggregationBuilder customerNameAgg = AggregationBuilders
                .terms("customerName_agg")
                .script(new Script(scriptCode)) // 根据脚本结果分组
                .subAggregation(AggregationBuilders
                        .count("workOrderCount")
                        .field("work_record_id")); // 统计工作单数量

        // 添加聚合到查询
        searchRequest.source().aggregation(customerNameAgg);
        searchRequest.source().size(0); // 设置为0，因为我们只需要聚合结果，不需要返回文档

        // 执行查询
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        // 处理查询结果
        Terms customerNameTerms = searchResponse.getAggregations().get("customerName_agg");
        if (null != customerNameTerms && CollectionUtils.isNotEmpty(customerNameTerms.getBuckets())) {
            for (Terms.Bucket customerNameBucket : customerNameTerms.getBuckets()) {
                ParsedValueCount workOrderCountAgg = customerNameBucket.getAggregations().get("workOrderCount");
                // 创建结果对象并填充数据
                WorkRecordTopResult result = new WorkRecordTopResult();
                result.setCustomerName(customerNameBucket.getKeyAsString());
                result.setWorkOrderCount((int) workOrderCountAgg.getValue());
                recordList.add(result);
            }
        }
        return AjaxResult.ok(recordList);
    }

    @Override
    public AjaxResult queryAgentSatisfaction(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<SatisfactionResult> recordList = queryDiffSatisfactionFromEs(seatingWorkloadVo);
        List<DiffSatisfactionSummaryResult> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(recordList)) {
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, List<SatisfactionResult>> mapResult = new HashMap<>();
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
            }
            for (Map.Entry<String, List<SatisfactionResult>> entry : mapResult.entrySet()) {
                String unionKey = entry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                List<SatisfactionResult> eachAgentResultList = entry.getValue();
                if (CollectionUtils.isNotEmpty(eachAgentResultList)) {
                    BigDecimal sum = eachAgentResultList.stream()
                            .map(SatisfactionResult::getRating)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    int count = eachAgentResultList.size();
                    //如下写法，7除以2得到的结果是4，故修改
//                    BigDecimal average = count == 0 ? BigDecimal.ZERO : sum.divide(BigDecimal.valueOf(count), BigDecimal.ROUND_HALF_UP);
//                    BigDecimal bigDecimal = average.setScale(1, BigDecimal.ROUND_HALF_UP);// 保留1位小数，并使用四舍五入模式
                    BigDecimal average = count == 0 ? BigDecimal.ZERO : sum.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP);
                    BigDecimal bigDecimal = average.setScale(1, BigDecimal.ROUND_HALF_UP);

                    DiffSatisfactionSummaryResult workRecordReportVo = new DiffSatisfactionSummaryResult()
                            .setDimensionName(agentName)
                            .setRating(bigDecimal);
                    resultList.add(workRecordReportVo);
                }
            }
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult queryWorkOrderTypeSatisfaction(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<SatisfactionResult> recordList = queryDiffSatisfactionFromEs(seatingWorkloadVo);
        List<SatisfactionSummaryVo> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(recordList)) {
            //先将工单类型进行国际化
            String headerLanguage = ServletUtils.getHeaderLanguage();
            List<WorkRecordTypeVO> workRecordTypeVOList = crmAgentWorkRecordTypeDefService.queryWorkRecordTypeByLanguage(headerLanguage);
            Map<String, String> workRecordTypeMap = new HashMap<>();
            Map<String, String> inUseWorkRecordTypeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(workRecordTypeVOList)) {
                workRecordTypeMap = workRecordTypeVOList.stream().collect(Collectors.toMap(WorkRecordTypeVO::getWorkRecordTypeValue, WorkRecordTypeVO::getWorkRecordTypeName));
            }
            for (SatisfactionResult crmAgentWorkRecord : recordList) {
                String workRecordTypeCode = crmAgentWorkRecord.getWorkRecordTypeCode();
                if (StringUtil.isEmpty(workRecordTypeCode)) {
                    crmAgentWorkRecord.setWorkRecordTypeCode("No type");
                    crmAgentWorkRecord.setWorkRecordTypeName("No type");
                    inUseWorkRecordTypeMap.put("No type", "No type");
                } else {
                    String typeName = workRecordTypeMap.get(workRecordTypeCode);
                    if (StringUtil.isEmpty(typeName)) {
                        crmAgentWorkRecord.setWorkRecordTypeName("No type");
                        inUseWorkRecordTypeMap.put(workRecordTypeCode, "No type");
                    } else {
                        crmAgentWorkRecord.setWorkRecordTypeName(typeName);
                        inUseWorkRecordTypeMap.put(workRecordTypeCode, typeName);
                    }
                }
            }

            //国际化之后，进行平均满意度的计算
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, Map<String, List<SatisfactionResult>>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId(),
                                Collectors.groupingBy(p -> TransUtil.trans(p.getWorkRecordTypeName()) + "#" + p.getWorkRecordTypeCode())));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId(),
                                Collectors.groupingBy(p -> TransUtil.trans(p.getWorkRecordTypeName()) + "#" + p.getWorkRecordTypeCode())));
            }
            for (Map.Entry<String, Map<String, List<SatisfactionResult>>> outerEntry : mapResult.entrySet()) {
                String unionKey = outerEntry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                Map<String, List<SatisfactionResult>> eachMap = outerEntry.getValue();
                List<WorkRecordSatisfactionResult> workRecordRateResultList = new ArrayList<>();
                for (Map.Entry<String, List<SatisfactionResult>> innerEntry : eachMap.entrySet()) {
                    String innerUnionKey = innerEntry.getKey();
                    String[] split2 = innerUnionKey.split("#");
                    String typeName = split2[0];
                    String typeCode = split2[1];
                    List<SatisfactionResult> eachWorkOrderList = innerEntry.getValue();
                    if (CollectionUtils.isNotEmpty(eachWorkOrderList)) {
                        BigDecimal sum = eachWorkOrderList.stream()
                                .map(SatisfactionResult::getRating)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        int count = eachWorkOrderList.size();

                        BigDecimal average = count == 0 ? BigDecimal.ZERO : sum.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP);
                        BigDecimal bigDecimal = average.setScale(1, BigDecimal.ROUND_HALF_UP); // 保留1位小数，并使用四舍五入模式

                        WorkRecordSatisfactionResult workRecordReportVo = new WorkRecordSatisfactionResult()
                                .setTypeName(typeName)
                                .setTypeCode(typeCode)
                                .setRating(bigDecimal);
                        workRecordRateResultList.add(workRecordReportVo);
                    }
                }
                processWorkRecordRateResultList(workRecordRateResultList, inUseWorkRecordTypeMap);
                SatisfactionSummaryVo workRecordReportVo = new SatisfactionSummaryVo().setAgentName(agentName).setWorkOrderSatisfactionList(workRecordRateResultList);
                resultList.add(workRecordReportVo);
            }
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult queryChannelSatisfaction(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<SatisfactionSummaryVo> resultList = new ArrayList<>();
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();

        // 定义工单表索引名称
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }

        //查询指定条件下，正在使用中的渠道
        List<InUseChannel> inUseChannelList = queryInUseChannelFromEs(1, startTime, endTime, filterIdList, indexName);
        if (CollectionUtils.isEmpty(inUseChannelList)) {
            return AjaxResult.ok(resultList);
        }
        Map<String, String> channelMap = new HashMap<>();
        for (InUseChannel inUseChannel : inUseChannelList) {
            channelMap.put(inUseChannel.getChannelTypeId(), TransUtil.trans(inUseChannel.getChannelTypeName()));
        }
        //查询工单数据
        List<SatisfactionResult> recordList = queryDiffSatisfactionFromEs(seatingWorkloadVo);
        if (CollectionUtils.isNotEmpty(recordList)) {
            //先将渠道进行国际化
            for (SatisfactionResult crmAgentWorkRecord : recordList) {
                String channelTypeName = crmAgentWorkRecord.getChannelTypeName();
                String trans = TransUtil.trans(channelTypeName);
                crmAgentWorkRecord.setChannelTypeName(trans);
            }
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, Map<String, List<SatisfactionResult>>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId(),
                                Collectors.groupingBy(p -> TransUtil.trans(p.getChannelTypeName()) + "#" + p.getChannelTypeId())));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId(),
                                Collectors.groupingBy(p -> TransUtil.trans(p.getChannelTypeName()) + "#" + p.getChannelTypeId())));
            }
            for (Map.Entry<String, Map<String, List<SatisfactionResult>>> outerEntry : mapResult.entrySet()) {
                String unionKey = outerEntry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                Map<String, List<SatisfactionResult>> eachMap = outerEntry.getValue();
                List<WorkRecordSatisfactionResult> workRecordRateResultList = new ArrayList<>();
                for (Map.Entry<String, List<SatisfactionResult>> innerEntry : eachMap.entrySet()) {
                    String innerUnionKey = innerEntry.getKey();
                    String[] split2 = innerUnionKey.split("#");
                    String typeName = split2[0];
                    String typeCode = split2[1];
                    List<SatisfactionResult> eachWorkOrderList = innerEntry.getValue();
                    if (CollectionUtils.isNotEmpty(eachWorkOrderList)) {
                        BigDecimal sum = eachWorkOrderList.stream()
                                .map(SatisfactionResult::getRating)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        int count = eachWorkOrderList.size();

                        BigDecimal average = count == 0 ? BigDecimal.ZERO : sum.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP);
                        BigDecimal bigDecimal = average.setScale(1, BigDecimal.ROUND_HALF_UP);

                        WorkRecordSatisfactionResult workRecordReportVo = new WorkRecordSatisfactionResult()
                                .setTypeName(typeName)
                                .setTypeCode(typeCode)
                                .setRating(bigDecimal);
                        workRecordRateResultList.add(workRecordReportVo);
                    }
                }
                //将没有数据的满意度内容补0
                processWorkRecordRateResultList(workRecordRateResultList, channelMap);

                SatisfactionSummaryVo workRecordReportVo = new SatisfactionSummaryVo().setAgentName(agentName).setWorkOrderSatisfactionList(workRecordRateResultList);
                resultList.add(workRecordReportVo);
            }
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult queryHandleWorkOrderSum(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<HandleWorkOrderResult> resultList = new ArrayList<>();
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();


        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        recordList = handleWorkRecordEsResultList(recordList);

        if (CollectionUtils.isNotEmpty(recordList)) {
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, List<WorkOrderRecordEsResult>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
            }
            for (Map.Entry<String, List<WorkOrderRecordEsResult>> innerEntry : mapResult.entrySet()) {
                String unionKey = innerEntry.getKey();
                String[] split = unionKey.split("#");
                String xName = split[0];
                List<WorkOrderRecordEsResult> eachStatusWorkOrderList = innerEntry.getValue();
                int totalCount = eachStatusWorkOrderList.size();
                resultList.add(new HandleWorkOrderResult().setAgentName(xName).setWorkOrderCount(totalCount));
            }
            return AjaxResult.ok(resultList);
        }
        return AjaxResult.ok();
    }

    @Override
    public AjaxResult queryWorkOrderGroupByChannel(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        //查询非机器人工单
        String startDate = seatingWorkloadVo.getStartDate();
        String endDate = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        List<WorkRecordReportVo> resultList = new ArrayList<>();
        // 定义工单表索引名称
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }
        //查询指定条件下，正在使用中的渠道
        List<InUseChannel> inUseChannelList = queryInUseChannelFromEs(1, startDate, endDate, filterIdList, indexName);
        if (CollectionUtils.isEmpty(inUseChannelList)) {
            return AjaxResult.ok(resultList);
        }

        Map<String, String> channelMap = new HashMap<>();
        for (InUseChannel inUseChannel : inUseChannelList) {
            channelMap.put(TransUtil.trans(inUseChannel.getChannelTypeName()) + "#" + inUseChannel.getChannelTypeId(), inUseChannel.getChannelTypeId());
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startDate, endDate, filterIdList);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        recordList = handleWorkRecordEsResultList(recordList);

        if (CollectionUtils.isNotEmpty(recordList)) {
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, Map<String, List<WorkOrderRecordEsResult>>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId(),
                                Collectors.groupingBy(p -> TransUtil.trans(p.getChannelTypeName()) + "#" + p.getChannelTypeId())));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                //返回值需要做国际化
                //先按照座席进行分组，再按照渠道进行分组
                //多条件分组（用国际化之后的channelTypeName）
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId(),
                                Collectors.groupingBy(p -> TransUtil.trans(p.getChannelTypeName()) + "#" + p.getChannelTypeId())));
            }
            for (Map.Entry<String, Map<String, List<WorkOrderRecordEsResult>>> outerEntry : mapResult.entrySet()) {
                String unionKey = outerEntry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                Map<String, List<WorkOrderRecordEsResult>> eachChannelMap = outerEntry.getValue();
                List<ChanelWorkRecordNumResult> chanelWorkRecordNumResultList = new ArrayList<>();
                for (Map.Entry<String, String> levelEntry : channelMap.entrySet()) {
                    String channelUnionKey = levelEntry.getKey();
                    String[] split2 = channelUnionKey.split("#");
                    String channelTypeName = split2[0];
                    String channelTypeCode = split2[1];
                    //补充空集合就是补0
                    int size = eachChannelMap.getOrDefault(channelUnionKey, new ArrayList<>()).size();
                    ChanelWorkRecordNumResult chanelWorkRecordNumResult = new ChanelWorkRecordNumResult().setChannelTypeCode(channelTypeCode).setChannelTypeName(channelTypeName).setEachChannelTotalCount(size);
                    chanelWorkRecordNumResultList.add(chanelWorkRecordNumResult);
                }
                WorkRecordReportVo workRecordReportVo = new WorkRecordReportVo().setAgentName(agentName).setWorkOrderCountList(chanelWorkRecordNumResultList);
                resultList.add(workRecordReportVo);
            }
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult queryWorkOrderStatus(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<WorkRecordSummaryVo> resultList = new ArrayList<>();
        String startDate = seatingWorkloadVo.getStartDate();
        String endDate = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        //查询非机器人工单
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }

        //工单状态条件
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery();
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 1));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 2));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 3));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 4));
        statusQueryBuilder.should(QueryBuilders.termQuery("status", 5));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startDate, endDate, filterIdList);
        boolQueryBuilder.must(statusQueryBuilder);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        recordList = handleWorkRecordEsResultList(recordList);

        if (CollectionUtils.isNotEmpty(recordList)) {
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, List<WorkOrderRecordEsResult>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
            }
            for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : mapResult.entrySet()) {
                String unionKey = entry.getKey();
                String[] split = unionKey.split("#");
                String agentName = split[0];
                List<WorkOrderRecordEsResult> eachStatusWorkOrderList = entry.getValue();
                //汇总三种状态的工单数量
                //已解决 status值 为3，4，5
                //未解决 status值 为1，2
                //待回复 status值 为2
                Map<Integer, Integer> statusCounts = eachStatusWorkOrderList.stream()
                        .collect(Collectors.groupingBy(WorkOrderRecordEsResult::getStatus, Collectors.summingInt(i -> 1))); // 使用summingInt统计数量
                int count12 = statusCounts.getOrDefault(1, 0) + statusCounts.getOrDefault(2, 0);
                int count345 = statusCounts.getOrDefault(3, 0) + statusCounts.getOrDefault(4, 0) + statusCounts.getOrDefault(5, 0);
                int count2 = statusCounts.getOrDefault(2, 0);
                WorkRecordNumResult chanelWorkRecordNumResult1 = new WorkRecordNumResult().setTypeCode("1").setTypeName(MessageUtils.get("resolve.ticket.count")).setEachTotalCount(count345);
                WorkRecordNumResult chanelWorkRecordNumResult2 = new WorkRecordNumResult().setTypeCode("2").setTypeName(MessageUtils.get("un.resolve.ticket.count")).setEachTotalCount(count12);
                WorkRecordNumResult chanelWorkRecordNumResult3 = new WorkRecordNumResult().setTypeCode("3").setTypeName(MessageUtils.get("waiting.for.reply.ticket.count")).setEachTotalCount(count2);
                List<WorkRecordNumResult> chanelWorkRecordNumResultList = new ArrayList<>();
                chanelWorkRecordNumResultList.add(chanelWorkRecordNumResult1);
                chanelWorkRecordNumResultList.add(chanelWorkRecordNumResult2);
                chanelWorkRecordNumResultList.add(chanelWorkRecordNumResult3);
                WorkRecordSummaryVo workRecordReportVo = new WorkRecordSummaryVo().setAgentName(agentName).setWorkOrderCountList(chanelWorkRecordNumResultList);
                resultList.add(workRecordReportVo);
            }
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult queryWorkOrderTypeDistribution(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<WorkRecordSummaryVo> resultList = new ArrayList<>();
        String startDate = seatingWorkloadVo.getStartDate();
        String endDate = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        //查询非机器人工单
        //获取语言，用于获取工单类型的国际化内容
        String headerLanguage = ServletUtils.getHeaderLanguage();

        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }
        List<InUseWorkOrderType> inUseWorkOrderTypeList = queryInUseWorkOrderTypeList(seatingWorkloadVo, indexName);
        if (CollectionUtils.isEmpty(inUseWorkOrderTypeList)) {
            return AjaxResult.ok(resultList);
        }

        //查询当前语言下的工单类型
        List<WorkRecordTypeVO> workRecordTypeVOList = crmAgentWorkRecordTypeDefService.queryWorkRecordTypeByLanguage(headerLanguage);
        Map<String, String> workRecordTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(workRecordTypeVOList)) {
            workRecordTypeMap = workRecordTypeVOList.stream().collect(Collectors.toMap(WorkRecordTypeVO::getWorkRecordTypeValue, WorkRecordTypeVO::getWorkRecordTypeName));
        }

        //处理上述处于占用状态的工单类型信息，对类型名称进行国际化
        Map<String, String> workOrderTypeMap = new HashMap<>();
        for (InUseWorkOrderType inUseWorkOrderType : inUseWorkOrderTypeList) {
            String workRecordTypeCode = inUseWorkOrderType.getWorkRecordTypeCode();
            String workRecordTypeName = workRecordTypeMap.get(inUseWorkOrderType.getWorkRecordTypeCode());
            if (StringUtil.isEmpty(workRecordTypeCode)) {
                workRecordTypeCode = "No type";
            }
            if (StringUtil.isEmpty(workRecordTypeName)) {
                workRecordTypeName = "No type";
            }
            workOrderTypeMap.put(workRecordTypeName + "#" + workRecordTypeCode, workRecordTypeName);
        }

        //查询工单数据
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startDate, endDate, filterIdList);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        if (CollectionUtils.isEmpty(recordList)) {
            return AjaxResult.ok(resultList);
        }
        //处理坐席名称
        recordList = handleWorkRecordEsResultList(recordList);

        //处理返回值，将类型名进行国际化
        recordList = handleWorkRecordTypeInternational(recordList, workRecordTypeMap);

        if (CollectionUtils.isNotEmpty(recordList)) {
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, Map<String, List<WorkOrderRecordEsResult>>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId(),
                                Collectors.groupingBy(p -> p.getWorkRecordTypeName() + "#" + p.getWorkRecordTypeCode())));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId(),
                                Collectors.groupingBy(p -> p.getWorkRecordTypeName() + "#" + p.getWorkRecordTypeCode())));
            }
            for (Map.Entry<String, Map<String, List<WorkOrderRecordEsResult>>> outerEntry : mapResult.entrySet()) {
                String unionKey = outerEntry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                Map<String, List<WorkOrderRecordEsResult>> eachChannelMap = outerEntry.getValue();
                List<WorkRecordNumResult> workRecordNumResultList = new ArrayList<>();
                //数据补0，用如下方式也能保证顺序一致，便于后续数据的导出
                for (Map.Entry<String, String> entry : workOrderTypeMap.entrySet()) {
                    String typeUnionKey = entry.getKey();
                    int size = eachChannelMap.getOrDefault(typeUnionKey, new ArrayList<>()).size();
                    String[] split2 = typeUnionKey.split("#");
                    String typeName = split2[0];
                    String typeCode = split2[1];
                    WorkRecordNumResult chanelWorkRecordNumResult = new WorkRecordNumResult().setTypeName(typeName).setTypeCode(typeCode).setEachTotalCount(size);
                    workRecordNumResultList.add(chanelWorkRecordNumResult);
                }

                WorkRecordSummaryVo workRecordReportVo = new WorkRecordSummaryVo().setAgentName(agentName).setWorkOrderCountList(workRecordNumResultList);
                resultList.add(workRecordReportVo);
            }
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult queryWorkOrderLevelDistribution(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<WorkRecordSummaryVo> resultList = new ArrayList<>();
        String startDate = seatingWorkloadVo.getStartDate();
        String endDate = seatingWorkloadVo.getEndDate();
        Integer workOrderStatus = seatingWorkloadVo.getWorkOrderStatus();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }

        //查询非机器人工单
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery();
        if (workOrderStatus != null) {
            //工单状态维度 1-全部 2-未解决 3-已解决
            if ("2".equals(workOrderStatus)) {
                statusQueryBuilder.should(QueryBuilders.termQuery("status", 1));
                statusQueryBuilder.should(QueryBuilders.termQuery("status", 2));
            }
            if ("3".equals(workOrderStatus)) {
                statusQueryBuilder.should(QueryBuilders.termQuery("status", 3));
                statusQueryBuilder.should(QueryBuilders.termQuery("status", 4));
                statusQueryBuilder.should(QueryBuilders.termQuery("status", 5));
            }
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startDate, endDate, filterIdList);
        boolQueryBuilder.must(statusQueryBuilder);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        recordList = handleWorkRecordEsResultList(recordList);

        if (CollectionUtils.isNotEmpty(recordList)) {
            //用如下数据作为基准，没有的数据，补0
            Map<String, String> levelMap = new HashMap<>();
            levelMap.put("P1", "P1");
            levelMap.put("P2", "P2");
            levelMap.put("P3", "P3");
            levelMap.put("P4", "P4");
            levelMap.put("P5", "P5");

            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, Map<String, List<WorkOrderRecordEsResult>>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId(),
                                Collectors.groupingBy(p -> p.getPriorityLevelName())));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId(),
                                Collectors.groupingBy(p -> p.getPriorityLevelName())));
            }
            for (Map.Entry<String, Map<String, List<WorkOrderRecordEsResult>>> outerEntry : mapResult.entrySet()) {
                String unionKey = outerEntry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                Map<String, List<WorkOrderRecordEsResult>> eachChannelMap = outerEntry.getValue();
                List<WorkRecordNumResult> workRecordNumResultList = new ArrayList<>();
                for (Map.Entry<String, String> levelEntry : levelMap.entrySet()) {
                    String levelName = levelEntry.getKey();
                    int size = eachChannelMap.getOrDefault(levelName, new ArrayList<>()).size();
                    WorkRecordNumResult chanelWorkRecordNumResult = new WorkRecordNumResult().setTypeName(levelName).setEachTotalCount(size);
                    workRecordNumResultList.add(chanelWorkRecordNumResult);
                }
                WorkRecordSummaryVo workRecordReportVo = new WorkRecordSummaryVo().setAgentName(agentName).setWorkOrderCountList(workRecordNumResultList);
                resultList.add(workRecordReportVo);
            }
        }
        return AjaxResult.ok(resultList);
    }

    @Override
    public AjaxResult queryHandleTimeDistribution(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<WorkRecordSummaryVo> resultList = new ArrayList<>();
        //查询非机器人工单
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();

        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(resultList);
        }
        //查询已解决的工单
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        boolQueryBuilder.must(QueryBuilders.termQuery("status", 3));
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        recordList = handleWorkRecordEsResultList(recordList);

        if (CollectionUtils.isNotEmpty(recordList)) {
            Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
            String roleId = getRoleType();
            Map<String, List<WorkOrderRecordEsResult>> mapResult = new HashMap<>();
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult = recordList.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
            }
            for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : mapResult.entrySet()) {
                String unionKey = entry.getKey();
                String[] split1 = unionKey.split("#");
                String agentName = split1[0];
                List<WorkOrderRecordEsResult> eachAgentResultList = entry.getValue();
                Integer count1 = 0;
                Integer count2 = 0;
                Integer count3 = 0;
                Integer count4 = 0;
                List<WorkRecordNumResult> workRecordNumResultList = new ArrayList<>();
                //统计当前座席下总的已解决工单数量
                if (CollectionUtils.isNotEmpty(eachAgentResultList)) {
                    for (WorkOrderRecordEsResult workRecordResult : eachAgentResultList) {
                        Date createTime = Date.from(workRecordResult.getCreateTime().atZone(ZoneId.systemDefault()).toInstant());
                        Date resolveTime = Date.from(workRecordResult.getResolveTime().atZone(ZoneId.systemDefault()).toInstant());
                        // 计算解决时间时长，单位为毫秒
                        long timeDifference = resolveTime.getTime() - createTime.getTime();
//                        //将毫秒单位转化为小时
//                        long hours = TimeUnit.MILLISECONDS.toHours(timeDifference);
                        //判断解决时间是在那个时间范围中0-1小时，1-3小时，3-8小时，8以上
                        if (timeDifference > 0 && timeDifference <= 1 * 60 * 60 * 1000) {
                            count1++;
                        } else if (timeDifference > 1 * 60 * 60 * 1000 && timeDifference <= 3 * 60 * 60 * 1000) {
                            count2++;
                        } else if (timeDifference > 3 * 60 * 60 * 1000 && timeDifference <= 8 * 60 * 60 * 1000) {
                            count3++;
                        } else if (timeDifference > 8 * 60 * 60 * 1000) {
                            count4++;
                        }
                    }
                    WorkRecordNumResult workRecordNumResult1 = new WorkRecordNumResult().setTypeCode("1").setTypeName(MessageUtils.get("zero.to.three.hour")).setEachTotalCount(count1);
                    WorkRecordNumResult workRecordNumResult2 = new WorkRecordNumResult().setTypeCode("2").setTypeName(MessageUtils.get("one.to.three.hour")).setEachTotalCount(count2);
                    WorkRecordNumResult workRecordNumResult3 = new WorkRecordNumResult().setTypeCode("3").setTypeName(MessageUtils.get("three.to.eight.hour")).setEachTotalCount(count3);
                    WorkRecordNumResult workRecordNumResult4 = new WorkRecordNumResult().setTypeCode("4").setTypeName(MessageUtils.get("over.eight.hour")).setEachTotalCount(count4);
                    workRecordNumResultList.add(workRecordNumResult1);
                    workRecordNumResultList.add(workRecordNumResult2);
                    workRecordNumResultList.add(workRecordNumResult3);
                    workRecordNumResultList.add(workRecordNumResult4);
                    WorkRecordSummaryVo workRecordReportVo = new WorkRecordSummaryVo().setAgentName(agentName).setWorkOrderCountList(workRecordNumResultList);
                    resultList.add(workRecordReportVo);
                }
            }
        }
        return AjaxResult.ok(resultList);
    }

    public static void main(String[] args) {
        // 计算解决时间时长，单位为毫秒
        long timeDifference = 910000;
        //将毫秒单位转化为小时
        long hours = TimeUnit.MILLISECONDS.toHours(timeDifference);
        System.out.println("解决时间是："+hours);
    }

    @Override
    public AjaxResult queryRobotChannelWorkOrder(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<GroupTableDataResult> tableResultList = new ArrayList<>();
        //查询机器人工单
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        Integer renderType = seatingWorkloadVo.getRenderType();
        Integer timeDimension = seatingWorkloadVo.getTimeDimension();
        if (null == timeDimension) {
            timeDimension = 2;
        }
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(tableResultList);
        }

        //查询当前条件下的数据，然后分组
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(2, startTime, endTime, new ArrayList<>());
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        //机器人工单不需要处理如下dept的问题
        //recordList = handleWorkRecordEsResultList(recordList);
        if (CollectionUtils.isEmpty(recordList)) {
            return AjaxResult.ok(new ArrayList<>());
        }

        List<GroupBarDataResult> barResultList = new ArrayList<>();
        List<String> formatTimeRangeList = new ArrayList<>();
        //将时间范围进行划分，划分为日/周/月
        List<String> timeRangeX = splitDateRange(startTime, endTime, timeDimension);

        Map<String, List<WorkOrderRecordEsResult>> mapResult = recordList.stream()
                .collect(Collectors.groupingBy(p -> p.getChannelTypeName() + "#" + p.getChannelTypeId()));
        for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : mapResult.entrySet()) {
            String unionKey = entry.getKey();
            String[] split = unionKey.split("#");
            String groupName = split[0];
            List<WorkOrderRecordEsResult> eachResultList = entry.getValue();
            if (CollectionUtils.isNotEmpty(eachResultList)) {
                //将返回结果进行处理，从而保证用一些公用方法
                List<WorkRecordCommonUseResult> resultList = new ArrayList<>();
                for (WorkOrderRecordEsResult workRecordResult : eachResultList) {
                    WorkRecordCommonUseResult workRecordCommonUseResult = new WorkRecordCommonUseResult();
                    BeanUtils.copyProperties(workRecordResult, workRecordCommonUseResult);
                    workRecordCommonUseResult.setCreateTime(convertTimeToActualUtcTime(workRecordResult.getCreateTime()));
                    workRecordCommonUseResult.setResolveTime(convertTimeToActualUtcTime(workRecordResult.getResolveTime()));
                    resultList.add(workRecordCommonUseResult);
                }

                //处理该分组下数据，根据时间范围进行一一对应，没有数据也会根据时间维度补0
                List<DateWorkOrderCount> dataList = handleTimeRangeDataCount(resultList, timeDimension, timeRangeX);

                //由于没有数据也会根据时间维度补0，所以下边的方法，不用对各list进行判空
                HandleDiffTimeDataResult workOrderYResult = handleDiffTimeData(timeRangeX, dataList, timeDimension);
                if (renderType == 2) {
                    GroupBarDataResult dataResult = new GroupBarDataResult()
                            .setGroupTypeName(TransUtil.trans(groupName))
                            .setBarWorkOrderList(workOrderYResult.getBarDataList());
                    barResultList.add(dataResult);
                    formatTimeRangeList = workOrderYResult.getTimeRangeList();
                } else {
                    GroupTableDataResult dataResult = new GroupTableDataResult()
                            .setGroupTypeName(TransUtil.trans(groupName))
                            .setTableWorkOrderList(workOrderYResult.getTableDataList());
                    tableResultList.add(dataResult);
                }
            }
        }
        if (renderType == 2) {
            BarDataResult barDataResult = new BarDataResult().setTimeRangeList(formatTimeRangeList).setBarDataResultList(barResultList);
            return AjaxResult.ok(barDataResult);
        }
        return AjaxResult.ok(tableResultList);
    }

    @Override
    public AjaxResult queryWorkOrderIncrementPercent(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            WorkOrderCreateTypeSummary workOrderCreateTypeSummary = new WorkOrderCreateTypeSummary().setRealPercent(BigDecimal.ZERO).setRobotPercent(BigDecimal.ZERO);
            return AjaxResult.ok(workOrderCreateTypeSummary);
        }

        WorkOrderCreateTypeSummary workOrderCreateTypeSummary = queryWorkOrderCreateTypeSummary(startTime, endTime, companyId, indexName);

        //查询数据
        Integer realCount = workOrderCreateTypeSummary.getRealCount();
        //因为用1减去，所以如下robotCount暂时用不到了
        Integer robotCount = workOrderCreateTypeSummary.getRobotCount();

        // 计算总和
        int totalCount = workOrderCreateTypeSummary.getRealCount() + workOrderCreateTypeSummary.getRobotCount();
        if (totalCount == 0) {
            workOrderCreateTypeSummary.setRealPercent(BigDecimal.ZERO).setRobotPercent(BigDecimal.ZERO);
            return AjaxResult.ok(workOrderCreateTypeSummary);
        }
        // 计算 realCount 的占比
        BigDecimal percentage = new BigDecimal(realCount).divide(new BigDecimal(totalCount), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal resultPercent = percentage.multiply(new BigDecimal(100)); //例如此处resultPercent是：33.3300
        BigDecimal realCountRatio = resultPercent.setScale(2, RoundingMode.DOWN); //例如此处bigDecimal是：33.33

        //用100将去上述结果，得到机器人工单的百分比
        BigDecimal robotCountRatio = new BigDecimal(100).subtract(realCountRatio);
//        // 计算 robotCount 的占比
//        BigDecimal robotCountRatio = new BigDecimal(robotCount).divide(new BigDecimal(totalCount), 2, BigDecimal.ROUND_HALF_UP);
        workOrderCreateTypeSummary.setRealPercent(realCountRatio).setRobotPercent(robotCountRatio);
        return AjaxResult.ok(workOrderCreateTypeSummary);
    }

    @Override
    public AjaxResult queryHandleWorkOrderNumTrend(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        Integer renderType = seatingWorkloadVo.getRenderType();
        Integer timeDimension = seatingWorkloadVo.getTimeDimension();
        if (null == timeDimension) {
            timeDimension = 2;
        }
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        List<GroupTableDataResult> tableResultList = new ArrayList<>();
        List<GroupBarDataResult> barResultList = new ArrayList<>();
        List<String> formatTimeRangeList = new ArrayList<>();

        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(tableResultList);
        }

        //将时间范围进行划分，划分为日/周/月
        List<String> timeRangeX = splitDateRange(startTime, endTime, timeDimension);
        //查询当前条件下的数据，然后分组

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        recordList = handleWorkRecordEsResultList(recordList);

        if (CollectionUtils.isEmpty(recordList)) {
            return AjaxResult.ok(new ArrayList<>());
        }

        Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
        String roleId = getRoleType();
        Map<String, List<WorkOrderRecordEsResult>> mapResult = new HashMap<>();
        //用拼接的方式进行分组，是避免出现name相同的情况
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            mapResult = recordList.stream()
                    .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            mapResult = recordList.stream()
                    .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
        }
        // 从所有分组中随机选择 5 个
        // 将分组的 key 集合进行随机排序
        List<String> keys = new ArrayList<>(mapResult.keySet());
        Collections.shuffle(keys);

        // 取前五个 key 对应的分组
        Map<String, List<WorkOrderRecordEsResult>> selectedGroups = keys.stream()
                .limit(5)
                .collect(Collectors.toMap(key -> key, mapResult::get));

        for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : selectedGroups.entrySet()) {
            String unionKey = entry.getKey();
            String[] split = unionKey.split("#");
            String groupName = split[0];
            List<WorkOrderRecordEsResult> eachResultList = entry.getValue();
            if (CollectionUtils.isNotEmpty(eachResultList)) {
                //将返回结果进行处理，从而保证用一些公用方法
                List<WorkRecordCommonUseResult> resultList = new ArrayList<>();
                for (WorkOrderRecordEsResult workRecordResult : eachResultList) {
                    WorkRecordCommonUseResult workRecordCommonUseResult = new WorkRecordCommonUseResult();
                    BeanUtils.copyProperties(workRecordResult, workRecordCommonUseResult);
                    workRecordCommonUseResult.setCreateTime(convertTimeToActualUtcTime(workRecordResult.getCreateTime()));
                    workRecordCommonUseResult.setResolveTime(convertTimeToActualUtcTime(workRecordResult.getResolveTime()));
                    resultList.add(workRecordCommonUseResult);
                }

                //处理该分组下数据，根据时间范围进行一一对应，没有数据也会根据时间维度补0
                List<DateWorkOrderCount> dataList = handleTimeRangeDataCount(resultList, timeDimension, timeRangeX);

                //由于没有数据也会根据时间维度补0，所以下边的方法，不用对各list进行判空
                HandleDiffTimeDataResult workOrderYResult = handleDiffTimeData(timeRangeX, dataList, timeDimension);
                if (renderType == 2) {
                    GroupBarDataResult dataResult = new GroupBarDataResult()
                            .setGroupTypeName(TransUtil.trans(groupName))
                            .setBarWorkOrderList(workOrderYResult.getBarDataList());
                    barResultList.add(dataResult);
                    formatTimeRangeList = workOrderYResult.getTimeRangeList();
                } else {
                    GroupTableDataResult dataResult = new GroupTableDataResult()
                            .setGroupTypeName(TransUtil.trans(groupName))
                            .setTableWorkOrderList(workOrderYResult.getTableDataList());
                    tableResultList.add(dataResult);
                }
            }
        }
        if (renderType == 2) {
            BarDataResult barDataResult = new BarDataResult().setTimeRangeList(formatTimeRangeList).setBarDataResultList(barResultList);
            return AjaxResult.ok(barDataResult);
        }
        return AjaxResult.ok(tableResultList);
    }

    @Override
    public AjaxResult queryAgentSatisfactionTrend(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<GroupTableDataResult> tableResultList = new ArrayList<>();
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        Integer renderType = seatingWorkloadVo.getRenderType();
        Integer timeDimension = seatingWorkloadVo.getTimeDimension();
        if (null == timeDimension) {
            timeDimension = 2;
        }
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(tableResultList);
        }

        //查询当前条件下的数据，然后分组
        List<SatisfactionResult> recordList = queryDiffSatisfactionFromEs(seatingWorkloadVo);
        if (CollectionUtils.isEmpty(recordList)) {
            return AjaxResult.ok(new ArrayList<>());
        }
        recordList = handleSatisfactionResultList(recordList);

        List<GroupBarDataResult> barResultList = new ArrayList<>();
        List<String> formatTimeRangeList = new ArrayList<>();
        //将时间范围进行划分，划分为日/周/月
        List<String> timeRangeX = splitDateRange(startTime, endTime, timeDimension);

        Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
        String roleId = getRoleType();
        Map<String, List<SatisfactionResult>> mapResult = new HashMap<>();
        //用拼接的方式进行分组，是避免出现name相同的情况
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            mapResult = recordList.stream()
                    .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            mapResult = recordList.stream()
                    .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
        }
        // 从所有分组中随机选择 5 个
        // 将分组的 key 集合进行随机排序
        List<String> keys = new ArrayList<>(mapResult.keySet());
        Collections.shuffle(keys);

        // 取前五个 key 对应的分组
        Map<String, List<SatisfactionResult>> selectedGroups = keys.stream()
                .limit(5)
                .collect(Collectors.toMap(key -> key, mapResult::get));

        for (Map.Entry<String, List<SatisfactionResult>> entry : selectedGroups.entrySet()) {
            String unionKey = entry.getKey();
            String[] split = unionKey.split("#");
            String groupName = split[0];
            List<SatisfactionResult> eachResultList = entry.getValue();
            if (CollectionUtils.isNotEmpty(eachResultList)) {
                //将返回结果进行处理，从而保证用一些公用方法
                List<WorkRecordCommonUseResult> resultList = new ArrayList<>();
                for (SatisfactionResult workRecordResult : eachResultList) {
                    WorkRecordCommonUseResult workRecordCommonUseResult = new WorkRecordCommonUseResult();
                    BeanUtils.copyProperties(workRecordResult, workRecordCommonUseResult);
                    workRecordCommonUseResult.setCreateTime(convertTimeToActualUtcTime(workRecordResult.getCreateTime()));
                    resultList.add(workRecordCommonUseResult);
                }

                //处理该分组下数据，根据时间范围进行一一对应，没有数据也会根据时间维度补0
                List<DateWorkOrderCount> dataList = handleTimeRangeDataSatisfaction(resultList, timeDimension, timeRangeX);

                //由于没有数据也会根据时间维度补0，所以下边的方法，不用对各list进行判空
                HandleDiffTimeDataResult workOrderYResult = handleDiffTimeData(timeRangeX, dataList, timeDimension);
                if (renderType == 2) {
                    GroupBarDataResult dataResult = new GroupBarDataResult()
                            .setGroupTypeName(TransUtil.trans(groupName))
                            .setBarWorkOrderList(workOrderYResult.getBarDataList());
                    barResultList.add(dataResult);
                    formatTimeRangeList = workOrderYResult.getTimeRangeList();
                } else {
                    GroupTableDataResult dataResult = new GroupTableDataResult()
                            .setGroupTypeName(TransUtil.trans(groupName))
                            .setTableWorkOrderList(workOrderYResult.getTableDataList());
                    tableResultList.add(dataResult);
                }
            }
        }
        if (renderType == 2) {
            BarDataResult barDataResult = new BarDataResult().setTimeRangeList(formatTimeRangeList).setBarDataResultList(barResultList);
            return AjaxResult.ok(barDataResult);
        }
        return AjaxResult.ok(tableResultList);
    }

    @Override
    public AjaxResult queryHandleWorkOrderTimeTrend(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<GroupTableDataResult> tableResultList = new ArrayList<>();
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(tableResultList);
        }
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        Integer renderType = seatingWorkloadVo.getRenderType();
        Integer timeDimension = seatingWorkloadVo.getTimeDimension();
        if (null == timeDimension) {
            timeDimension = 2;
        }
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();

        //查询已解决的工单，然后分组
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        boolQueryBuilder.must(QueryBuilders.termQuery("status", 3));
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        if (CollectionUtils.isEmpty(recordList)) {
            return AjaxResult.ok(tableResultList);
        }
        recordList = handleWorkRecordEsResultList(recordList);

        List<GroupBarDataResult> barResultList = new ArrayList<>();
        List<String> formatTimeRangeList = new ArrayList<>();
        //将时间范围进行划分，划分为日/周/月
        List<String> timeRangeX = splitDateRange(startTime, endTime, timeDimension);

        Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
        String roleId = getRoleType();
        Map<String, List<WorkOrderRecordEsResult>> mapResult = new HashMap<>();
        //用拼接的方式进行分组，是避免出现name相同的情况
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            mapResult = recordList.stream()
                    .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            mapResult = recordList.stream()
                    .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
        }

        // 从所有分组中随机选择 5 个
        // 将分组的 key 集合进行随机排序
        List<String> keys = new ArrayList<>(mapResult.keySet());
        Collections.shuffle(keys);

        // 取前五个 key 对应的分组
        Map<String, List<WorkOrderRecordEsResult>> selectedGroups = keys.stream()
                .limit(5)
                .collect(Collectors.toMap(key -> key, mapResult::get));

        for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : selectedGroups.entrySet()) {
            String unionKey = entry.getKey();
            String[] split = unionKey.split("#");
            String groupName = split[0];
            List<WorkOrderRecordEsResult> eachResultList = entry.getValue();
            if (CollectionUtils.isNotEmpty(eachResultList)) {
                //将返回结果进行处理，从而保证用一些公用方法
                List<WorkRecordCommonUseResult> resultList = new ArrayList<>();
                for (WorkOrderRecordEsResult workRecordResult : eachResultList) {
                    WorkRecordCommonUseResult workRecordCommonUseResult = new WorkRecordCommonUseResult();
                    BeanUtils.copyProperties(workRecordResult, workRecordCommonUseResult);
                    workRecordCommonUseResult.setCreateTime(convertTimeToActualUtcTime(workRecordResult.getCreateTime()));
                    workRecordCommonUseResult.setResolveTime(convertTimeToActualUtcTime(workRecordResult.getResolveTime()));
                    resultList.add(workRecordCommonUseResult);
                }

                //处理该分组下数据，根据时间范围进行一一对应，没有数据也会根据时间维度补0
                List<DateWorkOrderCount> dataList = handleTimeRangeDataAvgHandleTime(resultList, timeDimension, timeRangeX);

                //由于没有数据也会根据时间维度补0，所以下边的方法，不用对各list进行判空
                HandleDiffTimeDataResult workOrderYResult = handleDiffTimeData(timeRangeX, dataList, timeDimension);
                if (renderType == 2) {
                    GroupBarDataResult dataResult = new GroupBarDataResult()
                            .setGroupTypeName(TransUtil.trans(groupName))
                            .setBarWorkOrderList(workOrderYResult.getBarDataList());
                    barResultList.add(dataResult);
                    formatTimeRangeList = workOrderYResult.getTimeRangeList();
                } else {
                    GroupTableDataResult dataResult = new GroupTableDataResult()
                            .setGroupTypeName(TransUtil.trans(groupName))
                            .setTableWorkOrderList(workOrderYResult.getTableDataList());
                    tableResultList.add(dataResult);
                }
            }
        }
        if (renderType == 2) {
            BarDataResult barDataResult = new BarDataResult().setTimeRangeList(formatTimeRangeList).setBarDataResultList(barResultList);
            return AjaxResult.ok(barDataResult);
        }
        return AjaxResult.ok(tableResultList);
    }

    //不考虑工单状态，因为任何状态的工单都可以进行用户评分
    @Override
    public AjaxResult queryWorkOrderFeedBack(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<WorkOrderFeedBack> resultList = new ArrayList<>();
        Map<String, BigDecimal> baseCountMap = new HashMap<>();
        Map<String, BigDecimal> satisfactionMap = new HashMap<>();
        //已评价工单数占未评价工单数的比例
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();

        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return AjaxResult.ok(new ArrayList<>());
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList1 = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);
        if (CollectionUtils.isEmpty(recordList1)) {
            return AjaxResult.ok(resultList);
        }
        recordList1 = handleWorkRecordEsResultList(recordList1);
        //回评率，只要length>0即可，不用考虑有多少条 （所以查询出上述数据之后，从中再获取评价字段不为空的数据，就是回评数据）
        List<WorkOrderRecordEsResult> recordList2 = recordList1.stream()
                .filter(record -> record.getTicketSatisfaction() != null && !record.getTicketSatisfaction().isEmpty())
                .collect(Collectors.toList());

        Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
        String roleId = getRoleType();
        //座席或者座席组下的工单
        Map<String, List<WorkOrderRecordEsResult>> mapResult1 = new HashMap<>();
        //用拼接的方式进行分组，是避免出现name相同的情况
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            mapResult1 = recordList1.stream()
                    .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            mapResult1 = recordList1.stream()
                    .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
        }

        for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : mapResult1.entrySet()) {
            String unionKey = entry.getKey();
            List<WorkOrderRecordEsResult> eachResultList = entry.getValue();
            int size = eachResultList.size();
            baseCountMap.put(unionKey, new BigDecimal(size));
            //已评分工单数量，初始值设置为0
            satisfactionMap.put(unionKey, BigDecimal.ZERO);
        }

        //已经进行了评分的工单
        //如果都没有评分数据，默认设置为0
        Map<String, List<WorkOrderRecordEsResult>> mapResult2 = new HashMap<>();
        if (CollectionUtils.isNotEmpty(recordList2)) {
            //用拼接的方式进行分组，是避免出现name相同的情况
            if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
                mapResult2 = recordList2.stream()
                        .collect(Collectors.groupingBy(p -> getDeptNameByDeptId(p.getDeptId(), deptInfoMap) + "#" + p.getDeptId()));
            } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
                mapResult2 = recordList2.stream()
                        .collect(Collectors.groupingBy(p -> p.getAgentName() + "#" + p.getAgentId()));
            }

            for (Map.Entry<String, List<WorkOrderRecordEsResult>> entry : mapResult2.entrySet()) {
                String unionKey = entry.getKey();
                List<WorkOrderRecordEsResult> eachResultList = entry.getValue();
                int size = eachResultList.size();
                //根据实际情况，更新之前默认设置为0的数据
                satisfactionMap.put(unionKey, new BigDecimal(size));
            }
        }
        //计算回评率
        for (String data : baseCountMap.keySet()) {
            String[] split = data.split("#");
            String groupName = split[0];

            BigDecimal totalCount = baseCountMap.get(data);
            BigDecimal satisfactionCount = satisfactionMap.get(data);

            BigDecimal percentage = satisfactionCount.divide(totalCount, 4, BigDecimal.ROUND_HALF_UP); //例如此处percentage是：0.3333
            BigDecimal resultPercent = percentage.multiply(new BigDecimal(100)); //例如此处resultPercent是：33.3300
            BigDecimal feedBack = resultPercent.setScale(2, RoundingMode.DOWN); //例如此处bigDecimal是：33.33

            WorkOrderFeedBack workOrderFeedBack = new WorkOrderFeedBack().setAgentName(groupName).setPercent(feedBack);
            resultList.add(workOrderFeedBack);
        }
        //根据回评率从大到小进行排序
        List<WorkOrderFeedBack> sortedList = resultList.stream()
                .sorted(Comparator.comparing(WorkOrderFeedBack::getPercent).reversed())
                .collect(Collectors.toList());

        return AjaxResult.ok(sortedList);
    }

    //从es中查询工单满意度
    private List<SatisfactionResult> queryDiffSatisfactionFromEs(SeatingWorkloadNewVo seatingWorkloadVo) throws Exception {
        List<SatisfactionResult> recordList = new ArrayList<>();
        //查询非机器人工单
        //自动工单没有工单类型，设置为No type
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        AjaxResult ajaxResult = checkIndexExist(indexName);
        if (ajaxResult.getCode() == 201) {
            //如果校验index信息，返回的是201，那么直接返回空数据
            return recordList;
        }
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
        //除了共有的查询条件，还需要补充的是，满意度数据条件，不为null，且list集合数据size大于0
        //es中将满意度和工单表整到了一起，满意度字段是个集合属性，用如下方式
        // 构建第一个 nested 查询：检查 ticket_satisfaction 是否存在
        QueryBuilder firstNestedQuery = QueryBuilders.nestedQuery(
                "ticket_satisfaction",
                QueryBuilders.existsQuery("ticket_satisfaction"),
                ScoreMode.None
        );

        // 构建第二个 nested 查询：检查 ticket_satisfaction.some_field_in_nested 是否存在 （some_field_in_nested，要替换为实际的字段名称）
        QueryBuilder secondNestedQuery = QueryBuilders.nestedQuery(
                "ticket_satisfaction",
                QueryBuilders.boolQuery()
//                        .must(QueryBuilders.existsQuery("ticket_satisfaction.some_field_in_nested")),
                        .must(QueryBuilders.existsQuery("ticket_satisfaction.rating")),
                ScoreMode.None
        );

        // 组合两个 nested 查询到一个 bool 查询中
        boolQueryBuilder
                .filter(firstNestedQuery)
                .filter(secondNestedQuery);


        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQueryBuilder);

        // 执行查询
        List<WorkOrderRecordEsResult> esResultList = queryWorkOrderRecordEsResult(indexName, sourceBuilder);
        //处理返回值数据中的满意度评分数据（按照满意度评分的总数据，进行返回值集合的组装，便于后续分组计算）
        if (CollectionUtils.isNotEmpty(esResultList)) {
            for (WorkOrderRecordEsResult workOrderRecordEsResult : esResultList) {
                List<TicketSatisfaction> ticketSatisfactionList = workOrderRecordEsResult.getTicketSatisfaction();
                for (TicketSatisfaction ticketSatisfaction : ticketSatisfactionList) {
                    SatisfactionResult satisfactionResult = new SatisfactionResult();
                    satisfactionResult.setDeptId(workOrderRecordEsResult.getDeptId());
                    satisfactionResult.setAgentId(workOrderRecordEsResult.getAgentId());
                    satisfactionResult.setAgentName(workOrderRecordEsResult.getAgentName());
                    satisfactionResult.setChannelTypeId(workOrderRecordEsResult.getChannelTypeId());
                    satisfactionResult.setChannelTypeName(workOrderRecordEsResult.getChannelTypeName());
                    satisfactionResult.setWorkRecordTypeCode(workOrderRecordEsResult.getWorkRecordTypeCode());
                    satisfactionResult.setWorkRecordTypeName(workOrderRecordEsResult.getWorkRecordTypeName());
                    satisfactionResult.setRating(new BigDecimal(ticketSatisfaction.getRating()));
                    satisfactionResult.setCreateTime(workOrderRecordEsResult.getCreateTime());
                    recordList.add(satisfactionResult);
                }
            }
            recordList = handleSatisfactionResultList(recordList);
        }
        return recordList;
    }

    private Map<Integer, Integer> slaQuerySummary(List<WorkOrderRecordEsResult> recordList, Integer value) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 根据不同条件进行数据汇总
        Map<Integer, Integer> result = recordList.stream()
                .filter(record -> {
                    // 计算时间差
                    long hoursDifferenceShouldResolve = Duration.between(record.getShouldResolveTime(), now).toHours();
                    long hoursDifferenceResolve = record.getCreateTime() != null ? Duration.between(record.getCreateTime(), record.getShouldResolveTime()).toHours() : Long.MIN_VALUE;

                    switch (value) {
                        case 1: // 超时24小时内未完成
                            return hoursDifferenceShouldResolve >= 0 && hoursDifferenceShouldResolve < 24 &&
                                    (record.getStatus() == 1 || record.getStatus() == 2);
                        case 2: // 超时一天未完成
                            return hoursDifferenceShouldResolve >= 24 && hoursDifferenceShouldResolve < 48 &&
                                    (record.getStatus() == 1 || record.getStatus() == 2);
                        case 3: // 超时两天未完成
                            return hoursDifferenceShouldResolve >= 48 && hoursDifferenceShouldResolve < 72 &&
                                    (record.getStatus() == 1 || record.getStatus() == 2);
                        case 4: // 超时三天未完成
                            return hoursDifferenceShouldResolve >= 72 && hoursDifferenceShouldResolve < 168 &&
                                    (record.getStatus() == 1 || record.getStatus() == 2);
                        case 5: // 超时一周未完成
                            return hoursDifferenceShouldResolve >= 168 &&
                                    (record.getStatus() == 1 || record.getStatus() == 2);
                        case 6: // 未超时未完成
                            return hoursDifferenceShouldResolve < 0 &&
                                    (record.getStatus() == 1 || record.getStatus() == 2);
                        case 7: // 超时解决
                            return hoursDifferenceResolve >= 0 && record.getStatus() == 3;
                        case 8: // 按时解决
                            return hoursDifferenceResolve < 0 && record.getStatus() == 3;
                        default:
                            return false;
                    }
                })
                .collect(Collectors.groupingBy(record -> value,
                        Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));
        return result;
    }

    //返回数据为工单数量
    private List<DateWorkOrderCount> handleTimeRangeDataCount(List<WorkRecordCommonUseResult> recordList, Integer timeDimension, List<String> dateRangeList) {
        List<DateWorkOrderCount> dateWorkOrderCountList = new ArrayList<>();
        Map<String, BigDecimal> dateRangeCountMap = new HashMap<>();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将所有的日期范围数据的初始值设置为0
        for (String dateRange : dateRangeList) {
            dateRangeCountMap.put(dateRange, BigDecimal.ZERO);
        }
        if (CollectionUtils.isNotEmpty(recordList)) {
            for (WorkRecordCommonUseResult record : recordList) {
                LocalDateTime createTime = record.getCreateTime();
                for (String dateRange : dateRangeList) {
                    String[] rangeParts;
                    LocalDate startRangeDate;
                    LocalDate endRangeDate;
                    LocalDateTime startRangeDateTime;
                    LocalDateTime endRangeDateTime;
                    switch (timeDimension) {
                        case 1:
                            if (createTime.format(dateFormatter).equals(dateRange)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange).add(new BigDecimal("1")));
                            }
                            break;
                        case 2:
                            rangeParts = dateRange.split("至");
                            startRangeDate = LocalDate.parse(rangeParts[0], dateFormatter);
                            endRangeDate = LocalDate.parse(rangeParts[1], dateFormatter);
                            LocalDate createDate = createTime.toLocalDate();
                            if (!createDate.isBefore(startRangeDate) && !createDate.isAfter(endRangeDate)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange).add(new BigDecimal("1")));
                            }
                            break;
                        case 3:
                            if (createTime.format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM)).equals(dateRange)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange).add(new BigDecimal("1")));
                            }
                            break;
                        case 4:
                            rangeParts = dateRange.split(" 至 ");
                            startRangeDateTime = LocalDateTime.parse(rangeParts[0], dateTimeFormatter);
                            endRangeDateTime = LocalDateTime.parse(rangeParts[1], dateTimeFormatter);
                            if (createTime.isAfter(startRangeDateTime) && !createTime.isAfter(endRangeDateTime)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange).add(new BigDecimal("1")));
                            }
                            break;
                    }
                }
            }

            // 将 map 转为 list
            for (Map.Entry<String, BigDecimal> entry : dateRangeCountMap.entrySet()) {
                dateWorkOrderCountList.add(new DateWorkOrderCount()
                        .setDate(entry.getKey())
                        .setWorkOrderCount(entry.getValue()));
            }
        }
        return dateWorkOrderCountList;
    }

    //返回数据为工单满意度
    private List<DateWorkOrderCount> handleTimeRangeDataSatisfaction(List<WorkRecordCommonUseResult> recordList, Integer timeDimension, List<String> dateRangeList) {
        List<DateWorkOrderCount> dateWorkOrderCountList = new ArrayList<>();
        Map<String, Integer> dateRangeCountMap = new HashMap<>();
        Map<String, BigDecimal> dateRangeTotalRatingMap = new HashMap<>();
        Map<String, BigDecimal> dateRangeAvgRatingMap = new HashMap<>();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将所有的日期范围数据的工单个数初始值设置为0
        for (String dateRange : dateRangeList) {
            dateRangeCountMap.put(dateRange, 0);
        }
        // 将所有日期范围数据的满意度总和初始值设置为0
        for (String dateRange : dateRangeList) {
            dateRangeTotalRatingMap.put(dateRange, BigDecimal.ZERO);
        }
        // 将所有日期范围数据的满意度平均值初始值设置为0
        for (String dateRange : dateRangeList) {
            dateRangeAvgRatingMap.put(dateRange, BigDecimal.ZERO);
        }

        if (CollectionUtils.isNotEmpty(recordList)) {
            for (WorkRecordCommonUseResult record : recordList) {
                LocalDateTime createTime = record.getCreateTime();
                BigDecimal rating = record.getRating();
                for (String dateRange : dateRangeList) {
                    String[] rangeParts;
                    LocalDate startRangeDate;
                    LocalDate endRangeDate;
                    LocalDateTime startRangeDateTime;
                    LocalDateTime endRangeDateTime;
                    switch (timeDimension) {
                        case 1:
                            if (createTime.format(dateFormatter).equals(dateRange)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange) + 1);
                                dateRangeTotalRatingMap.put(dateRange, dateRangeTotalRatingMap.get(dateRange).add(rating));
                            }
                            break;
                        case 2:
                            rangeParts = dateRange.split("至");
                            startRangeDate = LocalDate.parse(rangeParts[0], dateFormatter);
                            endRangeDate = LocalDate.parse(rangeParts[1], dateFormatter);
                            LocalDate createDate = createTime.toLocalDate();
                            if (!createDate.isBefore(startRangeDate) && !createDate.isAfter(endRangeDate)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange) + 1);
                                dateRangeTotalRatingMap.put(dateRange, dateRangeTotalRatingMap.get(dateRange).add(rating));
                            }
                            break;
                        case 3:
                            if (createTime.format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM)).equals(dateRange)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange) + 1);
                                dateRangeTotalRatingMap.put(dateRange, dateRangeTotalRatingMap.get(dateRange).add(rating));
                            }
                            break;
                        case 4:
                            rangeParts = dateRange.split(" 至 ");
                            startRangeDateTime = LocalDateTime.parse(rangeParts[0], dateTimeFormatter);
                            endRangeDateTime = LocalDateTime.parse(rangeParts[1], dateTimeFormatter);
                            if (createTime.isAfter(startRangeDateTime) && !createTime.isAfter(endRangeDateTime)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange) + 1);
                                dateRangeTotalRatingMap.put(dateRange, dateRangeTotalRatingMap.get(dateRange).add(rating));
                            }
                            break;
                    }
                }
            }

            // 计算平均评分并保存到dateRangeAvgRatingMap中
            for (String date : dateRangeCountMap.keySet()) {
                // 获取对应日期的总评分和评分次数
                BigDecimal totalRating = dateRangeTotalRatingMap.get(date);
                Integer count = dateRangeCountMap.get(date);

                // 如果除数是0，返回结果为0
                if (count == 0) {
                    dateRangeAvgRatingMap.put(date, BigDecimal.ZERO);
                } else {
                    // 计算平均评分
                    BigDecimal avgRating = totalRating.divide(new BigDecimal(count), 2, BigDecimal.ROUND_HALF_UP); // 保留两位小数，四舍五入

                    // 将平均评分保存到dateRangeAvgRatingMap中
                    dateRangeAvgRatingMap.put(date, avgRating);
                }
            }

            // 将 map 转为 list
            for (Map.Entry<String, BigDecimal> entry : dateRangeAvgRatingMap.entrySet()) {
                dateWorkOrderCountList.add(new DateWorkOrderCount()
                        .setDate(entry.getKey())
                        .setWorkOrderCount(entry.getValue()));
            }
        }
        return dateWorkOrderCountList;
    }

    //返回数据为工单平均处理时长
    private List<DateWorkOrderCount> handleTimeRangeDataAvgHandleTime(List<WorkRecordCommonUseResult> recordList, Integer timeDimension, List<String> dateRangeList) {
        List<DateWorkOrderCount> dateWorkOrderCountList = new ArrayList<>();
        Map<String, Integer> dateRangeCountMap = new HashMap<>();
        Map<String, BigDecimal> dateRangeTotalResolveTimeMap = new HashMap<>();
        Map<String, BigDecimal> dateRangeAvgResolveTimeMap = new HashMap<>();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将所有的日期范围数据的工单个数初始值设置为0
        for (String dateRange : dateRangeList) {
            dateRangeCountMap.put(dateRange, 0);
        }
        // 将所有日期范围数据的处理时间总和初始值设置为0
        for (String dateRange : dateRangeList) {
            dateRangeTotalResolveTimeMap.put(dateRange, BigDecimal.ZERO);
        }
        // 将所有日期范围数据的处理时间平均值初始值设置为0
        for (String dateRange : dateRangeList) {
            dateRangeAvgResolveTimeMap.put(dateRange, BigDecimal.ZERO);
        }

        if (CollectionUtils.isNotEmpty(recordList)) {
            for (WorkRecordCommonUseResult record : recordList) {
                LocalDateTime createTime = record.getCreateTime();
                LocalDateTime resolveTime = record.getResolveTime();
                for (String dateRange : dateRangeList) {
                    String[] rangeParts;
                    LocalDate startRangeDate;
                    LocalDate endRangeDate;
                    LocalDateTime startRangeDateTime;
                    LocalDateTime endRangeDateTime;
                    switch (timeDimension) {
                        case 1:
                            if (createTime.format(dateFormatter).equals(dateRange)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange) + 1);
                                BigDecimal timeDifference = calTimeDifference(createTime, resolveTime);
                                dateRangeTotalResolveTimeMap.put(dateRange, dateRangeTotalResolveTimeMap.get(dateRange).add(timeDifference));
                            }
                            break;
                        case 2:
                            rangeParts = dateRange.split("至");
                            startRangeDate = LocalDate.parse(rangeParts[0], dateFormatter);
                            endRangeDate = LocalDate.parse(rangeParts[1], dateFormatter);
                            LocalDate createDate = createTime.toLocalDate();
                            if (!createDate.isBefore(startRangeDate) && !createDate.isAfter(endRangeDate)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange) + 1);
                                BigDecimal timeDifference = calTimeDifference(createTime, resolveTime);
                                dateRangeTotalResolveTimeMap.put(dateRange, dateRangeTotalResolveTimeMap.get(dateRange).add(timeDifference));
                                ;
                            }
                            break;
                        case 3:
                            if (createTime.format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM)).equals(dateRange)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange) + 1);
                                BigDecimal timeDifference = calTimeDifference(createTime, resolveTime);
                                dateRangeTotalResolveTimeMap.put(dateRange, dateRangeTotalResolveTimeMap.get(dateRange).add(timeDifference));
                            }
                            break;
                        case 4:
                            rangeParts = dateRange.split(" 至 ");
                            startRangeDateTime = LocalDateTime.parse(rangeParts[0], dateTimeFormatter);
                            endRangeDateTime = LocalDateTime.parse(rangeParts[1], dateTimeFormatter);
                            if (createTime.isAfter(startRangeDateTime) && !createTime.isAfter(endRangeDateTime)) {
                                dateRangeCountMap.put(dateRange, dateRangeCountMap.get(dateRange) + 1);
                                BigDecimal timeDifference = calTimeDifference(createTime, resolveTime);
                                dateRangeTotalResolveTimeMap.put(dateRange, dateRangeTotalResolveTimeMap.get(dateRange).add(timeDifference));
                            }
                            break;
                    }
                }
            }

            // 计算平均处理时长
            for (String date : dateRangeCountMap.keySet()) {
                // 获取对应日期的总处理时长和工单个数
                BigDecimal total = dateRangeTotalResolveTimeMap.get(date);
                Integer count = dateRangeCountMap.get(date);

                // 如果除数是0，返回结果为0
                if (count == 0) {
                    dateRangeAvgResolveTimeMap.put(date, BigDecimal.ZERO);
                } else {
                    // 计算平均时间，单位毫秒
                    BigDecimal avgTimeInMillis = total.divide(new BigDecimal(count), 2, BigDecimal.ROUND_HALF_UP);
                    // 将毫秒转换为小时
                    BigDecimal avgTimeInHours = avgTimeInMillis.divide(new BigDecimal(1000 * 60 * 60), 2, BigDecimal.ROUND_HALF_UP);
                    dateRangeAvgResolveTimeMap.put(date, avgTimeInHours);
                }
            }

            // 将 map 转为 list
            for (Map.Entry<String, BigDecimal> entry : dateRangeAvgResolveTimeMap.entrySet()) {
                dateWorkOrderCountList.add(new DateWorkOrderCount()
                        .setDate(entry.getKey())
                        .setWorkOrderCount(entry.getValue()));
            }
        }
        return dateWorkOrderCountList;
    }

    // 计算时间差，以毫秒为单位
    private BigDecimal calTimeDifference(LocalDateTime createTime, LocalDateTime resolveTime) {
        Duration duration = Duration.between(createTime, resolveTime);
        long millis = duration.toMillis();

        // 使用 BigDecimal 接收时间差，并保留两位小数
        BigDecimal timeDifference = new BigDecimal(millis).setScale(2, BigDecimal.ROUND_HALF_UP);
        return timeDifference;
    }

    private List<WorkRecordCommonUseResult> queryDiffTypeWorkOrder(SeatingWorkloadNewVo seatingWorkloadVo, Integer type) throws Exception {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        // 定义工单表索引名称
        String indexName = ticketIndex + companyId;

        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        if (StringUtil.isEmpty(endTime)) {
            //如果有的接口，涉及到时间维度选择，选择了按日的维度，可能只传入开始日期，结束日期不传，此处加判断，让结束日期等于开始日期，避免程序报错
            endTime = startTime;
        }
        //处理入参时间
        if (StringUtil.isNotEmpty(startTime) && StringUtil.isNotEmpty(endTime)) {
            startTime = formatDate(startTime);
            endTime = formatDate(endTime);
        } else {
            // 获取当前月份
            YearMonth currentMonth = YearMonth.now();
            // 获取当前月份的第一天和最后一天
            LocalDate firstDayOfMonth = currentMonth.atDay(1);
            LocalDate lastDayOfMonth = currentMonth.atEndOfMonth();
            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS);
            // 格式化日期
            startTime = firstDayOfMonth.format(formatter);
            endTime = lastDayOfMonth.format(formatter);
        }
        //将处理后的时间赋值给入参实体
        seatingWorkloadVo.setStartDate(startTime);
        seatingWorkloadVo.setEndDate(endTime);
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(type, TimeZoneUtils.requestTimeConversion(startTime), TimeZoneUtils.requestTimeConversion(endTime), filterIdList);
        searchSourceBuilder.query(boolQueryBuilder);
        List<WorkOrderRecordEsResult> recordList = queryWorkOrderRecordEsResult(indexName, searchSourceBuilder);

        List<WorkRecordCommonUseResult> resultList = new ArrayList<>();
        //将返回结果进行处理，从而保证这个方法能够让部分接口复用
        if (CollectionUtils.isNotEmpty(recordList)) {
            for (WorkOrderRecordEsResult workRecordResult : recordList) {
                WorkRecordCommonUseResult workRecordCommonUseResult = new WorkRecordCommonUseResult();
                BeanUtils.copyProperties(workRecordResult, workRecordCommonUseResult);
                workRecordCommonUseResult.setCreateTime(convertTimeToActualUtcTime(workRecordResult.getCreateTime()));
                resultList.add(workRecordCommonUseResult);
            }
        }
        return resultList;
    }

    private List<String> splitDateRange(String startDateStr, String endDateStr, int timeDimension) {
        List<String> result = new ArrayList<>();
        try {
            if (StringUtil.isEmpty(endDateStr)) {
                //如果有的接口，涉及到时间维度选择，选择了按 日 的维度，可能只传入开始日期，结束日期不传，此处加判断，让结束日期等于开始日期，避免根据小时进行划分，范围结果过多
                endDateStr = startDateStr;
            }

            //将入参的yyyy-MM-dd HH:mm:ss格式进行处理，只保留yyyy-MM-dd
            String startDateStrTmp = startDateStr.split(" ")[0];
            String endDateStrTmp = endDateStr.split(" ")[0];

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD);
            LocalDate startDate = LocalDate.parse(startDateStrTmp, formatter);
            LocalDate endDate = LocalDate.parse(endDateStrTmp, formatter);

            switch (timeDimension) {
                case 1:
                    // 按天划分
                    while (!startDate.isAfter(endDate)) {
                        result.add(startDate.format(formatter));
                        startDate = startDate.plusDays(1);
                    }
                    break;
                case 2:
                    // 按周划分
                    while (!startDate.isAfter(endDate)) {
                        LocalDate endOfWeek = startDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                        if (endOfWeek.isAfter(endDate)) {
                            endOfWeek = endDate;
                        }
                        result.add(startDate.format(formatter) + "至" + endOfWeek.format(formatter));
                        startDate = endOfWeek.plusDays(1);
                    }
                    break;
                case 3:
                    // 按月划分
                    while (!startDate.isAfter(endDate)) {
                        LocalDate endOfMonth = startDate.with(TemporalAdjusters.lastDayOfMonth());
                        if (endOfMonth.isAfter(endDate)) {
                            endOfMonth = endDate;
                        }
                        result.add(startDate.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyy-MM")));
                        startDate = endOfMonth.plusDays(1);
                    }
                    break;
                case 4:
                    // 按小时划分
                    LocalDateTime startDateTime = startDate.atStartOfDay();
                    LocalDateTime endDateTime = startDate.plusDays(1).atStartOfDay(); // Include the end day completely
                    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                    while (!startDateTime.isAfter(endDateTime.minusHours(1))) {
                        LocalDateTime nextHour = startDateTime.plusHours(1);
                        result.add(startDateTime.format(dateTimeFormatter) + " 至 " + nextHour.format(dateTimeFormatter));
                        startDateTime = nextHour;
                    }
                    break;
                default:
                    throw new IllegalArgumentException("Invalid time dimension: " + timeDimension);
            }
        } catch (Exception e) {
            log.error("将时间进行范围截取出现异常：", e);
        }
        return result;
    }

    //处理小时维度的问题
    private String getHourAndFormat(String timeRange) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String[] parts = timeRange.split(" 至 ");
        LocalDateTime startTime = LocalDateTime.parse(parts[0], formatter);
        LocalDateTime endTime = LocalDateTime.parse(parts[1], formatter);

        // 获取小时并进行格式化
        int startHour = startTime.getHour();
        int endHour = endTime.getHour();

        // 处理跨天的情况
        if (endHour == 0 && startHour == 23) {
            endHour = 24;
        }
        return startHour + "-" + endHour;
    }

    //type 1-真人工单 2-机器人工单
    //todo 需要加一个状态的选择，有的需要排除待分配，有的需要包括待分配，排除待分配的那些数据，是不需要考虑坐席和部门的（待分配的内容这两个字段数据为空）
    private BoolQueryBuilder packageCommonSearchBuilder(Integer type, String startTime, String endTime, List<String> filterIdList) {
        //获取用户信息
        SysUserVo loginUser = SecurityUtil.getLoginUser();
        String userId = loginUser.getUserId();
        String deptId = loginUser.getDeptId();
        SysRoleVo sysRoleVo = loginUser.getRoleList().get(0);
        String roleId = sysRoleVo.getRoleId();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 时间范围查询
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String formatDateStart = formatDate(startTime);
            String formatDateEnd = formatDate(endTime);
            //将入参时间进行处理，转化为UTC+8，因为后台数据库存储的内容是UTC+8的时间
            formatDateStart = TimeZoneUtils.requestTimeConversion(formatDateStart);
            formatDateEnd = TimeZoneUtils.requestTimeConversion(formatDateEnd);

            boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(formatDateStart).lte(formatDateEnd));
        }

//        // 公共条件
//        boolQueryBuilder.must(QueryBuilders.termQuery("company_id", companyId));
//        boolQueryBuilder.must(QueryBuilders.termQuery("data_status", Constants.NORMAL));

        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            if (type == 1) {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
//                boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", ""));
                if (!CollectionUtils.isEmpty(filterIdList)) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("dept_id", filterIdList));
                }
            } else if (type == 2) {
                boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", "1001"));
            }
        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId)) {
            if (type == 1) {
                boolQueryBuilder.must(QueryBuilders.termQuery("dept_id", deptId));
                if (!CollectionUtils.isEmpty(filterIdList)) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("dept_id", filterIdList));
                }
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            } else if (type == 2) {
                boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", "1001"));
            }
        } else if (UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            if (type == 1) {
                boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", userId));
                if (!CollectionUtils.isEmpty(filterIdList)) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("agent_id", filterIdList));
                }
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("agent_id", "1001"));
            } else if (type == 2) {
                boolQueryBuilder.must(QueryBuilders.termQuery("agent_id", "1001"));
            }
        }
        //不能返回searchSourceBuilder，因为如果有别的boolQueryBuilder，会覆盖这个公共方法中的boolQueryBuilder
//        return searchSourceBuilder;
        return boolQueryBuilder;
    }

    private String getRoleType() {
        //如果登录用户的角色是管理员，那么x轴就是座席组（部门名称），如果登录用户的角色是座席管理员，那么x轴就是座席
        SysUserVo loginUser = SecurityUtil.getLoginUser();
        SysRoleVo sysRoleVo = loginUser.getRoleList().get(0);
        String roleId = sysRoleVo.getRoleId();
        return roleId;
    }

    private String formatDate(String dateStr) {
        //入参格式如果是：yyyy-MM-dd，用如下方式
//        // 使用指定的格式解析日期字符串
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD);
//        LocalDate date = LocalDate.parse(dateStr, formatter);
//        // 使用相同的格式重新格式化日期字符串
//        return date.format(formatter);

        //入参格式改为：yyyy-MM-dd HH:mm:ss，用如下方式
        // 使用 SimpleDateFormat 解析字符串为 Date 对象
        try {
            SimpleDateFormat originalFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
            Date inputDate = originalFormat.parse(dateStr);

            // 使用新的 SimpleDateFormat 格式化 Date 对象为新的字符串
            SimpleDateFormat newFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
            String formattedDate = newFormat.format(inputDate);

            return formattedDate;
        } catch (Exception e) {
            log.error("");
        }
        return dateStr;
    }

    //根据公司id查询部门信息
    private Map<String, String> queryDeptInfoByCompanyId() {
        Map<String, String> map = new HashMap<>();
        R<Map<String, String>> mapR = userClient.queryDeptInfoByCompanyId();
        if (AjaxResult.SUCCESS == mapR.getCode()) {
            map = mapR.getData();
        }
        return map;
    }

    private String getDeptNameByDeptId(String deptId, Map<String, String> map) {
        return Optional.ofNullable(map.get(deptId)).orElse("Others");
    }

    //==============================如下是导出excel相关代码==============================

    /**
     * 导出excel
     *
     * @param firstRow   表头字段信息
     * @param rowList    导出具体行数据
     * @param exportName excel名
     * @throws IOException
     */
    private void exportWithResponse(List<String> firstRow, List<List<String>> rowList, String exportName) throws IOException {
        if (CollectionUtils.isEmpty(firstRow)) {
            return;
        }
        String[] firstRowArray = firstRow.toArray(new String[firstRow.size()]);
        Workbook wb = writeToExcelByList(firstRowArray, rowList);

        String fileName = URLEncoder.encode(exportName, "UTF-8");
        ServletUtil.getResponse().setContentType("application/vnd.ms-excel;charset=utf-8");
        ServletUtil.getResponse().setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream outputStream = ServletUtil.getResponse().getOutputStream();
        wb.write(outputStream);
        outputStream.close();
    }

    /**
     * @Description: array 表头数据 list 具体数据
     */
    private Workbook writeToExcelByList(String[] array, List<List<String>> list) {
        //创建工作薄
        Workbook wb = new XSSFWorkbook();

        //设置列名样式
        CellStyle headerStyle = wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.LEFT);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);//上下居中
        headerStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());//设置背景色
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);//必须设置 否则无效
        setCellBorderStyle(headerStyle);
        Font headerFont = wb.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setFontName("微软雅黑");
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        //创建sheet
        Sheet sheet = wb.createSheet(MessageUtils.get("statistic.work.record.sheet.name"));

        //在sheet中添加表头，由于不涉及到标题，所以表头行数从0开始
        Row row = sheet.createRow((int) 0);
        for (int i = 0; i < array.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(array[i]);
            cell.setCellStyle(headerStyle);
        }

        //数据样式
        CellStyle dataStyle = wb.createCellStyle();
        //设置居中样式，水平居中
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font dataFont = wb.createFont();
        dataFont.setFontHeightInPoints((short) 12);
        dataFont.setFontName("微软雅黑");
        dataStyle.setFont(dataFont);

        //数据填充
        try {
            int index = 1;
            for (List value : list) {
                row = sheet.createRow(index);
                index++;
                List data = value;
                for (int j = 0; j < data.size(); j++) {
                    Cell cell = row.createCell(j);
                    // 为当前列赋值
                    if (data.get(j) != null) {
                        cell.setCellValue(data.get(j).toString());
                    } else {
                        cell.setCellValue("");
                    }
                    //设置数据的样式
                    cell.setCellStyle(dataStyle);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 宽度自适应的问题，必须在单元格设值以后进行
        for (int k = 0; k < array.length; k++) {
            sheet.autoSizeColumn(k);
        }
        return wb;
    }

    private void setCellBorderStyle(CellStyle cellStyle) {
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN); //解决填充背景色没有边框问题
        cellStyle.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
    }

    @Override
    public void exportWorkOrderStatus(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename3") + "_" + getCurrentTime();  //这是文件名
        try {
            // 获取数据
            AjaxResult ajaxResult = queryWorkOrderStatus(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            List<WorkRecordSummaryVo> resultList = (List<WorkRecordSummaryVo>) data;
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();

            // 表头国际化
            firstRow.add(getFirstColName());
            firstRow.add(MessageUtils.get("resolve.ticket.count"));
            firstRow.add(MessageUtils.get("un.resolve.ticket.count"));
            firstRow.add(MessageUtils.get("waiting.for.reply.ticket.count"));

            //数据装入
            if (CollectionUtils.isNotEmpty(resultList)) {
                for (WorkRecordSummaryVo vo : resultList) {
                    List<String> row = new ArrayList<>();
                    String agentName = vo.getAgentName();
                    row.add(agentName);

                    List<WorkRecordNumResult> list = vo.getWorkOrderCountList();
                    for (WorkRecordNumResult eachBean : list) {
                        row.add(eachBean.getEachTotalCount() + "");
                    }
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            } else {
                exportEmptyExcel(exportName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("工单状态-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    //根据角色，获取第一列的名字，是座席名称还是座席组名称
    private String getFirstColName() {
        String roleId = getRoleType();
        Map<String, List<WorkRecordResult>> mapResult = new HashMap<>();
        //用拼接的方式进行分组，是避免出现name相同的情况
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            return MessageUtils.get("statistic.export.agent.group.name");
        } else {
            return MessageUtils.get("statistic.export.agent.name");
        }
    }

    //--------------------------------------------如下是部分导出接口--------------------------------------------
    @Override
    public void queryHandleWorkOrderSumExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<HandleWorkOrderResult> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryHandleWorkOrderSum(seatingWorkloadVo);
            queryResultList = (List<HandleWorkOrderResult>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出座席处理的工单总数excel报错:statistic/queryHandleWorkOrderSum/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 表头国际化
        firstRow.add(MessageUtils.get("agenty.name"));
        firstRow.add(MessageUtils.get("work.order.count"));

        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            for (HandleWorkOrderResult rt : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(rt.getAgentName());
                row.add(String.valueOf(rt.getWorkOrderCount()));
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename1") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出座席处理的工单总数excel出现异常", e);
        }
    }

    @Override
    public void queryWorkOrderGroupByChannelExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<WorkRecordReportVo> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryWorkOrderGroupByChannel(seatingWorkloadVo);
            queryResultList = (List<WorkRecordReportVo>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出按渠道分类的工单数量excel报错:queryWorkOrderGroupByChannel/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            // 表头国际化
            firstRow.add(MessageUtils.get("agenty.name"));
            List<ChanelWorkRecordNumResult> fieldList = queryResultList.get(0).getWorkOrderCountList();
            for (ChanelWorkRecordNumResult fl : fieldList) {
                firstRow.add(fl.getChannelTypeName());
            }
            //行数据
            for (WorkRecordReportVo rt1 : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(rt1.getAgentName());
                List<ChanelWorkRecordNumResult> workOrderCountList = rt1.getWorkOrderCountList();
                for (ChanelWorkRecordNumResult rt2 : workOrderCountList) {
                    row.add(String.valueOf(rt2.getEachChannelTotalCount()));
                }
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename2") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出按渠道分类的工单数量excel出现异常", e);
        }
    }

    @Override
    public void queryWorkOrderTypeDistributionExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<WorkRecordSummaryVo> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryWorkOrderTypeDistribution(seatingWorkloadVo);
            queryResultList = (List<WorkRecordSummaryVo>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出工单类型分布excel报错:queryWorkOrderTypeDistribution/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            // 表头国际化
            firstRow.add(MessageUtils.get("agenty.name"));
            List<WorkRecordNumResult> fieldList = queryResultList.get(0).getWorkOrderCountList();
            for (WorkRecordNumResult fl : fieldList) {
                firstRow.add(fl.getTypeName());
            }
            //行数据
            for (WorkRecordSummaryVo rt1 : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(rt1.getAgentName());
                List<WorkRecordNumResult> workOrderCountList = rt1.getWorkOrderCountList();
                for (WorkRecordNumResult rt2 : workOrderCountList) {
                    row.add(String.valueOf(rt2.getEachTotalCount()));
                }
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename4") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出工单类型分布excel出现异常", e);
        }
    }

    @Override
    public void queryWorkOrderLevelDistributionExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<WorkRecordSummaryVo> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryWorkOrderLevelDistribution(seatingWorkloadVo);
            queryResultList = (List<WorkRecordSummaryVo>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出工单优先级分布excel报错:queryWorkOrderLevelDistribution/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            // 表头国际化
            firstRow.add(MessageUtils.get("agenty.name"));
            List<WorkRecordNumResult> fieldList = queryResultList.get(0).getWorkOrderCountList();
            for (WorkRecordNumResult fl : fieldList) {
                firstRow.add(fl.getTypeName());
            }
            //行数据
            for (WorkRecordSummaryVo rt1 : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(rt1.getAgentName());
                List<WorkRecordNumResult> workOrderCountList = rt1.getWorkOrderCountList();
                for (WorkRecordNumResult rt2 : workOrderCountList) {
                    row.add(String.valueOf(rt2.getEachTotalCount()));
                }
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename5") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出工单优先级分布excel出现异常", e);
        }
    }

    @Override
    public void queryAvgHandleTimeExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<AgentWorkRecordAvgTimeVo> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryAvgHandleTime(seatingWorkloadVo);
            queryResultList = (List<AgentWorkRecordAvgTimeVo>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出工单平均解决时间excel报错:queryAvgHandleTime/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 表头国际化
        firstRow.add(MessageUtils.get("agenty.name"));
        firstRow.add(MessageUtils.get("work.order.avg.resolution.time"));

        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            //行数据
            for (AgentWorkRecordAvgTimeVo rt : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(rt.getAgentName());
                row.add(String.valueOf(rt.getRecordAvgTime()));
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename6") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出工单平均解决时间excel出现异常", e);
        }
    }

    @Override
    public void queryCustomerWorkRecordTopExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<WorkRecordTopResult> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryCustomerWorkRecordTop(seatingWorkloadVo);
            queryResultList = (List<WorkRecordTopResult>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出查询客户工单top10excel报错:queryCustomerWorkRecordTop/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 表头国际化
        firstRow.add(MessageUtils.get("rank"));
        firstRow.add(MessageUtils.get("customer.name"));
        firstRow.add(MessageUtils.get("customer.work.order.count"));

        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            int i = 1;
            //行数据
            for (WorkRecordTopResult rt : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(String.valueOf((i++)));
                row.add(rt.getCustomerName());
                row.add(String.valueOf(rt.getWorkOrderCount()));
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename19") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出查询客户工单top10excel出现异常", e);
        }
    }

    @Override
    public void queryAgentSatisfactionExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<DiffSatisfactionSummaryResult> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryAgentSatisfaction(seatingWorkloadVo);
            queryResultList = (List<DiffSatisfactionSummaryResult>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出满意度报表-座席维度excel报错:queryAgentSatisfaction/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 表头国际化
        firstRow.add(MessageUtils.get("agenty.name"));
        firstRow.add(MessageUtils.get("satisfaction.rating"));

        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            int i = 1;
            //行数据
            for (DiffSatisfactionSummaryResult rt : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(rt.getDimensionName());
                row.add(String.valueOf(rt.getRating()));
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename9") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出满意度报表-座席维度excel出现异常", e);
        }
    }

    @Override
    public void queryWorkOrderTypeSatisfactionExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<SatisfactionSummaryVo> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryWorkOrderTypeSatisfaction(seatingWorkloadVo);
            queryResultList = (List<SatisfactionSummaryVo>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出满意度报表-工单类型维度excel报错:queryWorkOrderTypeSatisfaction/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            // 表头国际化
            firstRow.add(MessageUtils.get("agent.name"));
            List<WorkRecordSatisfactionResult> fieldList = queryResultList.get(0).getWorkOrderSatisfactionList();
            for (WorkRecordSatisfactionResult fl : fieldList) {
                firstRow.add(fl.getTypeName());
            }
            //行数据
            for (SatisfactionSummaryVo rt1 : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(rt1.getAgentName());
                List<WorkRecordSatisfactionResult> workRecordSatisfactionResultList = rt1.getWorkOrderSatisfactionList();
                for (WorkRecordSatisfactionResult rt2 : workRecordSatisfactionResultList) {
                    row.add(String.valueOf(rt2.getRating()));
                }
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename10") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出满意度报表-工单类型维度excel出现异常", e);
        }
    }

    @Override
    public void queryChannelSatisfactionExport(SeatingWorkloadNewVo seatingWorkloadVo) {

        List<SatisfactionSummaryVo> queryResultList = new ArrayList<>();
        try {
            AjaxResult queryAjax = queryChannelSatisfaction(seatingWorkloadVo);
            queryResultList = (List<SatisfactionSummaryVo>) queryAjax.getData();
        } catch (Exception e) {
            log.error("导出满意度报表-渠道维度excel报错:queryChannelSatisfaction/export,", e);
        }

        // 导出到excel中
        // 表头
        List<String> firstRow = new ArrayList<>();
        // 行数据
        List<List<String>> rowList = new ArrayList<>();
        //数据装入
        if (CollectionUtils.isNotEmpty(queryResultList)) {
            // 表头国际化
            firstRow.add(MessageUtils.get("agenty.name"));
            List<WorkRecordSatisfactionResult> fieldList = queryResultList.get(0).getWorkOrderSatisfactionList();
            for (WorkRecordSatisfactionResult fl : fieldList) {
                firstRow.add(fl.getTypeName());
            }
            //行数据
            for (SatisfactionSummaryVo rt1 : queryResultList) {
                List<String> row = new ArrayList<>();
                row.add(rt1.getAgentName());
                List<WorkRecordSatisfactionResult> workRecordSatisfactionResultList = rt1.getWorkOrderSatisfactionList();
                for (WorkRecordSatisfactionResult rt2 : workRecordSatisfactionResultList) {
                    row.add(String.valueOf(rt2.getRating()));
                }
                rowList.add(row);
            }
        }
        try {
            // 导出excel逻辑
            exportWithResponse(firstRow, rowList, MessageUtils.get("statistic.export.work.record.filename11") + "_" + DateUtils.dateTimeNow());
        } catch (IOException e) {
            log.error("导出满意度报表-渠道维度excel出现异常", e);
        }
    }

    @Override
    public void exportHandleTimeDistribution(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename7") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = queryHandleTimeDistribution(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            List<WorkRecordSummaryVo> resultList = (List<WorkRecordSummaryVo>) data;
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();

            // 表头国际化
            firstRow.add(getFirstColName());
            firstRow.add(MessageUtils.get("zero.to.three.hour"));
            firstRow.add(MessageUtils.get("one.to.three.hour"));
            firstRow.add(MessageUtils.get("three.to.eight.hour"));
            firstRow.add(MessageUtils.get("over.eight.hour"));

            //数据装入
            if (CollectionUtils.isNotEmpty(resultList)) {
                for (WorkRecordSummaryVo vo : resultList) {
                    List<String> row = new ArrayList<>();
                    String agentName = vo.getAgentName();
                    row.add(agentName);

                    List<WorkRecordNumResult> list = vo.getWorkOrderCountList();
                    for (WorkRecordNumResult eachBean : list) {
                        row.add(eachBean.getEachTotalCount() + "");
                    }
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            } else {
                exportEmptyExcel(exportName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("工单处理时长分布-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    @Override
    public void exportSla(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename8") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = querySla(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            List<WorkOrderSla> resultList = (List<WorkOrderSla>) data;
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();


            // 表头国际化
            String[] slaTypes = {
                    TicketSlaTypeEnum.PUNCTUAL_SOLVE.getName(),  // 按时解决
                    TicketSlaTypeEnum.TIMEOUT_SOLVE.getName(), // 超时解决
                    TicketSlaTypeEnum.NO_TIMEOUT_UNRESOLVED.getName(), // 未超时未完成
                    TicketSlaTypeEnum.TIMEOUT_DAY_UNRESOLVED.getName(), // 超时一天未完成
                    TicketSlaTypeEnum.TIMEOUT_TWO_DAY_UNRESOLVED.getName(), // 超时两天未完成
                    TicketSlaTypeEnum.TIMEOUT_THREE_DAY_UNRESOLVED.getName(), // 超时三天未完成
                    TicketSlaTypeEnum.TIMEOUT_WEEK_UNRESOLVED.getName(), // 超时一周未完成
            };

            firstRow.add(getFirstColName());
            firstRow.add(TransUtil.trans(slaTypes[0]));
            firstRow.add(TransUtil.trans(slaTypes[1]));
            firstRow.add(TransUtil.trans(slaTypes[2]));
            firstRow.add(TransUtil.trans(slaTypes[3]));
            firstRow.add(TransUtil.trans(slaTypes[4]));
            firstRow.add(TransUtil.trans(slaTypes[5]));
            firstRow.add(TransUtil.trans(slaTypes[6]));

            //数据装入
            if (CollectionUtils.isNotEmpty(resultList)) {
                for (WorkOrderSla vo : resultList) {
                    List<String> row = new ArrayList<>();
                    String agentName = vo.getAgentName();
                    row.add(agentName);

                    List<SlaWorkOrderCount> list = vo.getWorkOrderNumList();
                    for (SlaWorkOrderCount eachBean : list) {
                        row.add(eachBean.getEachTotalCount() + "");
                    }
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            } else {
                exportEmptyExcel(exportName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("SLA报表-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    @Override
    public void exportWorkOrderIncrement(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename12") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = queryWorkOrderIncrement(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            List<GroupTableDataResult> resultList = (List<GroupTableDataResult>) data;
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();

            // 表头国际化
            firstRow.add(MessageUtils.get("statistic.export.time.range"));
            for (GroupTableDataResult groupTableDataResult : resultList) {
                //由于查询过程中，分组维度已经经过了国际化，所以如下直接取值，即动态列
                firstRow.add(groupTableDataResult.getGroupTypeName());
            }

            //数据装入
            if (CollectionUtils.isNotEmpty(resultList)) {
                //用如下内容获取timeRange集合
                List<DiffTimeTableDataResult> timeRangeVoList = resultList.get(0).getTableWorkOrderList();
                for (DiffTimeTableDataResult timeRangeResult : timeRangeVoList) {
                    String timeRange = timeRangeResult.getTimeRange();
                    List<String> row = new ArrayList<>();
                    row.add(timeRange);
                    for (GroupTableDataResult groupTableDataResult : resultList) {
                        List<DiffTimeTableDataResult> tableWorkOrderList = groupTableDataResult.getTableWorkOrderList();
                        for (DiffTimeTableDataResult diffTimeTableDataResult : tableWorkOrderList) {
                            if (diffTimeTableDataResult.getTimeRange().equals(timeRange)) {
                                row.add(diffTimeTableDataResult.getNum() + "");
                            }
                        }
                    }
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            } else {
                exportEmptyExcel(exportName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("机器人工单报表-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    @Override
    public void exportRobotChannelWorkOrder(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename13") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = queryRobotChannelWorkOrder(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            List<GroupTableDataResult> resultList = (List<GroupTableDataResult>) data;
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();

            // 表头国际化
            firstRow.add(MessageUtils.get("statistic.export.time.range"));
            for (GroupTableDataResult groupTableDataResult : resultList) {
                //由于查询过程中，分组维度已经经过了国际化，所以如下直接取值，即动态列
                firstRow.add(groupTableDataResult.getGroupTypeName());
            }

            //数据装入
            if (CollectionUtils.isNotEmpty(resultList)) {
                //用如下内容获取timeRange集合
                List<DiffTimeTableDataResult> timeRangeVoList = resultList.get(0).getTableWorkOrderList();
                for (DiffTimeTableDataResult timeRangeResult : timeRangeVoList) {
                    String timeRange = timeRangeResult.getTimeRange();
                    List<String> row = new ArrayList<>();
                    row.add(timeRange);
                    for (GroupTableDataResult groupTableDataResult : resultList) {
                        List<DiffTimeTableDataResult> tableWorkOrderList = groupTableDataResult.getTableWorkOrderList();
                        for (DiffTimeTableDataResult diffTimeTableDataResult : tableWorkOrderList) {
                            if (diffTimeTableDataResult.getTimeRange().equals(timeRange)) {
                                row.add(diffTimeTableDataResult.getNum() + "");
                            }
                        }
                    }
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            } else {
                exportEmptyExcel(exportName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("按渠道分类机器人工单数量-导出错误：", e);
            exportEmptyExcel(exportName);
        }

    }

    @Override
    public void exportRobotWorkOrderPercent(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename14") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = queryWorkOrderIncrementPercent(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            if (null == data) {
                exportEmptyExcel(exportName);
            } else {
                WorkOrderCreateTypeSummary workOrderCreateTypeSummary = (WorkOrderCreateTypeSummary) data;
                // 表头
                List<String> firstRow = new ArrayList<>();
                // 行数据
                List<List<String>> rowList = new ArrayList<>();

                // 表头国际化
                firstRow.add(MessageUtils.get("statistic.export.work.order.classification"));
                firstRow.add(MessageUtils.get("statistic.export.work.order.total.count"));
                firstRow.add(MessageUtils.get("statistic.export.work.order.percent"));

                //数据装入
                List<String> row1 = new ArrayList<>();
                row1.add(MessageUtils.get("robot.work.ticket"));
                row1.add(workOrderCreateTypeSummary.getRobotCount() + "");
                row1.add(workOrderCreateTypeSummary.getRobotPercent() + "%");
                rowList.add(row1);

                List<String> row2 = new ArrayList<>();
                row2.add(MessageUtils.get("real.work.ticket"));
                row2.add(workOrderCreateTypeSummary.getRealCount() + "");
                row2.add(workOrderCreateTypeSummary.getRealPercent() + "%");
                rowList.add(row2);

                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("机器人工单占比-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    @Override
    public void exportHandleWorkOrderNumTrend(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename15") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = queryHandleWorkOrderNumTrend(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            List<GroupTableDataResult> resultList = (List<GroupTableDataResult>) data;
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();

            // 表头国际化
            firstRow.add(MessageUtils.get("statistic.export.time.range"));
            for (GroupTableDataResult groupTableDataResult : resultList) {
                //由于查询过程中，分组维度已经经过了国际化，所以如下直接取值，即动态列
                firstRow.add(groupTableDataResult.getGroupTypeName());
            }

            //数据装入
            if (CollectionUtils.isNotEmpty(resultList)) {
                //用如下内容获取timeRange集合
                List<DiffTimeTableDataResult> timeRangeVoList = resultList.get(0).getTableWorkOrderList();
                for (DiffTimeTableDataResult timeRangeResult : timeRangeVoList) {
                    String timeRange = timeRangeResult.getTimeRange();
                    List<String> row = new ArrayList<>();
                    row.add(timeRange);
                    for (GroupTableDataResult groupTableDataResult : resultList) {
                        List<DiffTimeTableDataResult> tableWorkOrderList = groupTableDataResult.getTableWorkOrderList();
                        for (DiffTimeTableDataResult diffTimeTableDataResult : tableWorkOrderList) {
                            if (diffTimeTableDataResult.getTimeRange().equals(timeRange)) {
                                row.add(diffTimeTableDataResult.getNum() + "");
                            }
                        }
                    }
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            } else {
                exportEmptyExcel(exportName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("各座席工单处理数量趋势变化-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    @Override
    public void exportAgentSatisfactionTrend(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename16") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = queryAgentSatisfactionTrend(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            List<GroupTableDataResult> resultList = (List<GroupTableDataResult>) data;
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();

            // 表头国际化
            firstRow.add(MessageUtils.get("statistic.export.time.range"));
            for (GroupTableDataResult groupTableDataResult : resultList) {
                //由于查询过程中，分组维度已经经过了国际化，所以如下直接取值，即动态列
                firstRow.add(groupTableDataResult.getGroupTypeName());
            }

            //数据装入
            if (CollectionUtils.isNotEmpty(resultList)) {
                //用如下内容获取timeRange集合
                List<DiffTimeTableDataResult> timeRangeVoList = resultList.get(0).getTableWorkOrderList();
                for (DiffTimeTableDataResult timeRangeResult : timeRangeVoList) {
                    String timeRange = timeRangeResult.getTimeRange();
                    List<String> row = new ArrayList<>();
                    row.add(timeRange);
                    for (GroupTableDataResult groupTableDataResult : resultList) {
                        List<DiffTimeTableDataResult> tableWorkOrderList = groupTableDataResult.getTableWorkOrderList();
                        for (DiffTimeTableDataResult diffTimeTableDataResult : tableWorkOrderList) {
                            if (diffTimeTableDataResult.getTimeRange().equals(timeRange)) {
                                row.add(diffTimeTableDataResult.getNum() + "");
                            }
                        }
                    }
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            } else {
                exportEmptyExcel(exportName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("各座席满意度平均得分趋势变化-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    @Override
    public void exportHandleWorkOrderTimeTrend(SeatingWorkloadNewVo seatingWorkloadVo) {
        seatingWorkloadVo.setRenderType(1);
        String exportName = MessageUtils.get("statistic.export.work.record.filename17") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = queryHandleWorkOrderTimeTrend(seatingWorkloadVo);
            Object data = ajaxResult.getData();
            List<GroupTableDataResult> resultList = (List<GroupTableDataResult>) data;
            // 表头
            List<String> firstRow = new ArrayList<>();
            // 行数据
            List<List<String>> rowList = new ArrayList<>();

            // 表头国际化
            firstRow.add(MessageUtils.get("statistic.export.time.range"));
            for (GroupTableDataResult groupTableDataResult : resultList) {
                //由于查询过程中，分组维度已经经过了国际化，所以如下直接取值，即动态列
                firstRow.add(groupTableDataResult.getGroupTypeName());
            }

            //数据装入
            if (CollectionUtils.isNotEmpty(resultList)) {
                //用如下内容获取timeRange集合
                List<DiffTimeTableDataResult> timeRangeVoList = resultList.get(0).getTableWorkOrderList();
                for (DiffTimeTableDataResult timeRangeResult : timeRangeVoList) {
                    String timeRange = timeRangeResult.getTimeRange();
                    List<String> row = new ArrayList<>();
                    row.add(timeRange);
                    for (GroupTableDataResult groupTableDataResult : resultList) {
                        List<DiffTimeTableDataResult> tableWorkOrderList = groupTableDataResult.getTableWorkOrderList();
                        for (DiffTimeTableDataResult diffTimeTableDataResult : tableWorkOrderList) {
                            if (diffTimeTableDataResult.getTimeRange().equals(timeRange)) {
                                row.add(diffTimeTableDataResult.getNum() + "");
                            }
                        }
                    }
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            } else {
                exportEmptyExcel(exportName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("各座席工单处理时间趋势变化-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    @Override
    public void exportWorkOrderFeedBack(SeatingWorkloadNewVo seatingWorkloadVo) {
        String exportName = MessageUtils.get("statistic.export.work.record.filename18") + "_" + getCurrentTime();
        try {
            // 获取数据
            AjaxResult ajaxResult = queryWorkOrderFeedBack(seatingWorkloadVo);
            List<WorkOrderFeedBack> resultList = (List<WorkOrderFeedBack>) ajaxResult.getData();
            if (CollectionUtils.isEmpty(resultList)) {
                exportEmptyExcel(exportName);
            } else {
                // 表头
                List<String> firstRow = new ArrayList<>();
                // 行数据
                List<List<String>> rowList = new ArrayList<>();

                // 表头国际化
                firstRow.add(getFirstColName());
                firstRow.add(MessageUtils.get("statistic.export.work.order.feedback.rate"));
                //数据装入
                for (WorkOrderFeedBack workOrderFeedBack : resultList) {
                    List<String> row = new ArrayList<>();
                    row.add(workOrderFeedBack.getAgentName());
                    row.add(workOrderFeedBack.getPercent() + "%");
                    rowList.add(row);
                }
                String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
                ExportExcelUtil.exportExcel(firstRow, rowList, exportName, sheetName);
            }
        } catch (Exception e) {
            //如果出现异常，打日志，然后导出一个空的excel给前端
            log.error("各座席统计回评率-导出错误：", e);
            exportEmptyExcel(exportName);
        }
    }

    //数据补0
    private void processWorkRecordRateResultList(List<WorkRecordSatisfactionResult> workRecordRateResultList, Map<String, String> inUseMap) {
        for (Map.Entry<String, String> entry : inUseMap.entrySet()) {
            String typeCode = entry.getKey();
            String typeName = entry.getValue();

            // 判断workRecordRateResultList中是否包含该typeCode
//            boolean containsTypeCode = workRecordRateResultList.stream()
//                    .anyMatch(result -> result.getTypeCode().equals(typeCode));
            boolean containsTypeCode = workRecordRateResultList.stream()
                    .anyMatch(result -> Optional.ofNullable(result.getTypeCode()).isPresent() && result.getTypeCode().equals(typeCode));

            // 如果不存在，则添加一个rating为0的WorkRecordSatisfactionResult
            if (!containsTypeCode) {
                workRecordRateResultList.add(new WorkRecordSatisfactionResult().setTypeCode(typeCode).setTypeName(typeName).setRating(BigDecimal.ZERO));
            }
        }
        // 获取 channelMap 中 typeCode 的顺序，用于返回数据顺序保持一致
        List<String> typeCodeOrder = new ArrayList<>(inUseMap.keySet());
        //上述内容补充数据之后，根据channelMap调整顺序，使之返回顺序保持一致
        // 对 workRecordRateResultList 进行排序
        workRecordRateResultList.sort(Comparator.comparingInt(o -> typeCodeOrder.indexOf(o.getTypeCode())));

    }

    private void exportEmptyExcel(String exportName) {
        try {
            List<String> firstRow = new ArrayList<>();
            List<List<String>> rowList = new ArrayList<>();
            String sheetName = MessageUtils.get("statistic.work.record.sheet.name");
            ExportExcelUtil.exportWithResponse(firstRow, rowList, exportName, sheetName);
        } catch (IOException e) {
            log.error("导出空的excel，文件名为【{}】出现异常:", exportName, e);
        }
    }

    private String getCurrentTime() {
        // 获取当前时间
        Date now = new Date();
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.YYYYMMDDHHMMSS);
        // 格式化当前时间
        String formattedDate = dateFormat.format(now);
        return formattedDate;
    }

    //处理座席组名称为空的数据，这样的数据不参与计算（这样的数据说明那个dept已经不用了）
    private List<WorkRecordResult> handleWorkRecordResultList(List<WorkRecordResult> recordList) {
        List<WorkRecordResult> handleRecordList = new ArrayList<>();
        Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
        String roleId = getRoleType();
        //用拼接的方式进行分组，是避免出现name相同的情况
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            if (CollectionUtils.isNotEmpty(recordList)) {
                for (WorkRecordResult workRecordResult : recordList) {
                    String deptId = workRecordResult.getDeptId();
                    String deptNameByDeptIdResult = getDeptNameByDeptId(deptId, deptInfoMap);
                    if (!"Others".equals(deptNameByDeptIdResult)) {
                        handleRecordList.add(workRecordResult);
                    }
                }
                return handleRecordList;
            }

        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            //数据库工单表中，座席名字是不为空的，所以这个先不用处理，原样返回
            return recordList;
        }
        return recordList;
    }

    private List<SatisfactionResult> handleSatisfactionResultList(List<SatisfactionResult> recordList) {
        List<SatisfactionResult> handleRecordList = new ArrayList<>();
        Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
        String roleId = getRoleType();
        //用拼接的方式进行分组，是避免出现name相同的情况
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            if (CollectionUtils.isNotEmpty(recordList)) {
                for (SatisfactionResult workRecordResult : recordList) {
                    String deptId = workRecordResult.getDeptId();
                    String deptNameByDeptIdResult = getDeptNameByDeptId(deptId, deptInfoMap);
                    if (!"Others".equals(deptNameByDeptIdResult)) {
                        handleRecordList.add(workRecordResult);
                    }
                }
                return handleRecordList;
            }

        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            //数据库工单表中，座席名字是不为空的，所以这个先不用处理，原样返回
            return recordList;
        }
        return recordList;
    }

    // 数据库的数据，都是按照UTC+8存储的，查询出来的时间相关的属性，用LocalDateTime接收，
    // 如果是直接返回给前端，那么会自动转用户所在时区
    // 如果不是直接返回，需要进行其他处理，就需要将返回值的时间格式化，然后再转换成实际对应时区
    private static LocalDateTime convertTimeToActualUtcTime(LocalDateTime localDateTime) {
        if (null == localDateTime) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String formattedString = localDateTime.format(formatter);
        String convertTimeStr = TimeZoneUtils.responseTimeConversion(formattedString);
        //将上述结果转化为localDateTime格式返回
        LocalDateTime actualUtcDateTime = LocalDateTime.parse(convertTimeStr, formatter);
        return actualUtcDateTime;
    }

    /**
     * 验证索引是否存在
     *
     * @param index 索引名称
     * @return 存在状态
     * @throws IOException
     */
    private boolean headIndexExists(String index) {
        try {
            GetIndexRequest req = new GetIndexRequest(index);
            boolean exists = restHighLevelClient.indices().exists(req, RequestOptions.DEFAULT);
            log.info("当前索引{}, 是否存在: {}", index, exists);
            return exists;
        } catch (Exception e) {
            log.error("验证索引失败:", e);
        }
        return false;
    }

    /**
     * 创建索引
     *
     * @param indexName 索引名称
     * @throws IOException
     */
    private void createIndex(String indexName) {
        try {
            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            boolean acknowledged = createIndexResponse.isAcknowledged();
            log.info("创建索引完成：{}", acknowledged);
        } catch (Exception e) {
            log.error("创建索引报错", e);
        }
    }

    //处理座席组名称为空的数据，这样的数据不参与计算（这样的数据说明那个dept已经不用了）
    //处理返回值数据中，座席组（理解为部门）名称的问题
    private List<WorkOrderRecordEsResult> handleWorkRecordEsResultList(List<WorkOrderRecordEsResult> recordList) {
        List<WorkOrderRecordEsResult> handleRecordList = new ArrayList<>();
        Map<String, String> deptInfoMap = queryDeptInfoByCompanyId();
        String roleId = getRoleType();
        //用拼接的方式进行分组，是避免出现name相同的情况
        if (UserRoleEnum.ADMINISTRATOR.getId().equals(roleId)) {
            if (CollectionUtils.isNotEmpty(recordList)) {
                for (WorkOrderRecordEsResult workRecordResult : recordList) {
                    String deptId = workRecordResult.getDeptId();
                    String deptNameByDeptIdResult = getDeptNameByDeptId(deptId, deptInfoMap);
                    if (!"Others".equals(deptNameByDeptIdResult)) {
                        handleRecordList.add(workRecordResult);
                    }
                }
                return handleRecordList;
            }

        } else if (UserRoleEnum.ATTENDANT_LEADER.getId().equals(roleId) || UserRoleEnum.ATTENDANT_STAFF.getId().equals(roleId)) {
            //数据库工单表中，座席名字是不为空的，所以这个先不用处理，原样返回
            handleRecordList = recordList;
        }
        return handleRecordList;
    }

    private void handleAgentInfoEmpty(WorkOrderRecordEsResult workOrderRecordEsResult) {
        if (StringUtil.isEmpty(workOrderRecordEsResult.getAgentId())) {
            workOrderRecordEsResult.setAgentId("Others");
        }
        if (StringUtil.isEmpty(workOrderRecordEsResult.getAgentName())) {
            workOrderRecordEsResult.setAgentName("Others");
        }
    }

    private List<WorkOrderRecordEsResult> queryWorkOrderRecordEsResult(String indexName, SearchSourceBuilder searchSourceBuilder) throws Exception {
        SearchRequest searchRequest = new SearchRequest(indexName);
        //加如下条件设置返回条数的上限，因为默认返回10条数据
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        //获取总数量
        long totalCount = searchResponse.getHits().getTotalHits().value;
        List<WorkOrderRecordEsResult> recordList = new ArrayList<>();
        if (totalCount > 0) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> source = hit.getSourceAsMap();
                // 创建 ObjectMapper 实例
                ObjectMapper objectMapper = new ObjectMapper();
                // 注册 JavaTimeModule
                objectMapper.registerModule(new JavaTimeModule());
                String json = objectMapper.writeValueAsString(source);
                // 将 JSON 字符串转换为 Java 对象
//                WorkOrderRecordEsResult result = objectMapper.readValue(json, WorkOrderRecordEsResult.class);
                TicketInfoIndex ticketInfoIndex = objectMapper.readValue(json, TicketInfoIndex.class);
                WorkOrderRecordEsResult result = new WorkOrderRecordEsResult();
                BeanUtils.copyProperties(ticketInfoIndex,result);
                //如果座席Id和座席名称为空，处理为others
                handleAgentInfoEmpty(result);
                recordList.add(result);
            }
        }
        return recordList;
    }

    private List<InUseChannel> queryInUseChannelFromEs(Integer type, String startTime, String endTime, List<String> filterIdList, String indexName) throws Exception {
        List<InUseChannel> inUseChannelList = new ArrayList<>();
        BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(type, startTime, endTime, filterIdList);
        SearchRequest searchRequest = new SearchRequest(indexName);
        // 创建查询构建器
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 添加条件过滤
        sourceBuilder.query(boolQueryBuilder);  // 添加条件过滤
        // 构建聚合查询
        sourceBuilder.aggregation(AggregationBuilders
                .terms("distinct_channel_type_id")
                .field("channel_type_id")
                .size(10000)  // 可以根据需要调整size
                .subAggregation(AggregationBuilders
                        .terms("distinct_channel_type_name")
                        .field("channel_type_name")
                        .size(10000)));

        searchRequest.source(sourceBuilder);

        // 执行查询
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        // 处理聚合结果 （将返回信息整合到inUseChannelList）
        Aggregations aggregations = searchResponse.getAggregations();
        Terms channelTypeIdAgg = aggregations.get("distinct_channel_type_id");
        if (null != channelTypeIdAgg && CollectionUtils.isNotEmpty(channelTypeIdAgg.getBuckets())) {
            for (Terms.Bucket bucket : channelTypeIdAgg.getBuckets()) {
                String channelTypeId = bucket.getKeyAsString();
                Terms channelTypeNameAgg = bucket.getAggregations().get("distinct_channel_type_name");
                InUseChannel result = new InUseChannel();
                result.setChannelTypeId(channelTypeId);
                for (Terms.Bucket nameBucket : channelTypeNameAgg.getBuckets()) {
                    String channelTypeName = nameBucket.getKeyAsString();
                    result.setChannelTypeName(channelTypeName);
                }
                inUseChannelList.add(result);
            }
        }
        return inUseChannelList;
    }

    //公用方法，用于校验索引是否存在，如果不存在，返回空数据
    private AjaxResult checkIndexExist(String indexName) {
        try {
            // 查询索引是否存在
            boolean indexExists = headIndexExists(indexName);
            // 索引不存在直接创建索引
            if (!indexExists) {
                createIndex(indexName);
                //用201来表示没数据，这样直接返回对应的空集合
                return AjaxResult.ok().setCode(201);
            }
        } catch (Exception e) {
            //如果上述方法出现异常，那么也先返回201
            return AjaxResult.ok().setCode(201);
        }
        return AjaxResult.ok().setCode(200);
    }

    //查询机器人工单和真人工单数量
    private WorkOrderCreateTypeSummary queryWorkOrderCreateTypeSummary(String startTime, String endTime, String companyId, String indexName) throws Exception {
        WorkOrderCreateTypeSummary workOrderCreateTypeSummary;
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            // 时间范围查询
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                String formatDateStart = formatDate(startTime);
                String formatDateEnd = formatDate(endTime);
                //将入参时间进行处理，转化为UTC+8，因为后台数据库存储的内容是UTC+8的时间
                formatDateStart = TimeZoneUtils.requestTimeConversion(formatDateStart);
                formatDateEnd = TimeZoneUtils.requestTimeConversion(formatDateEnd);

                boolQueryBuilder.must(QueryBuilders.rangeQuery("create_time").gte(formatDateStart).lte(formatDateEnd));
            }
            //查询真人工单和机器人工单的数量，统计各自占比，所以条件中不需要考虑当前登录用户角色区分、部门筛选条件等信息，直接用公司id进行查询
            //由于索引命名上已经区分了公司id，所以下边的条件可以不要
//            boolQueryBuilder.must(QueryBuilders.termQuery("company_id", companyId));
//            boolQueryBuilder.must(QueryBuilders.termQuery("data_status", Constants.NORMAL));

            // 构造聚合
            FilterAggregationBuilder robotCountAgg = AggregationBuilders
                    .filter("robotCount", QueryBuilders.termQuery("agent_id", 1001));
            FilterAggregationBuilder realCountAgg = AggregationBuilders
                    .filter("realCount", QueryBuilders.boolQuery().mustNot(QueryBuilders.termQuery("agent_id", 1001)));

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQueryBuilder)
                    .aggregation(robotCountAgg)
                    .aggregation(realCountAgg);

            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source(sourceBuilder);

            // 执行查询
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 解析结果
            ParsedFilter robotCountResult = response.getAggregations().get("robotCount");
            ParsedFilter realCountResult = response.getAggregations().get("realCount");

            Integer robotCount = (int) robotCountResult.getDocCount();
            Integer realCount = (int) realCountResult.getDocCount();

            // 映射到实体
            workOrderCreateTypeSummary = new WorkOrderCreateTypeSummary();
            workOrderCreateTypeSummary.setRobotCount(robotCount);
            workOrderCreateTypeSummary.setRealCount(realCount);
        } catch (IOException e) {
            log.error("查询机器人和真人工单各自数量出现异常：", e);
            //出现异常的时候，按照如下，设置数据为0
            workOrderCreateTypeSummary = new WorkOrderCreateTypeSummary().setRealPercent(BigDecimal.ZERO).setRobotPercent(BigDecimal.ZERO);
        }
        return workOrderCreateTypeSummary;
    }

    private List<InUseWorkOrderType> queryInUseWorkOrderTypeList(SeatingWorkloadNewVo seatingWorkloadVo, String indexName) {
        String startTime = seatingWorkloadVo.getStartDate();
        String endTime = seatingWorkloadVo.getEndDate();
        List<String> filterIdList = seatingWorkloadVo.getFilterIdList();
        List<InUseWorkOrderType> inUseWorkOrderTypeList = new ArrayList<>();
        try {
            //获取所有被使用的工单类型（从es中查询）
            BoolQueryBuilder boolQueryBuilder = packageCommonSearchBuilder(1, startTime, endTime, filterIdList);
            // 构建搜索请求
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQueryBuilder)
                    .fetchSource(new String[]{"work_record_type_code", "work_record_type_name"}, null);

            SearchRequest searchRequest = new SearchRequest(indexName)
                    .source(sourceBuilder);

            // 执行查询
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 解析响应
            inUseWorkOrderTypeList = new ArrayList<>();
            for (SearchHit hit : response.getHits().getHits()) {
                InUseWorkOrderType workOrderType = new InUseWorkOrderType();

                // 获取字段值，并处理默认值逻辑
                String workRecordTypeCode = (String) hit.getSourceAsMap().getOrDefault("work_record_type_code", "No type");
                String workRecordTypeName = (String) hit.getSourceAsMap().getOrDefault("work_record_type_name", "No type");

                if (StringUtil.isEmpty(workRecordTypeCode)) {
                    workRecordTypeCode = "No type";
                }
                if (StringUtil.isEmpty(workRecordTypeName)) {
                    workRecordTypeName = "No type";
                }

                workOrderType.setWorkRecordTypeCode(workRecordTypeCode);
                workOrderType.setWorkRecordTypeName(workRecordTypeName);

                inUseWorkOrderTypeList.add(workOrderType);
            }
        } catch (IOException e) {
            log.error("查询指定条件下被使用的工单类型出现异常：", e);
        }
        return inUseWorkOrderTypeList;
    }

    //处理工单类型国际化
    private List<WorkOrderRecordEsResult> handleWorkRecordTypeInternational(List<WorkOrderRecordEsResult> recordList, Map<String, String> workRecordTypeMap) {
        List<WorkOrderRecordEsResult> handleRecordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(recordList)) {
            for (WorkOrderRecordEsResult workRecordResult : recordList) {
                String workRecordTypeCode = workRecordResult.getWorkRecordTypeCode();
                if (StringUtil.isEmpty(workRecordTypeCode)) {
                    workRecordResult.setWorkRecordTypeCode("No type");
                    workRecordResult.setWorkRecordTypeName("No type");
                    workRecordTypeMap.put("No type", "No type");
                } else {
                    String typeName = workRecordTypeMap.get(workRecordTypeCode);
                    if (StringUtil.isEmpty(typeName)) {
                        workRecordResult.setWorkRecordTypeName("No type");
                        workRecordTypeMap.put(workRecordTypeCode, "No type");
                    } else {
                        workRecordResult.setWorkRecordTypeName(typeName);
                        workRecordTypeMap.put(workRecordTypeCode, typeName);
                    }
                }
                handleRecordList.add(workRecordResult);
            }
            return handleRecordList;
        }
        return handleRecordList;
    }
}

