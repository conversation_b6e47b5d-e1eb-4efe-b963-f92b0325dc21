package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAwsConnect;
import com.goclouds.crm.platform.call.mapper.CrmAwsConnectMapper;
import com.goclouds.crm.platform.call.service.CrmAwsConnectService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_aws_connect(aws账号关联connect实例)】的数据库操作Service实现
* @createDate 2023-05-23 13:53:36
*/
@Service
public class CrmAwsConnectServiceImpl extends ServiceImpl<CrmAwsConnectMapper, CrmAwsConnect>
    implements CrmAwsConnectService {

}




