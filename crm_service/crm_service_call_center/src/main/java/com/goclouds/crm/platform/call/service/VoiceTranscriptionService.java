package com.goclouds.crm.platform.call.service;

import com.goclouds.crm.platform.common.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @description
 * @createTime 2025年04月25日 10:11
 */
public interface VoiceTranscriptionService {

    AjaxResult transcription(MultipartFile file, String companyId, String language, int isOpenChannel);

    AjaxResult queryTranscription(String transcriptionId, String self);
}
