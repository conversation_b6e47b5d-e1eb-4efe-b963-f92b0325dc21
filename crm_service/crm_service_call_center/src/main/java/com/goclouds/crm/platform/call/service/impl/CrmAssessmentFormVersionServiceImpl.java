package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.*;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentCategoryVo;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentFormVersionVo;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentRuleVo;
import com.goclouds.crm.platform.call.mapper.CrmAssessmentFormVersionMapper;
import com.goclouds.crm.platform.call.service.*;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CrmAssessmentFormVersionServiceImpl extends ServiceImpl<CrmAssessmentFormVersionMapper, CrmAssessmentFormVersion>
        implements CrmAssessmentFormVersionService {

    private final CrmAssessmentCategoryService categoryService;
    private final CrmAssessmentRuleService ruleService;
    private final CrmAssessmentRulePointService rulePointService;
    private final CrmAssessmentFormInfoService assessmentFormInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveVersion(CrmAssessmentFormVersionVo versionVo, Boolean deploy) {
        String userId = SecurityUtil.getUserId();
        String versionId;

        // 1. 删除旧草稿数据
        categoryService.removeDraftCategoriesByAssessmentId(versionVo.getAssessmentId());
        ruleService.removeDraftRulesByAssessmentId(versionVo.getAssessmentId());

        // 2. 保存新的草稿数据（status=1）
        Map<String, String> draftIdMap = new HashMap<>();
        saveDraftCategories(versionVo.getCategoryVoList(), versionVo.getAssessmentId(), null, userId, draftIdMap);
        saveDraftRules(versionVo.getRuleVoList(), versionVo.getAssessmentId(), draftIdMap, userId);

        // 3. 保存表单信息（正式）
        if (StringUtils.isNotBlank(versionVo.getAssessmentId())) {
            versionVo.getFormInfo().setAssessmentId(versionVo.getAssessmentId());
        }
        CrmAssessmentFormInfo formInfo = assessmentFormInfoService.saveOrUpdateFormInfo(versionVo.getFormInfo()).getData();

        // 4. 创建正式版本
        String versionNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        CrmAssessmentFormVersion version = new CrmAssessmentFormVersion();
        version.setVersionNo(versionNo);
        version.setDeployStatus(deploy ? 1 : 0);
        version.setVersionId(UUID.randomUUID().toString().replace("-", ""));
        version.setAssessmentId(formInfo.getAssessmentId());
        version.setCreator(userId);
        version.setCreateTime(LocalDateTime.now());
        this.save(version);
        versionId = version.getVersionId();

        // 5. 保存正式分类数据
        Map<String, String> categoryIdMapping = new HashMap<>();
        if (versionVo.getCategoryVoList() != null && !versionVo.getCategoryVoList().isEmpty()) {
            saveVersionCategoriesAndMapping(versionVo.getCategoryVoList(), versionId, null, userId, categoryIdMapping);
        }

        // 6. 保存正式规则数据
        Integer maxSort = baseMapper.selectMaxRuleSort(versionId);
        int currentMaxSort = maxSort != null ? maxSort : 0;
        if (versionVo.getRuleVoList() != null && !versionVo.getRuleVoList().isEmpty()) {
            for (CrmAssessmentRuleVo ruleVo : versionVo.getRuleVoList()) {
                String originalCategoryId = ruleVo.getCategoryId();
                String newCategoryId = categoryIdMapping.get(originalCategoryId);
                if (newCategoryId != null) {
                    CrmAssessmentRule versionRule = new CrmAssessmentRule();
                    BeanUtils.copyProperties(ruleVo, versionRule);
                    versionRule.setRuleId(UUID.randomUUID().toString().replace("-", ""));
                    versionRule.setCategoryId(newCategoryId);
                    versionRule.setVersionId(versionId);
                    versionRule.setStatus(0); // 正式
                    versionRule.setCreator(userId);
                    versionRule.setCreateTime(LocalDateTime.now());
                    versionRule.setRuleSort(++currentMaxSort);
                    ruleService.save(versionRule);

                    if (ruleVo.getRulePointList() != null) {
                        rulePointService.delPoiint(versionRule.getRuleId());
                        for (CrmAssessmentRulePoint point : ruleVo.getRulePointList()) {
                            CrmAssessmentRulePoint versionPoint = new CrmAssessmentRulePoint();
                            BeanUtils.copyProperties(point, versionPoint);
                            versionPoint.setRuleId(versionRule.getRuleId());
                            rulePointService.addRulePoint(versionPoint);
                        }
                    }
                }
            }
        }

        return versionId;
    }


    @Override
    public List<CrmAssessmentFormVersion> getVersionList(String assessmentId) {
        LambdaQueryWrapper<CrmAssessmentFormVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAssessmentFormVersion::getAssessmentId, assessmentId);
        queryWrapper.orderByDesc(CrmAssessmentFormVersion::getVersionNo);
        return this.list(queryWrapper);
    }

    /**
     * 保存草稿状态的分类树（无 versionId）
     */
    private void saveDraftCategories(List<CrmAssessmentCategoryVo> categories, String parentId, String userId) {
        if (categories == null || categories.isEmpty()) return;

        for (CrmAssessmentCategoryVo categoryVo : categories) {
            String newCategoryId = UUID.randomUUID().toString().replace("-", "");
            categoryVo.setCategoryId(newCategoryId);
            categoryVo.setParentId(parentId);
            categoryVo.setCreator(userId);
            categoryVo.setCreateTime(LocalDateTime.now());
            categoryVo.setStatus(1); // 草稿
            categoryVo.setVersionId(null); // 草稿不绑定版本

            List<CrmAssessmentCategoryVo> children = categoryVo.getChildren();
            categoryVo.setChildren(null);
            categoryService.saveCategory(categoryVo);

            if (children != null && !children.isEmpty()) {
                saveDraftCategories(children, newCategoryId, userId);
            }
            categoryVo.setChildren(children);
        }
    }

    /**
     * 保存正式版本的分类，并建立原始ID → 新ID映射
     */
    private void saveVersionCategoriesAndMapping(List<CrmAssessmentCategoryVo> categories, String versionId,
                                                 String parentId, String userId, Map<String, String> idMap) {
        if (categories == null || categories.isEmpty()) return;

        for (CrmAssessmentCategoryVo categoryVo : categories) {
            String originalId = categoryVo.getCategoryId();
            String newCategoryId = UUID.randomUUID().toString().replace("-", "");

            if (originalId != null) {
                idMap.put(originalId, newCategoryId);
            }

            CrmAssessmentCategoryVo newCategory = new CrmAssessmentCategoryVo();
            BeanUtils.copyProperties(categoryVo, newCategory);
            newCategory.setCategoryId(newCategoryId);
            newCategory.setParentId(parentId);
            newCategory.setCreator(userId);
            newCategory.setCreateTime(LocalDateTime.now());
            newCategory.setVersionId(versionId);
            newCategory.setStatus(0); // 正式状态

            List<CrmAssessmentCategoryVo> children = categoryVo.getChildren();
            newCategory.setChildren(null);
            categoryService.saveCategory(newCategory);

            if (children != null && !children.isEmpty()) {
                saveVersionCategoriesAndMapping(children, versionId, newCategoryId, userId, idMap);
            }

            categoryVo.setChildren(children);
        }
    }


    private void saveDraftCategories(List<CrmAssessmentCategoryVo> categories, String assessmentId,
                                     String parentId, String userId, Map<String, String> idMap) {
        if (categories == null || categories.isEmpty()) return;

        for (CrmAssessmentCategoryVo categoryVo : categories) {
            String originalId = categoryVo.getCategoryId();
            String newId = UUID.randomUUID().toString().replace("-", "");
            if (originalId != null) {
                idMap.put(originalId, newId);
            }

            CrmAssessmentCategoryVo newCategory = new CrmAssessmentCategoryVo();
            BeanUtils.copyProperties(categoryVo, newCategory);
            newCategory.setCategoryId(newId);
            newCategory.setAssessmentId(assessmentId);
            newCategory.setParentId(parentId);
            newCategory.setCreator(userId);
            newCategory.setCreateTime(LocalDateTime.now());
            newCategory.setVersionId(null); // 草稿无版本号
            newCategory.setStatus(1); // 草稿状态
            newCategory.setChildren(null);

            categoryService.saveCategory(newCategory);

            if (categoryVo.getChildren() != null) {
                saveDraftCategories(categoryVo.getChildren(), assessmentId, newId, userId, idMap);
            }
        }
    }

    private void saveDraftRules(List<CrmAssessmentRuleVo> ruleVoList, String assessmentId,
                                Map<String, String> categoryIdMap, String userId) {
        if (ruleVoList == null || ruleVoList.isEmpty()) return;

        int sort = 0;
        for (CrmAssessmentRuleVo ruleVo : ruleVoList) {
            String newCategoryId = categoryIdMap.get(ruleVo.getCategoryId());
            if (newCategoryId == null) continue;

            CrmAssessmentRule draftRule = new CrmAssessmentRule();
            BeanUtils.copyProperties(ruleVo, draftRule);
            draftRule.setRuleId(UUID.randomUUID().toString().replace("-", ""));
            draftRule.setAssessmentId(assessmentId);
            draftRule.setCategoryId(newCategoryId);
            draftRule.setVersionId(null);
            draftRule.setStatus(1); // 草稿
            draftRule.setCreator(userId);
            draftRule.setCreateTime(LocalDateTime.now());
            draftRule.setRuleSort(++sort);

            ruleService.save(draftRule);

            if (ruleVo.getRulePointList() != null) {
                for (CrmAssessmentRulePoint point : ruleVo.getRulePointList()) {
                    CrmAssessmentRulePoint newPoint = new CrmAssessmentRulePoint();
                    BeanUtils.copyProperties(point, newPoint);
                    newPoint.setRuleId(draftRule.getRuleId());
                    rulePointService.addRulePoint(newPoint);
                }
            }
        }
    }


}
