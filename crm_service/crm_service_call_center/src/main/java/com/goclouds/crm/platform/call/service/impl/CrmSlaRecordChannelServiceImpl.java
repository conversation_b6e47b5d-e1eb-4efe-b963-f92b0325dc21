package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmSlaRecordChannel;
import com.goclouds.crm.platform.call.service.CrmSlaRecordChannelService;
import com.goclouds.crm.platform.call.mapper.CrmSalRecordChannelMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_sal_record_channel】的数据库操作Service实现
* @createDate 2025-03-13 15:51:43
*/
@Service
@RequiredArgsConstructor
public class CrmSlaRecordChannelServiceImpl extends ServiceImpl<CrmSalRecordChannelMapper, CrmSlaRecordChannel>
    implements CrmSlaRecordChannelService {

}




