package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmExtDefTemplate;
import com.goclouds.crm.platform.call.service.CrmExtDefTemplateService;
import com.goclouds.crm.platform.call.mapper.CrmExtDefTemplateMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【crm_ext_def_template(属性定义模板表)】的数据库操作Service实现
* @createDate 2023-11-16 14:24:35
*/
@Service
public class CrmExtDefTemplateServiceImpl extends ServiceImpl<CrmExtDefTemplateMapper, CrmExtDefTemplate>
    implements CrmExtDefTemplateService{

}




