package com.goclouds.crm.platform.call.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExtDef;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExtOptionDef;
import com.goclouds.crm.platform.call.domain.CrmExtDefTemplate;
import com.goclouds.crm.platform.call.domain.CrmExtOptionDefTemplate;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderExtOptionDefVo;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderExtVo;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordTypeVO;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordExtDefMapper;
import com.goclouds.crm.platform.call.service.*;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.InputPropTypeEnum;
import com.goclouds.crm.platform.common.utils.RedisCacheUtil;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.openfeignClient.client.channel.ChannelClient;
import com.goclouds.crm.platform.openfeignClient.client.channel.ConnectsServiceClient;
import com.goclouds.crm.platform.openfeignClient.client.customer.CustomerTagClient;
import com.goclouds.crm.platform.openfeignClient.client.system.UserPreferencesConfigClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.channel.ChannelDefVo;
import com.goclouds.crm.platform.openfeignClient.domain.channel.ChannelVo;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CustomerTagCategoryGroupResult;
import com.goclouds.crm.platform.openfeignClient.domain.customer.CustomerTagDefResult;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysLanguageDefVo;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysPropertyTypeDefVo;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import com.goclouds.crm.platform.utils.trans.TransUtil;
import com.goclouds.crm.platform.utils.ServletUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【crm_agent_work_record_ext_def(工单属性定义表;)】的数据库操作Service实现
 * @createDate 2023-09-20 15:52:31
 */
@Service
@Slf4j
public class CrmAgentWorkRecordExtDefServiceImpl extends ServiceImpl<CrmAgentWorkRecordExtDefMapper, CrmAgentWorkRecordExtDef>
        implements CrmAgentWorkRecordExtDefService {
    @Autowired
    private CrmAgentWorkRecordExtOptionDefService optionDefService;

    @Resource
    private ConnectsServiceClient connectsServiceClient;

    @Resource
    private CustomerTagClient customerTagClient;

    @Resource
    private ChannelClient channelClient;

    @Autowired
    private CrmAgentWorkRecordTypeDefService crmAgentWorkRecordTypeDefService;

    @Resource
    private UserPreferencesConfigClient preferencesConfigClient;

    @Autowired
    private CrmExtDefTemplateService extDefTemplateService;

    @Autowired
    private CrmExtOptionDefTemplateService extOptionDefTemplateService;

    @Override
    public AjaxResult<List<WorkOrderExtVo>> queryList(Integer queryType) {
        QueryWrapper<String> wrapper = new QueryWrapper<String>();
        wrapper.and(subWrapper -> subWrapper.eq("t1.is_system_default", 1).or().eq("t1.company_id", SecurityUtil.getLoginUser().getCompanyId()));
        wrapper.eq("t1.data_status", 1);
        wrapper.eq("t1.company_id", SecurityUtil.getLoginUser().getCompanyId());
        // 国际化查询
            wrapper.eq("t1.language_code", ServletUtils.getHeaderLanguage());
        wrapper.orderByAsc("t1.work_record_ext_def_order");
        List<WorkOrderExtOptionDefVo> list = this.baseMapper.queryWorkOrderExtList(wrapper);

        // ext-option是一对多的关系（仅在多选时，其他ext没有option）
        if (CollectionUtils.isNotEmpty(list)) {
            // 如果查询的类型是人工类型的，则排除是否转人工字段
            if(queryType!= null && queryType != 8){
                list = list.stream()
                        .filter(item -> !"transferToAgentFlag".equals(item.getWorkRecordExtDefCode()))
                        .collect(Collectors.toList());
            }
            List<WorkOrderExtVo> extVoList = queryOptionValue(list);
            return AjaxResult.ok(extVoList);
        }

        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addWorkOrderExt(WorkOrderExtVo workOrderExtVo) {
        try {
            String userId = SecurityUtil.getLoginUser().getUserId();
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            String languageCode = workOrderExtVo.getLanguageCode();
            //判断要保存的属性名称属性值是否唯一
            QueryWrapper<CrmAgentWorkRecordExtDef> wrapperName = new QueryWrapper<CrmAgentWorkRecordExtDef>()
                    .eq("company_id", companyId)
                    .eq("language_code", languageCode)
                    .eq("work_record_ext_def_name", workOrderExtVo.getWorkRecordExtDefName())
                    .eq("data_status", Constants.NORMAL);
            CrmAgentWorkRecordExtDef nameResult = this.getOne(wrapperName);
            if (nameResult != null) {
                //该属性名称已经存在
                return AjaxResult.failure(MessageUtils.get("property.name.already.exist"));
            }
            QueryWrapper<CrmAgentWorkRecordExtDef> wrapperValue = new QueryWrapper<CrmAgentWorkRecordExtDef>()
                    .eq("company_id", companyId)
                    .eq("language_code", languageCode)
                    .eq("work_record_ext_def_code", workOrderExtVo.getWorkRecordExtDefCode())
                    .eq("data_status", Constants.NORMAL);
            CrmAgentWorkRecordExtDef valueResult = this.getOne(wrapperValue);
            if (valueResult != null) {
                //该属性编码已经存在
                return AjaxResult.failure(MessageUtils.get("property.code.already.exist"));
            }
            //校验选项值
            String propertyTypeId = workOrderExtVo.getPropertyTypeId();
            List<WorkOrderExtOptionDefVo> optionDefList = workOrderExtVo.getWorkOrderExtOptionDefList();
            AjaxResult ajaxResult = checkExtOptionDef(propertyTypeId, optionDefList);
            if (200 != ajaxResult.getCode()) {
                return ajaxResult;
            }
            R<List<SysLanguageDefVo>> listR = preferencesConfigClient.queryLanguage();
            List<SysLanguageDefVo> list=new ArrayList<>();
            if (listR.getCode() == 200) {
                list = listR.getData();
            }
            if(CollectionUtils.isNotEmpty(list)) {
                for (SysLanguageDefVo languageDefVo : list) {
                    String eachLanguageCode = languageDefVo.getLanguageCode();
                    String languageIsoCode = languageDefVo.getLanguageIsoCode();
                    //获取上次排序的最大值
                    LambdaQueryWrapper<CrmAgentWorkRecordExtDef> queryWrapper = new LambdaQueryWrapper<CrmAgentWorkRecordExtDef>()
                            .eq(CrmAgentWorkRecordExtDef::getCompanyId, companyId)
                            .eq(CrmAgentWorkRecordExtDef::getDataStatus, Constants.NORMAL)
                            .eq(CrmAgentWorkRecordExtDef::getLanguageCode, eachLanguageCode)
                            .orderByDesc(CrmAgentWorkRecordExtDef::getWorkRecordExtDefOrder)
                            .last("LIMIT 1");
                    CrmAgentWorkRecordExtDef maxOrderOne = this.getOne(queryWrapper);
                    Integer currentOrder;
                    if (maxOrderOne != null) {
                        Integer extDefOrder = maxOrderOne.getWorkRecordExtDefOrder();
                        currentOrder = extDefOrder + 1;
                    } else {
                        currentOrder = 1;
                    }
                    //保存工单扩展属性定义表
                    CrmAgentWorkRecordExtDef crmAgentWorkRecordExtDef = new CrmAgentWorkRecordExtDef();
                    String workOrderExtDefId = IdWorker.get32UUID();
                    crmAgentWorkRecordExtDef.setWorkRecordExtDefId(workOrderExtDefId);
                    crmAgentWorkRecordExtDef.setCompanyId(companyId);
                    crmAgentWorkRecordExtDef.setPropertyTypeId(workOrderExtVo.getPropertyTypeId());
                    String workRecordExtDefName = workOrderExtVo.getWorkRecordExtDefName();
                    String prompt = workOrderExtVo.getPrompt();
                    //不用翻译当前入参语言对应的内容
                    if(eachLanguageCode.equals(languageCode)){
                        crmAgentWorkRecordExtDef.setWorkRecordExtDefName(workRecordExtDefName);
                        crmAgentWorkRecordExtDef.setPrompt(prompt);
                    }else{
                        String trans = TransUtil.trans(workRecordExtDefName, languageIsoCode);
                        crmAgentWorkRecordExtDef.setWorkRecordExtDefName(trans);
                        if(StringUtil.isNotEmpty(prompt)){
                            crmAgentWorkRecordExtDef.setPrompt(TransUtil.trans(prompt, languageIsoCode));
                        }
                    }
                    crmAgentWorkRecordExtDef.setWorkRecordExtDefCode(workOrderExtVo.getWorkRecordExtDefCode());
                    crmAgentWorkRecordExtDef.setIsRequired(workOrderExtVo.getIsRequired());
                    crmAgentWorkRecordExtDef.setIsSystemDefault(0);
                    crmAgentWorkRecordExtDef.setWorkRecordExtDefOrder(currentOrder);

                    //选项类型1-系统定义，2-外部接口 默认是1
                    crmAgentWorkRecordExtDef.setOptionsType(1);
                    //如果为外部接口地址，则输入接口地址
                    crmAgentWorkRecordExtDef.setInterfaceUrl(null);
                    crmAgentWorkRecordExtDef.setDataStatus(Constants.NORMAL);
                    crmAgentWorkRecordExtDef.setCreator(userId);
                    crmAgentWorkRecordExtDef.setCreateTime(new Date());
                    crmAgentWorkRecordExtDef.setModifier(userId);
                    crmAgentWorkRecordExtDef.setModifyTime(new Date());
                    crmAgentWorkRecordExtDef.setLanguageCode(eachLanguageCode);
                    this.baseMapper.insert(crmAgentWorkRecordExtDef);
                    //保存工单自定义属性选项定义表
                    saveExtOptionDef(optionDefList, userId, workOrderExtDefId,languageCode,eachLanguageCode,languageIsoCode,1);
                }
            }
        } catch (Exception e) {
            log.error("新增工单扩展属性出现异常：",e);
            return AjaxResult.failure().setMsg(MessageUtils.get("operate.failure"));
        }
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateWorkOrderExt(WorkOrderExtVo workOrderExtVo) {
        try {
            String userId = SecurityUtil.getLoginUser().getUserId();
            String workOrderExtDefId = workOrderExtVo.getWorkRecordExtDefId();
            String languageCode = workOrderExtVo.getLanguageCode();
            String companyId = SecurityUtil.getLoginUser().getCompanyId();
            //校验选项值
            String propertyTypeId = workOrderExtVo.getPropertyTypeId();
            List<WorkOrderExtOptionDefVo> optionDefList = workOrderExtVo.getWorkOrderExtOptionDefList();
            AjaxResult ajaxResult = checkExtOptionDef(propertyTypeId, optionDefList);
            if (200 != ajaxResult.getCode()) {
                return ajaxResult;
            }
            //属性编码不可以修改，修改别的
            this.update(new LambdaUpdateWrapper<CrmAgentWorkRecordExtDef>()
                    .set(CrmAgentWorkRecordExtDef::getPropertyTypeId, workOrderExtVo.getPropertyTypeId())
                    .set(CrmAgentWorkRecordExtDef::getPrompt, workOrderExtVo.getPrompt())
                    .set(CrmAgentWorkRecordExtDef::getIsRequired, workOrderExtVo.getIsRequired())
                    .set(CrmAgentWorkRecordExtDef::getWorkRecordExtDefName, workOrderExtVo.getWorkRecordExtDefName())
                    .set(CrmAgentWorkRecordExtDef::getModifier, userId)
                    .set(CrmAgentWorkRecordExtDef::getModifyTime, new Date())
                    .eq(CrmAgentWorkRecordExtDef::getLanguageCode, languageCode)
                    .eq(CrmAgentWorkRecordExtDef::getWorkRecordExtDefId, workOrderExtDefId));
            //删除之前的旧数据
            deleteExtOptionDef(workOrderExtDefId);
            //重新存储新数据
            saveExtOptionDef(optionDefList, userId, workOrderExtDefId, languageCode, null, null, 2);
            CrmAgentWorkRecordExtDef extDef = this.getOne(new QueryWrapper<CrmAgentWorkRecordExtDef>()
                    .eq("company_id", companyId)
                    .eq("data_status", Constants.NORMAL)
                    .eq("work_record_ext_def_id", workOrderExtDefId));
            if (extDef != null) {
                String workRecordExtDefCode = extDef.getWorkRecordExtDefCode();
                List<CrmAgentWorkRecordExtDef> list = this.list(new QueryWrapper<CrmAgentWorkRecordExtDef>()
                        .eq("company_id", companyId)
                        .eq("data_status", Constants.NORMAL)
                        .ne("language_code", languageCode)
                        .eq("work_record_ext_def_code", workRecordExtDefCode));
                if(CollectionUtils.isNotEmpty(list)){
                    R<List<SysLanguageDefVo>> listR = preferencesConfigClient.queryLanguage();
                    List<SysLanguageDefVo> languageDefVoList=new ArrayList<>();
                    if (listR.getCode() == 200) {
                        languageDefVoList = listR.getData();
                    }
                    Map<String, String> languageIsoCodeMap=new HashMap<>();
                    if(CollectionUtils.isNotEmpty(languageDefVoList)){
                        //将该list转为map
                        languageIsoCodeMap = languageDefVoList.stream().collect(Collectors.toMap(SysLanguageDefVo::getLanguageCode, SysLanguageDefVo::getLanguageIsoCode));
                    }
                    for (CrmAgentWorkRecordExtDef workRecordExtDef : list) {
                        String preDefId = workRecordExtDef.getWorkRecordExtDefId();
                        String preLanguageCode = workRecordExtDef.getLanguageCode();
                        if (!preLanguageCode.equals(languageCode)) {
                            String languageIsoCode = languageIsoCodeMap.get(preLanguageCode);
                            //删除之前的旧数据
                            deleteExtOptionDef(preDefId);
                            //重新存储新数据
                            saveExtOptionDef(optionDefList, userId, preDefId, languageCode, preLanguageCode, languageIsoCode, 1);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("更新工单扩展属性出现异常：",e);
            return AjaxResult.failure().setMsg(MessageUtils.get("operate.failure"));
        }
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteWorkOrderExt(String workOrderExtDefId,String languageCode) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        CrmAgentWorkRecordExtDef extDef = this.getOne(new QueryWrapper<CrmAgentWorkRecordExtDef>()
                .eq("company_id", companyId)
                .eq("data_status", Constants.NORMAL)
                .eq("work_record_ext_def_id", workOrderExtDefId));
        if (extDef != null) {
            String workRecordExtDefCode = extDef.getWorkRecordExtDefCode();
            //根据这个code查询出来其他所有语言对应扩展属性的主键
            List<CrmAgentWorkRecordExtDef> list = this.list(new QueryWrapper<CrmAgentWorkRecordExtDef>()
                    .eq("company_id", companyId)
                    .eq("data_status", Constants.NORMAL)
                    .eq("work_record_ext_def_code", workRecordExtDefCode));
            if (CollectionUtils.isNotEmpty(list)) {
                for (CrmAgentWorkRecordExtDef crmCustomerExtDef : list) {
                    String preExtDefId = crmCustomerExtDef.getWorkRecordExtDefId();
                    String preLanguageCode = crmCustomerExtDef.getLanguageCode();
                    boolean remove = this.remove(new LambdaQueryWrapper<CrmAgentWorkRecordExtDef>()
                            .eq(CrmAgentWorkRecordExtDef::getIsSystemDefault, 0)  // 只能删除扩展字段
                            .eq(CrmAgentWorkRecordExtDef::getWorkRecordExtDefId, preExtDefId));
                    if (remove) {
                        deleteExtOptionDef(preExtDefId);
                        //将剩下的数据重新排序
                        List<CrmAgentWorkRecordExtDef> selectList = this.list(new QueryWrapper<CrmAgentWorkRecordExtDef>()
                                .eq("company_id", companyId)
                                .eq("data_status", Constants.NORMAL)
                                .eq("language_code",preLanguageCode)
                                .orderByAsc("work_record_ext_def_order")
                        );
                        if (!CollectionUtils.isEmpty(selectList)) {
                            for (int i = 0; i < selectList.size(); i++) {
                                CrmAgentWorkRecordExtDef workRecordExtDef = selectList.get(i);
                                workRecordExtDef.setWorkRecordExtDefOrder(i + 1);
                            }
                            this.updateBatchById(selectList);
                        }
                    }
                }
            }
        }
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateWorkOrderExtSort(List<WorkOrderExtVo> workOrderExtVoList,String languageCode) {
        String userId = SecurityUtil.getLoginUser().getUserId();
        List<CrmAgentWorkRecordExtDef> saveList = new ArrayList<>();
        TreeSet<Integer> orderSet = new TreeSet<>();
        if (!CollectionUtils.isEmpty(workOrderExtVoList)) {
            //获取所有的排序序号放入有序集合，TreeSet默认是升序
            for (WorkOrderExtVo workOrderExtVo : workOrderExtVoList) {
                orderSet.add(workOrderExtVo.getWorkRecordExtDefOrder());
            }
            //set转list,有索引
            List<Integer> list = new ArrayList<>(orderSet);
            //根据入参list中的实体顺序，更新order
            for (int i = 0; i < workOrderExtVoList.size(); i++) {
                WorkOrderExtVo workOrderExtVo = workOrderExtVoList.get(i);
                CrmAgentWorkRecordExtDef CrmAgentWorkRecordExtDef = new CrmAgentWorkRecordExtDef();
                CrmAgentWorkRecordExtDef.setWorkRecordExtDefId(workOrderExtVo.getWorkRecordExtDefId());
                CrmAgentWorkRecordExtDef.setWorkRecordExtDefOrder(list.get(i));
                CrmAgentWorkRecordExtDef.setModifier(userId);
                CrmAgentWorkRecordExtDef.setModifyTime(new Date());
                CrmAgentWorkRecordExtDef.setLanguageCode(languageCode);
                saveList.add(CrmAgentWorkRecordExtDef);
            }
        }
        this.updateBatchById(saveList);
        return AjaxResult.ok().setMsg(MessageUtils.get("operate.success"));
    }

    @Override
    public IPage<CrmAgentWorkRecordExtDef> listWorkOrderExt(IPage<CrmAgentWorkRecordExtDef> page,String languageCode) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        IPage<CrmAgentWorkRecordExtDef> crmAgentWorkRecordExtDefIPage = this.baseMapper.selectPage(page, new QueryWrapper<CrmAgentWorkRecordExtDef>()
                .eq("company_id", companyId)
                .eq("data_status", Constants.NORMAL)
                .eq("language_code", languageCode)
                .orderByAsc("work_record_ext_def_order")
        );
        R<List<SysPropertyTypeDefVo>> listR = preferencesConfigClient.queryOptionList();
        List<SysPropertyTypeDefVo> list = new ArrayList<>();
        if (listR.getCode() == 200) {
            list = listR.getData();
        }
        if(CollectionUtils.isNotEmpty(list)){
            Map<String, String> map = list.stream().collect(Collectors.toMap(SysPropertyTypeDefVo::getPropertyTypeId, SysPropertyTypeDefVo::getPropertyTypeName));
            List<CrmAgentWorkRecordExtDef> records = crmAgentWorkRecordExtDefIPage.getRecords();
            if(CollectionUtils.isNotEmpty(records)){
                for(CrmAgentWorkRecordExtDef def:records){
                    String propertyTypeId = def.getPropertyTypeId();
                    String propertyTypeName = map.get(propertyTypeId).toString();
                    String trans = TransUtil.trans(propertyTypeName);
                    def.setPropertyTypeName(trans);
                }
            }
        }
        return crmAgentWorkRecordExtDefIPage;
    }

    @Override
    public AjaxResult queryWorkOrderExtById(String workOrderExtDefId) {
        QueryWrapper<CrmAgentWorkRecordExtDef> queryWrapper = new QueryWrapper<CrmAgentWorkRecordExtDef>()
                .eq("work_record_ext_def_id", workOrderExtDefId)
                .eq("data_status", Constants.NORMAL);
        CrmAgentWorkRecordExtDef crmAgentWorkRecordExtDef = this.getOne(queryWrapper);
        WorkOrderExtVo workOrderExtVoResult = new WorkOrderExtVo();
        BeanUtils.copyProperties(crmAgentWorkRecordExtDef, workOrderExtVoResult);
        QueryWrapper<CrmAgentWorkRecordExtOptionDef> optionQueryWrapper = new QueryWrapper<CrmAgentWorkRecordExtOptionDef>()
                .eq("work_record_ext_def_id", workOrderExtDefId)
                .eq("data_status", Constants.NORMAL)
                .orderByAsc("option_order");
        List<WorkOrderExtOptionDefVo> workOrderExtOptionDefList = new ArrayList<>();
        List<CrmAgentWorkRecordExtOptionDef> list = optionDefService.list(optionQueryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            for (CrmAgentWorkRecordExtOptionDef optionDef : list) {
                WorkOrderExtOptionDefVo workOrderExtOptionDefVo = new WorkOrderExtOptionDefVo();
                BeanUtils.copyProperties(optionDef, workOrderExtOptionDefVo);
                workOrderExtOptionDefList.add(workOrderExtOptionDefVo);
            }
        }
        workOrderExtVoResult.setWorkOrderExtOptionDefList(workOrderExtOptionDefList);
        return AjaxResult.ok(workOrderExtVoResult);
    }


    @Override
    public AjaxResult<List<WorkOrderExtVo>> queryDefineExtList() {
        List<WorkOrderExtOptionDefVo> list = this.baseMapper.queryWorkOrderExtList(new QueryWrapper<String>()
                .eq("t1.company_id", SecurityUtil.getLoginUser().getCompanyId())
                .eq("t1.is_system_default",0)
                // 国际化查询
                .eq("t1.language_code", ServletUtils.getHeaderLanguage())
                .eq("t1.data_status", 1));

        // ext-option是一对多的关系（仅在多选时，其他ext没有option）
        if (CollectionUtils.isNotEmpty(list)) {
            List<WorkOrderExtVo> extVoList = queryOptionValue(list);
            return AjaxResult.ok(extVoList);
        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public List<String> queryExtInfoByCode(String code) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<CrmAgentWorkRecordExtDef>()
                        .in(CrmAgentWorkRecordExtDef::getWorkRecordExtDefCode,
                                Arrays.asList(code.split(",")))).stream()
                .map(CrmAgentWorkRecordExtDef::getWorkRecordExtDefName)
                .collect(Collectors.toList());
    }

    @Override
    public List<WorkOrderExtVo> queryWorkRecordByUserId(String userId,String redisKey, Integer queryType) {
        List<WorkOrderExtVo> cacheList = RedisCacheUtil.getCacheList(redisKey + userId);
        // 表头国际化
        Map<String, String> extDefMap = getExtDefMap();
        // 查询缓存中是否存在，
        if(CollectionUtils.isNotEmpty(cacheList)){
            if (CollectionUtils.isNotEmpty(extDefMap)) {
                cacheList
                        .forEach(vo -> {
                            if (StringUtil.isNotBlank(extDefMap.get(vo.getWorkRecordExtDefCode()))) {
                                vo.setWorkRecordExtDefName(extDefMap.get(vo.getWorkRecordExtDefCode()));
                            }
                        });
            }
            return cacheList;
        }
        // 不存在查询数据库中的系统字端, 查询为中文的,进行存储redis
        LambdaQueryWrapper<CrmAgentWorkRecordExtDef> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CrmAgentWorkRecordExtDef::getIsSystemDefault, 1);
        queryWrapper.eq(CrmAgentWorkRecordExtDef::getLanguageCode, "zh-CN");
        queryWrapper.eq(CrmAgentWorkRecordExtDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId());
        queryWrapper.orderByAsc(CrmAgentWorkRecordExtDef::getWorkRecordExtDefOrder);
        // 查询数据
        List<CrmAgentWorkRecordExtDef> list = this.list(queryWrapper);

        // 如果查询的类型是人工类型的，则排除是否转人工字段
        if(queryType!= null && queryType != 8){
            list = list.stream()
                    .filter(item -> !"transferToAgentFlag".equals(item.getWorkRecordExtDefCode()))
                    .collect(Collectors.toList());
        }

        List<WorkOrderExtVo> vos = new ArrayList<>();
        // 转换格式,存储redis
        list.forEach(item -> vos.add(new WorkOrderExtVo(item)));
        RedisCacheUtil.setCacheList(redisKey + userId, vos);

        // 存储完redis,进行国际化
        if (CollectionUtils.isNotEmpty(extDefMap)) {
            vos.forEach(vo -> {
                        if (StringUtil.isNotBlank(extDefMap.get(vo.getWorkRecordExtDefCode()))) {
                            vo.setWorkRecordExtDefName(extDefMap.get(vo.getWorkRecordExtDefCode()));
                        }
                    });
        }
        return vos;
    }

    @Override
    public void addUserWorkRecordExt(List<String> workRecordExtDefIds, String redisKey) {
        RedisCacheUtil.deleteObject(redisKey+SecurityUtil.getUserId());
        List<WorkOrderExtVo> vos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(workRecordExtDefIds)){
            LambdaQueryWrapper<CrmAgentWorkRecordExtDef> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(CrmAgentWorkRecordExtDef::getWorkRecordExtDefId, workRecordExtDefIds);
            queryWrapper.orderByAsc(CrmAgentWorkRecordExtDef::getWorkRecordExtDefOrder);
            List<CrmAgentWorkRecordExtDef> list = this.list(queryWrapper);
            list.forEach(item -> vos.add(new WorkOrderExtVo(item)));
            RedisCacheUtil.setCacheList(redisKey + SecurityUtil.getUserId(), vos);
        }
    }

    private AjaxResult checkExtOptionDef(String propertyTypeId, List<WorkOrderExtOptionDefVo> optionDefList) {
        //根据propertyTypeId类型判断是否为空（单选下拉框、单选下拉框、单选框、多选框和开关是需要选项的，所以optionDefList不能为空）
        if (InputPropTypeEnum.Single_Select.getCode().equals(propertyTypeId) ||
                InputPropTypeEnum.Multiple_Select.getCode().equals(propertyTypeId) ||
                InputPropTypeEnum.Single_Button.getCode().equals(propertyTypeId) ||
                InputPropTypeEnum.Multiple_Button.getCode().equals(propertyTypeId)
//                || InputPropTypeEnum.Switch.getCode().equals(propertyTypeId)
        ) {
            //查询要保存的数据是否有空值，是否有重复值
            Set<String> nameSet = new HashSet<>();
            Set<String> valueSet = new HashSet<>();
            if (CollectionUtils.isEmpty(optionDefList)) {
                //选项数据不能为空
                return AjaxResult.failure(MessageUtils.get("option.data.can.not.be.empty"));
            }
            for (int i = 0; i < optionDefList.size(); i++) {
                WorkOrderExtOptionDefVo optionDef = optionDefList.get(i);
                String optionName = optionDef.getOptionName();
                String optionValue = optionDef.getOptionValue();
                if (StringUtil.isEmpty(optionName) || StringUtil.isEmpty(optionValue)) {
                    //选项名称或者是选项值不能为空
                    return AjaxResult.failure(MessageUtils.get("option.name.or.value.can.not.be.empty"));
                }
                nameSet.add(optionName);
                valueSet.add(optionValue);
            }
            if (nameSet.size() != optionDefList.size()) {
                //选项名称有重复
                return AjaxResult.failure(MessageUtils.get("option.name.repeat"));
            }
            if (valueSet.size() != optionDefList.size()) {
                //选项值有重复
                return AjaxResult.failure(MessageUtils.get("option.value.repeat"));
            }
        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    private void saveExtOptionDef(List<WorkOrderExtOptionDefVo> optionDefList, String userId, String workOrderExtDefId,
            String languageCode,String eachLanguageCode,String languageIsoCode,Integer saveType) {
        if (!CollectionUtils.isEmpty(optionDefList)) {
            List<CrmAgentWorkRecordExtOptionDef> saveOptionDefList = new ArrayList<>();
            for (int i = 0; i < optionDefList.size(); i++) {
                WorkOrderExtOptionDefVo workOrderExtOptionDefVo = optionDefList.get(i);
                CrmAgentWorkRecordExtOptionDef optionDef = new CrmAgentWorkRecordExtOptionDef();
                BeanUtils.copyProperties(workOrderExtOptionDefVo, optionDef);
                if (saveType == 1) {
                    if (!eachLanguageCode.equals(languageCode)) {
                        String optionName = optionDef.getOptionName();
                        String trans = TransUtil.trans(optionName, languageIsoCode);
                        optionDef.setOptionName(trans);
                    }
                }
                //补充除了选项展示名称和选项值之外的其他字段
                optionDef.setOptionId(IdWorker.get32UUID());
                optionDef.setWorkRecordExtDefId(workOrderExtDefId);
                optionDef.setCreator(userId);
                optionDef.setCreateTime(new Date());
                optionDef.setOptionOrder(i + 1);
                optionDef.setModifier(userId);
                optionDef.setModifyTime(new Date());
                optionDef.setDataStatus(Constants.NORMAL);
                saveOptionDefList.add(optionDef);
            }
            optionDefService.saveBatch(saveOptionDefList);
        }
    }

    private void deleteExtOptionDef(String workOrderExtDefId) {
        optionDefService.remove(new LambdaQueryWrapper<CrmAgentWorkRecordExtOptionDef>().eq(CrmAgentWorkRecordExtOptionDef::getWorkRecordExtDefId, workOrderExtDefId));
        //逻辑删除之前的旧数据
//        List<CrmAgentWorkRecordExtOptionDef> selectList = optionDefService.list(new QueryWrapper<CrmAgentWorkRecordExtOptionDef>()
//                .eq("work_record_ext_def_id", workOrderExtDefId)
//                .eq("data_status", Constants.NORMAL)
//        );
//        if (!CollectionUtils.isEmpty(selectList)) {
//            selectList.forEach(item -> {
//                item.setDataStatus(Constants.DELETE);
//            });
//            optionDefService.updateBatchById(selectList);
//        }
    }

    /**
     *  拼装工单自定义属性下拉值
     * @param list 下拉值
     * @return 自定义属性下拉值
     */
    private List<WorkOrderExtVo> queryOptionValue(List<WorkOrderExtOptionDefVo> list ){
        List<WorkOrderExtVo> extDefVoList = new ArrayList<>();
//        Map<String, List<WorkOrderExtOptionDefVo>> optionDefVoMap = list.stream().collect(Collectors.groupingBy(WorkOrderExtOptionDefVo::getWorkRecordExtDefId));
        // 将字段排序
        Map<String, List<WorkOrderExtOptionDefVo>> optionDefVoMap =  list.stream().sorted(Comparator.comparingInt(WorkOrderExtOptionDefVo:: getWorkRecordExtDefOrder)).collect(Collectors.groupingBy(WorkOrderExtOptionDefVo::getWorkRecordExtDefId,LinkedHashMap:: new,Collectors.toList()));
        Long start = System.currentTimeMillis();
        log.info("方法开始时间{}", start);
        optionDefVoMap
                .forEach((key, value) -> {
                    WorkOrderExtOptionDefVo vo = value.get(0);
                    WorkOrderExtVo workOrderExtVo = new WorkOrderExtVo()
                            .setWorkRecordExtDefId(key)
                            .setWorkOrderExtOptionDefList(new ArrayList<>());
                    BeanUtils.copyProperties(vo, workOrderExtVo);
                    // 将选项值进行排序
                    List<WorkOrderExtOptionDefVo> collect = value.stream().sorted(Comparator.comparing(WorkOrderExtOptionDefVo::getOptionOrder)).collect(Collectors.toList());
                    collect.forEach(item ->{
                        if(item.getOptionId()!=null){
                            workOrderExtVo.getWorkOrderExtOptionDefList().add(item);
                        }
                    });

                    // 单独处理渠道的值
                    if ("priorityLevelName".equals(workOrderExtVo.getWorkRecordExtDefCode())){
                        Long priorityLevelStart = System.currentTimeMillis();
                        log.info("priorityLevelName 开始时间 {}", priorityLevelStart);
                        List<WorkOrderExtOptionDefVo> workOrderExtOptionDefList = workOrderExtVo.getWorkOrderExtOptionDefList();
                        for (WorkOrderExtOptionDefVo workOrder:workOrderExtOptionDefList) {
                            workOrder.setOptionName(TransUtil.trans(workOrder.getOptionName()));
                        }
                        Long priorityLevelEnd = System.currentTimeMillis();
                        log.info("channelConfigName {} 用时 {}", priorityLevelEnd, priorityLevelEnd-priorityLevelStart);
                    }else if ("channelConfigName".equals(workOrderExtVo.getWorkRecordExtDefCode())) {
                        Long channelConfigNameStart = System.currentTimeMillis();
                        log.info("channelConfigName 开始时间 {}", channelConfigNameStart);
                        R<List<ChannelVo>> r = channelClient.channelListByConnectIdCopy(null);
                        if (r.getCode() == AjaxResult.SUCCESS && CollectionUtils.isNotEmpty(r.getData())) {
                            r.getData()
                                    .stream()
                                    .map(channel -> {
                                        WorkOrderExtOptionDefVo workOrderExtOptionDefVo = new WorkOrderExtOptionDefVo()
                                                .setOptionId(IdWorker.get32UUID())
                                                .setOptionName(channel.getName())
                                                .setOptionValue(channel.getName())
                                                .setIsSystemDefault(1)
                                                .setPropertyTypeId(InputPropTypeEnum.Multiple_Select.getCode());
                                        return workOrderExtOptionDefVo;
                                    })
                                    .forEach(workOrderExtVo.getWorkOrderExtOptionDefList()::add);
                        }
                        Long priorityLevelEnd = System.currentTimeMillis();
                        log.info("channelConfigName {} 用时 {}", priorityLevelEnd, priorityLevelEnd-channelConfigNameStart);
                    }else if("workRecordTypeName".equals(workOrderExtVo.getWorkRecordExtDefCode())){
                        Long channelConfigNameStart = System.currentTimeMillis();
                        log.info("workRecordTypeName 开始时间 {}", channelConfigNameStart);
                        // 查询工单类型表
                        List<WorkRecordTypeVO> workRecordTypeVOS = crmAgentWorkRecordTypeDefService.queryWorkRecordType();
                        workRecordTypeVOS.stream().map(ticketType ->{
                            WorkOrderExtOptionDefVo workOrderExtOptionDefVo = new WorkOrderExtOptionDefVo()
                                    .setOptionId(IdWorker.get32UUID())
                                    .setOptionName(ticketType.getWorkRecordTypeName())
                                    .setOptionValue(ticketType.getWorkRecordTypeId())
                                    .setIsSystemDefault(1)
                                    .setPropertyTypeId(InputPropTypeEnum.Multiple_Input.getCode());
                            return workOrderExtOptionDefVo;
                        }).forEach(workOrderExtVo.getWorkOrderExtOptionDefList()::add);
                        Long priorityLevelEnd = System.currentTimeMillis();
                        log.info("workRecordTypeName {} 用时 {}", priorityLevelEnd, priorityLevelEnd-channelConfigNameStart);
                    }else if ("channelTypeName".equals(workOrderExtVo.getWorkRecordExtDefCode())){
                        Long channelConfigNameStart = System.currentTimeMillis();
                        log.info("channelTypeName 开始时间 {}", channelConfigNameStart);
                        R<List<ChannelDefVo>> r = channelClient.innerChannelType();
                        if (r.getCode() == AjaxResult.SUCCESS && CollectionUtils.isNotEmpty(r.getData())) {
                            r.getData()
                                    .stream()
                                    .map(channel -> {
                                        WorkOrderExtOptionDefVo workOrderExtOptionDefVo = new WorkOrderExtOptionDefVo()
                                                .setOptionId(IdWorker.get32UUID())
                                                .setOptionName(channel.getName())
                                                .setOptionValue(channel.getCode())
                                                .setIsSystemDefault(1)
                                                .setPropertyTypeId(InputPropTypeEnum.Single_Select.getCode());
                                        return workOrderExtOptionDefVo;
                                    })
                                    .forEach(workOrderExtVo.getWorkOrderExtOptionDefList()::add);
                        }
                        Long priorityLevelEnd = System.currentTimeMillis();
                        log.info("channelTypeName {} 用时 {}", priorityLevelEnd, priorityLevelEnd-channelConfigNameStart);
                    }else if ("workRecordTag".equals(workOrderExtVo.getWorkRecordExtDefCode())){
                        //进行工大标签的数据整理
                        Long workRecordTagStartTime = System.currentTimeMillis();
                        log.info("workRecordTag 开始时间 {}", workRecordTagStartTime);
                        R<List<CustomerTagCategoryGroupResult>> r = customerTagClient.queryTagSelectListForUserSelect(SecurityUtil.getLoginUser().getCompanyId(),"3");
                        if (r.getCode() == AjaxResult.SUCCESS && CollectionUtils.isNotEmpty(r.getData())) {
                            //进行数据装填
                            List<CustomerTagCategoryGroupResult> data = r.getData();
                            //将数据进行批量处理
                            List<CustomerTagDefResult> def = new ArrayList<>();
                            data.forEach(
                                    item ->
                                            def.addAll(item.getTagList())
                            );
                            def.stream().map(
                                            tagDef -> new WorkOrderExtOptionDefVo()
                                                    .setOptionId(IdWorker.get32UUID())
                                                    .setOptionName(tagDef.getTagContent())
                                                    .setOptionValue(tagDef.getTagId())
                                                    .setTagColor(tagDef.getTagColor())
                                                    .setTagColorCode(tagDef.getTagColorCode())
                                                    .setCategoryId(tagDef.getCategoryId())
                                                    .setCategoryContent(tagDef.getCategoryContent())
                                                    .setIsSystemDefault(1)
                                                    .setPropertyTypeId(InputPropTypeEnum.Multiple_Select.getCode()))
                                    .forEach(workOrderExtVo.getWorkOrderExtOptionDefList()::add);
                        }
                        Long priorityLevelEnd = System.currentTimeMillis();
                        log.info("workRecordTag {} 用时 {}", priorityLevelEnd, priorityLevelEnd-workRecordTagStartTime);
                    }

                    extDefVoList.add(workOrderExtVo);
                });
        Long end = System.currentTimeMillis();
        log.info("方法开始时间 {} 用时 {}", end, end-start);
        return extDefVoList;
    }

    @Override
    @Transactional
    public AjaxResult generateDefaultWorkOrderExtProps(String companyId) {
        //先删除之前保存的旧【默认数据】，再存储新数据(由于其他的表用到的是code码，不用考虑用了主键，不能删除旧数据的问题)
        //由于实体中添加了
        List<CrmAgentWorkRecordExtDef> extDefList = this.list(new LambdaQueryWrapper<CrmAgentWorkRecordExtDef>()
                .eq(CrmAgentWorkRecordExtDef::getCompanyId, companyId)
                .eq(CrmAgentWorkRecordExtDef::getIsSystemDefault, 1));
        if(CollectionUtils.isNotEmpty(extDefList)){
            List<String> idList = extDefList.stream().map(CrmAgentWorkRecordExtDef::getWorkRecordExtDefId).collect(Collectors.toList());
            //先删除option表
            for (String eachExtId : idList) {
                Map<String, Object> deleteMap = new HashMap<>();
                deleteMap.put("work_record_ext_def_id",eachExtId);
                optionDefService.removeByMap(deleteMap);
            }
            //再删除ext表
//            this.removeBatchByIds(idList);
            //是否系统默认字段 0-否 1-是，生成默认扩展属性的时候，只提前删除旧的「默认」属性
            this.baseMapper.deleteBatchExtDefByIdList(companyId,idList,1);
        }
        //扩展属性归属类型 propType 1-客户 2-工单
        LambdaQueryWrapper<CrmExtDefTemplate> queryWrapper = new LambdaQueryWrapper<CrmExtDefTemplate>()
                .eq(CrmExtDefTemplate::getDataStatus, Constants.NORMAL)
                .eq(CrmExtDefTemplate::getPropType, 2);
        //从模板表中查询扩展属性数据
        List<CrmExtDefTemplate> extDefTemplateList = extDefTemplateService.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(extDefTemplateList)){
            List<String> idList = extDefTemplateList.stream().map(CrmExtDefTemplate::getExtDefId).collect(Collectors.toList());
            //查询默认选项卡
            LambdaQueryWrapper<CrmExtOptionDefTemplate> optionDefTemplateLambdaQueryWrapper = new LambdaQueryWrapper<CrmExtOptionDefTemplate>()
                    .eq(CrmExtOptionDefTemplate::getDataStatus, Constants.NORMAL)
                    .in(CrmExtOptionDefTemplate::getExtDefId, idList);
            List<CrmExtOptionDefTemplate> optionDefTemplateList = extOptionDefTemplateService.list(optionDefTemplateLambdaQueryWrapper);
            Map<String, List<CrmExtOptionDefTemplate>> optionDefVoMap=new HashMap<>();
            //将默认选项卡集合根据扩展属性Id进行分组
            if(CollectionUtils.isNotEmpty(optionDefTemplateList)){
                optionDefVoMap = optionDefTemplateList.stream().collect(Collectors.groupingBy(CrmExtOptionDefTemplate::getExtDefId));
            }
            List<CrmAgentWorkRecordExtDef> defaultExtDefList = new ArrayList<>();
            List<CrmAgentWorkRecordExtOptionDef> defaultOptionDefList=new ArrayList<>();
            for(CrmExtDefTemplate crmExtDefTemplate:extDefTemplateList){
                String defaultExtDefId = crmExtDefTemplate.getExtDefId();
                CrmAgentWorkRecordExtDef CrmAgentWorkRecordExtDef = new CrmAgentWorkRecordExtDef();
                BeanUtils.copyProperties(crmExtDefTemplate,CrmAgentWorkRecordExtDef);
                //因为两个实体部分字段不一致，所以部分属性需要单独赋值
                String newExtDefId = IdWorker.get32UUID();
                CrmAgentWorkRecordExtDef.setWorkRecordExtDefId(newExtDefId);
                CrmAgentWorkRecordExtDef.setCompanyId(companyId);
                CrmAgentWorkRecordExtDef.setWorkRecordExtDefCode(crmExtDefTemplate.getExtDefCode());
                CrmAgentWorkRecordExtDef.setWorkRecordExtDefName(crmExtDefTemplate.getExtDefName());
                CrmAgentWorkRecordExtDef.setWorkRecordExtDefOrder(crmExtDefTemplate.getExtDefOrder());
                CrmAgentWorkRecordExtDef.setCreateTime(new Date());
                CrmAgentWorkRecordExtDef.setModifyTime(new Date());
                CrmAgentWorkRecordExtDef.setDataStatus(Constants.NORMAL);
                defaultExtDefList.add(CrmAgentWorkRecordExtDef);
                //判断是否有选项卡
                List<CrmExtOptionDefTemplate> extOptionDefTemplateList = optionDefVoMap.get(defaultExtDefId);
                if(CollectionUtils.isNotEmpty(extOptionDefTemplateList)){
                    for(CrmExtOptionDefTemplate optionDefTemplate:extOptionDefTemplateList){
                        CrmAgentWorkRecordExtOptionDef CrmAgentWorkRecordExtOptionDef = new CrmAgentWorkRecordExtOptionDef();
                        BeanUtils.copyProperties(optionDefTemplate,CrmAgentWorkRecordExtOptionDef);
                        //因为两个实体部分字段不一致，所以部分属性需要单独赋值
                        String newExtOptionDefId = IdWorker.get32UUID();
                        CrmAgentWorkRecordExtOptionDef.setOptionId(newExtOptionDefId);
                        CrmAgentWorkRecordExtOptionDef.setWorkRecordExtDefId(newExtDefId);
                        CrmAgentWorkRecordExtDef.setCreateTime(new Date());
                        CrmAgentWorkRecordExtDef.setModifyTime(new Date());
                        defaultOptionDefList.add(CrmAgentWorkRecordExtOptionDef);
                    }
                }
            }
            //批量存储扩展属性和选项卡
            this.saveBatch(defaultExtDefList);
            optionDefService.saveBatch(defaultOptionDefList);
        }
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public Map<String, String> getExtDefMap() {
        Map<String, String> extDefMap = new HashMap<>();
        List<CrmAgentWorkRecordExtDef> extDefList = this.baseMapper.selectList(new LambdaQueryWrapper<CrmAgentWorkRecordExtDef>()
                .eq(CrmAgentWorkRecordExtDef::getCompanyId, SecurityUtil.getLoginUser().getCompanyId())
                .eq(CrmAgentWorkRecordExtDef::getLanguageCode, ServletUtils.getHeaderLanguage()));
        if (CollectionUtils.isNotEmpty(extDefList)) {
            // map [code, name]
            extDefMap.putAll(extDefList.stream().collect(Collectors.toMap(CrmAgentWorkRecordExtDef::getWorkRecordExtDefCode, CrmAgentWorkRecordExtDef::getWorkRecordExtDefName)));
        }
        return extDefMap;
    }

    @Override
    public List<WorkOrderExtVo> queryContactDetailByUserId(String userId, String redisKey) {
        List<WorkOrderExtVo> cacheList = RedisCacheUtil.getCacheList(redisKey + userId);
        // 查询缓存中是否存在，
        if(CollectionUtils.isNotEmpty(cacheList)){
            // 表头国际化
            Map<String, String> extDefMap = getExtDefMap();
            if (CollectionUtils.isNotEmpty(extDefMap)) {
                cacheList
                        .forEach(vo -> {
                            if (StringUtil.isNotBlank(extDefMap.get(vo.getWorkRecordExtDefCode()))) {
                                vo.setWorkRecordExtDefName(extDefMap.get(vo.getWorkRecordExtDefCode()));
                            }
                        });
            }
            return cacheList;
        }
        return new ArrayList<>();
    }

    /**
     * 将json字符串中的key名称中包含下划线的字段，转成驼峰命名格式
     *
     * @param str String格式的JSON串
     * @return 转换后的对象（可能是JSONObject或JSONArray）
     */
    private static Object convertUnderlineToCamelCase(String str) {
        Object obj = JSON.parse(str);
        convert(obj);
        return obj;
    }
    private static void convert(Object obj) {
        if (obj instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) obj;
            for (Object json : jsonArray) {
                convert(json);
            }
        } else if (obj instanceof JSONObject) {
            JSONObject json = (JSONObject) obj;
            Set<String> keySet = json.keySet();
            String[] keyArray = keySet.toArray(new String[keySet.size()]);
            for (String key : keyArray) {
                Object value = json.get(key);
                String[] keyStrs = key.split("_");
                if (keyStrs.length > 1) {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < keyStrs.length; i++) {
                        String keyStr = keyStrs[i];
                        if (!keyStr.isEmpty()) {
                            if (i == 0) {
                                sb.append(keyStr);
                            } else {
                                int c = keyStr.charAt(0);
                                if (c >= 97 && c <= 122) {
                                    int v = c - 32;
                                    sb.append((char) v);
                                    if (keyStr.length() > 1) {
                                        sb.append(keyStr.substring(1));
                                    }
                                } else {
                                    sb.append(keyStr);
                                }
                            }
                        }
                    }
                    json.remove(key);
                    json.put(sb.toString(), value);
                }
                convert(value);
            }
        }
    }


    @Override
    public AjaxResult<List<WorkOrderExtVo>> querySpecialTicketExtDefList(Integer queryType, List<String> extCodeList, List<String> propertyTypeCodeList) {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        QueryWrapper<String> wrapper = new QueryWrapper<String>();
        //is_system_default：是否系统默认字段（是的话不可修改，只能调整顺序） 0-否 1-是
//        wrapper.and(subWrapper -> subWrapper.and(w -> w.eq("t1.is_system_default", 1)
//                        .in(CollectionUtils.isNotEmpty(extCodeList),
//                                "t1.work_record_ext_def_code", extCodeList))
//                .or()
//                .eq("t1.is_system_default", 0)
//
//        );
        wrapper.and(subWrapper -> {
            subWrapper.and(w -> w.eq("t1.is_system_default", 1)
                            .in(CollectionUtils.isNotEmpty(extCodeList),
                                    "t1.work_record_ext_def_code", extCodeList))
                    .or(w -> {
                        w.eq("t1.is_system_default", 0);
                        if (CollectionUtils.isNotEmpty(propertyTypeCodeList)) {
                            w.in("t1.property_type_id", propertyTypeCodeList);
                        }

                    });
        });

        wrapper.eq("t1.data_status", 1);
        wrapper.eq("t1.company_id", companyId);
        // 国际化查询
        wrapper.eq("t1.language_code", ServletUtils.getHeaderLanguage());
        wrapper.orderByAsc("t1.work_record_ext_def_order");
        List<WorkOrderExtOptionDefVo> list = this.baseMapper.queryWorkOrderExtList(wrapper);

        // ext-option是一对多的关系（仅在多选时，其他ext没有option）
        if (CollectionUtils.isNotEmpty(list)) {
            List<WorkOrderExtVo> extVoList = queryOptionValue(list);
            return AjaxResult.ok(extVoList);
        }

        return AjaxResult.ok(Arrays.asList(), MessageUtils.get("operate.success"));
    }
}




