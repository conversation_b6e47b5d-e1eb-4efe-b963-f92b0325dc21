package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.call.domain.vo.ChannelWorkOrderVo;
import com.goclouds.crm.platform.call.domain.vo.RobotWorkOrderVo;
import com.goclouds.crm.platform.call.domain.vo.WorkOrderRecordResponseVO;
import com.goclouds.crm.platform.call.domain.vo.workbench.WorkOrderRecordFinalEsResult;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
 * @author: sunlinan
 * @description:
 * @date: 2024-11-20 10:49
 **/
public interface WorkRecordEsService {
    AjaxResult queryChannelWorkOrder(ChannelWorkOrderVo channelWorkOrderVo) throws Exception;

    AjaxResult queryWorkOrderTypeDetail(ChannelWorkOrderVo channelWorkOrderVo) throws Exception;

    AjaxResult queryWorkOrderEffect() throws Exception;

    IPage<WorkOrderRecordFinalEsResult> queryPendingWorkOrder(IPage<Object> pageParam) throws Exception;

    IPage<WorkOrderRecordFinalEsResult> queryWorkOrderToBeAllocated(IPage<Object> pageParam) throws Exception;

    AjaxResult queryWorkOrderOverstock() throws Exception;

    AjaxResult queryRobotWorkOrder(RobotWorkOrderVo robotWorkOrderVo) throws Exception;
}
