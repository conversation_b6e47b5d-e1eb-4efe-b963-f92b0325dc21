package com.goclouds.crm.platform.call.service;

import com.goclouds.crm.platform.call.domain.AmazonOrder.AmazonOrderRequest;
import com.goclouds.crm.platform.call.domain.AmazonOrder.AmazonOrderResponse;
import com.goclouds.crm.platform.call.domain.AmazonOrder.AmazonTokenRequest;
import com.goclouds.crm.platform.call.domain.AmazonOrder.AmazonTokenResponse;
import com.goclouds.crm.platform.openfeignClient.domain.AmazonOrder.AmazonOrderRpcResponse;

/**
 * <AUTHOR> pengliang.sun
 * @description :
 */
public interface AmazonOrderService {
    AmazonOrderRpcResponse amazonOrderList(AmazonOrderRequest request);

    AmazonTokenResponse amazonToken(AmazonTokenRequest request);
}
