package com.goclouds.crm.platform.call.strategy;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> pengliang.sun
 * @description : 策略实现类
 */
@Component
public class AddTicketSmartFillFactory implements ApplicationContextAware {

    private Map<AigcTicketSmartFillEnum,AigcTicketSmartFillStrategy> aigcTicketSmartFillStrategyMap
            =new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext)
            throws BeansException {
        Map<String, AigcTicketSmartFillStrategy> tmepMap =
                applicationContext.getBeansOfType(AigcTicketSmartFillStrategy.class);
        tmepMap.values().forEach(strategyService ->
                aigcTicketSmartFillStrategyMap.put(strategyService.aigcTicketSmartFillEnum(), strategyService));
    }

    public AigcTicketSmartFillStrategy getAigcTicketSmartFillStrategy(AigcTicketSmartFillEnum aigcTicketSmartFillEnum) {
        return  aigcTicketSmartFillStrategyMap.get(aigcTicketSmartFillEnum);
    }

}
