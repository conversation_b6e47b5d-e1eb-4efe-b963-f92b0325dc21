package com.goclouds.crm.platform.call.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilter;
import com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilterCondition;
import com.goclouds.crm.platform.call.domain.vo.CreateOrModifyFilterVO;
import com.goclouds.crm.platform.call.domain.vo.WorkRecordFilterResponseVO;
import com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordFilterMapper;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordFilterConditionService;
import com.goclouds.crm.platform.call.service.CrmAgentWorkRecordFilterService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.exception.ServiceException;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.utils.MessageUtils;
import com.goclouds.crm.platform.utils.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【crm_agent_work_record_filter(工单筛选器;)】的数据库操作Service实现
* @createDate 2023-09-19 15:30:11
*/
@Service
public class CrmAgentWorkRecordFilterServiceImpl extends ServiceImpl<CrmAgentWorkRecordFilterMapper, CrmAgentWorkRecordFilter>
    implements CrmAgentWorkRecordFilterService {

    @Autowired
    private CrmAgentWorkRecordFilterConditionService crmAgentWorkRecordFilterConditionService;

    @Override
    public List<WorkRecordFilterResponseVO> queryWorkRecordFilter() {
        // 获取到当前登录用户id
        String userId = SecurityUtil.getUserId();
        return this.baseMapper.queryWorkRecordFilter(userId);
    }

    @Override
    public AjaxResult<Object> createFilter(CreateOrModifyFilterVO createOrModifyFilterVO) {
        CrmAgentWorkRecordFilter filter = new CrmAgentWorkRecordFilter();
        BeanUtils.copyProperties(createOrModifyFilterVO, filter);
        filter.setUserId(SecurityUtil.getUserId())
                .setWorkRecordFilterId(IdWorker.get32UUID())
                .setDefaultFilter(0)
                .setDataStatus(1)
                .setCreator(SecurityUtil.getUserId())
                .setCreateTime(new Date())
                .setModifier(SecurityUtil.getUserId())
                .setModifyTime(new Date());
        this.save(filter);

        List<CrmAgentWorkRecordFilterCondition> conditionList = createOrModifyFilterVO.getConditionList().stream()
                .filter(Objects::nonNull)
                .map(vo -> {
                    CrmAgentWorkRecordFilterCondition target = new CrmAgentWorkRecordFilterCondition();
                    BeanUtils.copyProperties(vo, target);
                    target.setWorkRecordFilterId(filter.getWorkRecordFilterId())
                            .setDataStatus(1)
                            .setCreator(SecurityUtil.getUserId())
                            .setCreateTime(new Date())
                            .setModifier(SecurityUtil.getUserId())
                            .setModifyTime(new Date());
                    return target;
                }).collect(Collectors.toList());
        // 如果属性list为空 不能保存
        if (CollectionUtils.isEmpty(conditionList)) {
            throw new ServiceException(MessageUtils.get(""));
        }
        crmAgentWorkRecordFilterConditionService.saveBatch(conditionList);
        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }

    @Override
    public AjaxResult<Object> modifyFilter(CreateOrModifyFilterVO createOrModifyFilterVO) {
        // 查询当前筛选器是否存在
        CrmAgentWorkRecordFilter filter = this.baseMapper.selectById(createOrModifyFilterVO.getWorkRecordFilterId());
        if(StringUtil.isNull(filter)){
            return AjaxResult.failure("未查询到筛选器");
        }

        BeanUtils.copyProperties(createOrModifyFilterVO, filter);
        filter.setModifier(SecurityUtil.getUserId())
                .setModifyTime(new Date());
        // 更新FilterName
        this.updateById(filter);
        // 先删除对应的属性list  再添加属性list
        crmAgentWorkRecordFilterConditionService.remove(new LambdaQueryWrapper<CrmAgentWorkRecordFilterCondition>().eq(CrmAgentWorkRecordFilterCondition::getWorkRecordFilterId, createOrModifyFilterVO.getWorkRecordFilterId()));
        // 进行添加
        List<CrmAgentWorkRecordFilterCondition> conditionList = createOrModifyFilterVO.getConditionList().stream()
                .filter(Objects::nonNull)
                .map(vo -> {
                    CrmAgentWorkRecordFilterCondition target = new CrmAgentWorkRecordFilterCondition();
                    BeanUtils.copyProperties(vo, target);
                    target.setWorkRecordFilterId(filter.getWorkRecordFilterId())
                            .setDataStatus(1)
                            .setCreator(SecurityUtil.getUserId())
                            .setCreateTime(new Date())
                            .setModifier(SecurityUtil.getUserId())
                            .setModifyTime(new Date());
                    return target;
                }).collect(Collectors.toList());
        // 如果属性list为空 不能保存
        if (CollectionUtils.isEmpty(conditionList)) {
            throw new ServiceException(MessageUtils.get(""));
        }
        crmAgentWorkRecordFilterConditionService.saveBatch(conditionList);

        return AjaxResult.ok(null, MessageUtils.get("operate.success"));
    }
}




