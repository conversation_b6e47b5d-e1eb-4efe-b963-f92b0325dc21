package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.call.domain.smartFill.*;
import com.goclouds.crm.platform.common.domain.AjaxResult;

/**
 * @author: sunlinan
 * @description:
 * @date: 2025-04-14 14:04
 **/
public interface TicketSmartFillService {
    IPage<TicketSmartFillVo> queryTicketSmartFillList(IPage<Object> pageParam);

    AjaxResult queryTicketSmartFillDetail(String smartFillId);

    AjaxResult createTicketSmartFill(AddTicketSmartFillParam addTicketSmartFillParam);

    AjaxResult updateTicketSmartFill(UpdateTicketSmartFillParam updateTicketSmartFillParam);

    AjaxResult deleteTicketSmartFill(String smartFillId);

    AjaxResult<AigcTicketSmartFillResponse> aigcTicketSmartFill(AigcTicketSmartFillRequest aigcTicketSmartFillRequest);

    AjaxResult addAigcTicketSmartFill(AddAigcTicketSmartFillRequest addAigcTicketSmartFillRequest);

    AjaxResult<TicketSmartFillLanguageResponse> getTicketSmartFillLanguage();

}
