package com.goclouds.crm.platform.call.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.call.domain.CrmAssessmentCategory;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentCategoryCascadeVo;
import com.goclouds.crm.platform.call.domain.vo.CrmAssessmentCategoryVo;

import java.util.List;

/**
 * 评估表分类Service接口
 */
public interface CrmAssessmentCategoryService extends IService<CrmAssessmentCategory> {

    /**
     * 分页查询分类
     *
     * @param pageParam 分页参数
     * @param category 查询条件
     * @return 分页结果
     */
    IPage<CrmAssessmentCategory> queryCategoryPages(IPage<Object> pageParam, CrmAssessmentCategory category);

    /**
     * 根据版本ID查询分类列表
     *
     * @param versionId 版本ID
     * @return 分类列表
     */
    List<CrmAssessmentCategory> listCategoriesByVersionId(String versionId);

    /**
     * 根据父级ID查询子分类
     *
     * @param parentId 父级ID
     * @return 子分类列表
     */
    List<CrmAssessmentCategory> listCategoriesByParentId(String parentId);

    /**
     * 查询顶级分类列表
     *
     * @param versionId 版本ID
     * @return 顶级分类列表
     */
    List<CrmAssessmentCategory> listTopCategories(String versionId);

    /**
     * 保存分类
     *
     * @param categoryVo 分类VO
     * @return 是否成功
     */
    boolean saveCategory(CrmAssessmentCategoryVo categoryVo);

    /**
     * 更新分类
     *
     * @param categoryVo 分类VO
     * @return 是否成功
     */
    boolean updateCategory(CrmAssessmentCategoryVo categoryVo);

    /**
     * 删除分类
     *
     * @param categoryId 分类ID
     * @return 是否成功
     */
    boolean deleteCategory(String categoryId);

    /**
     * 获取分类详情
     *
     * @param categoryId 分类ID
     * @return 分类详情
     */
    CrmAssessmentCategory getCategoryDetail(String categoryId);

    /**
     * 获取分类树结构
     *
     * @param versionId 版本ID
     * @return 分类树结构
     */
    List<CrmAssessmentCategoryVo> getCategoryTree(String versionId);

    /**
     * 获取分类树结构
     *
     * @param
     * @return 分类树结构
     */
    List<CrmAssessmentCategoryVo> getCategoryTreeByAssessmentId(CrmAssessmentCategory category);

    void removeDraftCategoriesByAssessmentId(String assessmentId);
}