spring:
  application:
    name: crm-service-call
  cloud:
    nacos:
      discovery:
        #server-addr: 10.100.0.246:8848
        server-addr: ${serveraddr:192.168.0.58:30101}
        namespace: ${namespace:41463689-cadb-4df6-88cd-92bf60e8afbb}
#        namespace: 8f98cc46-2262-4796-b2c3-674d5feac2f3
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yaml
        namespace: ${spring.cloud.nacos.discovery.namespace}
        username: nacos
        password: nacos
        # 共享配置文件数组
        shared-configs:
          - data_id: spring-common-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true

