<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmTicketAssessmentRecordRuleItemMapper">

    <resultMap id="CrmTicketAssessmentRecordRuleItemResultMap"
               type="com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecordRuleItem">
        <id column="assessment_record_rule_item_id" property="assessmentRecordRuleItemId" jdbcType="VARCHAR"/>
        <id column="assessment_record_id" property="assessmentRecordId" jdbcType="VARCHAR"/>
        <result column="assessment_rule_id" property="assessmentRuleId" jdbcType="VARCHAR"/>
        <result column="assessment_status" property="assessmentStatus" jdbcType="INTEGER"/>
        <result column="assessment_type" property="assessmentType" jdbcType="INTEGER"/>
        <result column="ai_score" property="aiScore" jdbcType="DECIMAL"/>
        <result column="manual_score" property="manualScore" jdbcType="DECIMAL"/>
        <result column="manual_option_id" property="manualOptionId" jdbcType="VARCHAR"/>
        <result column="manual_option_name" property="manualOptionName" jdbcType="VARCHAR"/>
        <result column="assessment_remark" property="assessmentRemark" jdbcType="VARCHAR"/>
        <result column="rule_sort" property="ruleSort" jdbcType="INTEGER"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="data_status" property="dataStatus" jdbcType="INTEGER"/>
    </resultMap>

    <update id="updateInfoByAssessmentRecordRuleItemId">
        update crm_ticket_assessment_record_rule_item
        set assessment_status = #{assessmentStatus}, modify_time = now()
        <if test="aiScore != null">
            , ai_score = #{aiScore}
        </if>
        where assessment_record_rule_item_id = #{recordRuleItemId}
    </update>

    <!-- 获取智能质检评估记录列表 -->
    <select id="getAssessmentRecordRuleItemList" resultMap="CrmTicketAssessmentRecordRuleItemResultMap">
        select *
        from crm_ticket_assessment_record_rule_item
        where data_status = 1
    </select>

    <!-- 获取评估记录所有的记录规则列表 -->
    <select id="getAllRecordRule"
            resultType="com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecordRuleItem">
        select *
        from crm_ticket_assessment_record_rule_item ctarri
        where ctarri.assessment_record_id = #{recordId}
    </select>

<!--    &lt;!&ndash; 获取评估记录所有的AI打分的规则 &ndash;&gt;-->
<!--    <select id="selectAllAIRule" resultType="com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecordRuleItem">-->
<!--        select ctarri.*-->
<!--        from crm_ticket_assessment_record_rule_item ctarri-->
<!--                 inner join crm_ticket_assessment_rule ctar-->
<!--                            on ctar.rule_id = ctarri.assessment_rule_id and assessment_type = 1-->
<!--        where ctarri.assessment_record_id = #{recordId}-->
<!--    </select>-->

    <!-- 获取评估规则数据 -->
    <resultMap id="assessmentRuleResultMap" type="com.goclouds.crm.platform.call.domain.vo.AssessmentRuleVO">
        <!-- 主表字段映射 -->
        <id property="assessmentRecordRuleItemId" column="assessment_record_rule_item_id"/>
        <id property="assessmentRecordId" column="assessment_record_id"/>
        <result property="ruleId" column="assessment_rule_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="assessmentStatus" column="assessment_status"/>
        <result property="description" column="description"/>
        <result property="reference" column="reference"/>
        <result property="aiAssessmentRule" column="aiAssessmentRule"/>
        <result property="assessmentType" column="assessmentType"/>
        <result property="totalScore" column="totalScore"/>
        <result property="aiScore" column="ai_score"/>
        <result property="manualScore" column="manual_score"/>
        <result property="manualOptionId" column="manual_option_id"/>
        <result property="manualOptionName" column="manual_option_name"/>
        <result property="assessmentRemark" column="assessment_remark"/>
        <result property="ruleSort" column="rule_sort"/>

        <!-- 关联质检点集合 -->
        <collection property="qualityCheckpoints" ofType="com.goclouds.crm.platform.call.domain.vo.AssessmentRuleQualityCheckpointVO">
            <id property="pointId" column="point_id"/>
            <result property="ruleId" column="rule_id"/>
            <result property="pointDesc" column="point_desc"/>
            <result property="pointScore" column="pointScore"/>
            <result property="pointSort" column="pointSort"/>
        </collection>
    </resultMap>
    <select id="selectAssessmentRuleWithCheckpoints" resultMap="assessmentRuleResultMap">
        select
            ctarri.assessment_record_rule_item_id,
            ctarri.assessment_record_id,
            ctarri.assessment_rule_id,
            ctar.rule_name,
            ctarri.assessment_status,
            ctar.description,
            ctar.reference,
            ctar.score_rule as aiAssessmentRule,
            ctar.score_type as assessmentType,
            ctar.score as totalScore,
            ctarri.ai_score,
            ctarri.manual_score,
            ctarri.manual_option_id,
            ctarri.manual_option_name,
            ctarri.assessment_remark,
            ctarri.rule_sort,
            ctarp.point_id,
            ctarp.rule_id,
            ctarp.point_desc,
            ctarp.score as pointScore,
            ctarp.point_sort as pointSort
        from crm_ticket_assessment_record_rule_item ctarri
                 inner join crm_ticket_assessment_rule ctar on ctar.rule_id = ctarri.assessment_rule_id
                 left join crm_ticket_assessment_rule_point ctarp on ctarp.rule_id = ctar.rule_id
        where ctarri.assessment_record_rule_item_id = #{recordRuleItemId}
        order by ctarp.point_sort asc
    </select>


</mapper>
