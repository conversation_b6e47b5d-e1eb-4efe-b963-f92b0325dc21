<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordPriorityLevelDefMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordPriorityLevelDef">
            <id property="priorityLevelId" column="priority_level_id" jdbcType="VARCHAR"/>
            <result property="priorityLevelCode" column="priority_level_code" jdbcType="VARCHAR"/>
            <result property="priorityLevelName" column="priority_level_name" jdbcType="VARCHAR"/>
            <result property="order" column="order" jdbcType="INTEGER"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        priority_level_id,priority_level_code,priority_level_name,
        order,data_status,creator,
        create_time,modifier,modify_time
    </sql>
</mapper>
