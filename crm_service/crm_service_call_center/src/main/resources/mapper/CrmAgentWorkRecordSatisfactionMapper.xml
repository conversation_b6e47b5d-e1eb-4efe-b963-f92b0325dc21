<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordSatisfactionMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordSatisfaction">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="workRecordId" column="work_record_id" jdbcType="VARCHAR"/>
            <result property="rating" column="rating" jdbcType="DECIMAL"/>
            <result property="commentContent" column="comment_content" jdbcType="VARCHAR"/>
            <result property="commentUser" column="comment_user" jdbcType="VARCHAR"/>
            <result property="commentTime" column="comment_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,work_record_id,rating,
        comment_content,comment_user,comment_time,
        data_status
    </sql>
</mapper>
