<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmCallPushMessageConfigMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmCallPushMessageConfig">
            <result property="configId" column="config_id" jdbcType="VARCHAR"/>
            <result property="configUrl" column="config_url" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="verifySecret" column="verify_secret" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        config_id,config_url,company_id,
        verify_secret,modifier,modify_time,
        creator,create_time,data_status
    </sql>
</mapper>
