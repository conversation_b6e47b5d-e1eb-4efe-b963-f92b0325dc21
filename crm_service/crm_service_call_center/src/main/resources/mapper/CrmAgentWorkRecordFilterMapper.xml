<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordFilterMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilter">
            <id property="workRecordFilterId" column="work_record_filter_id" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="workRecordFilterName" column="work_record_filter_name" jdbcType="VARCHAR"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="defaultFilter" column="default_filter" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_filter_id,user_id,work_record_filter_name,
        data_status,default_filter,creator,
        create_time,modifier,modify_time
    </sql>
</mapper>
