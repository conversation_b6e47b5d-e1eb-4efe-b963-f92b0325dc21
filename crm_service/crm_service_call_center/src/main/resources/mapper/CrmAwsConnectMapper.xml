<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAwsConnectMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAwsConnect">
            <id property="connectId" column="connect_id" jdbcType="VARCHAR"/>
            <result property="awsUserId" column="aws_user_id" jdbcType="VARCHAR"/>
            <result property="connectAlias" column="connect_alias" jdbcType="VARCHAR"/>
            <result property="connectUrl" column="connect_url" jdbcType="VARCHAR"/>
            <result property="bound" column="bound" jdbcType="TINYINT"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="connectStatus" column="connect_status" jdbcType="TINYINT"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        connect_id,aws_user_id,connect_alias,
        connect_url,bound,modifier,
        modify_time,creator,create_time,
        connect_status,data_status
    </sql>
</mapper>
