<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordWaitExecuteMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordWaitExecute">
            <id property="waitExecuteId" column="wait_execute_id" jdbcType="VARCHAR"/>
            <result property="workRecordId" column="work_record_id" jdbcType="VARCHAR"/>
            <result property="waitExecuteEvent" column="wait_execute_event" jdbcType="VARCHAR"/>
            <result property="waitExecuteStatus" column="wait_execute_status" jdbcType="INTEGER"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        wait_execute_id,work_record_id,wait_execute_event,
        wait_execute_status,modifier,modify_time,
        creator,create_time,data_status
    </sql>
</mapper>
