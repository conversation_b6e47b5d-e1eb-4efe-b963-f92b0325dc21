<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmCallWorkTimeHourMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmCallWorkTimeHour">
            <id property="hourId" column="hour_id" jdbcType="VARCHAR"/>
            <result property="workTimeId" column="work_time_id" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIME"/>
            <result property="endTime" column="end_time" jdbcType="TIME"/>
            <result property="weekType" column="week_type" jdbcType="INTEGER"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        hour_id,work_time_id,start_time,
        end_time,week_type,modifier,
        modify_time,creator,create_time,
        data_status
    </sql>
</mapper>
