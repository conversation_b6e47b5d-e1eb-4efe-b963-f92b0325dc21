<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordDetailMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordDetail">
            <id property="workRecordDetailId" column="work_record_detail_id" jdbcType="VARCHAR"/>
            <result property="workRecordId" column="work_record_id" jdbcType="VARCHAR"/>
            <result property="contactId" column="contact_id" jdbcType="VARCHAR"/>
            <result property="initiationTime" column="initiation_time" jdbcType="TIMESTAMP"/>
            <result property="disconnectTime" column="disconnect_time" jdbcType="TIMESTAMP"/>
            <result property="timeOfDuration" column="time_of_duration" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_detail_id,work_record_id,contact_id,
        initiation_time,disconnect_time,time_of_duration,
        creator,create_time,data_status,type
    </sql>
</mapper>
