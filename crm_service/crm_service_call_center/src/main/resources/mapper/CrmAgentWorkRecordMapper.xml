<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecord">
            <id property="workRecordId" column="work_record_id" jdbcType="VARCHAR"/>
            <result property="wordRecordCode" column="word_record_code" jdbcType="VARCHAR"/>
            <result property="oldWorkRecordCode" column="old_work_record_code" jdbcType="VARCHAR"/>
            <result property="channelTypeId" column="channel_type_id" jdbcType="VARCHAR"/>
            <result property="channelConfigId" column="channel_config_id" jdbcType="VARCHAR"/>
            <result property="channelConfigName" column="channel_config_name" jdbcType="VARCHAR"/>
            <result property="workRecordTheme" column="work_record_theme" jdbcType="VARCHAR"/>
            <result property="contentSummary" column="content_summary" jdbcType="VARCHAR"/>
            <result property="reminderStatus" column="reminder_status" jdbcType="INTEGER"/>
            <result property="channelTypeName" column="channel_type_name" jdbcType="VARCHAR"/>
            <result property="workRecordTypeCode" column="work_record_type_code" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="customerTelephone" column="customer_telephone" jdbcType="VARCHAR"/>
            <result property="customerMood" column="customer_mood" jdbcType="INTEGER"/>
            <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
            <result property="agentName" column="agent_name" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="DECIMAL"/>
            <result property="acceptType" column="accept_type" jdbcType="INTEGER"/>
            <result property="createType" column="create_type" jdbcType="INTEGER"/>
            <result property="workRecordTypeId" column="work_record_type_id" jdbcType="VARCHAR"/>
            <result property="workRecordTypeName" column="work_record_type_name" jdbcType="VARCHAR"/>
            <result property="priorityLevelId" column="priority_level_id" jdbcType="VARCHAR"/>
            <result property="priorityLevelName" column="priority_level_name" jdbcType="VARCHAR"/>
            <result property="initiationTime" column="initiation_time" jdbcType="TIMESTAMP"/>
            <result property="resolveTime" column="resolve_time" jdbcType="TIMESTAMP"/>
            <result property="shouldResolveTime" column="should_resolve_time" jdbcType="TIMESTAMP"/>
            <result property="firstResponseTime" column="first_response_time" jdbcType="TIMESTAMP"/>
            <result property="finalResponseTime" column="final_response_time" jdbcType="TIMESTAMP"/>
            <result property="shouldResponseTime" column="should_response_time" jdbcType="TIMESTAMP"/>
            <result property="terminateTime" column="terminate_time" jdbcType="TIMESTAMP"/>
            <result property="transferTime" column="transfer_time" jdbcType="TIMESTAMP"/>
            <result property="timeOfDuration" column="time_of_duration" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="DECIMAL"/>
            <result property="lastMessageDeliveryTime" column="last_message_delivery_time" jdbcType="TIMESTAMP"/>
            <result property="lastMessageReplyTime" column="last_message_reply_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_id,word_record_code,old_work_record_code,
        channel_type_id,channel_type_name,customer_id,
        customer_name,customer_telephone,agent_id,
        agent_name,status,accept_type,
        create_type,work_record_type_id,priority_level_id,
        priority_level_name,initiation_time,resolve_time,
        should_resolve_time,first_response_time,final_response_time,
        should_response_time,terminate_time,transfer_time,
        time_of_duration,remark,modifier,
        modify_time,creator,create_time,
        data_status,last_message_delivery_time,last_message_reply_time
    </sql>

</mapper>
