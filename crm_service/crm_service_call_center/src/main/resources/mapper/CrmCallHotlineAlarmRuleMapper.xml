<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmCallHotlineAlarmRuleMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmCallHotlineAlarmRule">
            <id property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
            <result property="ruleContent" column="rule_content" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        rule_id,rule_content,company_id,
        user_id,creator,create_time,
        modifier,modify_time,data_status
    </sql>
</mapper>
