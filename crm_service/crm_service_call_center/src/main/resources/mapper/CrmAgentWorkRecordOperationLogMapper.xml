<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordOperationLogMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordOperationLog">
            <id property="operationLogId" column="operation_log_id" jdbcType="VARCHAR"/>
            <result property="operationLogType" column="operation_log_type" jdbcType="INTEGER"/>
            <result property="operationLogReason" column="operation_log_reason" jdbcType="VARCHAR"/>
            <result property="operationLogDescribe" column="operation_log_describe" jdbcType="VARCHAR"/>
            <result property="operatorName" column="operator_name" jdbcType="VARCHAR"/>
            <result property="workRecordId" column="work_record_id" jdbcType="VARCHAR"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        operation_log_id,operation_log_type,operation_log_reason,
        operation_log_describe,work_record_id,data_status,
        creator,create_time,modifier,
        modify_time
    </sql>
</mapper>
