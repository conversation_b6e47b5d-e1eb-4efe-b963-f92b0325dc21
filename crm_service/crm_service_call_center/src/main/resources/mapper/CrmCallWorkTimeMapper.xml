<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmCallWorkTimeMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmCallWorkTime">
            <id property="workTimeId" column="work_time_id" jdbcType="VARCHAR"/>
            <result property="workTimeName" column="work_time_name" jdbcType="VARCHAR"/>
            <result property="timezoneName" column="timezone_name" jdbcType="VARCHAR"/>
            <result property="timezoneValue" column="timezone_value" jdbcType="VARCHAR"/>
            <result property="workTimeDescription" column="work_time_description" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_time_id,work_time_name,timezone_name,
        timezone_value,work_time_description,modifier,
        modify_time,creator,create_time,
        data_status
    </sql>
</mapper>
