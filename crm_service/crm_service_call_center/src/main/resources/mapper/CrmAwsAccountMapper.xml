<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAwsAccountMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAwsAccount">
            <id property="awsUserId" column="aws_user_id" jdbcType="VARCHAR"/>
            <result property="awsUserName" column="aws_user_name" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="accessKey" column="access_key" jdbcType="VARCHAR"/>
            <result property="secretAccessKey" column="secret_access_key" jdbcType="VARCHAR"/>
            <result property="regions" column="regions" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        aws_user_id,aws_user_name,company_id,access_key,
        secret_access_key,regions,creator,create_time,modifier,modify_time,data_status
    </sql>
</mapper>
