<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordContentMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordContent">
            <id property="workRecordContentId" column="work_record_content_id" jdbcType="VARCHAR"/>
            <result property="workRecordId" column="work_record_id" jdbcType="VARCHAR"/>
            <result property="workRecordDetailId" column="work_record_detail_id" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="contentType" column="content_type" jdbcType="INTEGER"/>
            <result property="replyType" column="reply_type" jdbcType="INTEGER"/>
            <result property="workFiled1" column="work_filed1" jdbcType="VARCHAR"/>
            <result property="workFiled2" column="work_filed2" jdbcType="VARCHAR"/>
            <result property="workFiled3" column="work_filed3" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_content_id,work_record_id,work_record_detail_id,
        work_filed1,work_filed2,
        work_filed3,creator,create_time,
        data_status
    </sql>
</mapper>
