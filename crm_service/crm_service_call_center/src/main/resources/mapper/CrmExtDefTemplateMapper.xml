<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmExtDefTemplateMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmExtDefTemplate">
            <id property="extDefId" column="ext_def_id" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="propertyTypeId" column="property_type_id" jdbcType="VARCHAR"/>
            <result property="extDefName" column="ext_def_name" jdbcType="VARCHAR"/>
            <result property="extDefCode" column="ext_def_code" jdbcType="VARCHAR"/>
            <result property="isSystemDefault" column="is_system_default" jdbcType="INTEGER"/>
            <result property="isRequired" column="is_required" jdbcType="INTEGER"/>
            <result property="extDefOrder" column="ext_def_order" jdbcType="INTEGER"/>
            <result property="prompt" column="prompt" jdbcType="VARCHAR"/>
            <result property="optionsType" column="options_type" jdbcType="INTEGER"/>
            <result property="interfaceUrl" column="interface_url" jdbcType="VARCHAR"/>
            <result property="languageCode" column="language_code" jdbcType="VARCHAR"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="propType" column="prop_type" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ext_def_id,company_id,property_type_id,
        ext_def_name,ext_def_code,is_system_default,
        is_required,ext_def_order,prompt,
        options_type,interface_url,language_code,
        data_status,creator,create_time,
        modifier,modify_time,prop_type
    </sql>
</mapper>
