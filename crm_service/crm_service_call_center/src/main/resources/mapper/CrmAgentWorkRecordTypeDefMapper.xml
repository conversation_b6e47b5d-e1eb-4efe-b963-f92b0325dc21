<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordTypeDefMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordTypeDef">
            <id property="workRecordTypeId" column="work_record_type_id" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="workRecordTypeValue" column="work_record_type_value" jdbcType="VARCHAR"/>
            <result property="workRecordTypeName" column="work_record_type_name" jdbcType="VARCHAR"/>
            <result property="order" column="order" jdbcType="INTEGER"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_type_id,company_id,work_record_type_value,
        work_record_type_name,`order`,data_status,
        creator,create_time,modifier,
        modify_time
    </sql>
</mapper>
