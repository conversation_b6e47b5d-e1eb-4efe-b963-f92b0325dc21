<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmSalRecordChannelMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmSlaRecordChannel">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="workRecordSlaId" column="work_record_sla_id" jdbcType="VARCHAR"/>
            <result property="typeValue" column="type_value" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="BIGINT"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,work_record_sla_id,type_value,
        type,company_id,modifier,
        modify_time,creator,create_time,
        data_status
    </sql>
</mapper>
