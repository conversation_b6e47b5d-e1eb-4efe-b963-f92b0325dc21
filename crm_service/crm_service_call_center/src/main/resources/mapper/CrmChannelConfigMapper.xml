<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmChannelConfigMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmChannelConfig">
            <id property="channelId" column="channel_id" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="channelType" column="channel_type" jdbcType="TINYINT"/>
            <result property="enableStatus" column="enable_status" jdbcType="TINYINT"/>
            <result property="connectInstanceId" column="connect_instance_id" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
            <result property="isOpenConnect" column="is_open_connect" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        channel_id,name,channel_type,
        enable_status,connect_instance_id,modifier,
        modify_time,creator,create_time,
        data_status,is_open_connect
    </sql>
    <select id="getChannelMappingRules"
            resultType="com.goclouds.crm.platform.call.domain.vo.ChannelConfigMappingDTO">
        select ci.code ,ci.name ,ci.mapping_rule  from crm_channel_config c
           inner join crm_channel_info_config_inst ci on c.channel_id = ci.channel_id
           inner join crm_aws_connect ac on ac.connect_id = c.connect_instance_id
        where c.data_status = 1
          and c.enable_status = 1
          and ci.data_status = 1
          and ci.field_type = 2
          and c.connect_instance_id = #{instanceId}
    </select>

    <select id="getChannelByEmailTo"
            resultType="com.goclouds.crm.platform.call.domain.vo.ChannelByEmailToVo">
        select c.channel_id,c.name
        from crm_channel_config c
                 inner join crm_channel_info_config_inst ci on c.channel_id = ci.channel_id
        where c.data_status = 1
          and c.enable_status = 1
          and ci.data_status = 1
          and ci.field_type = 1
          and ci.code = 'email_account'
          and c.company_id = #{companyId}
          and ci.name = #{emailTo}
    </select>
</mapper>
