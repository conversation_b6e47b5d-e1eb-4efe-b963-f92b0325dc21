<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmCallTicketSmartFillDetailMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmCallTicketSmartFillDetail">
            <id property="detailId" column="detail_id" jdbcType="VARCHAR"/>
            <result property="smartFillId" column="smart_fill_id" jdbcType="VARCHAR"/>
            <result property="attrName" column="attr_name" jdbcType="VARCHAR"/>
            <result property="attrValueExample" column="attr_value_example" jdbcType="VARCHAR"/>
            <result property="attrDescribe" column="attr_describe" jdbcType="VARCHAR"/>
            <result property="storeAttrOrNot" column="store_attr_or_not" jdbcType="INTEGER"/>
            <result property="storeAttrType" column="store_attr_type" jdbcType="VARCHAR"/>
            <result property="storeAttr" column="store_attr" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="attrOrder" column="attr_order" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        detail_id,smart_fill_id,attr_name,
        attr_value_example,attr_describe,store_attr_or_not,
        store_attr_type,store_attr,creator,
        create_time,modifier,modify_time,
        data_status,attr_order
    </sql>
</mapper>
