<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmCallWorkTimeHolidayMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmCallWorkTimeHoliday">
            <id property="holidayId" column="holiday_id" jdbcType="VARCHAR"/>
            <result property="workTimeId" column="work_time_id" jdbcType="VARCHAR"/>
            <result property="holidayName" column="holiday_name" jdbcType="VARCHAR"/>
            <result property="startDate" column="start_date" jdbcType="TIMESTAMP"/>
            <result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        holiday_id,work_time_id,holiday_name,
        start_date,end_date,modifier,
        modify_time,creator,create_time,
        data_status
    </sql>
</mapper>
