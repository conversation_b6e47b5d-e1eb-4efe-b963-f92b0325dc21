<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordExtDefMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordExtDef">
            <id property="workRecordExtDefId" column="work_record_ext_def_id" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="propertyTypeId" column="property_type_id" jdbcType="VARCHAR"/>
            <result property="workRecordExtDefName" column="work_record_ext_def_name" jdbcType="VARCHAR"/>
            <result property="workRecordExtDefCode" column="work_record_ext_def_code" jdbcType="VARCHAR"/>
            <result property="isSystemDefault" column="is_system_default" jdbcType="INTEGER"/>
            <result property="isRequired" column="is_required" jdbcType="INTEGER"/>
            <result property="workRecordExtDefOrder" column="work_record_ext_def_order" jdbcType="INTEGER"/>
            <result property="prompt" column="prompt" jdbcType="VARCHAR"/>
            <result property="optionsType" column="options_type" jdbcType="INTEGER"/>
            <result property="interfaceUrl" column="interface_url" jdbcType="VARCHAR"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_ext_def_id,company_id,property_type_id,
        work_record_ext_def_name,work_record_ext_def_code,is_system_default,
        is_ required,work_record_ext_def_order,prompt,
        options_type,interface_url,data_status,
        creator,create_time,modifier,
        modify_time
    </sql>

    <delete id="deleteBatchExtDefByIdList">
        delete
        from crm_agent_work_record_ext_def
        where company_id = #{companyId}
        and is_system_default = #{isSystemDefault}
        and work_record_ext_def_id in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
