<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmExtOptionDefTemplateMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmExtOptionDefTemplate">
            <id property="optionId" column="option_id" jdbcType="VARCHAR"/>
            <result property="extDefId" column="ext_def_id" jdbcType="VARCHAR"/>
            <result property="optionName" column="option_name" jdbcType="VARCHAR"/>
            <result property="optionValue" column="option_value" jdbcType="VARCHAR"/>
            <result property="optionOrder" column="option_order" jdbcType="INTEGER"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        option_id,ext_def_id,option_name,
        option_value,option_order,data_status,
        creator,create_time,modifier,
        modify_time
    </sql>
</mapper>
