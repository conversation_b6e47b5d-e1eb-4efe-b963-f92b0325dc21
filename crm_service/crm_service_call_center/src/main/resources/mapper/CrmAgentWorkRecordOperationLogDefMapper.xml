<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordOperationLogDefMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordOperationLogDef">
            <id property="contentId" column="content_id" jdbcType="VARCHAR"/>
            <result property="languageCode" column="language_code" jdbcType="VARCHAR"/>
            <result property="operationType" column="operation_type" jdbcType="INTEGER"/>
            <result property="logContent" column="log_content" jdbcType="VARCHAR"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        content_id,language_code,operation_type,
        log_content,data_status,creator,
        create_time,modifier,modify_time
    </sql>
</mapper>
