<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordSlaDefMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordSlaDef">
            <id property="workRecordSlaId" column="work_record_SLA_id" jdbcType="VARCHAR"/>
            <result property="priorityLevelId" column="priority_level_id" jdbcType="VARCHAR"/>
            <result property="responseTime" column="response_time" jdbcType="INTEGER"/>
            <result property="nextResponseTime" column="next_response_time" jdbcType="INTEGER"/>
            <result property="nextResponseTimeUnit" column="next_response_time_unit" jdbcType="VARCHAR"/>
            <result property="responseTimeUnit" column="response_time_unit" jdbcType="VARCHAR"/>
            <result property="resolveTime" column="resolve_time" jdbcType="INTEGER"/>
            <result property="resolveTimeUnit" column="resolve_time_unit" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_SLA_id,priority_level_id,response_time,
        response_time_unit,nextResponseTime,nextResponseTimeUnit,resolve_time,resolve_time_unit,
        company_id,data_status,creator,
        create_time,modifier,modify_time
    </sql>
</mapper>
