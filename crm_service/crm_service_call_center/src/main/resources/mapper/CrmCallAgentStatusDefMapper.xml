<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmCallAgentStatusDefMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmCallAgentStatusDef">
            <result property="agentStatusId" column="agent_status_id" jdbcType="VARCHAR"/>
            <result property="agentStatusName" column="agent_status_name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="enableStatus" column="enable_status" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="initStatus" column="init_status" jdbcType="BIGINT"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        agent_status_id,agent_status_name,description,
        enable_status,type,init_status,company_id,
        modifier,modify_time,creator,
        create_time,data_status
    </sql>
</mapper>
