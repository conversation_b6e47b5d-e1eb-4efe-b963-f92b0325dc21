<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmChannelInfoConfigInstMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmChannelInfoConfigInst">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="channelId" column="channel_id" jdbcType="VARCHAR"/>
            <result property="channelSubtype" column="channel_subtype" jdbcType="TINYINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="fieldType" column="field_type" jdbcType="TINYINT"/>
            <result property="mappingRule" column="mapping_rule" jdbcType="VARCHAR"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,channel_id,channel_subtype,
        code,name,field_type,
        mapping_rule,data_status,create_time,
        creator,modifier,modify_time
    </sql>
</mapper>
