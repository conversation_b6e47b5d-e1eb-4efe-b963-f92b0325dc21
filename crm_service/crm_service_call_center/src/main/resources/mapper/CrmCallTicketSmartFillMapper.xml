<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmCallTicketSmartFillMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmCallTicketSmartFill">
            <id property="smartFillId" column="smart_fill_id" jdbcType="VARCHAR"/>
            <result property="languageCode" column="language_code" jdbcType="VARCHAR"/>
            <result property="ticketTypeCode" column="ticket_type_code" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        smart_fill_id,language_code,ticket_type_code,
        company_id,creator,create_time,
        modifier,modify_time,data_status
    </sql>


    <resultMap id="ticketSmartFillVoMap" type="com.goclouds.crm.platform.call.domain.smartFill.TicketSmartFillVo">
        <id property="smartFillId" column="smart_fill_id"/>
        <result property="languageCode" column="language_code"/>
        <result property="ticketTypeCode" column="ticket_type_code"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <collection property="ticketSmartFillDetailList" ofType="com.goclouds.crm.platform.call.domain.smartFill.TicketSmartFillDetailVo"
                    select="selectDetailsBySmartFillId" column="smart_fill_id"/>
    </resultMap>

    <select id="selectPageWithDetails" resultMap="ticketSmartFillVoMap">
        SELECT
            smart_fill_id,
            language_code,
            ticket_type_code,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
            DATE_FORMAT(modify_time, '%Y-%m-%d %H:%i:%s') as modify_time
        FROM
            crm_call_ticket_smart_fill
        WHERE
            data_status = 1
          AND company_id = #{companyId}
        ORDER BY
            modify_time DESC
    </select>

    <select id="selectTicketSmartFillDetail" resultMap="ticketSmartFillVoMap">
        SELECT
            smart_fill_id,
            language_code,
            ticket_type_code,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
            DATE_FORMAT(modify_time, '%Y-%m-%d %H:%i:%s') as modify_time
        FROM
            crm_call_ticket_smart_fill
        WHERE
            data_status = 1
          AND company_id = #{companyId}
          AND smart_fill_id = #{smartFillId}
    </select>

    <select id="selectDetailsBySmartFillId" resultType="com.goclouds.crm.platform.call.domain.smartFill.TicketSmartFillDetailVo">
        SELECT
            attr_name as attrName,
            attr_value_example as attrValueExample,
            attr_describe as attrDescribe,
            store_attr_or_not as storeAttrOrNot,
            store_attr_type as storeAttrType,
            store_attr as storeAttr,
            attr_order as attrOrder
        FROM
            crm_call_ticket_smart_fill_detail
        WHERE
            smart_fill_id = #{smart_fill_id}
          AND data_status = 1
        ORDER BY
            attr_order asc
    </select>
</mapper>
