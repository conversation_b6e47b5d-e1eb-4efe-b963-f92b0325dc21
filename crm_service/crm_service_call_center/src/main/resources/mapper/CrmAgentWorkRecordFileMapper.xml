<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordFileMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFile">
            <id property="workRecordFileId" column="work_record_file_id" jdbcType="VARCHAR"/>
            <result property="workRecordId" column="work_record_id" jdbcType="VARCHAR"/>
            <result property="workRecordDetailId" column="work_record_detail_id" jdbcType="VARCHAR"/>
            <result property="workRecordContentId" column="work_record_content_id" jdbcType="VARCHAR"/>
            <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="bucketName" column="bucket_name" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_file_id,work_record_id,work_record_detail_id,
        work_record_content_id,file_path,file_name,
        bucket_name,creator,create_time,
        data_status
    </sql>
</mapper>
