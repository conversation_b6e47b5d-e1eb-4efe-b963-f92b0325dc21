<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmTicketAssessmentRecordMapper">

    <!-- 获取工单最新评估记录 -->
    <select id="getLatestAssessment" resultType="com.goclouds.crm.platform.call.domain.vo.AssessmentBriefForRecordVO">
        select
            ctar.assessment_record_id,
            ctar.assessment_status,
            ctar.assessor_id,
            (select user_name from sys_user where user_id = ctar.assessor_id) as assessor_name,
            ctar.assessment_id,
            ctar.assessment_version_id,
            ctar.total_score,
            ctar.ticket_id,
            ctar.channel_type_id,
            ctar.create_time,
            ctar.company_id,
            ctar.assessed_agent_id,
            (select user_name from sys_user where user_id = ctar.assessed_agent_id) as assessed_agent_name,
            ctafi.assessment_name,
            ctafi.full_score,
            ctafi.scoring_rule,
            ctafi.score_color,
            ctafv.version_no as assessment_version_no
        from crm_ticket_assessment_record ctar
                 left join crm_ticket_assessment_form_info ctafi on ctafi.assessment_id = ctar.assessment_id
                 left join crm_ticket_assessment_form_version ctafv on ctafv.version_id = ctar.assessment_version_id and ctafv.deploy_status = 1
                 left join sys_user su on su.user_id = ctar.assessor_id
        where ctar.data_status = 1 and ctar.ticket_id = #{ticketId}
        <if test="assessorId != null and assessorId != ''">
            and ctar.assessor_id = #{assessorId}
        </if>
        <if test="assessmentRecordId != null and assessmentRecordId != ''">
            and ctar.assessment_record_id = #{assessmentRecordId}
        </if>
        order by ctar.create_time desc limit 0,1
    </select>

    <!-- 获取可用的评估表列表 -->
    <select id="getAvailableAssessments" resultType="com.goclouds.crm.platform.call.domain.vo.AssessmentBriefVO">
        SELECT
            ctafi.assessment_id,
            ctafi.assessment_name,
            latest_version.version_id as assessment_version_id,
            latest_version.version_no as assessment_version_no
        FROM crm_ticket_assessment_form_info ctafi
        INNER JOIN (
            SELECT v1.* FROM crm_ticket_assessment_form_version v1
            INNER JOIN (
                SELECT assessment_id, MAX(create_time) as max_create_time
                FROM crm_ticket_assessment_form_version
                WHERE deploy_status = 1 GROUP BY assessment_id
            ) v2 ON v1.assessment_id = v2.assessment_id AND v1.create_time = v2.max_create_time AND v1.deploy_status = 1
        ) latest_version ON latest_version.assessment_id = ctafi.assessment_id
        <where>
            ctafi.data_status = 1 AND ctafi.status = 1
            AND ctafi.company_id = #{companyId}
            AND FIND_IN_SET(#{userId}, ctafi.raters)
            AND (
                (ctafi.ticket_type IS NULL OR ctafi.ticket_type = '')
                OR
                (ctafi.channel_config_id IS NULL OR ctafi.channel_config_id = '')
                <if test="ticketType != null and ticketType != ''">
                    OR FIND_IN_SET(#{ticketType}, ctafi.ticket_type)
                </if>
                <if test="channelConfigId != null and channelConfigId != ''">
                    OR FIND_IN_SET(#{channelConfigId}, ctafi.channel_config_id)
                </if>
            )
        </where>
    </select>

    <!-- 获取评估的规则详情 -->
    <resultMap id="assessmentRecordRuleDetailMap" type="com.goclouds.crm.platform.call.domain.vo.AssessmentRecordRuleDetailVO">
        <id property="assessmentRecordId" column="assessment_record_id"/>
        <result property="scoringRule" column="scoring_rule"/>
        <result property="totalScore" column="total_score"/>
        <result property="scoreColor" column="score_color"/>
        <result property="fullScore" column="full_score"/>
        <collection property="rules" ofType="com.goclouds.crm.platform.call.domain.vo.AssessmentRecordRuleItemsVO">
            <id property="assessmentRecordRuleItemId" column="assessment_record_rule_item_id"/>
            <result property="assessmentRuleId" column="assessment_rule_id"/>
            <result property="ruleName" column="rule_name"/>
            <result property="aiScore" column="ai_score"/>
            <result property="manualScore" column="manual_score"/>
        </collection>
    </resultMap>
    <select id="getAssessmentDetailRule" resultMap="assessmentRecordRuleDetailMap">
        SELECT
            ctar.assessment_record_id,
            ctar.assessment_status,
            ctar.total_score,
            ctarri.assessment_record_rule_item_id,
            ctarri.assessment_rule_id,
            ctarri.ai_score,
            ctarri.manual_score,
            ctar2.rule_name,
            ctafi.scoring_rule,
            ctafi.score_color,
            ctafi.full_score
        FROM crm_ticket_assessment_record ctar
                 LEFT JOIN crm_ticket_assessment_record_rule_item ctarri
                           ON ctarri.assessment_record_id = ctar.assessment_record_id
                 LEFT JOIN crm_ticket_assessment_rule ctar2
                           ON ctar2.rule_id = ctarri.assessment_rule_id
                 LEFT JOIN crm_ticket_assessment_form_info ctafi
                           ON ctafi.assessment_id = ctar.assessment_id
        WHERE ctar.assessment_record_id = #{recordId}
    </select>

    <!-- 评估记录分页列表 -->
    <select id="selectAssessmentRecordPages" resultType="com.goclouds.crm.platform.call.domain.vo.CrmTicketAssessmentRecordPageVO">
        SELECT
            ctar.assessment_record_id as assessmentRecordId,
            ctar.channel_config_id as channelConfigId,
            ctar.channel_type_id as channelTypeId,
            ctar.assessor_id as assessorId,
            (select user_name from sys_user where user_id = ctar.assessor_id) as assessorName,
            ctar.assessed_agent_id as assessedAgentId,
            ctar.ticket_id as ticketId,
            ctar.ticket_code as ticketCode,
            ctar.ticket_type as ticketType,
            ctar.ticket_type_name as ticketTypeName,
            ctar.assessment_status as assessmentStatus,
            ctar.create_time createTime,
            ctar.total_score totalScore,
            ctafi.assessment_name as assessmentName,
            ccd.name as channelTypeName,
            ccc.name as channelConfigName,
            (select user_name from sys_user where user_id = ctar.assessed_agent_id) as assessedAgentName
        FROM crm_ticket_assessment_record ctar
        LEFT JOIN crm_ticket_assessment_form_info ctafi ON ctafi.assessment_id = ctar.assessment_id
        LEFT JOIN crm_channel_def ccd ON ccd.code = ctar.channel_type_id
        LEFT join crm_channel_config ccc on ccc.channel_id = ctar.channel_config_id
        <where>
            ctar.data_status = 1
            AND ctar.company_id = #{dto.companyId}
            AND ctar.assessment_status = 2
            <if test="dto.assessmentId != null and dto.assessmentId != ''">
                AND ctar.assessment_id = #{dto.assessmentId}
            </if>
            <if test="dto.assessmentVersionId != null and dto.assessmentVersionId != ''">
                AND ctar.assessment_version_id = #{dto.assessmentVersionId}
            </if>
            <if test="dto.assessmentFormName != null and dto.assessmentFormName != ''">
                AND ctafi.assessment_name LIKE CONCAT('%', #{dto.assessmentFormName}, '%')
            </if>
            <if test="dto.channelId != null and dto.channelId != ''">
                AND ctar.channel_config_id = #{dto.channelId}
            </if>
            <if test="dto.ticketType != null and dto.ticketType != ''">
                AND ctar.ticket_type = #{dto.ticketType}
            </if>
            <if test="dto.assessorId != null and dto.assessorId != ''">
                AND ctar.assessor_id = #{dto.assessorId}
            </if>
            <if test="dto.assessmentStartTime != null and dto.assessmentStartTime != '' and dto.assessmentEndTime != null and dto.assessmentEndTime != ''">
                AND ctar.create_time BETWEEN #{dto.assessmentStartTime} AND #{dto.assessmentEndTime}
            </if>
            <if test="dto.assessedAgentId != null and dto.assessedAgentId != ''">
                AND ctar.assessed_agent_id = #{dto.assessedAgentId}
            </if>
            <if test="dto.ticketCode != null and dto.ticketCode != ''">
                AND ctar.ticket_code = #{dto.ticketCode}
            </if>
            <if test="dto.scoreFirst != null and dto.scoreLast != null">
                AND ctar.total_score BETWEEN #{dto.scoreFirst} AND #{dto.scoreLast}
            </if>
        </where>
        ORDER BY ctar.create_time DESC
    </select>

    <!-- 查询指定工单&指定用户最新的评估记录ID -->
    <select id="getLastAssessmentRecordByTicketAndUserId" resultType="java.lang.String">
        select assessment_record_id from crm_ticket_assessment_record ctar
        where ctar.data_status = 1
          and ctar.ticket_id = #{ticketId}
          and ctar.assessor_id = #{userId}
          and ctar.assessment_status &lt;&gt; 2
        order by ctar.create_time desc
            limit 0,1
    </select>


    <!-- 查询评估表信息是否存在，执行评估ID和版本ID -->
    <select id="getAssessmentFormByIdAndVersionId" resultType="java.lang.Boolean">
        SELECT
            count(ctafi.assessment_id)
        FROM crm_ticket_assessment_form_info ctafi
                 INNER JOIN crm_ticket_assessment_form_version ctafv ON ctafv.assessment_id = ctafi.assessment_id and ctafv.deploy_status = 1
        WHERE ctafi.data_status = 1
          AND ctafi.assessment_id = #{assessmentId}
          AND ctafv.version_id = #{versionId}
    </select>

    <!-- 查询评估表的规则列表，根据评估ID和版本ID -->
    <select id="getRuleBaseListByVersionId" resultType="com.goclouds.crm.platform.call.domain.CrmTicketAssessmentRecordRuleItem">
        SELECT
            ctar.rule_id as assessment_rule_id,
            ctar.score_type as assessment_type,
            ctar.rule_sort
        FROM crm_ticket_assessment_category ctac
        INNER JOIN crm_ticket_assessment_rule ctar
        ON ctar.category_id = ctac.category_id AND ctar.data_status = 1 and ctar.status = 0
        WHERE ctac.data_status = 1
          and ctar.assessment_id = #{assessmentId}
          and ctac.version_id = #{versionId}
    </select>

    <!-- 查询所有分类规则，根据评估记录ID -->
    <resultMap id="CategoryTreeMap" type="com.goclouds.crm.platform.call.domain.vo.AssessmentCategoryVO">
        <id column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_level" property="categoryLevel"/>
        <result column="parent_id" property="parentId"/>
        <result column="category_sort" property="categorySort"/>

        <collection property="rules" ofType="com.goclouds.crm.platform.call.domain.vo.AssessmentRuleVO">
            <id column="assessment_record_rule_item_id" property="assessmentRecordRuleItemId"/>
            <id column="assessment_record_id" property="assessmentRecordId"/>
            <result column="rule_id" property="ruleId"/>
            <result column="rule_name" property="ruleName"/>
            <result column="description" property="description"/>
            <result column="reference" property="reference"/>
            <result column="score_rule" property="aiAssessmentRule"/>
            <result column="score_type" property="assessmentType"/>
            <result column="rule_total_score" property="totalScore"/>

            <result column="assessment_status" property="assessmentStatus"/>
            <result column="ai_score" property="aiScore"/>
            <result column="manual_score" property="manualScore"/>
            <result column="manual_option_id" property="manualOptionId"/>
            <result column="manual_option_name" property="manualOptionName"/>
            <result column="assessment_remark" property="assessmentRemark"/>
            <result column="rule_sort" property="ruleSort"/>

            <collection property="qualityCheckpoints" ofType="com.goclouds.crm.platform.call.domain.vo.AssessmentRuleQualityCheckpointVO">
                <id column="point_id" property="pointId"/>
                <id column="point_rule_id" property="ruleId"/>
                <result column="point_desc" property="pointDesc"/>
                <result column="point_score" property="pointScore"/>
                <result column="point_sort" property="pointSort"/>
            </collection>
        </collection>
    </resultMap>
    <select id="getAllCategoryRuleByRecordId" resultMap="CategoryTreeMap">
        -- 第一步：从 assessment_record_rule_item 表中取出所有关联的分类
        WITH RECURSIVE category_tree AS (
            -- 1. 找当前评估记录相关的规则所对应的分类
            SELECT DISTINCT c.*
            FROM crm_ticket_assessment_record_rule_item i
                     JOIN crm_ticket_assessment_rule r ON i.assessment_rule_id = r.rule_id and r.data_status = 1 and r.status = 0
                     JOIN crm_ticket_assessment_category c ON r.category_id = c.category_id
            WHERE i.assessment_record_id = #{recordId}

            UNION ALL

            -- 2. 向上递归查找所有父分类（最多查到 level=1）
            SELECT pc.*
            FROM crm_ticket_assessment_category pc
                     JOIN category_tree ct ON ct.parent_id = pc.category_id
        )

                       -- 第二步：查询所有分类 + 所属规则（规则只出现在关联分类上）
        SELECT
            c.category_id,
            c.category_name,
            c.level AS category_level,
            c.parent_id,
            c.sort AS category_sort,
            r.rule_id,
            r.rule_name,
            r.description,
            r.reference,
            r.score_rule,
            r.score_type,
            r.score AS rule_total_score,
            i.assessment_record_rule_item_id,
            i.assessment_record_id,
            i.assessment_status,
            i.ai_score,
            i.manual_score,
            i.manual_option_id,
            i.manual_option_name,
            i.assessment_remark,
            i.rule_sort
        FROM category_tree c
                 LEFT JOIN crm_ticket_assessment_rule r ON r.category_id = c.category_id and r.data_status = 1 and r.status = 0
                 LEFT JOIN crm_ticket_assessment_record_rule_item i
                           ON i.assessment_rule_id = r.rule_id AND i.assessment_record_id = #{recordId}
        ORDER BY c.sort, i.rule_sort;
    </select>

    <!-- 查询所有质检点，根据评估记录ID -->
    <select id="getAllQualityCheckpointByRecordId"
            resultType="com.goclouds.crm.platform.call.domain.vo.AssessmentRuleQualityCheckpointVO">
        SELECT p.point_id,p.rule_id,p.point_desc, p.score as point_score, p.point_sort
        FROM crm_ticket_assessment_rule_point p
        WHERE p.rule_id IN (
            SELECT r.rule_id
            FROM crm_ticket_assessment_rule r
                     JOIN crm_ticket_assessment_category c ON r.category_id = c.category_id
            WHERE c.version_id = (
                SELECT assessment_version_id
                FROM crm_ticket_assessment_record
                WHERE assessment_record_id = #{recordId}
            )
        )
        ORDER BY p.rule_id, p.point_sort;
    </select>


    <!-- 查询评估记录信息，根据评估记录ID -->
    <select id="getAssessmentRecordByRecordId"
            resultType="com.goclouds.crm.platform.call.domain.vo.AssessmentBriefForRecordVO">
        select
            ctar.assessment_record_id,
            ctar.assessment_status,
            ctar.assessor_id,
            (select user_name from sys_user where user_id = ctar.assessor_id) as assessor_name,
            ctar.assessment_id,
            ctar.assessment_version_id,
            ctar.total_score,
            ctar.ticket_id,
            ctar.channel_type_id,
            ctar.create_time,
            ctar.company_id,
            ctar.assessed_agent_id,
            (select user_name from sys_user where user_id = ctar.assessed_agent_id) as assessed_agent_name,
            ctafi.assessment_name,
            ctafi.full_score,
            ctafi.scoring_rule,
            ctafi.score_color,
            ctafv.version_no as assessment_version_no
        from crm_ticket_assessment_record ctar
                 left join crm_ticket_assessment_form_info ctafi on ctafi.assessment_id = ctar.assessment_id
                 left join crm_ticket_assessment_form_version ctafv on ctafv.version_id = ctar.assessment_version_id
        where ctar.data_status = 1 and ctar.assessment_record_id = #{recordId}
    </select>


</mapper>
