<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordAutoMergeMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordAutoMerge">
            <id property="channelTypeId" column="channel_type_id" jdbcType="INTEGER"/>
            <id property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="autoMergeFlag" column="auto_merge_flag" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        channel_type_id,company_id,auto_merge_flag,
        creator,create_time,modifier,
        modify_time,data_status
    </sql>

    <select id="queryAutoMergeSetting" parameterType="java.lang.String" resultType="com.goclouds.crm.platform.call.domain.vo.WorkRecordAutoMergeVo">
        select t1.channel_type_id as channelTypeId, t2.name as channelTypeName, t1.auto_merge_flag as autoMergeFlag
        from crm_agent_work_record_auto_merge t1
                 left join crm_channel_def t2
                           on t1.channel_type_id = t2.code
        where t1.data_status = 1
          and t1.company_id = #{companyId};
    </select>
</mapper>
