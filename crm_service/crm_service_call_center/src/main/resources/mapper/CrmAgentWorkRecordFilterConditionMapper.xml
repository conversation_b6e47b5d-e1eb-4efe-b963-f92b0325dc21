<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordFilterConditionMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordFilterCondition">
            <id property="workRecordFilterConditionId" column="work_record_filter_condition_id" jdbcType="VARCHAR"/>
            <result property="workRecordExtDefCode" column="work_record_ext_def_code" jdbcType="VARCHAR"/>
            <result property="workRecordExtDefValue" column="work_record_ext_def_value" jdbcType="VARCHAR"/>
            <result property="workRecordFilterId" column="work_record_filter_id" jdbcType="VARCHAR"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_filter_condition_id,work_record_ext_def_code,work_record_ext_def_value,
        work_record_filter_id,data_status,creator,
        create_time,modifier,modify_time
    </sql>
</mapper>
