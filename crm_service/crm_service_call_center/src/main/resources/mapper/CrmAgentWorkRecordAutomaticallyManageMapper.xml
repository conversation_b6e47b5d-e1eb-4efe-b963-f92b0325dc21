<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goclouds.crm.platform.call.mapper.CrmAgentWorkRecordAutomaticallyManageMapper">

    <resultMap id="BaseResultMap" type="com.goclouds.crm.platform.call.domain.CrmAgentWorkRecordAutomaticallyManage">
            <result property="workRecordCloseId" column="work_record_close_id" jdbcType="VARCHAR"/>
            <result property="closeType" column="close_type" jdbcType="BIGINT"/>
            <result property="closeStatus" column="close_status" jdbcType="BIGINT"/>
            <result property="initStatus" column="init_status" jdbcType="BIGINT"/>
            <result property="order" column="order" jdbcType="BIGINT"/>
            <result property="time" column="time" jdbcType="INTEGER"/>
            <result property="timeUnit" column="time_unit" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
            <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="data_status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        work_record_close_id, close_type, close_status, init_status,
`order`, `time`, time_unit,
company_id, modifier, modify_time,
creator, create_time, data_status
    </sql>
</mapper>
