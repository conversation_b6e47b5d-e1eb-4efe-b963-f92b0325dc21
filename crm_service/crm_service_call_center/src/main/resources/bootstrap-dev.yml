spring:
  application:
    name: crm-service-call
  cloud:
    nacos:
      discovery:
        server-addr: 18.139.37.35:8848
        namespace: ${namespace:92bb5d5c-8041-4ca4-950a-d0b77a677df1}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yaml
        namespace: ${spring.cloud.nacos.discovery.namespace}
        username: nacos
        password: nacos
        # 共享配置文件数组
        shared-configs:
          - data_id: spring-common-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true
      
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  global-config:
#    enable-sql-runner: true