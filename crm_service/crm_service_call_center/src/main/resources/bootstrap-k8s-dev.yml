spring:
  application:
    name: crm-service-call
  cloud:
    nacos:
      discovery:
        #server-addr: 18.139.37.35:8848
        server-addr: 10.200.3.163:30101
        #namespace: ${namespace:1f3afe0d-365e-430c-b32e-d47777b36f61}
        namespace: ${namespace:8f98cc46-2262-4796-b2c3-674d5feac2f3}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yaml
        namespace: ${spring.cloud.nacos.discovery.namespace}
        username: nacos
        password: nacos
        # 共享配置文件数组
        shared-configs:
          - data_id: spring-common-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true
      
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  global-config:
#    enable-sql-runner: true