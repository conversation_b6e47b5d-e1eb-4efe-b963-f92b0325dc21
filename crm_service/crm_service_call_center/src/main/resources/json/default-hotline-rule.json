{"ivr": {"ivrQueue": [{"startNum": 0, "endNum": 1, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 1, "endNum": 5, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 5, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "avgInboundQueueTime": [{"startNum": 0, "endNum": 30, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 30, "endNum": 300, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 300, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}]}, "inbound": {"surgeThreshold": 120}, "connectionRate": {"totalConnectionRate": [{"startNum": 0, "endNum": 50, "colorCode": "#F22417", "isAlarmColor": 1}, {"startNum": 50, "endNum": 80, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 80, "endNum": null, "colorCode": "#3463FC", "isAlarmColor": 0}], "inboundConnectionRate": [{"startNum": 0, "endNum": 50, "colorCode": "#F22417", "isAlarmColor": 1}, {"startNum": 50, "endNum": 80, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 80, "endNum": null, "colorCode": "#3463FC", "isAlarmColor": 0}], "serviceTimeInboundConnectionRate": [{"startNum": 0, "endNum": 50, "colorCode": "#F22417", "isAlarmColor": 1}, {"startNum": 50, "endNum": 80, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 80, "endNum": null, "colorCode": "#3463FC", "isAlarmColor": 0}], "outboundConnectionRate": [{"startNum": 0, "endNum": 50, "colorCode": "#F22417", "isAlarmColor": 1}, {"startNum": 50, "endNum": 80, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 80, "endNum": null, "colorCode": "#3463FC", "isAlarmColor": 0}]}, "acw": {"inboundAvgTime": [{"startNum": 0, "endNum": 30, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 30, "endNum": 300, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 300, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "outboundAvgTime": [{"startNum": 0, "endNum": 30, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 30, "endNum": 300, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 300, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}]}, "callLossRate": {"totalCallLossRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "missedCallRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "ivrAbandonRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "queueAbandonRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "nonServiceTimeInboundRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}]}, "satisfaction": {"satisfactionAvg": [{"startNum": 0, "endNum": 3, "colorCode": "#F22417", "isAlarmColor": 1}, {"startNum": 3, "endNum": 4, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 4, "endNum": 5, "colorCode": "#3463FC", "isAlarmColor": 0}], "inboundSatisfactionAvg": [{"startNum": 0, "endNum": 3, "colorCode": "#F22417", "isAlarmColor": 1}, {"startNum": 3, "endNum": 4, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 4, "endNum": 5, "colorCode": "#3463FC", "isAlarmColor": 0}], "outboundSatisfactionAvg": [{"startNum": 0, "endNum": 3, "colorCode": "#F22417", "isAlarmColor": 1}, {"startNum": 3, "endNum": 4, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 4, "endNum": 5, "colorCode": "#3463FC", "isAlarmColor": 0}]}, "otherSetting": {"inboundSwitchRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "outboundSwitchRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "inboundAgentHangUpRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "outboundAgentHangUpRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}], "repeatEntryRate": [{"startNum": 0, "endNum": 10, "colorCode": "#3463FC", "isAlarmColor": 0}, {"startNum": 10, "endNum": 20, "colorCode": "#FCB830", "isAlarmColor": 0}, {"startNum": 20, "endNum": null, "colorCode": "#F22417", "isAlarmColor": 1}]}}